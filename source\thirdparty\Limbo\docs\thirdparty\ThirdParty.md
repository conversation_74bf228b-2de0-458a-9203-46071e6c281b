Limbo.ThirdParty {#ThirdParty}
---------

## Components 
| Components              | Languages & Libraries | Description                                                                                                                                              |
| ----------------------- | ----------------------| -------------------------------------------------------------------------------------------------------------------------------------------------------- |
| Third Party Libraries   | C/C++/Fortran         | OpenBLAS, Csdp, CThreadPool, dlx, libdivide                                                                                                   |
| OpenBLAS                | C/Fortran             | A numeric library. Refer to [OpenBLAS](http://www.openblas.net "OpenBLAS")                                                                               |
| Csdp                    | C                     | A semidefinite programming solver. Refer to [Csdp](https://projects.coin-or.org/Csdp "Csdp")                                                             |
| CThreadPool             | C/pthread             | A thread pool library. Refer to [CThreadPool](https://github.com/Pithikos/C-Thread-Pool "CThreadPool")                                                   |
| libdivide               | C++                   | A library for optimizing integer division. Refer to [libdivide](http://libdivide.com "libdivide")                                                        |
| LEF/DEF parsers         | C/C++                 | A library for reading LEF/DEF files. Refer to [LEF](https://en.wikipedia.org/wiki/Library_Exchange_Format) and [DEF](https://en.wikipedia.org/wiki/Design_Exchange_Format)    |
