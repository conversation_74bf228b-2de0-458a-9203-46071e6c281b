#!/usr/bin/env python3
##
# @file   test_shared_ddp.py
# <AUTHOR> Assistant
# @date   2024
# @brief  Test script for shared parameter DDP implementation validation
#

import os
import sys
import unittest
import numpy as np
import torch
import torch.distributed as dist
import torch.multiprocessing as mp
from unittest.mock import MagicMock, patch

# Add dreamplace to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'dreamplace'))

from dreamplace.ddp_shared_param_utils import SharedParamDDPPartitioner, SharedParamDDPDataCollection
from dreamplace.ops.electric_potential.electric_potential_shared_ddp import ElectricPotentialSharedDDP
from dreamplace.ops.electric_potential.electric_overflow_shared_ddp import ElectricOverflowSharedDDP
from dreamplace.ops.weighted_average_wirelength.weighted_average_wirelength_shared_ddp import WeightedAverageWirelengthSharedDDP, PinPosOp

class MockPlaceDB:
    """Mock PlaceDB for testing"""
    def __init__(self):
        self.num_movable_nodes = 100
        self.num_terminals = 20
        self.num_filler_nodes = 10
        self.num_physical_nodes = 120
        self.num_nodes = 130
        self.num_nets = 80
        
        # Create mock data
        self.node_size_x = np.random.uniform(1.0, 5.0, self.num_nodes)
        self.node_size_y = np.random.uniform(1.0, 5.0, self.num_nodes)
        self.node_names = np.array([f"node_{i}" for i in range(self.num_physical_nodes)])
        self.node_name2id_map = {f"node_{i}": i for i in range(self.num_physical_nodes)}
        
        # Create mock net data
        self.net_names = np.array([f"net_{i}" for i in range(self.num_nets)])
        self.net2pin_map = []
        self.pin2net_map = []
        self.pin2node_map = []
        self.pin_offset_x = []
        self.pin_offset_y = []
        
        pin_id = 0
        for net_id in range(self.num_nets):
            # Random number of pins per net (2-8)
            num_pins = np.random.randint(2, 9)
            net_pins = []
            for _ in range(num_pins):
                node_id = np.random.randint(0, self.num_physical_nodes)
                net_pins.append(pin_id)
                self.pin2net_map.append(net_id)
                self.pin2node_map.append(node_id)
                self.pin_offset_x.append(np.random.uniform(-0.5, 0.5))
                self.pin_offset_y.append(np.random.uniform(-0.5, 0.5))
                pin_id += 1
            self.net2pin_map.append(np.array(net_pins))
        
        self.net2pin_map = np.array(self.net2pin_map, dtype=object)
        self.pin2net_map = np.array(self.pin2net_map)
        self.pin2node_map = np.array(self.pin2node_map)
        self.pin_offset_x = np.array(self.pin_offset_x)
        self.pin_offset_y = np.array(self.pin_offset_y)
        
        # Create flat maps
        self.flat_net2pin_map = np.concatenate(self.net2pin_map)
        self.flat_net2pin_start_map = np.zeros(len(self.net2pin_map) + 1, dtype=np.int32)
        for i, net_pins in enumerate(self.net2pin_map):
            self.flat_net2pin_start_map[i+1] = self.flat_net2pin_start_map[i] + len(net_pins)
        
        # Layout parameters
        self.xl, self.yl = 0.0, 0.0
        self.xh, self.yh = 100.0, 100.0
        self.bin_size_x, self.bin_size_y = 2.0, 2.0
        self.num_bins_x = int((self.xh - self.xl) / self.bin_size_x)
        self.num_bins_y = int((self.yh - self.yl) / self.bin_size_y)
        self.bin_center_x = np.linspace(self.xl + self.bin_size_x/2, self.xh - self.bin_size_x/2, self.num_bins_x)
        self.bin_center_y = np.linspace(self.yl + self.bin_size_y/2, self.yh - self.bin_size_y/2, self.num_bins_y)
        self.row_height = 2.0
        self.site_width = 1.0

class TestSharedParamDDPPartitioner(unittest.TestCase):
    """Test shared parameter DDP partitioner"""
    
    def setUp(self):
        self.placedb = MockPlaceDB()
        
    def test_partition_nets(self):
        """Test net partitioning for WA computation"""
        world_size = 4
        partitioners = [SharedParamDDPPartitioner(rank, world_size) for rank in range(world_size)]
        
        # Test that all nets are covered
        all_nets = set()
        for partitioner in partitioners:
            net_partition = partitioner.partition_nets_for_wa(self.placedb)
            local_net_ids = net_partition['local_net_ids']
            all_nets.update(local_net_ids)
            
            # Check partition validity
            self.assertGreaterEqual(len(local_net_ids), 0)
            
        # Check that nets are properly distributed
        self.assertGreater(len(all_nets), 0)
        
    def test_partition_nodes(self):
        """Test node partitioning for density computation"""
        world_size = 4
        partitioners = [SharedParamDDPPartitioner(rank, world_size) for rank in range(world_size)]
        
        # Test that all movable nodes are covered
        all_nodes = set()
        for partitioner in partitioners:
            node_partition = partitioner.partition_nodes_for_density(self.placedb)
            local_node_ids = node_partition['local_node_ids']
            all_nodes.update(local_node_ids)
            
            # Check partition validity
            self.assertGreaterEqual(len(local_node_ids), 0)
            
        # Check that nodes are properly distributed
        self.assertGreater(len(all_nodes), 0)

class TestSharedParamDDPDataCollection(unittest.TestCase):
    """Test shared parameter DDP data collection"""
    
    def setUp(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.placedb = MockPlaceDB()
        
    def test_data_collection_creation(self):
        """Test shared parameter DDP data collection creation"""
        # Create mock parameters
        class MockParams:
            def __init__(self):
                self.gpu = torch.cuda.is_available()
        
        params = MockParams()
        
        # Create shared position parameters
        pos = [torch.randn(self.placedb.num_nodes * 2, device=self.device, requires_grad=True)]
        
        # Create partitioner
        partitioner = SharedParamDDPPartitioner(0, 2)
        
        # Create data collection
        data_collection = SharedParamDDPDataCollection(
            pos, params, self.placedb, self.device, partitioner
        )
        
        self.assertIsInstance(data_collection, SharedParamDDPDataCollection)
        self.assertEqual(data_collection.partitioner.rank, 0)
        self.assertEqual(data_collection.partitioner.world_size, 2)

class TestPinPosOp(unittest.TestCase):
    """Test pin position operation"""
    
    def setUp(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.placedb = MockPlaceDB()
        
    def test_pin_pos_forward(self):
        """Test pin position forward computation"""
        pin2node_map = torch.from_numpy(self.placedb.pin2node_map).to(self.device)
        pin_offset_x = torch.tensor(self.placedb.pin_offset_x, device=self.device)
        pin_offset_y = torch.tensor(self.placedb.pin_offset_y, device=self.device)
        
        pin_pos_op = PinPosOp(pin2node_map, pin_offset_x, pin_offset_y)
        
        # Create node positions
        node_pos = torch.randn(self.placedb.num_nodes * 2, device=self.device)
        
        # Compute pin positions
        pin_pos = pin_pos_op(node_pos)
        
        # Check output shape
        expected_shape = len(self.placedb.pin2node_map) * 2
        self.assertEqual(pin_pos.shape[0], expected_shape)

class TestSharedParamDDPElectricPotential(unittest.TestCase):
    """Test shared parameter DDP electric potential computation"""
    
    def setUp(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.placedb = MockPlaceDB()
        
    def test_electric_potential_shared_ddp_creation(self):
        """Test shared parameter DDP electric potential module creation"""
        node_size_x = torch.from_numpy(self.placedb.node_size_x).to(self.device)
        node_size_y = torch.from_numpy(self.placedb.node_size_y).to(self.device)
        bin_center_x = torch.from_numpy(self.placedb.bin_center_x).to(self.device)
        bin_center_y = torch.from_numpy(self.placedb.bin_center_y).to(self.device)
        
        # Create local node mask (assign first half of nodes to this GPU)
        local_node_mask = torch.zeros(self.placedb.num_nodes, dtype=torch.uint8, device=self.device)
        local_node_mask[:self.placedb.num_nodes//2] = 1
        
        electric_potential = ElectricPotentialSharedDDP(
            node_size_x=node_size_x,
            node_size_y=node_size_y,
            bin_center_x=bin_center_x,
            bin_center_y=bin_center_y,
            target_density=0.8,
            xl=self.placedb.xl, yl=self.placedb.yl,
            xh=self.placedb.xh, yh=self.placedb.yh,
            bin_size_x=self.placedb.bin_size_x,
            bin_size_y=self.placedb.bin_size_y,
            num_movable_nodes=self.placedb.num_movable_nodes,
            num_terminals=self.placedb.num_terminals,
            num_filler_nodes=self.placedb.num_filler_nodes,
            local_node_mask=local_node_mask,
            padding=0,
            ddp_rank=0,
            ddp_world_size=2
        )
        
        self.assertIsInstance(electric_potential, ElectricPotentialSharedDDP)
        self.assertEqual(electric_potential.ddp_rank, 0)
        self.assertEqual(electric_potential.ddp_world_size, 2)

class TestSharedParamDDPElectricOverflow(unittest.TestCase):
    """Test shared parameter DDP electric overflow computation"""

    def setUp(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.placedb = MockPlaceDB()

    def test_electric_overflow_shared_ddp_creation(self):
        """Test shared parameter DDP electric overflow module creation"""
        node_size_x = torch.from_numpy(self.placedb.node_size_x).to(self.device)
        node_size_y = torch.from_numpy(self.placedb.node_size_y).to(self.device)
        bin_center_x = torch.from_numpy(self.placedb.bin_center_x).to(self.device)
        bin_center_y = torch.from_numpy(self.placedb.bin_center_y).to(self.device)

        # Create local node mask (assign first half of nodes to this GPU)
        local_node_mask = torch.zeros(self.placedb.num_nodes, dtype=torch.uint8, device=self.device)
        local_node_mask[:self.placedb.num_nodes//2] = 1

        electric_overflow = ElectricOverflowSharedDDP(
            node_size_x=node_size_x,
            node_size_y=node_size_y,
            bin_center_x=bin_center_x,
            bin_center_y=bin_center_y,
            target_density=0.8,
            xl=self.placedb.xl, yl=self.placedb.yl,
            xh=self.placedb.xh, yh=self.placedb.yh,
            bin_size_x=self.placedb.bin_size_x,
            bin_size_y=self.placedb.bin_size_y,
            num_movable_nodes=self.placedb.num_movable_nodes,
            num_terminals=self.placedb.num_terminals,
            num_filler_nodes=self.placedb.num_filler_nodes,
            local_node_mask=local_node_mask,
            padding=0,
            ddp_rank=0,
            ddp_world_size=2
        )

        self.assertIsInstance(electric_overflow, ElectricOverflowSharedDDP)
        self.assertEqual(electric_overflow.ddp_rank, 0)
        self.assertEqual(electric_overflow.ddp_world_size, 2)

class TestSharedParamDDPWeightedAverageWirelength(unittest.TestCase):
    """Test shared parameter DDP weighted average wirelength computation"""
    
    def setUp(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.placedb = MockPlaceDB()
        
    def test_wirelength_shared_ddp_creation(self):
        """Test shared parameter DDP wirelength module creation"""
        partitioner = SharedParamDDPPartitioner(0, 2)
        net_partition = partitioner.partition_nets_for_wa(self.placedb)
        
        flat_netpin = torch.from_numpy(self.placedb.flat_net2pin_map).to(self.device)
        netpin_start = torch.from_numpy(self.placedb.flat_net2pin_start_map).to(self.device)
        pin2net_map = torch.from_numpy(self.placedb.pin2net_map).to(self.device)
        net_mask = torch.from_numpy(net_partition['net_mask']).to(self.device)
        pin_mask = torch.zeros(len(self.placedb.pin2net_map), dtype=torch.bool, device=self.device)
        gamma = torch.tensor(1.0, device=self.device)
        
        # Create pin position operation
        pin2node_map = torch.from_numpy(self.placedb.pin2node_map).to(self.device)
        pin_offset_x = torch.tensor(self.placedb.pin_offset_x, device=self.device)
        pin_offset_y = torch.tensor(self.placedb.pin_offset_y, device=self.device)
        pin_pos_op = PinPosOp(pin2node_map, pin_offset_x, pin_offset_y)
        
        wirelength = WeightedAverageWirelengthSharedDDP(
            flat_netpin=flat_netpin,
            netpin_start=netpin_start,
            pin2net_map=pin2net_map,
            net_mask=net_mask,
            pin_mask=pin_mask,
            gamma=gamma,
            pin_pos_op=pin_pos_op,
            algorithm='atomic',
            ddp_rank=0,
            ddp_world_size=2
        )
        
        self.assertIsInstance(wirelength, WeightedAverageWirelengthSharedDDP)
        self.assertEqual(wirelength.ddp_rank, 0)
        self.assertEqual(wirelength.ddp_world_size, 2)

def run_tests():
    """Run all tests"""
    print("[I] Running shared parameter DDP implementation tests...")
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_suite.addTest(unittest.makeSuite(TestSharedParamDDPPartitioner))
    test_suite.addTest(unittest.makeSuite(TestSharedParamDDPDataCollection))
    test_suite.addTest(unittest.makeSuite(TestPinPosOp))
    test_suite.addTest(unittest.makeSuite(TestSharedParamDDPElectricPotential))
    test_suite.addTest(unittest.makeSuite(TestSharedParamDDPElectricOverflow))
    test_suite.addTest(unittest.makeSuite(TestSharedParamDDPWeightedAverageWirelength))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    if result.wasSuccessful():
        print("[I] All tests passed!")
        return True
    else:
        print(f"[E] {len(result.failures)} test(s) failed, {len(result.errors)} error(s)")
        return False

if __name__ == '__main__':
    success = run_tests()
    sys.exit(0 if success else 1)
