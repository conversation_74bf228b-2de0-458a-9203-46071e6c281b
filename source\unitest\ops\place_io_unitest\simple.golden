num_nodes = 10
num_terminals = 2
node_name2id_map = {o0 : 0, o1 : 1, o2 : 2, o3 : 3, o4 : 4, o5 : 5, o6 : 6, o7 : 7, o8 : 8, o9 : 9}
node_names = [o0, o1, o2, o3, o4, o5, o6, o7, o8, o9]
node_x = [0, 0, 0, 0, 0, 0, 0, 0, 459, 500]
node_y = [0, 0, 0, 0, 0, 0, 0, 0, 470, 555]
node_orient = [N, N, N, N, N, N, N, N, N, N]
node_size_x = [16, 16, 16, 8, 8, 8, 8, 8, 40, 48]
node_size_y = [24, 24, 24, 24, 12, 12, 12, 12, 40, 24]
pin_direct = [OUTPUT, INPUT, INPUT, OUTPUT, INPUT, OUTPUT, OUTPUT, INPUT, OUTPUT, INPUT, INPUT, INPUT, OUTPUT, INPUT, INPUT, INPUT, INPUT, INPUT, INPUT, OUTPUT, INPUT, INPUT, OUTPUT, INPUT, INPUT, INPUT]
pin_offset_x = [-3, 20, -7, 4, 2, -1, 1, -2, 1, 2, 14, 7, 6, 1, -2, 12, 12, 8, -1, -1, 12, 8, -1, 8, 0, 23]
pin_offset_y = [8, 7, 12, 5, 17, 9, 14, 11, 9, 11, 19, 6, 9, 7, 4, 13, 13, 4, 0, 3, 10, 13, 3, 4, 11, 14]
net_name2id_map = {n1 : 0, n2 : 1, n5 : 2, n6 : 3, n3 : 4, n0 : 5, n4 : 6, n7 : 7}
net_names = [n1, n2, n5, n6, n3, n0, n4, n7]
net2pin_map = [[0, 1], [3, 2], [5, 4], [6, 7], [8, 9, 10], [12, 11, 13, 14], [19, 16, 17, 18, 15], [22, 21, 20, 23, 24, 25]]
flat_net2pin_map = [0, 1, 3, 2, 5, 4, 6, 7, 8, 9, 10, 12, 11, 13, 14, 19, 16, 17, 18, 15, 22, 21, 20, 23, 24, 25]
flat_net2pin_start_map = [0, 2, 4, 6, 8, 11, 15, 20, 26]
node2pin_map = [[11, 2, 8, 15, 6, 20], [9], [12, 16, 4], [13, 5, 7, 21], [22], [14, 0, 17], [3, 18, 23], [19, 24], [10, 25], [1]]
flat_node2pin_map = [11, 2, 8, 15, 6, 20, 9, 12, 16, 4, 13, 5, 7, 21, 22, 14, 0, 17, 3, 18, 23, 19, 24, 10, 25, 1]
flat_node2pin_start_map = [0, 6, 7, 10, 14, 15, 18, 21, 23, 25, 26]
pin2node_map = [5, 9, 0, 6, 2, 3, 0, 3, 0, 1, 8, 0, 2, 3, 5, 0, 2, 5, 6, 7, 0, 3, 4, 6, 7, 8]
pin2net_map = [0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 4, 5, 5, 5, 5, 6, 6, 6, 6, 6, 7, 7, 7, 7, 7, 7]
rows = [(459, 459, 555, 471), (459, 471, 555, 483), (459, 483, 555, 495), (459, 495, 555, 507), (459, 507, 555, 519), (459, 519, 555, 531), (459, 531, 555, 543), (459, 543, 555, 555)]
xl = 459
yl = 459
xh = 555
yh = 555
row_height = 12
site_width = 1
num_movable_pins = 23
