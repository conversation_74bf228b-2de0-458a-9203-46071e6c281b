# 100 -  lef reader, lefrReader.c
# 1000 - lef parser, error, lex.cpph, lef.y (CALLBACK & CHKERR)
# 1300 - from lefiError, lefiLayer.cpp
# 1350 - from lefiError, lefiMacro.cpp
# 1360 - from lefiError, lefiMisc.cpp
# 1400 - from lefiError, lefiNonDefault.cpp
# 1420 - from lefiError, lefiVia.cpp
# 1430 - from lefiError, lefiViaRule.cpp
# 1500 - lef parser, error, lef.y
# 2000 - lef parser, warning, lex.cpph
# 2500 - lef parser, warning, lef.y
# 3000 - lef parser, info, lex.cpph
# 4000 - lef writer, error, lefwWriter.cpp & lefwWriterCalls.cpp
# 4500 - lef writer, warning, lefwWriter.cpp & lefwWriterCalls.cpp
# 4700 - lef writer, info, lefwWriter.cpp & lefwWriterCalls.cpp
# emsMkError LEF -b lefMsgTable -m lef.msg -e -n
100  "lefrRead called before lefrInit\n"
101  "lefrSetRegisterUnusedCallbacks was not called to setup this data.\n"
201  "LEF items that were present but ignored because of no callback:\n"
203  "Number has exceeded the limit for an integer. See file %s at line %d.\n"
1000 "Expecting '='"
1001 "End of file in &ALIAS"
1002 "Incomplete lef file"
1003 "tag is missing for BEGINEXT"
1004 "Tag for BEGINEXT is empty"
1005 "\" is missing in tag"
1006 "Ending \" is missing"
1007 "ENDEXT is missing"
1008 "Invalid characters found in \'%s\'.\nThese characters might have created by character types other than English."
1009 "Symbol ';' should be separated by space(s)."
1011 "%s, see file %s at line %d.\nLast token was <%s\">; space is missing between the closing \" of the string and ;.\n"
1020 "Too many syntax errors."
1300 "The index number %d given for the layer property is invalid.\nValid index is from 0 to %d"
1301 "The index number %d given for the layer MINSIZE is invalid.\nValid index is from 0 to %d\n"
1302 "The index number %d given for the layer MINSTEP is invalid.\nValid index is from 0 to %d"
1303 "The index number %d given for the layer ARRAYCUTS is invalid.\nValid index is from 0 to %d"
1304 "The index number %d given for the layer SPACING is invalid.\nValid index is from 0 to %d"
1305 "Incorrect syntax defined for property LEF57_SPACING: %s.\nCorrect syntax is either \"SPACING minSpacing [CENTERTOCENTER]\"\n\"[LAYER secondLayerName | ADJACENTCUTS {2|3|4} WITHIN cutWithin | PARALLELOVERLAP | AREA cutArea]\" or\n\"SPACING eolSpace ENDOFLINE eolWidth WITHIN eolWITHIN [PARALLELEDGE parSpace WITHIN parWithin [TOWEDGES]]\"\n"
1306 "Incorrect syntax defined for property LEF57_MAXFLOATINGAREA: %s.\nCorrect syntax is \"MAXFLOATINGAREA maxArea\"\n"
1307 "Incorrect syntax defined for property LEF57_ARRAYSPACING: %s.\nCorrect syntax is ARRAYSPACING [LONGARRAY] [WIDTH viaWidth] CUTSPACING cutSpacing\n\tARRAYCUTS arrayCuts SPACING arraySpacing ...\n"
1308 "Incorrect syntax defined for property LEF57_ARRAYSPACING: %s.\nLONGARRAY is defined after CUTSPACING.\nCorrect syntax is ARRAYSPACING [LONGARRAY] [WIDTH viaWidth] CUTSPACING cutSpacing\n\tARRAYCUTS arrayCuts SPACING arraySpacing ...\n"
1309 "Incorrect syntax defined for property LEF57_ARRAYSPACING: %s.\nWIDTH is defined after CUTSPACING.\nCorrect syntax is ARRAYSPACING [LONGARRAY] [WIDTH viaWidth] CUTSPACING cutSpacing\n\tARRAYCUTS arrayCuts SPACING arraySpacing ...\n"
1310 "Incorrect syntax defined for property LEF57_ARRAYSPACING: %s.\nCUTSPACING has defined more than once.\nCorrect syntax is ARRAYSPACING [LONGARRAY] [WIDTH viaWidth] CUTSPACING cutSpacing\n\tARRAYCUTS arrayCuts SPACING arraySpacing ...\n"
1311 "Incorrect syntax defined for property LEF57_ARRAYSPACING: %s.\nCUTSPACING which is required is either has not been defined or defined in a wrong location.\nCorrect syntax is ARRAYSPACING [LONGARRAY] [WIDTH viaWidth] CUTSPACING cutSpacing\n\tARRAYCUTS arrayCuts SPACING array Spacing ...\n"
1312 "Incorrect syntax defined for property LEF57_ARRAYSPACING: %s.\nSPACING should be defined with ARRAYCUTS.\nCorrect syntax is ARRAYSPACING [LONGARRAY] [WIDTH viaWidth] CUTSPACING cutSpacing\n\tARRAYCUTS arrayCuts SPACING arraySpacing ...\n"
1313 "Incorrect syntax defined for property LEF57_ARRAYSPACING: %s.\nCorrect syntax is ARRAYSPACING [LONGARRAY] [WIDTH viaWidth] CUTSPACING cutSpacing\n\tARRAYCUTS arrayCuts SPACING arraySpacing ...\n"
1314 "Incorrect syntax defined for property LEF57_ARRAYSPACING: %s.\nARRAYCUTS is required but has not been defined.\nCorrect syntax is ARRAYSPACING [LONGARRAY] [WIDTH viaWidth] CUTSPACING cutSpacing\n\tARRAYCUTS arrayCuts SPACING arraySpacing ...\n"
1315 "Incorrect syntax defined for property LEF57_MINSTEP: %s.\nCorrect syntax is \"MINSTEP minStepLength [MAXEDGES maxEdges] [MINADJACENTLENGTH minAdjLength | MINBETWEENLENGTH minBetweenLength [EXCEPTSAMECORNERS]] ;\"\n"
1316 "Incorrect syntax defined for property LEF57_ANTENNACUMROUTINGPLUSCUT: %s.\nCorrect syntax is \"ANTANNACUMROUTINGPLUSCUT\"\n"
1317 "Incorrect syntax defined for property LEF57_ANTENNAGATEPLUSDIFF: %s.\nCorrect syntax is \"ANTENNAGATEPLUSDIFF plusDiffFactor\"\n"
1318 "Incorrect syntax defined for property LEF57_ANTENNAAREAMINUSDIFF: %s.\nCorrect syntax is \"ANTENNAAREAMINUSDIFF minusDiffFactor\"\n"
1319 "Incorrect syntax defined for property LEF57_ANTENNAAREADIFFREDUCEPWL: %s.\nCorrect syntax is \"ANTENNAAREADIFFREDUCEPWL (( diffArea1 metalDiffFactor1 ) ( diffArea2 metalDiffFactor2 )...)\"\n"
1320 "Incorrect syntax defined for property LEF57_SPACING: %s.\nCorrect syntax is \"SPACING cutSpacing [CENTERTOCENTER][SAMENET]\n\t[LAYER secondLayerName[STACK]\n\t| ADJACENTCUTS {2 | 3 | 4} WITHIN cutWithin [EXCEPTSAMEPGNET]\n\t| PARALLELOVERLAP\n\t| AREA cutArea ;\""
1321 "The property LEF57_SPACING with value %s is for TYPE CUT only.\nThe current layer has the TYPE %s.\nUpdate the property of your lef file with the correct syntax or remove this property from your lef file"
1322 "The property LEF57_SPACING with value %s is for TYPE ROUTING only.\nThe current layer has the TYPE %s.\nUpdate the property of your lef file with the correct syntax or remove this property from your lef file."
1323 "The property LEF57_MINSTEP with value %s is for TYPE ROUTING only.\nThe current layer has the TYPE %s.\nUpdate the property of your lef file with the correct syntax or remove this property from your lef file."
1324 "Incorrect syntax defined for the statement TWOWIDTHS.\nSpacing, which is required, is not defined."
1325 "Property LEF58_TYPE was added in incorrect layer type.\nIt has the value %s which is for layer type ROUTING.\nThe layer type is %s.\n"
1326 "Property LEF58_TYPE was added in incorrect layer type.\nIt has the value %s which is for layer type CUT.\nThe layer type is %s.\n"
1327 "Property LEF58_TYPE was added in incorrect layer type.\nIt has the value %s which is for layer type MASTERSLICE.\nThe layer type is %s.\n"
1328 "Property LEF58_TYPE has incorrect layer type %s.\nValue layer type are: POLYROUTING, MIMCAP, TSV, PASSIVATION, NWELL or PWELL.\n"
1329 "Incorrect syntax defined for property LEF58_TYPE: %s\nCorrect syntax is \"TYPE POLYROUTING | MIMCAP | TSV | PASSIVATION | NWELL | PWELL ;\"\n"
1330 "Incorrect syntax defined for property LEF57_ENCLOSURE: %s\nCorrect syntax is \"ENCLOSURE [ABOVE|BELOW] overhang1 overhang2\n\t[WIDTH minWidth [EXCEPTEXTRACUT cutWithin]\n\t|LENGTH minLength] ;\"\n"
1331 "The property LEF57_ENCLOSURE with value %s is for TYPE CUT only.\nThe current layer has the TYPE %s.\nUpdate the property of your lef file with the correct syntax or remove this property from your lef file.\n"
1350 "The index number %d given for the macro PIN is invalid.\nValid index is from 0 to %d"
1351 "There is an unexpected lef parser bug which cause it unable to retrieve ANTENNAMODEL data with the given index."
1352 "The index number %d given for the macro property is invalid.\nValid index is from 0 to %d"
1360 "The index number %d given for the geometry item is invalid.\nValid index is from 0 to %d."
1361 "The index number %d given for the geometry RECTANGLE is invalid.\nValid index is from 0 to %d."
1362 "The index number %d given for the geometry RECTANGLE ITERATE is invalid.\nValid index is from 0 to %d."
1363 "The index number %d given for the geometry PATH is invalid.\nValid index is from 0 to %d."
1364 "The index number %d given for the geometry PATH ITERATE is invalid.\nValid index is from 0 to %d."
1365 "The index number %d given for the geometry LAYER is invalid.\nValid index is from 0 to %d."
1366 "The index number %d given for the geometry LAYER EXCEPT PG NET is invalid.\nValid index is from 0 to %d."
1367 "The index number %d given for the geometry LAYER MINSPACING is invalid.\nValid index is from 0 to %d."
1368 "The index number %d given for the geometry LAYER RULE WIDTH is invalid.\nValid index is from 0 to %d."
1369 "The index number %d given for the geometry WIDTH is invalid.\nValid index is from 0 to %d."
1370 "The index number %d given for the geometry POLYGON is invalid.\nValid index is from 0 to %d."
1371 "The index number %d given for the geometry POLYGON ITERATE is invalid.\nValid index is from 0 to %d."
1372 "The index number %d given for the geometry CLASS is invalid.\nValid index is from 0 to %d."
1373 "The index number %d given for the geometry VIA is invalid.\nValid index is from 0 to %d."
1374 "The index number %d given for the geometry VIA ITERATE is invalid.\nValid index is from 0 to %d."
1375 "unknown geometry type."
1376 "The index number %d given for the IRDROP is invalid.\nValid index is from 0 to %d."
1377 "The index number %d given for the TRACK PATTERN  is invalid.\nValid index is from 0 to %d."
1400 "Invalid nondefaultvia callback."
1401 "Invalid nondefaultspacing callback."
1402 "The index number %d given for the NONDEFAULT LAYER is invalid.\nValid index is from 0 to %d."
1403 "The index number %d given for the NONDEFAULT VIA is invalid.\nValid index is from 0 to %d."
1404 "The index number %d given for the NONDEFAULT SPACING is invalid.\nValid index is from 0 to %d."
1405 "The index number %d given for the NONDEFAULT USE VIA is invalid.\nValid index is from 0 to %d."
1406 "The index number %d given for the NONDEFAULT USE VIARULE is invalid.\nValid index is from 0 to %d."
1407 "The index number %d given for the NONDEFAULT CUT is invalid.\nValid index is from 0 to %d."
1408 "The index number %d given for the NONDEFAULT PROPERTY is invalid.\nValid index is from 0 to %d."
1420 "The index number %d given for the VIA LAYER RECTANGLE is invalid.\nValid index is from 0 to %d."
1421 "The layer number %d given for the VIA LAYER is invalid.\nValid number is from 0 to %d."
1422 "The layer number %d given for the VIA PROPERTY is invalid.\nValid number is from 0 to %d."
1430 "too many via rule layers."
1431 "The index number %d given for the VIARULE PROPERTY is invalid.\nValid index is from 0 to %d."
1501 "Error found when processing LEF file '%s'\nUnit %d is a version 5.6 or later syntax\nYour lef file is defined with version %g."
1502 "The value %d defined for LEF UNITS DATABASE MICRONS is invalid\n. Correct value is 100, 200, 1000, 2000, 10000, or 20000"
1503 "Lef parser 5.7 does not support lef file with version %s. Parser will stop processing."
1504 "NAMESCASESENSITIVE statement is set with OFF.\nStarting version 5.6, NAMESCASENSITIVE is obsolete,\nif it is defined, it has to have the ON value.\nParser will stop processing."
1505 "MANUFACTURINGGRID statement was defined before UNITS.\nRefer to the LEF Language Reference manual for the order of LEF statements."
1506 "A MAXVIASTACK statement is defined before the LAYER statement.\nRefer to the LEF Language Reference manual for the order of LEF statements."
1507 "END LAYER name %s is different from the LAYER name %s.\nCorrect the LEF file before rerunning it through the LEF parser."
1508 "TYPE statement is a required statement in a LAYER and it is not defined."
1509 "PITCH statement is a required statement in a LAYER with TYPE ROUTING and it is not defined."
1510 "WIDTH statement is a required statement in a LAYER with TYPE ROUTING and it is not defined."
1511 "The DIRECTION statement which is required in a LAYER with TYPE ROUTING is not defined in LAYER %s.\nUpdate your lef file and add the DIRECTION statement for layer %s."
1512 "It is incorrect to define a SPACING statement in LAYER with TYPE MASTERSLICE or OVERLAP. Parser will stop processing."
1513 "DIRECTION statement can only be defined in LAYER with TYPE ROUTING. Parser will stop processing."
1514 "RESISTANCE statement can only be defined in LAYER with TYPE ROUTING. Parser will stop processing."
1515 "RESISTANCE statement can only be defined in LAYER with TYPE ROUTING. Parser will stop processing."
1516 "CAPACITANCE statement can only be defined in LAYER with TYPE ROUTING. Parser will stop processing."
1517 "CAPACITANCE statement can only be defined in LAYER with TYPE ROUTING. Parser will stop processing."
1518 "HEIGHT statement can only be defined in LAYER with TYPE ROUTING. Parser will stop processing."
1519 "WIREEXTENSION statement can only be defined in LAYER with TYPE ROUTING. Parser will stop processing."
1520 "THICKNESS statement can only be defined in LAYER with TYPE ROUTING. Parser will stop processing."
1521 "SHRINKAGE statement can only be defined in LAYER with TYPE ROUTING. Parser will stop processing."
1522 "CAPMULTIPLIER statement can only be defined in LAYER with TYPE ROUTING. Parser will stop processing."
1523 "EDGECAPACITANCE statement can only be defined in LAYER with TYPE ROUTING. Parser will stop processing."
1524 "ANTENNAAREAFACTOR statement can only be defined in LAYER with TYPE ROUTING. Parser will stop processing."
1525 "ANTENNALENGTHFACTOR statement can only be defined in LAYER with TYPE ROUTING. Parser will stop processing."
1526 "ANTENNALENGTHFACTOR statement is a version 5.3 or earlier syntax.\nYour lef file with version %g, has both old and new ANTENNALENGTHFACTOR syntax, which is incorrect."
1527 "ACCURRENTDENSITY statement can't be defined in LAYER with TYPE MASTERSLICE or OVERLAP. Parser will stop processing."
1528 "DCCURRENTDENSITY statement can't be defined in LAYER with TYPE MASTERSLICE or OVERLAP. Parser will stop processing."
1529 "CUTAREA statement can only be defined in LAYER with TYPE CUT. Parser will stop processing."
1530 "WIDTH statement can only be defined in LAYER with TYPE ROUTING. Parser will stop processing."
1531 "ANTENNAAREARATIO statement is a version 5.4 and later syntax.\nYour lef file is defined with version %g."
1532 "ANTENNADIFFAREARATIO statement is a version 5.4 or earlier syntax.\nYour lef file with version %g, has both old and new ANTENNAAREARATIO syntax, which is incorrect."
1533 "ANTENNAAREARATIO statement can only be defined in LAYER with TYPE ROUTING or CUT. Parser will stop processing."
1534 "ANTENNADIFFAREARATIO statement can only be defined in LAYER with TYPE ROUTING or CUT. Parser will stop processing."
1535 "ANTENNACUMAREARATIO statement is a version 5.4 and later syntax.\nYour lef file is defined with version %g."
1536 "ANTENNACUMAREARATIO statement is a version 5.4 or earlier old syntax.\nYour lef file with version %g, has both old and new ANTENNACUMAREARATIO syntax, which is incorrect."
1537 "ANTENNACUMAREARATIO statement can only be defined in LAYER with TYPE ROUTING or CUT. Parser will stop processing."
1538 "ANTENNACUMDIFFAREARATIO statement is a version 5.4 and later syntax.\nYour lef file is defined with version %g."
1539 "ANTENNACUMDIFFAREARATIO statement is a version 5.4 or earlier old syntax.\nYour lef file with version %g, has both old and new ANTENNACUMDIFFAREARATIO syntax, which is incorrect."
1540 "ANTENNACUMDIFFAREARATIO statement can only be defined in LAYER with TYPE ROUTING or CUT. Parser will stop processing."
1541 "ANTENNAAREAFACTOR can only be defined in LAYER with TYPE ROUTING or CUT. Parser will stop processing."
1542 "ANTENNASIDEAREARATIO can only be defined in LAYER with TYPE ROUTING. Parser will stop processing."
1543 "ANTENNASIDEAREARATIO statement is a version 5.4 and later syntax.\nYour lef file is defined with version %g."
1544 "ANTENNASIDEAREARATIO statement is a version 5.4 or earlier old syntax.\nYour lef file with version %g, has both old and new ANTENNASIDEAREARATIO syntax, which is incorrect."
1545 "ANTENNADIFFSIDEAREARATIO can only be defined in LAYER with TYPE ROUTING. Parser will stop processing."
1546 "ANTENNADIFFSIDEAREARATIO statement is a version 5.4 and later syntax.\nYour lef file is defined with version %g."
1547 "ANTENNADIFFSIDEAREARATIO statement is a version 5.4 or earlier syntax.\nYour lef file with version %g, has both old and new ANTENNADIFFSIDEAREARATIO syntax, which is incorrect."
1548 "ANTENNACUMSIDEAREARATIO can only be defined in LAYER with TYPE ROUTING. Parser will stop processing."
1549 "ANTENNACUMSIDEAREARATIO statement is a version 5.4 and later syntax.\nYour lef file is defined with version %g."
1550 "ANTENNACUMSIDEAREARATIO statement is a version 5.4 or earlier syntax.\nYour lef file with version %g, has both old and new ANTENNACUMSIDEAREARATIO syntax, which is incorrect."
1551 "ANTENNACUMDIFFSIDEAREARATIO can only be defined in LAYER with TYPE ROUTING. Parser will stop processing."
1552 "ANTENNACUMDIFFSIDEAREARATIO statement is a version 5.4 and later syntax.\nYour lef file is defined with version %g."
1553 "ANTENNACUMDIFFSIDEAREARATIO statement is a version 5.4 or earlier syntax.\nYour lef file with version %g, has both old and new ANTENNACUMDIFFSIDEAREARATIO syntax, which is incorrect."
1554 "ANTENNASIDEAREAFACTOR can only be defined in LAYER with TYPE ROUTING. Parser will stop processing."
1555 "ANTENNASIDEAREAFACTOR statement is a version 5.4 and later syntax.\nYour lef file is defined with version %g."
1556 "ANTENNASIDEAREAFACTOR statement is a version 5.4 or earlier syntax.\nYour lef file with version %g, has both old and new ANTENNASIDEAREAFACTOR syntax, which is incorrect."
1557 "ANTENNAMODEL can only be defined in LAYER with TYPE ROUTING. Parser will stop processing."
1558 "ANTENNAMODEL statement is a version 5.5 and later syntax.\nYour lef file is defined with version %g."
1559 "ANTENNAMODEL statement is a version 5.4 or earlier syntax.\nYour lef file with version %g, has both old and new ANTENNAMODEL syntax, which is incorrect."
1560 "ANTENNACUMROUTINGPLUSCUT can only be defined in LAYER with TYPE ROUTING or CUT. Parser will stop processing."
1561 "ANTENNAGATEPLUSDIFF can only be defined in LAYER with TYPE ROUTING or CUT. Parser will stop processing."
1562 "ANTENNAAREAMINUSDIFF can only be defined in LAYER with TYPE ROUTING or CUT. Parser will stop processing."
1563 "ANTENNAAREADIFFREDUCEPWL can only be defined in LAYER with TYPE ROUTING or CUT. Parser will stop processing."
1564 "SLOTWIREWIDTH statement is a version 5.4 and later syntax.\nYour lef file is defined with version %g."
1565 "SLOTWIRELENGTH statement is a version 5.4 and later syntax.\nYour lef file is defined with version %g."
1566 "SLOTWIDTH statement is a version 5.4 and later syntax.\nYour lef file is defined with version %g."
1567 "SLOTLENGTH statement is a version 5.4 and later syntax.\nYour lef file is defined with version %g."
1568 "MAXADJACENTSLOTSPACING statement is a version 5.4 and later syntax.\nYour lef file is defined with version %g."
1569 "MAXCOAXIALSLOTSPACING statement is a version 5.4 and later syntax.\nYour lef file is defined with version %g."
1570 "MAXEDGESLOTSPACING statement is a version 5.4 and later syntax.\nYour lef file is defined with version %g."
1571 "SPLITWIREWIDTH statement is a version 5.4 and later syntax.\n Your lef file is defined with version %g."
1572 "MINIMUMDENSITY statement is a version 5.4 and later syntax.\n Your lef file is defined with version %g."
1573 "MAXIMUMDENSITY statement is a version 5.4 and later syntax.\n Your lef file is defined with version %g."
1574 "DENSITYCHECKWINDOW statement is a version 5.4 and later syntax.\nYour lef file is defined with version %g."
1575 "DENSITYCHECKSTEP statement is a version 5.4 and later syntax.\nYour lef file is defined with version %g."
1576 "FILLACTIVESPACING statement is a version 5.4 and later syntax.\nYour lef file is defined with version %g."
1577 "MAXWIDTH statement can only be defined in LAYER with TYPE ROUTING.  Parser will stop processing."
1578 "MAXWIDTH statement is a version 5.5 and later syntax.\nYour lef file is defined with version %g."
1579 "MINWIDTH statement can only be defined in LAYER with TYPE ROUTING.  Parser will stop processing."
1580 "MINWIDTH statement is a version 5.5 and later syntax.\nYour lef file is defined with version %g."
1581 "MINENCLOSEDAREA statement is a version 5.5 and later syntax.\nYour lef file is defined with version %g."
1582 "PROTRUSION RULE statement is a version 5.5 and later syntax.\nYour lef file is defined with version %g."
1583 "SPACINGTABLE statement is a version 5.5 and later syntax.\nYour lef file is defined with version %g."
1584 "ENCLOSURE statement is a version 5.6 and later syntax.\nYour lef file is defined with version %g."
1585 "PREFERENCLOSURE statement is a version 5.6 and later syntax.\nYour lef file is defined with version %g."
1586 "RESISTANCE statement is a version 5.6 and later syntax.\nYour lef file is defined with version %g."
1587 "DIAGMINEDGELENGTH can only be defined in LAYER with TYPE ROUTING. Parser will stop processing."
1588 "DIAGMINEDGELENGTH statement is a version 5.6 and later syntax.\nYour lef file is defined with version %g."
1589 "An INFLUENCE table statement was defined before the PARALLELRUNLENGTH table statement.\nINFLUENCE table statement should be defined following the PARALLELRUNLENGTH.\nChange the LEF file and rerunning the parser."
1590 "There is multiple PARALLELRUNLENGTH table statements are defined within a layer.\nAccording to the LEF Reference Manual, only one PARALLELRUNLENGTH table statement is allowed per layer."
1591 "The total number of lengths defined in the PARALLELRUNLENGTH statement is not equal to\nthe  total number of spacings defined in the WIDTH statement in the SPACINGTABLE."
1592 "A PARALLELRUNLENGTH statement was already defined in the layer.\nIt is PARALLELRUNLENGTH or TOWWIDTHS is allowed per layer."
1593 "A TWOWIDTHS table statement was already defined in the layer.\nOnly one TWOWIDTHS statement is allowed per layer."
1594 "A INFLUENCE table statement was already defined in the layer.\nOnly one INFLUENCE statement is allowed per layer."
1595 "An INFLUENCE table statement was already defined before the layer.\nINFLUENCE statement has to be defined after the PARALLELRUNLENGTH table statement in the layer."
1596 "FROMABOVE statement is a version 5.5 and later syntax.\nYour lef file is defined with version %g."
1597 "FROMBELOW statement is a version 5.5 and later syntax.\nYour lef file is defined with version %g."
1598 "LENGTH WITHIN statement is a version 5.5 and later syntax.\nYour lef file is defined with version %g."
1599 "ANTENNAAREAFACTOR with DIFFUSEONLY statement is a version 5.4 and later syntax.\nYour lef file is defined with version %g."
1600 "CUTAREA statement can only be defined in LAYER with TYPE CUT."
1601 "WIDTH can only be defined in LAYER with TYPE ROUTING."
1602 "MAXVIASTACK statement has to be defined after the LAYER statement."
1603 "A MAXVIASTACK was already defined.\nOnly one MAXVIASTACK is allowed per lef file."
1604 "MAXVIASTACK statement is a version 5.5 and later syntax.\nYour lef file is defined with version %g."
1605 "DEFAULT statement is a version 5.6 and later syntax.\nYour lef file is defined with version %g."
1606 "A LAYER statement is missing in the VIA %s.\nAt least one LAYER is required per VIA statement."
1607 "END VIA name %s is different from the VIA name %s.\nCorrect the LEF file before rerunning it through the LEF parser."
1608 "A VIARULE statement requires two layers."
1609 "A DIRECTION statement was already defined in the layer.\nIt is DIRECTION or ENCLOSURE can be specified in a layer."
1610 "An OVERHANG statement is defined, but the required DIRECTION statement is not yet defined.\nUpdate the LEF file to define the DIRECTION statement before the OVERHANG."
1611 "An OVERHANG statement is defined in a VIARULE statement only.\nOVERHANG statement can only be defined in VIARULE GENERATE."
1612 "An METALOVERHANG statement is defined in a VIARULE statement only.\nOVERHANG statement can only be defined in VIARULE GENERATE."
1613 "An METALOVERHANG statement is defined, but the required DIRECTION statement is not yet defined.\nUpdate the LEF file to define the DIRECTION statement before the OVERHANG."
1614 "An ENCLOSURE statement is defined in a VIARULE statement only.\nOVERHANG statement can only be defined in VIARULE GENERATE."
1615 "END VIARULE name %s is different from the VIARULE name %s.\nCorrect the LEF file before rerunning it through the LEF parser."
1616 "SAMENET statement is required inside SPACING for any lef file with version 5.4 and earlier, but is not defined in the parsed lef file."
1617 "NONDEFAULTRULE statement requires at least one LAYER statement."
1618 "NONDEFAULTRULE statement requires at least one VIA statement."
1619 "END NONDEFAULTRULE name %s is different from the NONDEFAULTRULE name %s.\nCorrect the LEF file before rerunning it through the LEF parser."
1620 "HARDSPACING statement is a version 5.6 and later syntax.\nYour lef file is defined with version %g."
1621 "USEVIA statement is a version 5.6 and later syntax.\nYour lef file is defined with version %g."
1622 "USEVIARULE statement is a version 5.6 and later syntax.\nYour lef file is defined with version %g."
1623 "MINCUTS statement is a version 5.6 and later syntax.\nYour lef file is defined with version %g."
1624 "END LAYER name %s is different from the LAYER name %s.\nCorrect the LEF file before rerunning it through the LEF parser."
1625 "A WIDTH statement is required in the LAYER statement in NONDEFAULTRULE."
1626 "A SPACING statement is required in the LAYER statement in NONDEFAULTRULE for lef file with version 5.5 and earlier.\nYour lef file is defined with version %g. Update your lef to add a LAYER statement and try again."
1627 "RESISTANCE RPERSQ statement is a version 5.4 and later syntax.\nYour lef file is defined with version %g."
1628 "CAPACITANCE CPERSQDIST statement is a version 5.4 and later syntax.\nYour lef file is defined with version %g."
1629 "EDGECAPACITANCE statement is a version 5.4 and later syntax.\n Your lef file is defined with version %g."
1630 "DIAGWIDTH statement is a version 5.6 and later syntax.\nYour lef file is defined with version %g."
1631 "END SITE name %s is different from the SITE name %s.\nCorrect the LEF file before rerunning it through the LEF parser."
1632 "A CLASS statement is required in the SITE statement."
1633 "A SIZE  statement is required in the SITE statement."
1634 "END MACRO name %s is different from the MACRO name %s.\nCorrect the LEF file before rerunning it through the LEF parser."
1635 "COVER BUMP statement is a version 5.5 and later syntax.\nYour lef file is defined with version %g."
1636 "BLOCK BLACKBOX statement is a version 5.5 and later syntax.\nYour lef file is defined with version %g."
1637 "BLOCK SOFT statement is a version 5.6 and later syntax.\nYour lef file is defined with version %g."
1638 "PAD AREAIO statement is a version 5.5 and later syntax.\nYour lef file is defined with version %g."
1639 "SPACER statement is a version 5.4 and later syntax.\nYour lef file is defined with version %g."
1640 "ANTENNACELL statement is a version 5.4 and later syntax.\nYour lef file is defined with version %g."
1641 "WELLTAP statement is a version 5.6 and later syntax.\nYour lef file is defined with version %g."
1642 "ORIGIN statement has been defined more than once in a MACRO statement.\nOnly one ORIGIN statement can be defined in a Macro.\nParser will stop processing."
1643 "END PIN name %s is different from the PIN name %s.\nCorrect the LEF file before rerunning it through the LEF parser."
1644 "ANTENNASIZE statement is a version 5.3 and earlier syntax.\nYour lef file is defined with version %g."
1645 "ANTENNAMETALAREA statement is a version 5.3 and earlier syntax.\nYour lef file is defined with version %g."
1646 "ANTENNAMETALLENGTH statement is a version 5.3 and earlier syntax.\nYour lef file is defined with version %g."
1647 "ANTENNAPARTIALMETALAREA statement is a version 5.4 and later syntax.\nYour lef file is defined with version %g."
1648 "ANTENNAPARTIALMETALSIDEAREA statement is a version 5.4 and later syntax.\nYour lef file is defined with version %g."
1649 "ANTENNAPARTIALCUTAREA statement is a version 5.4 and later syntax.\nYour lef file is defined with version %g."
1650 "ANTENNADIFFAREA statement is a version 5.4 and later syntax.\nYour lef file is defined with version %g."
1651 "ANTENNAGATEAREA statement is a version 5.4 and later syntax.\nYour lef file is defined with version %g."
1652 "ANTENNAMAXAREACAR statement is a version 5.4 and later syntax.\nYour lef file is defined with version %g."
1653 "ANTENNAMAXSIDEAREACAR statement is a version 5.4 and later syntax.\nYour lef file is defined with version %g."
1654 "ANTENNAMAXCUTCAR statement is a version 5.4 and later syntax.\nYour lef file is defined with version %g."
1655 "ANTENNAMODEL statement is a version 5.5 and later syntax.\nYour lef file is defined with version %g."
1656 "NETEXPR statement is a version 5.6 and later syntax.\nYour lef file is defined with version %g."
1657 "SUPPLYSENSITIVITY statement is a version 5.6 and later syntax.\nYour lef file is defined with version %g."
1658 "GROUNDSENSITIVITY statement is a version 5.6 and later syntax.\nYour lef file is defined with version %g."
1659 "THE SPACING statement has the value %g in MACRO OBS.\nValue has to be 0 or greater."
1660 "THE DESIGNRULEWIDTH statement has the value %g in MACRO OBS.\nValue has to be 0 or greater."
1661 "DENSITY statement is a version 5.6 and later syntax.\nYour lef file is defined with version %g."
1662 "END ARRAY name %s is different from the ARRAY name %s.\nCorrect the LEF file before rerunning it through the LEF parser."
1663 "A CENTERTOCENTER statement was already defined in SPACING\nCENTERTOCENTER can only be defined once per LAYER CUT SPACING."
1664 "CENTERTOCENTER statement is a version 5.6 and later syntax.\nYour lef file is defined with version %g."
1665 "A SAMENET statement was already defined in SPACING\nSAMENET can only be defined once per LAYER CUT SPACING."
1666 "A PARALLELOVERLAP statement was already defined in SPACING\nPARALLELOVERLAP can only be defined once per LAYER CUT SPACING."
1667 "A SAMENET statement was already defined in SPACING\nEither SAMENET or LAYER can be defined, but not both."
1668 "ADJACENTCUTS statement is a version 5.5 and later syntax.\nYour lef file is defined with version %g."
1669 "A SAMENET statement was already defined in SPACING\nEither SAMENET or ADJACENTCUTS can be defined, but not both."
1670 "A SAMENET statement was already defined in SPACING\nEither SAMENET or AREA can be defined, but not both."
1671 "INPUTPINANTENNASIZE statement is a version 5.3 or earlier syntax.\nYour lef file with version %g, has both old and new INPUTPINANTENNASIZE syntax, which is incorrect."
1672 "OUTPUTPINANTENNASIZE statement is a version 5.3 or earlier syntax.\nYour lef file with version %g, has both old and new OUTPUTPINANTENNASIZE syntax, which is incorrect."
1673 "INOUTPINANTENNASIZE statement is a version 5.3 or earlier syntax.\nYour lef file with version %g, has both old and new INOUTPINANTENNASIZE syntax, which is incorrect."
1674 "ANTENNAINPUTGATEAREA statement is a version 5.4 and later syntax.\nYour lef file is defined with version %g.\nEither update your VERSION number to 5.4 or later, or use the 5.3 syntax:\n{ INPUTINATENNASIZE | OUTPUTPINANTENNASIZE | INOUTPINANTENNASIZE } value."
1675 "ANTENNAINPUTGATEAREA statement is a version 5.4 or later syntax.\nYour lef file with version %g, has both old and new ANTENNAINPUTGATEAREA syntax, which is incorrect."
1676 "ANTENNAINOUTDIFFAREA statement is a version 5.4 and later syntax.\nYour lef file is defined with version %g.\nEither update your VERSION number to 5.4 or later, or use the 5.3 syntax:\n{ INPUTINATENNASIZE | OUTPUTPINANTENNASIZE | INOUTPINANTENNASIZE } value."
1677 "ANTENNAINOUTDIFFAREA statement is a version 5.4 or later syntax.\nYour lef file with version %g, has both old and new ANTENNAINOUTDIFFAREA syntax, which is incorrect."
1678 "ANTENNAOUTPUTDIFFAREA statement is a version 5.4 and later syntax.\nYour lef file is defined with version %g.\nEither update your VERSION number to 5.4 or later, or use the 5.3 syntax:\n{ INPUTINATENNASIZE | OUTPUTPINANTENNASIZE | INOUTPINANTENNASIZE } value.".
1679 "ANTENNAOUTPUTDIFFAREA statement is a version 5.4 or later syntax.\nYour lef file with version %g, has both old and new ANTENNAOUTPUTDIFFAREA syntax, which is incorrect."
1680 "PARALLELOVERLAP is a version 5.7 or later syntax.\nYour lef file is defined with version %g."
1681 "ENDOFLINE is a version 5.7 or later syntax.\nYour lef file is defined with version %g."
1682 "NOTCHLENGTH is a version 5.7 or later syntax.\nYour lef file is defined with version %g."
1683 "EXCEPTSAMEPGNET is a version 5.7 or later syntax.\nYour lef file is defined with version %g."
1684 "SAMENET is a version 5.7 or later syntax.\nYour lef file is defined with version %g."
1685 "ARRAYSPACING is a version 5.7 or later syntax.\nYour lef file is defined with version %g."
1686 "ANTENNACUMROUTINGPLUSCUT is a version 5.7 or later syntax.\nYour lef file is defined with version %g."
1687 "ANTENNAGATEPLUSDIFF is a version 5.7 or later syntax.\nYour lef file is defined with version %g."
1688 "ANTENNAAREAMINUSDIFF is a version 5.7 or later syntax.\nYour lef file is defined with version %g."
1689 "ANTENNAAREADIFFREDUCEPWL is a version 5.7 or later syntax.\nYour lef file is defined with version %g."
1690 "EXCEPTEXTRACUT is a version 5.7 or later syntax.\nYour lef file is defined with version %g."
1691 "LENGTH is a version 5.7 or later syntax.\nYour lef file is defined with version %g."
1693 "AREA is a version 5.7 or later syntax.\nYour lef file is defined with version %g."
1694 "SPACINGTABLE ORTHOGONAL is a version 5.7 or later syntax.\nYour lef file is defined with version %g."
1696 "ENDOFNOTCHWIDTH is a version 5.7 or later syntax.\nYour lef file is defined with version %g."
1697 "TWOWIDTHS is a version 5.7 or later syntax.\nYour lef file is defined with version %g."
1698 "BUMP is a version 5.7 or later syntax.\nYour lef file is defined with version %g."
1699 "EXCEPTPGNET is a version 5.7 or later syntax.\nYour lef file is defined with version %g."
1700 "MINIMUMCUT WITHIN is a version 5.7 or later syntax.\nYour lef file is defined with version %g."
1701 "A LAYER statement is missing in Geometry.\nLAYER is a required statement before any geometry can be defined."
1702 "CURRENTDEN statement can only be defined in LAYER with TYPE ROUTING. Parser will stop processing."
1703 "ANTENNADIFFAREARATIO statement is a version 5.4 and later syntax.\nYour lef file is defined with version %g."
1704 "ANTENNADIFFAREARATIO statement is a version 5.4 or earlier old syntax.\nYour lef file with version %g, has both old and new ANTENNADIFFAREARATIO syntax, which is incorrect."
1705 "VIARULE statement in a layer, requires a DIRECTION construct statement."
1706 "An ENCLOSURE statement was already defined in the layer.\nIt is DIRECTION or ENCLOSURE can be specified in a layer."
1707 "ENCLOSURE statement is a version 5.5 and later syntax.\nYour lef file is defined with version %g."
1708 "A VIARULE GENERATE requires three layers."
1709 "VIARULE statement is a version 5.6 and later syntax.\nYour lef file is defined with version %g."
1710 "MAXEDGES is a version 5.7 or later syntax.\nYour lef file is defined with version %g."
1711 "NOSHAREDEDGE in LAYER ENCLOSURE is a version 5.8 or later syntax.\nYour lef file is defined with version %g."
1712 "MINFEATURE statement was defined before UNITS.\nRefer the LEF Language Reference manual for the order of LEF statements."
2000 "String has exceeded 1048576 characters, extra characters are truncated."
2001 "No VERSION statement found, using the default value %2g."
2002 "NAMESCASESENSITIVE is a required statement on LEF file with version 5.5 and earlier.\nWithout NAMESCASESENSITIVE defined, the LEF file is technically incorrect.\nRefer to the LEF/DEF 5.5 or earlier Language Reference manual on how to define this statement."
2003 "BUSBITCHARS is a required statement on LEF file with version 5.5 and earlier.\nWithout BUSBITCHARS defined, the LEF file is technically incorrect.\nRefer to the LEF/DEF 5.5 or earlier Language Reference manual on how to define this statement."
2004 "DIVIDERCHAR is a required statement on LEF file with version 5.5 and earlier.\nWithout DIVIDECHAR defined, the LEF file is technically incorrect.\nRefer to the LEF/DEF 5.5 or earlier Language Reference manual on how to define this statement."
2005 "DIVIDERCHAR has an invalid null value. Value is set to default /"
2006 "BUSBITCHAR has an invalid null value. Value is set to default []"
2007 "NAMESCASESENSITIVE statement is obsolete in version 5.6 and later.\nThe LEF parser will ignore this statement.\nTo avoid this warning in the future, remove this statement from the LEF file with version 5.6 or later."
2008 "NOWIREEXTENSIONATPIN statement is obsolete in version 5.6 or later.\nThe NOWIREEXTENSIONATPIN statement will be ignored."
2009 "USEMINSPACING PIN statement is obsolete in version 5.6 or later.\n The USEMINSPACING PIN statement will be ignored."
2010 "It is incorrect to have both SPACING rules & SPACINGTABLE rules within a ROUTING layer."
2011 "SLOTWIREWIDTH statement is obsolete in version 5.7 or later.\nThe LEF parser will ignore this statement.\nTo avoid this warning in the future, remove this statement from the LEF file with version 5.7 or later."
2012 "SLOTWIRELENGTH statement is obsolete in version 5.7 or later.\nThe LEF parser will ignore this statement.\nTo avoid this warning in the future, remove this statement from the LEF file with version 5.7 or later."
2013 "SLOTWIDTH statement is obsolete in version 5.7 or later.\nThe LEF parser will ignore this statement.\nTo avoid this warning in the future, remove this statement from the LEF file with version 5.7 or later."
2014 "SLOTLENGTH statement is obsolete in version 5.7 or later.\nThe LEF parser will ignore this statement.\nTo avoid this warning in the future, remove this statement from the LEF file with version 5.7 or later."
2015 "MAXADJACENTSLOTSPACING statement is obsolete in version 5.7 or later.\nThe LEF parser will ignore this statement.\nTo avoid this warning in the future, remove this statement from the LEF file with version 5.7 or later."
2016 "MAXCOAXIALSLOTSPACING statement is obsolete in version 5.7 or later.\nThe LEF parser will ignore this statement.\nTo avoid this warning in the future, remove this statement from the LEF file with version 5.7 or later."
2017 "MAXEDGESLOTSPACING statement is obsolete in version 5.7 or later.\nThe LEF parser will ignore this statement.\nTo avoid this warning in the future, remove this statement from the LEF file with version 5.7 or later."
2018 "SPLITWIREWIDTH statement is obsolete in version 5.7 or later.\nThe LEF parser will ignore this statement.\nTo avoid this warning in the future, remove this statement from the LEF file with version 5.7 or later."
2019 "TOPOFSTACKONLY statement is obsolete in version 5.6 and later.\nThe LEF parser will ignore this statement.\nTo avoid this warning in the future, remove this statement from the LEF file with version 5.6 or later."
2020 "FOREIGN statement in VIA is obsolete in version 5.6 and later.\nThe LEF parser will ignore this statement.\nTo avoid this warning in the future, remove this statement from the LEF file with version 5.6 or later."
2021 "turn-via is obsolete in version 5.6 and later.\n The LEF parser will ignore this statement.\nTo avoid this warning in the future, remove this statement from the LEF file with version 5.6 or later."
2022 "DIRECTION statement in VIARULE is obsolete in version 5.6 and later.\nThe LEF parser will ignore this statement.\nTo avoid this warning in the future, remove this statement from the LEF file with version 5.6 or later."
2023 "OVERHANG statement will be translated into similar ENCLOSURE rule"
2024 "METALOVERHANG statement is obsolete in version 5.6 and later.\nThe LEF parser will ignore this statement.\nTo avoid this warning in the future, remove this statement from the LEF file with version 5.6 or later."
2025 "SAMENET statement in NONDEFAULTRULE is obsolete in version 5.6 and later.\nThe LEF parser will ignore this statement.\nTo avoid this warning in the future, remove this statement from the LEF file with version 5.6 or later."
2026 "IRDROP statement is obsolete in version 5.4 and later.\nThe LEF parser will ignore this statement.\nTo avoid this warning in the future, remove this statement from the LEF file with version 5.4 or later."
2027 "MINFEATURE statement is obsolete in version 5.4 and later.\nThe LEF parser will ignore this statement.\nTo avoid this warning in the future, remove this statement from the LEF file with version 5.4 or later."
2028 "DIELECTRIC statement is obsolete in version 5.4 and later.\nThe LEF parser will ignore this statement.\nTo avoid this warning in the future, remove this statement from the LEF file with version 5.4 or later."
2029 "RESISTANCE RPERSQ statement is obsolete in version 5.6 and later.\nThe LEF parser will ignore this statement.\nTo avoid this warning in the future, remove this statement from the LEF file with version 5.6 or later."
2030 "CAPACITANCE CPERSQDIST statement is obsolete in version 5.6 and later.\nThe LEF parser will ignore this statement.\nTo avoid this warning in the future, remove this statement from the LEF file with version 5.6 or later."
2031 "EDGECAPACITANCE statement is obsolete in version 5.6 and later.\nThe LEF parser will ignore this statement.\nTo avoid this warning in the future, remove this statement from the LEF file with version 5.6 or later."
2032 "A SITE statement is defined before SYMMETRY statement.\nTo avoid this warning in the future, define SITE after SYMMETRY."
2033 "The statement COVER BUMP is a LEF version 5.5 syntax.\nYour LEF file is version 5.4 or earlier which is incorrect but will be allowed\nbecause this application does not enforce strict version checking.\nOther tools that enforce strict checking will have a syntax error when reading this file.\nYou can change the VERSION statement in this LEF file to 5.5 or higher to stop this warning."
2034 "The statement BLOCK BLACKBOX is a LEF verion 5.5 syntax.\nYour LEF file is version 5.4 or earlier which is incorrect but will be allowed\nbecause this application does not enforce strict version checking.\nOther tools that enforce strict checking will have a syntax error when reading this file.\nYou can change the VERSION statement in this LEF file to 5.5 or higher to stop this warning."
2035 "The statement PAD AREAIO is a LEF verion 5.5 syntax.\nYour LEF file is version 5.4 or earlier which is incorrect but will be allowed\nbecause this application does not enforce strict version checking.\nOther tools that enforce strict checking will have a syntax error when reading this file.\nYou can change the VERSION statement in this LEF file to 5.5 or higher to stop this warning."
2036 "SOURCE statement is obsolete in version 5.6 and later.\nThe LEF parser will ignore this statement.\nTo avoid this warning in the future, remove this statement from the LEF file with version 5.6 or later."
2037 "SOURCE statement is obsolete in version 5.6 and later.\nThe LEF parser will ignore this statement.\nTo avoid this warning in the future, remove this statement from the LEF file with version 5.6 or later."
2038 "MACRO POWER statement is obsolete in version 5.4 and later.\nThe LEF parser will ignore this statement.\nTo avoid this warning in the future, remove this statement from the LEF file with version 5.4 or later."
2039 "A SITE statement is defined before ORIGIN statement.\nTo avoid this warning in the future, define SITE after ORIGIN."
2040 "A PIN statement is defined before ORIGIN statement.\nTo avoid this warning in the future, define PIN after ORIGIN."
2041 "A OBS statement is defined before ORIGIN statement.\nTo avoid this warning in the future, define OBS after ORIGIN."
2042 "LEQ statement in MACRO is obsolete in version 5.6 and later.\nThe LEF parser will ignore this statement.\nTo avoid this warning in the future, remove this statement from the LEF file with version 5.6 or later."
2043 "FOREIGN statement in MACRO PIN is obsolete in version 5.6 and later.\nThe LEF parser will ignore this statement.\nTo avoid this warning in the future, remove this statement from the LEF file with version 5.6 or later."
2044 "LEQ statement in MACRO PIN is obsolete in version 5.6 and later.\nThe LEF parser will ignore this statement.\nTo avoid this warning in the future, remove this statement from the LEF file with version 5.6 or later."
2045 "MACRO POWER statement is obsolete in version 5.4 and later.\nThe LEF parser will ignore this statement.\nTo avoid this warning in the future, remove this statement from the LEF file with version 5.4 or later."
2046 "MACRO LEAKAGE statement is obsolete in version 5.4 and later.\nThe LEF parser will ignore this statement.\nTo avoid this warning in the future, remove this statement from the LEF file with version 5.4 or later."
2047 "MACRO RISETHRESH statement is obsolete in version 5.4 and later.\nThe LEF parser will ignore this statement.\nTo avoid this warning in the future, remove this statement from the LEF file with version 5.4 or later."
2048 "MACRO FALLTHRESH statement is obsolete in version 5.4 and later.\nThe LEF parser will ignore this statement.\nTo avoid this warning in the future, remove this statement from the LEF file with version 5.4 or later."
2049 "MACRO RISESATCUR statement is obsolete in version 5.4 and later.\nThe LEF parser will ignore this statement.\nTo avoid this warning in the future, remove this statement from the LEF file with version 5.4 or later."
2050 "MACRO FALLSATCUR statement is obsolete in version 5.4 and later.\nThe LEF parser will ignore this statement.\nTo avoid this warning in the future, remove this statement from the LEF file with version 5.4 or later."
2051 "MACRO VLO statement is obsolete in version 5.4 and later.\nThe LEF parser will ignore this statement.\nTo avoid this warning in the future, remove this statement from the LEF file with version 5.4 or later."
2052 "MACRO VHI statement is obsolete in version 5.4 and later.\nThe LEF parser will ignore this statement.\nTo avoid this warning in the future, remove this statement from the LEF file with version 5.4 or later."
2053 "MACRO TIEOFFR statement is obsolete in version 5.4 and later.\nThe LEF parser will ignore this statement.\nTo avoid this warning in the future, remove this statement from the LEF file with version 5.4 or later."
2054 "MACRO OUTPUTNOISEMARGIN statement is obsolete in version 5.4 and later.\nThe LEF parser will ignore this statement.\nTo avoid this warning in the future, remove this statement from the LEF file with version 5.4 or later."
2055 "MACRO OUTPUTRESISTANCE statement is obsolete in version 5.4 and later.\nThe LEF parser will ignore this statement.\nTo avoid this warning in the future, remove this statement from the LEF file with version 5.4 or later."
2056 "MACRO INPUTNOISEMARGIN statement is obsolete in version 5.4 and later.\nThe LEF parser will ignore this statement.\nTo avoid this warning in the future, remove this statement from the LEF file with version 5.4 or later."
2057 "MACRO CAPACITANCE statement is obsolete in version 5.4 and later.\nThe LEF parser will ignore this statement.\nTo avoid this warning in the future, remove this statement from the LEF file with version 5.4 or later."
2058 "MACRO RESISTANCE statement is obsolete in version 5.4 and later.\nThe LEF parser will ignore this statement.\nTo avoid this warning in the future, remove this statement from the LEF file with version 5.4 or later."
2059 "MACRO PULLDOWNRES statement is obsolete in version 5.4 and later.\nThe LEF parser will ignore this statement.\nTo avoid this warning in the future, remove this statement from the LEF file with version 5.4 or later."
2060 "MACRO CURRENTSOURCE statement is obsolete in version 5.4 and later.\nThe LEF parser will ignore this statement.\nTo avoid this warning in the future, remove this statement from the LEF file with version 5.4 or later."
2061 "MACRO CURRENTSOURCE statement is obsolete in version 5.4 and later.\nThe LEF parser will ignore this statement.\nTo avoid this warning in the future, remove this statement from the LEF file with version 5.4 or later."
2062 "MACRO RISEVOLTAGETHRESHOLD statement is obsolete in version 5.4 and later.\nThe LEF parser will ignore this statement.\nTo avoid this warning in the future, remove this statement from the LEF file with version 5.4 or later."
2063 "MACRO FALLVOLTAGETHRESHOLD statement is obsolete in version 5.4 and later.\nThe LEF parser will ignore this statement.\nTo avoid this warning in the future, remove this statement from the LEF file with version 5.4 or later."
2064 "MACRO IV_TABLES statement is obsolete in version 5.4 and later.\nThe LEF parser will ignore this statement.\nTo avoid this warning in the future, remove this statement from the LEF file with version 5.4 or later."
2065 "Either PATH, RECT or POLYGON statement is a required in MACRO/PIN/PORT."
2066 "MACRO TIMING statement is obsolete in version 5.4 and later.\nThe LEF parser will ignore this statement.\nTo avoid this warning in the future, remove this statement from the LEF file with version 5.4 or later."
2067 "DEFINE statement is obsolete in version 5.6 and later.\nThe LEF parser will ignore this statement.\nTo avoid this warning in the future, remove this statement from the LEF file with version 5.6 or later."
2068 "DEFINES statement is obsolete in version 5.6 and later.\nThe LEF parser will ignore this statement.\nTo avoid this warning in the future, remove this statement from the LEF file with version 5.6 or later."
2069 "DEFINEB statement is obsolete in version 5.6 and later.\nThe LEF parser will ignore this statement.\nTo avoid this warning in the future, remove this statement from the LEF file with version 5.6 or later."
2070 "UNIVERSALNOISEMARGIN statement is obsolete in version 5.4 and later.\nThe LEF parser will ignore this statement.\nTo avoid this warning in the future, remove this statement from the LEF file with version 5.4 or later."
2071 "EDGERATETHRESHOLD1 statement is obsolete in version 5.4 and later.\nThe LEF parser will ignore this statement.\nTo avoid this warning in the future, remove this statement from the LEF file with version 5.4 or later."
2072 "EDGERATETHRESHOLD2 statement is obsolete in version 5.4 and later.\nThe LEF parser will ignore this statement.\nTo avoid this warning in the future, remove this statement from the LEF file with version 5.4 or later."
2073 "EDGERATESCALEFACTOR statement is obsolete in version 5.4 and later.\nThe LEF parser will ignore this statement.\nTo avoid this warning in the future, remove this statement from the LEF file with version 5.4 or later."
2074 "NOISETABLE statement is obsolete in version 5.4 and later.\nThe LEF parser will ignore this statement.\nTo avoid this warning in the future, remove this statement from the LEF file with version 5.4 or later."
2075 "CORRECTIONTABLE statement is obsolete in version 5.4 and later.\nThe LEF parser will ignore this statement.\nTo avoid this warning in the future, remove this statement from the LEF file with version 5.4 or later."
2076 "Either PATH, RECT or POLYGON statement is required in MACRO/OBS."
2077 "A SPACING SAMENET section is defined but it is not legal in a LEF 5.7 version file.\nIt will be ignored which will probably cause real DRC violations to be ignored, and may\ncause false DRC violations to occur.\n\nTo avoid this warning, and correctly handle these DRC rules, you should modify your\nLEF to use the appropriate SAMENET keywords as described in the LEF/DEF 5.7\nmanual under the SPACING statements in the LAYER (Routing) and LAYER (Cut)\nsections listed in the LEF Table of Contents."
2078 "It is illegal to have more than one SPACINGTABLE rules within a ROUTING layer"
2079 "CURRENTDEN statement is obsolete in version 5.2 and later.\nThe LEF parser will ignore this statement.\nTo avoid this warning in the future, remove this statement from the LEF file with version 5.2 or later."
2080 "The number of cut values in multiple ARRAYSPACING ARRAYCUTS are not in increasing order.\nTo be consistent with the documentation, update the cut values to increasing order."
2502 "Message %s has been suppressed from output"
2503 "Message %s has exceeded the message display limit of %d"
3000 "There are still data after the END LIBRARY"
4000 "lefwInitCbk was already called, cannot call lefwInit again.\nWriter Exit.\n"
4001 "lefwInit was already called, cannot call lefwInitCbk again.\nWriter Exit.\n"
4002 "lefwInit was already called, cannot call lefwInitCbk again.\nWriter Exit.\n"
4003 "lefwLayerRoutingSpacingUseLengthThreshold cannot be called if\n\tlefwLayerRoutingSpacingRange has not been called.\n"
4004 "lefwLayerRoutingSpacingInfluence cannot be called if\n\tlefRange and rightRange in lefwLayerRoutingSpacing are both zero.\n"
4005 "lefwLayerRoutingSpacingInfluence cannot be called if\n\tlefRange and rightRange in lefwLayerRoutingSpacing are both zero."
4006 "Need an output file if writing in encrypted format.\n"
4100 "lefwWrite called before lefwInitCbk.\n"
4101 "lefwSetRegisterUnusedCallbacks was not called to setup this data.\n"
4500 "Callback for %s is required, but is not defined.\n\n"
4700 "LEF items that were present but ignored because of no callbacks were set.\n"
