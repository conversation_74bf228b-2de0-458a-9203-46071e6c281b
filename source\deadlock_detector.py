#!/usr/bin/env python3
##
# @file   deadlock_detector.py
# <AUTHOR> Assistant
# @date   2024
# @brief  DDP deadlock detection and recovery utilities
#

import torch
import torch.distributed as dist
import threading
import time
import signal
import os
import sys
import logging

logger = logging.getLogger(__name__)

class DeadlockDetector:
    """Detect and handle DDP deadlocks"""
    
    def __init__(self, timeout=60):
        self.timeout = timeout
        self.last_activity = time.time()
        self.monitoring = False
        self.monitor_thread = None
        
    def start_monitoring(self):
        """Start deadlock monitoring"""
        if self.monitoring:
            return
            
        self.monitoring = True
        self.last_activity = time.time()
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        logger.info("Deadlock monitoring started")
    
    def stop_monitoring(self):
        """Stop deadlock monitoring"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1.0)
        logger.info("Deadlock monitoring stopped")
    
    def update_activity(self):
        """Update last activity timestamp"""
        self.last_activity = time.time()
    
    def _monitor_loop(self):
        """Monitor loop running in background thread"""
        while self.monitoring:
            current_time = time.time()
            if current_time - self.last_activity > self.timeout:
                logger.error(f"Potential deadlock detected! No activity for {self.timeout}s")
                self._handle_deadlock()
                break
            time.sleep(1.0)
    
    def _handle_deadlock(self):
        """Handle detected deadlock"""
        rank = dist.get_rank() if dist.is_initialized() else 0
        
        logger.error(f"Rank {rank}: Deadlock detected, attempting recovery...")
        
        # Print stack trace of all threads
        import traceback
        import threading
        
        print(f"\n=== DEADLOCK DETECTED ON RANK {rank} ===")
        print("Stack traces of all threads:")
        
        for thread_id, frame in sys._current_frames().items():
            print(f"\nThread {thread_id}:")
            traceback.print_stack(frame)
        
        # Try to abort any pending operations
        try:
            if torch.cuda.is_available():
                torch.cuda.synchronize()
                torch.cuda.empty_cache()
        except Exception as e:
            logger.error(f"Error during CUDA cleanup: {e}")
        
        # Force exit if deadlock persists
        logger.error(f"Rank {rank}: Forcing process termination due to deadlock")
        os._exit(1)

class CommunicationMonitor:
    """Monitor DDP communication operations"""
    
    def __init__(self):
        self.operation_count = 0
        self.last_operation_time = time.time()
        self.pending_operations = set()
    
    def start_operation(self, operation_name):
        """Mark start of a communication operation"""
        self.operation_count += 1
        self.last_operation_time = time.time()
        self.pending_operations.add(operation_name)
        logger.debug(f"Started operation: {operation_name}")
    
    def end_operation(self, operation_name):
        """Mark end of a communication operation"""
        self.pending_operations.discard(operation_name)
        logger.debug(f"Completed operation: {operation_name}")
    
    def get_status(self):
        """Get current communication status"""
        return {
            'operation_count': self.operation_count,
            'last_operation_time': self.last_operation_time,
            'pending_operations': list(self.pending_operations),
            'time_since_last_op': time.time() - self.last_operation_time
        }

def safe_all_reduce(tensor, op=dist.ReduceOp.SUM, timeout=30):
    """Safe all-reduce with deadlock detection"""
    if not dist.is_initialized() or dist.get_world_size() <= 1:
        return tensor
    
    rank = dist.get_rank()
    operation_name = f"all_reduce_rank_{rank}"
    
    # Monitor this operation
    comm_monitor.start_operation(operation_name)
    deadlock_detector.update_activity()
    
    try:
        # Ensure tensor is contiguous
        if not tensor.is_contiguous():
            tensor = tensor.contiguous()
        
        logger.debug(f"Rank {rank}: Starting safe all_reduce")
        
        # Use async operation with timeout
        work = dist.all_reduce(tensor, op=op, async_op=True)
        
        # Wait with timeout
        start_time = time.time()
        while not work.is_completed():
            if time.time() - start_time > timeout:
                logger.error(f"Rank {rank}: All-reduce timeout after {timeout}s")
                work.abort()
                raise RuntimeError(f"All-reduce timeout on rank {rank}")
            
            deadlock_detector.update_activity()
            time.sleep(0.01)
        
        work.wait()
        logger.debug(f"Rank {rank}: Safe all_reduce completed")
        
    except Exception as e:
        logger.error(f"Rank {rank}: Safe all_reduce failed: {e}")
        raise e
    finally:
        comm_monitor.end_operation(operation_name)
        deadlock_detector.update_activity()
    
    return tensor

def safe_barrier(timeout=30):
    """Safe barrier with deadlock detection"""
    if not dist.is_initialized() or dist.get_world_size() <= 1:
        return
    
    rank = dist.get_rank()
    operation_name = f"barrier_rank_{rank}"
    
    # Monitor this operation
    comm_monitor.start_operation(operation_name)
    deadlock_detector.update_activity()
    
    try:
        logger.debug(f"Rank {rank}: Starting safe barrier")
        
        # Use async barrier with timeout
        work = dist.barrier(async_op=True)
        
        # Wait with timeout
        start_time = time.time()
        while not work.is_completed():
            if time.time() - start_time > timeout:
                logger.error(f"Rank {rank}: Barrier timeout after {timeout}s")
                work.abort()
                raise RuntimeError(f"Barrier timeout on rank {rank}")
            
            deadlock_detector.update_activity()
            time.sleep(0.01)
        
        work.wait()
        logger.debug(f"Rank {rank}: Safe barrier completed")
        
    except Exception as e:
        logger.error(f"Rank {rank}: Safe barrier failed: {e}")
        raise e
    finally:
        comm_monitor.end_operation(operation_name)
        deadlock_detector.update_activity()

def check_communication_health():
    """Check if DDP communication is healthy"""
    if not dist.is_initialized() or dist.get_world_size() <= 1:
        return True
    
    rank = dist.get_rank()
    world_size = dist.get_world_size()
    
    try:
        # Simple communication test
        test_tensor = torch.tensor([rank], dtype=torch.float32)
        if torch.cuda.is_available():
            test_tensor = test_tensor.cuda()
        
        safe_all_reduce(test_tensor)
        
        expected_sum = sum(range(world_size))
        if abs(test_tensor.item() - expected_sum) > 1e-6:
            logger.error(f"Communication test failed: expected {expected_sum}, got {test_tensor.item()}")
            return False
        
        logger.debug(f"Rank {rank}: Communication health check passed")
        return True
        
    except Exception as e:
        logger.error(f"Rank {rank}: Communication health check failed: {e}")
        return False

def setup_signal_handlers():
    """Setup signal handlers for graceful shutdown"""
    def signal_handler(signum, frame):
        rank = dist.get_rank() if dist.is_initialized() else 0
        logger.info(f"Rank {rank}: Received signal {signum}, shutting down...")
        
        # Stop monitoring
        deadlock_detector.stop_monitoring()
        
        # Cleanup
        try:
            if dist.is_initialized():
                dist.destroy_process_group()
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
        
        sys.exit(0)
    
    # Register signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

def print_deadlock_debug_info():
    """Print debug information for deadlock analysis"""
    rank = dist.get_rank() if dist.is_initialized() else 0
    
    print(f"\n=== DEADLOCK DEBUG INFO (Rank {rank}) ===")
    
    # Communication status
    status = comm_monitor.get_status()
    print(f"Communication status: {status}")
    
    # GPU status
    if torch.cuda.is_available():
        for i in range(torch.cuda.device_count()):
            allocated = torch.cuda.memory_allocated(i) / 1024**2
            reserved = torch.cuda.memory_reserved(i) / 1024**2
            print(f"GPU {i}: {allocated:.1f}MB allocated, {reserved:.1f}MB reserved")
    
    # Process info
    import psutil
    process = psutil.Process()
    print(f"CPU usage: {process.cpu_percent()}%")
    print(f"Memory usage: {process.memory_info().rss / 1024**2:.1f}MB")
    
    # Thread info
    print(f"Active threads: {threading.active_count()}")
    for thread in threading.enumerate():
        print(f"  Thread: {thread.name} (alive: {thread.is_alive()})")

# Global instances
deadlock_detector = DeadlockDetector(timeout=60)
comm_monitor = CommunicationMonitor()

# Setup signal handlers
setup_signal_handlers()

def start_deadlock_monitoring():
    """Start deadlock monitoring (call this at the beginning of training)"""
    deadlock_detector.start_monitoring()

def stop_deadlock_monitoring():
    """Stop deadlock monitoring (call this at the end of training)"""
    deadlock_detector.stop_monitoring()

# Auto-start monitoring if this module is imported
if dist.is_initialized():
    start_deadlock_monitoring()
