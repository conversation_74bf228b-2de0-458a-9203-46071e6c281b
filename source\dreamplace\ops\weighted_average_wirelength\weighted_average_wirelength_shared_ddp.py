##
# @file   weighted_average_wirelength_shared_ddp.py
# <AUTHOR> Assistant
# @date   2024
# @brief  Shared parameter DDP-aware weighted average wirelength computation
#

import time
import torch
import torch.distributed as dist
from torch import nn
from torch.autograd import Function

# Import original wirelength modules
import dreamplace.ops.weighted_average_wirelength.weighted_average_wirelength_cpp as weighted_average_wirelength_cpp
try:
    import dreamplace.ops.weighted_average_wirelength.weighted_average_wirelength_hip as weighted_average_wirelength_hip
    import dreamplace.ops.weighted_average_wirelength.weighted_average_wirelength_hip_atomic as weighted_average_wirelength_hip_atomic
except:
    pass

from dreamplace.ddp_shared_param_utils import all_reduce_tensor_sum
import pdb

class WeightedAverageWirelengthSharedDDPFunction(Function):
    """
    @brief Shared parameter DDP-aware weighted average wirelength computation
    Each GPU processes a subset of nets, but all use shared node positions
    """
    @staticmethod
    def forward(ctx, pos, flat_netpin, netpin_start, pin2net_map, net_mask, pin_mask, gamma, 
                pin_pos_op, num_threads, ddp_rank=0, ddp_world_size=1):
        """
        @param pos shared node positions across all GPUs
        @param flat_netpin flat netpin map (shared across all GPUs)
        @param netpin_start netpin start map (shared across all GPUs)
        @param pin2net_map pin to net mapping (shared across all GPUs)
        @param net_mask local net mask for current GPU (only this GPU's nets are enabled)
        @param pin_mask pin mask (shared across all GPUs)
        @param gamma wirelength parameter
        @param pin_pos_op function to convert pos to pin positions
        @param ddp_rank current GPU rank
        @param ddp_world_size total number of GPUs
        """
        # Store DDP info
        ctx.ddp_rank = ddp_rank
        ctx.ddp_world_size = ddp_world_size
        
        # Convert node positions to pin positions (shared computation)
        pin_pos = pin_pos_op(pos)
        
        # Compute local wirelength using original implementation
        # Only nets with net_mask[i] = 1 will contribute to the result
        if pin_pos.is_cuda:
            if hasattr(weighted_average_wirelength_hip_atomic, 'forward'):
                local_output = weighted_average_wirelength_hip_atomic.forward(
                    pin_pos.view(pin_pos.numel()), pin2net_map, net_mask, gamma)

                # 确保所有中间结果都在GPU上并且是一维的
                for i in range(1, len(local_output)):
                    if not local_output[i].is_cuda:
                        local_output[i] = local_output[i].cuda()
                    if local_output[i].ndimension() != 1:
                        local_output[i] = local_output[i].view(-1)
                
                # 打印调试信息
                if ddp_rank == 0:
                    print(f"[DEBUG] exp_xy_sum is_cuda: {local_output[3].is_cuda}, ndim: {local_output[3].ndimension()}")

            else:
                # 使用非原子版本
                local_output = weighted_average_wirelength_hip.forward(
                    pin_pos.view(pin_pos.numel()), flat_netpin, netpin_start, net_mask, gamma)
        else:
            local_output = weighted_average_wirelength_cpp.forward(
                pin_pos.view(pin_pos.numel()), flat_netpin, netpin_start, net_mask, gamma, num_threads)
        
        # Store context for backward pass
        ctx.flat_netpin = flat_netpin
        ctx.netpin_start = netpin_start
        ctx.pin2net_map = pin2net_map
        ctx.net_mask = net_mask
        ctx.pin_mask = pin_mask
        ctx.gamma = gamma
        ctx.pin_pos = pin_pos
        ctx.pin_pos_op = pin_pos_op
        ctx.num_threads = num_threads
        ctx.save_for_backward(pos)
        
        # Store intermediate results for atomic version
        if pin_pos.is_cuda and isinstance(local_output, list) and len(local_output) > 1:
            ctx.exp_xy = local_output[1]
            ctx.exp_nxy = local_output[2]
            ctx.exp_xy_sum = local_output[3]
            ctx.exp_nxy_sum = local_output[4]
            ctx.xyexp_xy_sum = local_output[5]
            ctx.xyexp_nxy_sum = local_output[6]
            ctx.use_atomic = True
        else:
            ctx.use_atomic = False
        
        # Extract wirelength value
        if isinstance(local_output, list):
            wl_value = local_output[0]
        else:
            wl_value = local_output
        
        # All-reduce to sum wirelength from all GPUs
        global_wl = all_reduce_tensor_sum(wl_value.clone())
        
        return global_wl

    @staticmethod
    def backward(ctx, grad_output):
        """Backward pass for shared parameter DDP wirelength"""
        # Compute gradients w.r.t. pin positions
        if ctx.pin_pos.is_cuda:
            if ctx.use_atomic and hasattr(weighted_average_wirelength_hip_atomic, 'backward'):
                pin_grad = weighted_average_wirelength_hip_atomic.backward(
                    grad_output,
                    ctx.pin_pos,
                    ctx.exp_xy, ctx.exp_nxy,
                    ctx.exp_xy_sum, ctx.exp_nxy_sum,
                    ctx.xyexp_xy_sum, ctx.xyexp_nxy_sum,
                    ctx.pin2net_map,
                    ctx.net_mask,
                    ctx.gamma
                )
            else:
                pin_grad = weighted_average_wirelength_hip.backward(
                    grad_output,
                    ctx.pin_pos,
                    ctx.flat_netpin,
                    ctx.netpin_start,
                    ctx.net_mask,
                    ctx.gamma
                )
        else:
            pin_grad = weighted_average_wirelength_cpp.backward(
                grad_output,
                ctx.pin_pos,
                ctx.flat_netpin,
                ctx.netpin_start,
                ctx.net_mask,
                ctx.gamma,
                ctx.num_threads
            )
        
        # Apply pin mask to zero out gradients for fixed macros
        pin_grad[:pin_grad.numel()//2].masked_fill_(ctx.pin_mask, 0.0)
        pin_grad[pin_grad.numel()//2:].masked_fill_(ctx.pin_mask, 0.0)
        
        # All-reduce pin gradients to sum across all GPUs
        all_reduce_tensor_sum(pin_grad)
        
        # Convert pin gradients back to node gradients
        # This requires the inverse of pin_pos_op transformation
        node_grad = ctx.pin_pos_op.backward(pin_grad)
        
        return node_grad, None, None, None, None, None, None, None, None, None, None

class WeightedAverageWirelengthSharedDDP(nn.Module):
    """
    @brief Shared parameter DDP-aware Weighted Average Wirelength computation
    Node positions are shared parameters, nets are partitioned across GPUs
    """
    def __init__(self, flat_netpin=None, netpin_start=None, pin2net_map=None, 
                 net_mask=None, pin_mask=None, gamma=None, pin_pos_op=None,
                 algorithm='atomic', num_threads=8, ddp_rank=0, ddp_world_size=1):
        """
        @brief initialization for shared parameter DDP wirelength
        @param flat_netpin flat netpin map (shared across all GPUs)
        @param netpin_start netpin start map (shared across all GPUs)
        @param pin2net_map pin to net mapping (shared across all GPUs)
        @param net_mask local net mask for current GPU
        @param pin_mask pin mask (shared across all GPUs)
        @param gamma wirelength parameter
        @param pin_pos_op function to convert node positions to pin positions
        @param algorithm 'atomic' or 'net-by-net'
        @param ddp_rank current GPU rank
        @param ddp_world_size total number of GPUs
        """
        super(WeightedAverageWirelengthSharedDDP, self).__init__()
        
        assert net_mask is not None and pin_mask is not None and gamma is not None, \
            "net_mask, pin_mask, gamma are required parameters"
        assert pin_pos_op is not None, \
            "pin_pos_op is required for shared parameter DDP"
            
        self.flat_netpin = flat_netpin
        self.netpin_start = netpin_start
        self.pin2net_map = pin2net_map
        self.net_mask = net_mask
        self.pin_mask = pin_mask
        self.gamma = gamma
        self.pin_pos_op = pin_pos_op
        self.algorithm = algorithm
        self.num_threads = num_threads
        self.ddp_rank = ddp_rank
        self.ddp_world_size = ddp_world_size
        
        # Validate algorithm support
        if algorithm == 'net-by-net':
            assert flat_netpin is not None and netpin_start is not None, \
                "flat_netpin, netpin_start are required for net-by-net algorithm"
        elif algorithm == 'atomic':
            assert pin2net_map is not None, \
                "pin2net_map is required for atomic algorithm"
        else:
            raise NotImplementedError(f"Algorithm {algorithm} not implemented for shared DDP")

    def forward(self, pos):
        """Forward pass for shared parameter DDP wirelength computation"""
        return WeightedAverageWirelengthSharedDDPFunction.apply(
            pos,
            self.flat_netpin,
            self.netpin_start,
            self.pin2net_map,
            self.net_mask,
            self.pin_mask,
            self.gamma,
            self.pin_pos_op,
            self.num_threads,
            self.ddp_rank,
            self.ddp_world_size
        )

class PinPosOp(nn.Module):
    """
    @brief Pin position computation operation
    Converts node positions to pin positions
    """
    def __init__(self, pin2node_map, pin_offset_x, pin_offset_y):
        """
        @brief Initialize pin position operation
        @param pin2node_map mapping from pins to nodes
        @param pin_offset_x pin x offsets
        @param pin_offset_y pin y offsets
        """
        super(PinPosOp, self).__init__()
        # 确保pin2node_map是long类型
        if pin2node_map.dtype != torch.int64:
            pin2node_map = pin2node_map.long()
        self.register_buffer('pin2node_map', pin2node_map)
        self.register_buffer('pin_offset_x', pin_offset_x)
        self.register_buffer('pin_offset_y', pin_offset_y)
        
    def forward(self, pos):
        """
        @brief Convert node positions to pin positions
        @param pos node positions [x_coords, y_coords]
        @return pin positions [pin_x_coords, pin_y_coords]
        """
        num_nodes = pos.shape[0] // 2
        num_pins = self.pin2node_map.shape[0]
        
        # Extract node positions
        node_x = pos[:num_nodes]
        node_y = pos[num_nodes:]
        
        # Compute pin positions
        pin_x = node_x[self.pin2node_map] + self.pin_offset_x
        pin_y = node_y[self.pin2node_map] + self.pin_offset_y
        
        # Combine pin positions
        pin_pos = torch.cat([pin_x, pin_y])
        
        return pin_pos
    
    def backward(self, pin_grad):
        """
        @brief Convert pin gradients back to node gradients
        @param pin_grad gradients w.r.t. pin positions
        @return gradients w.r.t. node positions
        """
        num_pins = self.pin2node_map.shape[0]
        num_nodes = pin_grad.shape[0] // 2
        
        # Extract pin gradients
        pin_grad_x = pin_grad[:num_pins]
        pin_grad_y = pin_grad[num_pins:]
        
        # Initialize node gradients
        node_grad = torch.zeros(num_nodes, dtype=pin_grad.dtype, device=pin_grad.device)
        
        # Accumulate pin gradients to node gradients
        node_grad_x = node_grad[:num_nodes//2]
        node_grad_y = node_grad[num_nodes//2:]
        
        # Use scatter_add to accumulate gradients
        node_grad_x.scatter_add_(0, self.pin2node_map, pin_grad_x)
        node_grad_y.scatter_add_(0, self.pin2node_map, pin_grad_y)
        
        return node_grad
