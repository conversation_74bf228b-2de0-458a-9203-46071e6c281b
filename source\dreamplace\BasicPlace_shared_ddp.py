##
# @file   BasicPlace_shared_ddp.py
# <AUTHOR> Assistant
# @date   2024
# @brief  Shared parameter DDP-aware basic placement class
#

import os
import sys
import time
import gzip
if sys.version_info[0] < 3:
    import cPickle as pickle
else:
    import _pickle as pickle
import re
import numpy as np
import torch
import torch.nn as nn

# Import original ops
import dreamplace.ops.move_boundary.move_boundary as move_boundary
import dreamplace.ops.hpwl.hpwl as hpwl
import dreamplace.ops.density_overflow.density_overflow as density_overflow
import dreamplace.ops.rmst_wl.rmst_wl as rmst_wl
import dreamplace.ops.greedy_legalize.greedy_legalize as greedy_legalize
import dreamplace.ops.draw_place.draw_place as draw_place

# Import shared parameter DDP-aware ops
import dreamplace.ops.electric_potential.electric_overflow_shared_ddp as electric_overflow_shared_ddp

# Import shared parameter DDP utilities
from dreamplace.ddp_shared_param_utils import SharedParamDDPPartitioner, SharedParamDDPDataCollection

import pdb

class BasicPlaceSharedDDP(nn.Module):
    """
    @brief Shared parameter DDP-aware basic placement class
    """
    def __init__(self, params, placedb, ddp_rank=0, ddp_world_size=1):
        """
        @brief initialization for shared parameter DDP basic placement
        @param params parameters
        @param placedb placement database
        @param ddp_rank current GPU rank
        @param ddp_world_size total number of GPUs
        """
        super(BasicPlaceSharedDDP, self).__init__()
        
        self.ddp_rank = ddp_rank
        self.ddp_world_size = ddp_world_size
        
        # Set device for current rank
        if params.gpu and ddp_world_size > 1:
            torch.cuda.set_device(ddp_rank)
            self.device = torch.device(f"cuda:{ddp_rank}")
        else:
            self.device = torch.device("cuda:0" if params.gpu else "cpu")
        
        # Create shared parameter DDP data partitioner
        self.partitioner = SharedParamDDPPartitioner(ddp_rank, ddp_world_size)
        
        # Initialize position parameters (shared across all GPUs)
        self.init_pos = np.zeros(placedb.num_nodes*2, dtype=placedb.dtype)
        # x position
        self.init_pos[0:placedb.num_physical_nodes] = placedb.node_x[0:placedb.num_physical_nodes]
        # y position  
        self.init_pos[placedb.num_nodes:placedb.num_nodes+placedb.num_physical_nodes] = placedb.node_y[0:placedb.num_physical_nodes]
        
        if params.global_place_flag:
            # global placement may move fixed macros
            self.init_pos[placedb.num_movable_nodes:placedb.num_nodes-placedb.num_filler_nodes] = placedb.node_x[placedb.num_movable_nodes:placedb.num_nodes-placedb.num_filler_nodes]
            self.init_pos[placedb.num_nodes+placedb.num_movable_nodes:placedb.num_nodes*2-placedb.num_filler_nodes] = placedb.node_y[placedb.num_movable_nodes:placedb.num_nodes-placedb.num_filler_nodes]
        
        # Convert to tensor and make it a parameter (shared across GPUs)
        self.pos = nn.ParameterList([
            nn.Parameter(torch.from_numpy(self.init_pos).to(self.device))
        ])
        
        # Create shared parameter DDP data collection
        self.data_collections = SharedParamDDPDataCollection(
            self.pos, params, placedb, self.device, self.partitioner)
        
        # Build operations
        self.op_collections = self.build_ops(params, placedb)
        
        if self.ddp_rank == 0:
            print(f"[I] Shared parameter DDP BasicPlace initialized with {ddp_world_size} GPUs")

    def build_ops(self, params, placedb):
        """
        @brief build operations for shared parameter DDP placement
        """
        class OpCollections:
            pass
        
        op_collections = OpCollections()
        
        # pin position operation
        op_collections.pin_pos_op = self.build_pin_pos(params, placedb, self.data_collections)
        
        # move boundary operation
        op_collections.move_boundary_op = self.build_move_boundary(params, placedb, self.data_collections)
        
        # hpwl operation for evaluation
        op_collections.hpwl_op = self.build_hpwl(params, placedb, self.data_collections, op_collections.pin_pos_op)
        
        # density overflow operation for evaluation (DDP-aware)
        op_collections.density_overflow_op = self.build_electric_overflow_shared_ddp(params, placedb, self.data_collections)
        
        # legalization operation
        op_collections.greedy_legalize_op = self.build_greedy_legalization(params, placedb, self.data_collections)
        
        # draw placement operation
        op_collections.draw_place_op = self.build_draw_placement(params, placedb)
        
        return op_collections

    def build_pin_pos(self, params, placedb, data_collections):
        """
        @brief build pin position operation
        """
        def pin_pos_op(pos):
            pin_pos = torch.zeros(len(data_collections.pin2node_map)*2, dtype=pos.dtype, device=pos.device)
            pin_pos[0:len(data_collections.pin2node_map)] = pos[data_collections.pin2node_map] + data_collections.pin_offset_x
            pin_pos[len(data_collections.pin2node_map):] = pos[placedb.num_nodes+data_collections.pin2node_map] + data_collections.pin_offset_y
            return pin_pos
        return pin_pos_op

    def build_move_boundary(self, params, placedb, data_collections):
        """
        @brief build move boundary operation
        """
        return move_boundary.MoveBoundary(
            data_collections.node_size_x, data_collections.node_size_y,
            xl=placedb.xl, yl=placedb.yl, xh=placedb.xh, yh=placedb.yh,
            num_movable_nodes=placedb.num_movable_nodes,
            num_filler_nodes=placedb.num_filler_nodes
        )

    def build_hpwl(self, params, placedb, data_collections, pin_pos_op):
        """
        @brief build hpwl operation for evaluation
        Uses global net_mask to ensure consistent HPWL evaluation across all GPUs
        """
        # Create global net mask (same as original BasicPlace.py)
        net_degrees = np.array([len(pins) for pins in placedb.net2pin_map])
        ignore_net_degree = getattr(placedb, 'ignore_net_degree', 100)
        global_net_mask = np.logical_and(2 <= net_degrees, net_degrees < ignore_net_degree).astype(np.uint8)

        wirelength_for_pin_op = hpwl.HPWL(
            flat_netpin=data_collections.flat_net2pin_map,
            netpin_start=data_collections.flat_net2pin_start_map,
            pin2net_map=data_collections.pin2net_map,
            net_mask=torch.from_numpy(global_net_mask).to(self.device),  # Use global net_mask
            algorithm='atomic',  # Specify algorithm explicitly
            num_threads=params.num_threads
        )
        
        def hpwl_op(pos):
            pin_pos = pin_pos_op(pos)
            return wirelength_for_pin_op(pin_pos)
        
        return hpwl_op

    def build_electric_overflow_shared_ddp(self, params, placedb, data_collections):
        """
        @brief build shared parameter DDP-aware electric overflow operation
        """
        return electric_overflow_shared_ddp.ElectricOverflowSharedDDP(
            node_size_x=data_collections.node_size_x, 
            node_size_y=data_collections.node_size_y,
            bin_center_x=data_collections.bin_center_x, 
            bin_center_y=data_collections.bin_center_y,
            target_density=params.target_density,
            xl=placedb.xl, yl=placedb.yl, xh=placedb.xh, yh=placedb.yh,
            bin_size_x=placedb.bin_size_x, bin_size_y=placedb.bin_size_y,
            num_movable_nodes=placedb.num_movable_nodes,
            num_terminals=placedb.num_terminals,
            num_filler_nodes=placedb.num_filler_nodes,
            local_node_mask=data_collections.local_node_mask,  # Pass local node mask
            padding=0,
            num_threads=params.num_threads,
            ddp_rank=self.ddp_rank,
            ddp_world_size=self.ddp_world_size
        )

    def build_greedy_legalization(self, params, placedb, data_collections):
        """
        @brief build greedy legalization operation
        """
        return greedy_legalize.GreedyLegalize(
            data_collections.node_size_x, data_collections.node_size_y,
            data_collections.flat_region_boxes, data_collections.flat_region_boxes_start, data_collections.node2fence_region_map,
            xl=placedb.xl, yl=placedb.yl, xh=placedb.xh, yh=placedb.yh,
            site_width=placedb.site_width, row_height=placedb.row_height,
            num_bins_x=placedb.num_bins_x, num_bins_y=placedb.num_bins_y,
            num_movable_nodes=placedb.num_movable_nodes,
            num_terminal_NIs=placedb.num_terminal_NIs, num_filler_nodes=placedb.num_filler_nodes,
            num_threads=params.num_threads
        )

    def build_draw_placement(self, params, placedb):
        """
        @brief build draw placement operation
        """
        return draw_place.DrawPlace(placedb)

    def validate(self, placedb, pos, iteration):
        """
        @brief validate placement solution
        @param placedb placement database
        @param pos position
        @param iteration optimization iteration
        @return hpwl, overflow, max_density
        """
        pos = torch.from_numpy(pos).to(self.device)
        hpwl = self.op_collections.hpwl_op(pos)
        overflow, max_density = self.op_collections.density_overflow_op(pos)
        
        return hpwl, overflow, max_density

    def plot(self, params, placedb, iteration, pos):
        """
        @brief plot placement
        @param params parameters
        @param placedb placement database  
        @param iteration optimization iteration
        @param pos position
        """
        pos = torch.from_numpy(pos).to(self.device)
        path = "%s/%s" % (params.result_dir, params.design_name())
        figname = "%s/plot/iter%s.png" % (path, '{:04d}'.format(iteration))
        os.makedirs(os.path.dirname(figname), exist_ok=True)
        self.op_collections.draw_place_op(pos, figname)

    def parameters(self):
        """
        @brief return parameters (shared position parameters)
        """
        return self.pos
