##
# @file   ddp_shared_param_utils.py
# <AUTHOR> Assistant
# @date   2024
# @brief  Shared parameter DDP utilities for DreamPlace
#

import torch
import torch.distributed as dist
import numpy as np
import os
import sys
from typing import Tuple, Dict, List, Optional
import logging

logger = logging.getLogger(__name__)

def setup_ddp(rank: int, world_size: int, backend: str = 'nccl', master_addr: str = 'localhost', master_port: str = '12355'):
    """
    @brief Setup distributed data parallel training
    @param rank current process rank
    @param world_size total number of processes
    @param backend communication backend ('nccl' for GPU, 'gloo' for CPU)
    @param master_addr master node address
    @param master_port master node port
    """
    # Set environment variables for distributed training
    os.environ['MASTER_ADDR'] = master_addr
    os.environ['MASTER_PORT'] = master_port
    os.environ['RANK'] = str(rank)
    os.environ['WORLD_SIZE'] = str(world_size)

    # Determine backend based on device availability
    if backend == 'nccl' and not torch.cuda.is_available():
        backend = 'gloo'
        logger.warning(f"CUDA not available, switching to gloo backend")

    # Initialize the process group
    try:
        dist.init_process_group(
            backend=backend,
            rank=rank,
            world_size=world_size
        )

        if rank == 0:
            logger.info(f"DDP initialized with backend={backend}, world_size={world_size}")

        # Set CUDA device for current rank if using NCCL
        if backend == 'nccl' and torch.cuda.is_available():
            torch.cuda.set_device(rank)

    except Exception as e:
        logger.error(f"Failed to initialize DDP: {e}")
        raise e

def cleanup_ddp():
    """
    @brief Cleanup distributed data parallel training
    """
    if dist.is_initialized():
        try:
            dist.destroy_process_group()
            logger.info("DDP process group destroyed")
        except Exception as e:
            logger.warning(f"Error during DDP cleanup: {e}")

def get_ddp_info():
    """
    @brief Get current DDP information
    @return tuple of (rank, world_size, is_initialized)
    """
    if dist.is_initialized():
        return dist.get_rank(), dist.get_world_size(), True
    else:
        return 0, 1, False

class SharedParamDDPPartitioner:
    """
    @brief Shared parameter DDP partitioner for DreamPlace
    Node positions are shared parameters across all GPUs
    Data is partitioned by nets for WA computation and by nodes for density computation
    """
    
    def __init__(self, rank: int, world_size: int):
        """
        @brief Initialize shared parameter DDP partitioner
        @param rank current GPU rank
        @param world_size total number of GPUs
        """
        self.rank = rank
        self.world_size = world_size
        
    def partition_nets_for_wa(self, placedb) -> Dict:
        """
        @brief Partition nets for weighted average wirelength computation
        Each GPU processes a subset of nets but keeps full pin data
        @param placedb placement database
        @return dictionary containing partitioned net data for current GPU
        """
        num_nets = len(placedb.net2pin_map)
        start_net, end_net = self._partition_range(num_nets, self.rank, self.world_size)

        logger.info(f"GPU {self.rank}: Processing nets {start_net} to {end_net-1} out of {num_nets}")

        # Create local net mask for this GPU's nets
        local_net_mask = np.zeros(num_nets, dtype=np.uint8)
        local_net_mask[start_net:end_net] = 1

        # Apply degree filtering
        net_degrees = np.array([len(pins) for pins in placedb.net2pin_map])
        ignore_net_degree = getattr(placedb, 'ignore_net_degree', 100)
        degree_mask = np.logical_and(2 <= net_degrees, net_degrees < ignore_net_degree).astype(np.uint8)

        # Combine local assignment and degree filtering
        final_net_mask = np.logical_and(local_net_mask, degree_mask).astype(np.uint8)

        return {
            'net_mask': final_net_mask,
            'start_net': start_net,
            'end_net': end_net,
            'num_local_nets': np.sum(final_net_mask),
            'local_net_ids': np.where(final_net_mask)[0]
        }
    
    def partition_nodes_for_density(self, placedb) -> Dict:
        """
        @brief Partition nodes for density computation with deduplication
        Each node's bin contribution is handled by only one GPU
        @param placedb placement database
        @return dictionary containing partitioned node data for current GPU
        """
        num_movable_nodes = placedb.num_movable_nodes
        start_node, end_node = self._partition_range(num_movable_nodes, self.rank, self.world_size)

        logger.info(f"GPU {self.rank}: Processing movable nodes {start_node} to {end_node-1} out of {num_movable_nodes}")

        # Create node assignment mask
        node_assignment = np.zeros(placedb.num_nodes, dtype=np.int32)

        # Assign movable nodes to GPUs
        for node_id in range(num_movable_nodes):
            assigned_gpu = self._get_node_assigned_gpu(node_id, num_movable_nodes, self.world_size)
            node_assignment[node_id] = assigned_gpu

        # Fixed nodes and fillers are assigned to GPU 0 for simplicity
        node_assignment[num_movable_nodes:] = 0

        # Create local node mask for this GPU
        local_node_mask = (node_assignment == self.rank).astype(np.uint8)

        return {
            'node_assignment': node_assignment,
            'local_node_mask': local_node_mask,
            'start_node': start_node,
            'end_node': end_node,
            'num_local_movable_nodes': end_node - start_node,
            'local_node_ids': np.where(local_node_mask)[0]
        }
    
    def _partition_range(self, total_size: int, rank: int, world_size: int) -> Tuple[int, int]:
        """
        @brief Calculate partition range for given rank
        @param total_size total number of items to partition
        @param rank current rank
        @param world_size total number of ranks
        @return (start_idx, end_idx) for current rank
        """
        items_per_rank = total_size // world_size
        remainder = total_size % world_size
        
        if rank < remainder:
            start_idx = rank * (items_per_rank + 1)
            end_idx = start_idx + items_per_rank + 1
        else:
            start_idx = remainder * (items_per_rank + 1) + (rank - remainder) * items_per_rank
            end_idx = start_idx + items_per_rank
            
        return start_idx, end_idx
    
    def _get_node_assigned_gpu(self, node_id: int, num_movable_nodes: int, world_size: int) -> int:
        """
        @brief Get which GPU a node is assigned to for density computation
        @param node_id node identifier
        @param num_movable_nodes total number of movable nodes
        @param world_size total number of GPUs
        @return assigned GPU rank
        """
        items_per_rank = num_movable_nodes // world_size
        remainder = num_movable_nodes % world_size
        
        if node_id < remainder * (items_per_rank + 1):
            return node_id // (items_per_rank + 1)
        else:
            return remainder + (node_id - remainder * (items_per_rank + 1)) // items_per_rank

class SharedParamDDPDataCollection:
    """
    @brief Shared parameter DDP data collection
    Manages shared node positions and partitioned computation data
    """
    
    def __init__(self, pos, params, placedb, device, partitioner: SharedParamDDPPartitioner):
        """
        @brief Initialize shared parameter DDP data collection
        @param pos shared position parameters
        @param params placement parameters
        @param placedb placement database
        @param device target device
        @param partitioner DDP data partitioner
        """
        self.partitioner = partitioner
        self.device = device
        self.pos = pos  # Shared parameters
        
        # Partition data for current GPU
        self.net_partition = partitioner.partition_nets_for_wa(placedb)
        self.node_partition = partitioner.partition_nodes_for_density(placedb)
        
        # Setup shared data (same across all GPUs)
        self._setup_shared_data(placedb, device)
        
        # Setup partitioned data
        self._setup_partitioned_data(placedb, device)
        
    def _setup_shared_data(self, placedb, device):
        """Setup shared data that's identical across all GPUs"""
        # Pin data (shared across all GPUs)
        self.pin_offset_x = torch.tensor(placedb.pin_offset_x, dtype=self.pos[0].dtype, device=device)
        self.pin_offset_y = torch.tensor(placedb.pin_offset_y, dtype=self.pos[0].dtype, device=device)
        self.pin2node_map = torch.from_numpy(placedb.pin2node_map).long().to(device)  # Ensure long type for indexing
        self.pin2net_map = torch.from_numpy(placedb.pin2net_map).long().to(device)    # Ensure long type for indexing

        # Node to pin mapping (shared across all GPUs)
        self.flat_node2pin_map = torch.from_numpy(placedb.flat_node2pin_map).long().to(device)
        self.flat_node2pin_start_map = torch.from_numpy(placedb.flat_node2pin_start_map).long().to(device)

        # Net data (shared across all GPUs)
        self.flat_net2pin_map = torch.from_numpy(placedb.flat_net2pin_map).long().to(device)
        self.flat_net2pin_start_map = torch.from_numpy(placedb.flat_net2pin_start_map).long().to(device)

        # Node data (shared across all GPUs)
        self.node_size_x = torch.tensor(placedb.node_size_x, dtype=self.pos[0].dtype, device=device)
        self.node_size_y = torch.tensor(placedb.node_size_y, dtype=self.pos[0].dtype, device=device)

        # Bin data (shared across all GPUs)
        self.bin_center_x = torch.from_numpy(placedb.bin_center_x).to(device)
        self.bin_center_y = torch.from_numpy(placedb.bin_center_y).to(device)
        
    def _setup_partitioned_data(self, placedb, device):
        """Setup partitioned data specific to current GPU"""
        # Net masks
        # All nets mask (shared across all GPUs, used for HPWL evaluation)
        self.net_mask_all = torch.from_numpy(np.ones(len(placedb.net2pin_map), dtype=np.uint8)).to(device)

        # Net mask for WA computation (only this GPU's nets)
        self.net_mask_ignore_large_degrees = torch.from_numpy(self.net_partition['net_mask']).to(device)

        # Node mask for density computation (only this GPU's nodes)
        self.local_node_mask = torch.from_numpy(self.node_partition['local_node_mask']).to(device)

        # Pin mask for fixed macros (shared logic)
        self.pin_mask_ignore_fixed_macros = (self.pin2node_map >= placedb.num_movable_nodes)
        
    def get_pin_pos(self):
        """
        @brief Compute pin positions from node positions (shared across all GPUs)
        This maintains the original pos -> pin_pos transformation
        @return pin positions tensor
        """
        num_nodes = self.pos[0].shape[0] // 2
        num_pins = self.pin2node_map.shape[0]
        
        # Extract node positions
        node_x = self.pos[0][:num_nodes]
        node_y = self.pos[0][num_nodes:]
        
        # Compute pin positions
        pin_x = node_x[self.pin2node_map] + self.pin_offset_x
        pin_y = node_y[self.pin2node_map] + self.pin_offset_y
        
        # Combine pin positions
        pin_pos = torch.cat([pin_x, pin_y])
        
        return pin_pos
    
    def bin_center_x_padded(self, placedb, padding):
        """Compute padded bin centers (same as original implementation)"""
        if padding == 0:
            return self.bin_center_x
        else:
            bin_size_x = (placedb.xh - placedb.xl) / placedb.num_bins_x
            xl = placedb.xl - padding * bin_size_x
            xh = placedb.xh + padding * bin_size_x
            bin_center_x_padded = torch.from_numpy(placedb.bin_centers(xl, xh, bin_size_x)).to(self.device)
            return bin_center_x_padded
            
    def bin_center_y_padded(self, placedb, padding):
        """Compute padded bin centers (same as original implementation)"""
        if padding == 0:
            return self.bin_center_y
        else:
            bin_size_y = (placedb.yh - placedb.yl) / placedb.num_bins_y
            yl = placedb.yl - padding * bin_size_y
            yh = placedb.yh + padding * bin_size_y
            bin_center_y_padded = torch.from_numpy(placedb.bin_centers(yl, yh, bin_size_y)).to(self.device)
            return bin_center_y_padded

def all_reduce_gradients_sum(tensor: torch.Tensor) -> None:
    """
    @brief All-reduce gradients with SUM operation (no averaging)
    @param tensor tensor to reduce
    """
    if dist.is_initialized() and dist.get_world_size() > 1:
        try:
            dist.all_reduce(tensor, op=dist.ReduceOp.SUM)
        except Exception as e:
            logger.error(f"Failed to all_reduce gradients: {e}")
            raise e

def all_reduce_tensor_sum(tensor: torch.Tensor) -> torch.Tensor:
    """
    @brief All-reduce tensor with SUM operation with robust error handling
    @param tensor tensor to reduce
    @return reduced tensor
    """
    if not dist.is_initialized() or dist.get_world_size() <= 1:
        return tensor

    rank = dist.get_rank()
    world_size = dist.get_world_size()

    try:
        # 添加预检查
        if not tensor.is_cuda:
            logger.warning(f"Rank {rank}: Tensor not on CUDA, moving to GPU")
            tensor = tensor.cuda()

        # 确保tensor是连续的
        if not tensor.is_contiguous():
            tensor = tensor.contiguous()

        # 同步CUDA流
        torch.cuda.synchronize()

        # 执行all_reduce
        logger.debug(f"Rank {rank}: Starting all_reduce, tensor shape: {tensor.shape}")
        dist.all_reduce(tensor, op=dist.ReduceOp.SUM)

        # 再次同步确保完成
        torch.cuda.synchronize()
        logger.debug(f"Rank {rank}: All_reduce completed successfully")

    except RuntimeError as e:
        error_msg = str(e)
        logger.error(f"Rank {rank}: NCCL RuntimeError: {error_msg}")

        if "NCCL" in error_msg or "timeout" in error_msg.lower():
            logger.error(f"Rank {rank}: NCCL communication failure detected")
            logger.error(f"Rank {rank}: This may be due to network issues or process synchronization problems")

            # 尝试恢复策略
            try:
                logger.warning(f"Rank {rank}: Attempting CUDA synchronization recovery")
                torch.cuda.synchronize()
                torch.cuda.empty_cache()
            except Exception as recovery_e:
                logger.error(f"Rank {rank}: Recovery failed: {recovery_e}")

        logger.warning(f"Rank {rank}: Continuing with local tensor value due to NCCL error")

    except Exception as e:
        logger.error(f"Rank {rank}: Unexpected error in all_reduce: {e}")
        logger.warning(f"Rank {rank}: Continuing with local tensor value")

    return tensor

def broadcast_tensor(tensor: torch.Tensor, src: int = 0) -> torch.Tensor:
    """
    @brief Broadcast tensor from source rank to all ranks
    @param tensor tensor to broadcast
    @param src source rank
    @return broadcasted tensor
    """
    if dist.is_initialized() and dist.get_world_size() > 1:
        try:
            dist.broadcast(tensor, src=src)
        except Exception as e:
            logger.error(f"Failed to broadcast tensor: {e}")
            raise e
    return tensor

def is_ddp_available() -> bool:
    """
    @brief Check if DDP is available and properly initialized
    @return True if DDP is available, False otherwise
    """
    return dist.is_available() and dist.is_initialized() and dist.get_world_size() > 1

def synchronize():
    """
    @brief Synchronize all processes in DDP with error handling
    """
    if is_ddp_available():
        try:
            # 简化版本：直接执行barrier，依赖PyTorch的内部机制
            dist.barrier()
        except Exception as e:
            logger.error(f"Failed to synchronize processes: {e}")
            logger.warning(f"Continuing without synchronization")
            # 对于同步失败，记录错误但继续执行

def setup_nccl_environment():
    """
    @brief Setup NCCL environment variables for better stability and debugging
    """
    import os

    # NCCL调试和超时设置
    nccl_env = {
        'NCCL_DEBUG': 'WARN',  # 设置为INFO可以看到更多调试信息
        'NCCL_TIMEOUT': '1800',  # 30分钟超时
        'NCCL_BLOCKING_WAIT': '1',  # 阻塞等待，更好的错误报告
        'NCCL_ASYNC_ERROR_HANDLING': '1',  # 异步错误处理
        'NCCL_DESYNC_DEBUG': '1',  # 同步调试
        'NCCL_IB_TIMEOUT': '22',  # InfiniBand超时
        'NCCL_IB_RETRY_CNT': '7',  # InfiniBand重试次数
        'NCCL_SOCKET_NTHREADS': '8',  # Socket线程数
        'NCCL_NSOCKS_PERTHREAD': '4',  # 每线程socket数
        'NCCL_BUFFSIZE': '8388608',  # 8MB缓冲区
        'NCCL_P2P_DISABLE': '0',  # 启用P2P通信
        'NCCL_SHM_DISABLE': '0',  # 启用共享内存
        'NCCL_NET_GDR_LEVEL': '3',  # GPU Direct RDMA级别
    }

    for key, value in nccl_env.items():
        if key not in os.environ:
            os.environ[key] = value
            logger.info(f"Set {key}={value}")
        else:
            logger.info(f"{key} already set to {os.environ[key]}")

def enhanced_setup_ddp(rank: int, world_size: int, backend: str = 'nccl',
                      master_addr: str = 'localhost', master_port: str = '12355'):
    """
    @brief Enhanced DDP setup with NCCL optimization and error handling
    @param rank current process rank
    @param world_size total number of processes
    @param backend communication backend
    @param master_addr master node address
    @param master_port master node port
    """
    # 设置NCCL环境变量
    if backend == 'nccl':
        setup_nccl_environment()

    # 设置环境变量
    os.environ['MASTER_ADDR'] = master_addr
    os.environ['MASTER_PORT'] = master_port
    os.environ['RANK'] = str(rank)
    os.environ['WORLD_SIZE'] = str(world_size)

    # 确定后端
    if backend == 'nccl' and not torch.cuda.is_available():
        backend = 'gloo'
        logger.warning(f"CUDA not available, switching to gloo backend")

    try:
        # 设置超时时间（30分钟）
        timeout = torch.timedelta(minutes=30)

        # 初始化进程组
        dist.init_process_group(
            backend=backend,
            rank=rank,
            world_size=world_size,
            timeout=timeout
        )

        if rank == 0:
            logger.info(f"Enhanced DDP initialized: backend={backend}, world_size={world_size}, timeout=30min")

        # 设置CUDA设备
        if backend == 'nccl' and torch.cuda.is_available():
            torch.cuda.set_device(rank)

            # 验证通信
            test_tensor = torch.tensor([rank], dtype=torch.float32).cuda()
            dist.all_reduce(test_tensor, op=dist.ReduceOp.SUM)
            expected_sum = sum(range(world_size))

            if abs(test_tensor.item() - expected_sum) > 1e-6:
                raise RuntimeError(f"DDP communication test failed: expected {expected_sum}, got {test_tensor.item()}")

            logger.info(f"Rank {rank}: DDP communication test passed")

    except Exception as e:
        logger.error(f"Rank {rank}: Failed to setup enhanced DDP: {e}")
        raise e
