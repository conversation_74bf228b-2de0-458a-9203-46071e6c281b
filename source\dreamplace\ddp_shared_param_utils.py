##
# @file   ddp_shared_param_utils.py
# <AUTHOR> Assistant
# @date   2024
# @brief  Shared parameter DDP utilities for DreamPlace
#

import torch
import torch.distributed as dist
import numpy as np
import os
import sys
from typing import Tuple, Dict, List, Optional
import logging

logger = logging.getLogger(__name__)

def setup_ddp(rank: int, world_size: int, backend: str = 'nccl', master_addr: str = 'localhost', master_port: str = '12355'):
    """
    @brief Setup distributed data parallel training
    @param rank current process rank
    @param world_size total number of processes
    @param backend communication backend ('nccl' for GPU, 'gloo' for CPU)
    @param master_addr master node address
    @param master_port master node port
    """
    # Set environment variables for distributed training
    os.environ['MASTER_ADDR'] = master_addr
    os.environ['MASTER_PORT'] = master_port
    os.environ['RANK'] = str(rank)
    os.environ['WORLD_SIZE'] = str(world_size)

    # Determine backend based on device availability
    if backend == 'nccl' and not torch.cuda.is_available():
        backend = 'gloo'
        logger.warning(f"CUDA not available, switching to gloo backend")

    # Initialize the process group
    try:
        dist.init_process_group(
            backend=backend,
            rank=rank,
            world_size=world_size
        )

        if rank == 0:
            logger.info(f"DDP initialized with backend={backend}, world_size={world_size}")

        # Set CUDA device for current rank if using NCCL
        if backend == 'nccl' and torch.cuda.is_available():
            torch.cuda.set_device(rank)

    except Exception as e:
        logger.error(f"Failed to initialize DDP: {e}")
        raise e

def cleanup_ddp():
    """
    @brief Cleanup distributed data parallel training
    """
    if dist.is_initialized():
        try:
            dist.destroy_process_group()
            logger.info("DDP process group destroyed")
        except Exception as e:
            logger.warning(f"Error during DDP cleanup: {e}")

def get_ddp_info():
    """
    @brief Get current DDP information
    @return tuple of (rank, world_size, is_initialized)
    """
    if dist.is_initialized():
        return dist.get_rank(), dist.get_world_size(), True
    else:
        return 0, 1, False

class SharedParamDDPPartitioner:
    """
    @brief Shared parameter DDP partitioner for DreamPlace
    Node positions are shared parameters across all GPUs
    Data is partitioned by nets for WA computation and by nodes for density computation
    """
    
    def __init__(self, rank: int, world_size: int):
        """
        @brief Initialize shared parameter DDP partitioner
        @param rank current GPU rank
        @param world_size total number of GPUs
        """
        self.rank = rank
        self.world_size = world_size
        
    def partition_nets_for_wa(self, placedb) -> Dict:
        """
        @brief Partition nets for weighted average wirelength computation
        Creates continuous and flat data structures for efficient processing
        @param placedb placement database
        @return dictionary containing partitioned net data for current GPU
        """
        # First, filter nets by degree to get valid nets
        net_degrees = np.array([len(pins) for pins in placedb.net2pin_map])
        ignore_net_degree = getattr(placedb, 'ignore_net_degree', 100)
        valid_net_mask = np.logical_and(2 <= net_degrees, net_degrees < ignore_net_degree)
        valid_net_ids = np.where(valid_net_mask)[0]

        # Partition valid nets among GPUs
        num_valid_nets = len(valid_net_ids)
        start_idx, end_idx = self._partition_range(num_valid_nets, self.rank, self.world_size)

        # Get continuous local net IDs
        local_valid_net_ids = valid_net_ids[start_idx:end_idx]

        logger.info(f"GPU {self.rank}: Processing {len(local_valid_net_ids)} nets "
                   f"(indices {start_idx} to {end_idx-1} of {num_valid_nets} valid nets)")

        # Create sparse mask for compatibility with existing code
        local_net_mask = np.zeros(len(placedb.net2pin_map), dtype=np.uint8)
        local_net_mask[local_valid_net_ids] = 1

        # Create flat data structures for local nets
        local_flat_net2pin_map = []
        local_flat_net2pin_start_map = [0]

        for net_id in local_valid_net_ids:
            pins = placedb.net2pin_map[net_id]
            local_flat_net2pin_map.extend(pins)
            local_flat_net2pin_start_map.append(len(local_flat_net2pin_map))

        return {
            'net_mask': local_net_mask,  # Sparse mask for compatibility
            'local_net_ids': local_valid_net_ids,  # Continuous local net IDs
            'num_local_nets': len(local_valid_net_ids),
            'start_idx': start_idx,
            'end_idx': end_idx,
            # Flat and continuous data structures
            'local_flat_net2pin_map': np.array(local_flat_net2pin_map, dtype=np.int32),
            'local_flat_net2pin_start_map': np.array(local_flat_net2pin_start_map, dtype=np.int32),
            'local_net_degrees': net_degrees[local_valid_net_ids]
        }
    
    def partition_nodes_for_density(self, placedb) -> Dict:
        """
        @brief Partition nodes for density computation with deduplication
        Creates continuous and flat data structures for efficient processing
        @param placedb placement database
        @return dictionary containing partitioned node data for current GPU
        """
        num_movable_nodes = placedb.num_movable_nodes
        start_node, end_node = self._partition_range(num_movable_nodes, self.rank, self.world_size)

        logger.info(f"GPU {self.rank}: Processing movable nodes {start_node} to {end_node-1} out of {num_movable_nodes}")

        # Create continuous local movable node IDs
        local_movable_node_ids = np.arange(start_node, end_node, dtype=np.int32)

        # For compatibility, create sparse masks
        node_assignment = np.zeros(placedb.num_nodes, dtype=np.int32)

        # Assign movable nodes to GPUs (continuous assignment)
        for rank in range(self.world_size):
            rank_start, rank_end = self._partition_range(num_movable_nodes, rank, self.world_size)
            node_assignment[rank_start:rank_end] = rank

        # Fixed nodes and fillers are assigned to GPU 0 for simplicity
        node_assignment[num_movable_nodes:] = 0

        # Create local node mask for this GPU
        local_node_mask = (node_assignment == self.rank).astype(np.uint8)

        # Create flat data structures for local nodes
        local_node_sizes_x = placedb.node_size_x[local_movable_node_ids] if len(local_movable_node_ids) > 0 else np.array([])
        local_node_sizes_y = placedb.node_size_y[local_movable_node_ids] if len(local_movable_node_ids) > 0 else np.array([])

        # Include fixed nodes and fillers for GPU 0
        if self.rank == 0:
            # Add fixed nodes and fillers
            fixed_and_filler_ids = np.arange(num_movable_nodes, placedb.num_nodes, dtype=np.int32)
            all_local_node_ids = np.concatenate([local_movable_node_ids, fixed_and_filler_ids])
            all_local_sizes_x = np.concatenate([local_node_sizes_x, placedb.node_size_x[fixed_and_filler_ids]])
            all_local_sizes_y = np.concatenate([local_node_sizes_y, placedb.node_size_y[fixed_and_filler_ids]])
        else:
            all_local_node_ids = local_movable_node_ids
            all_local_sizes_x = local_node_sizes_x
            all_local_sizes_y = local_node_sizes_y

        return {
            'node_assignment': node_assignment,  # Sparse assignment for compatibility
            'local_node_mask': local_node_mask,  # Sparse mask for compatibility
            'start_node': start_node,
            'end_node': end_node,
            'num_local_movable_nodes': end_node - start_node,
            # Continuous and flat data structures
            'local_movable_node_ids': local_movable_node_ids,
            'all_local_node_ids': all_local_node_ids,
            'local_node_sizes_x': all_local_sizes_x,
            'local_node_sizes_y': all_local_sizes_y,
            'num_local_nodes': len(all_local_node_ids)
        }
    
    def _partition_range(self, total_size: int, rank: int, world_size: int) -> Tuple[int, int]:
        """
        @brief Calculate partition range for given rank
        @param total_size total number of items to partition
        @param rank current rank
        @param world_size total number of ranks
        @return (start_idx, end_idx) for current rank
        """
        items_per_rank = total_size // world_size
        remainder = total_size % world_size
        
        if rank < remainder:
            start_idx = rank * (items_per_rank + 1)
            end_idx = start_idx + items_per_rank + 1
        else:
            start_idx = remainder * (items_per_rank + 1) + (rank - remainder) * items_per_rank
            end_idx = start_idx + items_per_rank
            
        return start_idx, end_idx
    
    def _get_node_assigned_gpu(self, node_id: int, num_movable_nodes: int, world_size: int) -> int:
        """
        @brief Get which GPU a node is assigned to for density computation
        @param node_id node identifier
        @param num_movable_nodes total number of movable nodes
        @param world_size total number of GPUs
        @return assigned GPU rank
        """
        items_per_rank = num_movable_nodes // world_size
        remainder = num_movable_nodes % world_size
        
        if node_id < remainder * (items_per_rank + 1):
            return node_id // (items_per_rank + 1)
        else:
            return remainder + (node_id - remainder * (items_per_rank + 1)) // items_per_rank

class SharedParamDDPDataCollection:
    """
    @brief Shared parameter DDP data collection
    Manages shared node positions and partitioned computation data
    """
    
    def __init__(self, pos, params, placedb, device, partitioner: SharedParamDDPPartitioner):
        """
        @brief Initialize shared parameter DDP data collection
        @param pos shared position parameters
        @param params placement parameters
        @param placedb placement database
        @param device target device
        @param partitioner DDP data partitioner
        """
        self.partitioner = partitioner
        self.device = device
        self.pos = pos  # Shared parameters
        
        # Partition data for current GPU
        self.net_partition = partitioner.partition_nets_for_wa(placedb)
        self.node_partition = partitioner.partition_nodes_for_density(placedb)
        
        # Setup shared data (same across all GPUs)
        self._setup_shared_data(placedb, device)
        
        # Setup partitioned data
        self._setup_partitioned_data(placedb, device)
        
    def _setup_shared_data(self, placedb, device):
        """Setup shared data that's identical across all GPUs"""
        # Pin data (shared across all GPUs)
        self.pin_offset_x = torch.tensor(placedb.pin_offset_x, dtype=self.pos[0].dtype, device=device)
        self.pin_offset_y = torch.tensor(placedb.pin_offset_y, dtype=self.pos[0].dtype, device=device)
        self.pin2node_map = torch.from_numpy(placedb.pin2node_map).to(device)
        self.pin2net_map = torch.from_numpy(placedb.pin2net_map).to(device)

        # Node to pin mapping (shared across all GPUs)
        self.flat_node2pin_map = torch.from_numpy(placedb.flat_node2pin_map).to(device)
        self.flat_node2pin_start_map = torch.from_numpy(placedb.flat_node2pin_start_map).to(device)

        # Net data (shared across all GPUs)
        self.flat_net2pin_map = torch.from_numpy(placedb.flat_net2pin_map).to(device)
        self.flat_net2pin_start_map = torch.from_numpy(placedb.flat_net2pin_start_map).to(device)

        # Node data (shared across all GPUs)
        self.node_size_x = torch.tensor(placedb.node_size_x, dtype=self.pos[0].dtype, device=device)
        self.node_size_y = torch.tensor(placedb.node_size_y, dtype=self.pos[0].dtype, device=device)

        # Bin data (shared across all GPUs)
        self.bin_center_x = torch.from_numpy(placedb.bin_center_x).to(device)
        self.bin_center_y = torch.from_numpy(placedb.bin_center_y).to(device)
        
    def _setup_partitioned_data(self, placedb, device):
        """Setup partitioned data specific to current GPU"""
        # Net masks
        # All nets mask (shared across all GPUs, used for HPWL evaluation)
        self.net_mask_all = torch.from_numpy(np.ones(len(placedb.net2pin_map), dtype=np.uint8)).to(device)

        # Net mask for WA computation (only this GPU's nets)
        self.net_mask_ignore_large_degrees = torch.from_numpy(self.net_partition['net_mask']).to(device)

        # Continuous and flat net data for local computation
        self.local_net_ids = torch.from_numpy(self.net_partition['local_net_ids']).to(device)
        self.local_flat_net2pin_map = torch.from_numpy(self.net_partition['local_flat_net2pin_map']).to(device)
        self.local_flat_net2pin_start_map = torch.from_numpy(self.net_partition['local_flat_net2pin_start_map']).to(device)
        self.num_local_nets = self.net_partition['num_local_nets']

        # Node mask for density computation (only this GPU's nodes)
        self.local_node_mask = torch.from_numpy(self.node_partition['local_node_mask']).to(device)

        # Continuous and flat node data for local computation
        self.local_movable_node_ids = torch.from_numpy(self.node_partition['local_movable_node_ids']).to(device)
        self.all_local_node_ids = torch.from_numpy(self.node_partition['all_local_node_ids']).to(device)
        self.local_node_sizes_x = torch.from_numpy(self.node_partition['local_node_sizes_x']).to(device)
        self.local_node_sizes_y = torch.from_numpy(self.node_partition['local_node_sizes_y']).to(device)
        self.num_local_nodes = self.node_partition['num_local_nodes']

        # Pin mask for fixed macros (shared logic)
        self.pin_mask_ignore_fixed_macros = (self.pin2node_map >= placedb.num_movable_nodes)
        
    def get_pin_pos(self):
        """
        @brief Compute pin positions from node positions (shared across all GPUs)
        This maintains the original pos -> pin_pos transformation
        @return pin positions tensor
        """
        num_nodes = self.pos[0].shape[0] // 2
        num_pins = self.pin2node_map.shape[0]
        
        # Extract node positions
        node_x = self.pos[0][:num_nodes]
        node_y = self.pos[0][num_nodes:]
        
        # Compute pin positions
        pin_x = node_x[self.pin2node_map] + self.pin_offset_x
        pin_y = node_y[self.pin2node_map] + self.pin_offset_y
        
        # Combine pin positions
        pin_pos = torch.cat([pin_x, pin_y])
        
        return pin_pos
    
    def bin_center_x_padded(self, placedb, padding):
        """Compute padded bin centers (same as original implementation)"""
        if padding == 0:
            return self.bin_center_x
        else:
            bin_size_x = (placedb.xh - placedb.xl) / placedb.num_bins_x
            xl = placedb.xl - padding * bin_size_x
            xh = placedb.xh + padding * bin_size_x
            bin_center_x_padded = torch.from_numpy(placedb.bin_centers(xl, xh, bin_size_x)).to(self.device)
            return bin_center_x_padded
            
    def bin_center_y_padded(self, placedb, padding):
        """Compute padded bin centers (same as original implementation)"""
        if padding == 0:
            return self.bin_center_y
        else:
            bin_size_y = (placedb.yh - placedb.yl) / placedb.num_bins_y
            yl = placedb.yl - padding * bin_size_y
            yh = placedb.yh + padding * bin_size_y
            bin_center_y_padded = torch.from_numpy(placedb.bin_centers(yl, yh, bin_size_y)).to(self.device)
            return bin_center_y_padded

def all_reduce_gradients_sum(tensor: torch.Tensor) -> None:
    """
    @brief All-reduce gradients with SUM operation (no averaging)
    @param tensor tensor to reduce
    """
    if dist.is_initialized() and dist.get_world_size() > 1:
        try:
            dist.all_reduce(tensor, op=dist.ReduceOp.SUM)
        except Exception as e:
            logger.error(f"Failed to all_reduce gradients: {e}")
            raise e

def all_reduce_tensor_sum(tensor: torch.Tensor) -> torch.Tensor:
    """
    @brief All-reduce tensor with SUM operation
    @param tensor tensor to reduce
    @return reduced tensor
    """
    if dist.is_initialized() and dist.get_world_size() > 1:
        try:
            dist.all_reduce(tensor, op=dist.ReduceOp.SUM)
        except Exception as e:
            logger.error(f"Failed to all_reduce tensor: {e}")
            raise e
    return tensor

def broadcast_tensor(tensor: torch.Tensor, src: int = 0) -> torch.Tensor:
    """
    @brief Broadcast tensor from source rank to all ranks
    @param tensor tensor to broadcast
    @param src source rank
    @return broadcasted tensor
    """
    if dist.is_initialized() and dist.get_world_size() > 1:
        try:
            dist.broadcast(tensor, src=src)
        except Exception as e:
            logger.error(f"Failed to broadcast tensor: {e}")
            raise e
    return tensor

def is_ddp_available() -> bool:
    """
    @brief Check if DDP is available and properly initialized
    @return True if DDP is available, False otherwise
    """
    return dist.is_available() and dist.is_initialized() and dist.get_world_size() > 1

def synchronize():
    """
    @brief Synchronize all processes in DDP
    """
    if is_ddp_available():
        try:
            dist.barrier()
        except Exception as e:
            logger.error(f"Failed to synchronize processes: {e}")
            raise e
