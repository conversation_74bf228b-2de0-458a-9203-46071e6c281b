%!PS-Adobe-2.0 EPSF-2.0
%%Title: constraints.fig
%%Creator: fig2dev Version 3.2 Patchlevel 5-alpha5
%%CreationDate: Thu Aug 10 21:59:32 2006
%%For: brian@bullwinkle (brian,,,)
%%BoundingBox: 0 0 320 131
%Magnification: 1.0000
%%EndComments
/$F2psDict 200 dict def
$F2psDict begin
$F2psDict /mtrx matrix put
/col-1 {0 setgray} bind def
/col0 {0.000 0.000 0.000 srgb} bind def
/col1 {0.000 0.000 1.000 srgb} bind def
/col2 {0.000 1.000 0.000 srgb} bind def
/col3 {0.000 1.000 1.000 srgb} bind def
/col4 {1.000 0.000 0.000 srgb} bind def
/col5 {1.000 0.000 1.000 srgb} bind def
/col6 {1.000 1.000 0.000 srgb} bind def
/col7 {1.000 1.000 1.000 srgb} bind def
/col8 {0.000 0.000 0.560 srgb} bind def
/col9 {0.000 0.000 0.690 srgb} bind def
/col10 {0.000 0.000 0.820 srgb} bind def
/col11 {0.530 0.810 1.000 srgb} bind def
/col12 {0.000 0.560 0.000 srgb} bind def
/col13 {0.000 0.690 0.000 srgb} bind def
/col14 {0.000 0.820 0.000 srgb} bind def
/col15 {0.000 0.560 0.560 srgb} bind def
/col16 {0.000 0.690 0.690 srgb} bind def
/col17 {0.000 0.820 0.820 srgb} bind def
/col18 {0.560 0.000 0.000 srgb} bind def
/col19 {0.690 0.000 0.000 srgb} bind def
/col20 {0.820 0.000 0.000 srgb} bind def
/col21 {0.560 0.000 0.560 srgb} bind def
/col22 {0.690 0.000 0.690 srgb} bind def
/col23 {0.820 0.000 0.820 srgb} bind def
/col24 {0.500 0.190 0.000 srgb} bind def
/col25 {0.630 0.250 0.000 srgb} bind def
/col26 {0.750 0.380 0.000 srgb} bind def
/col27 {1.000 0.500 0.500 srgb} bind def
/col28 {1.000 0.630 0.630 srgb} bind def
/col29 {1.000 0.750 0.750 srgb} bind def
/col30 {1.000 0.880 0.880 srgb} bind def
/col31 {1.000 0.840 0.000 srgb} bind def

end
save
newpath 0 131 moveto 0 0 lineto 320 0 lineto 320 131 lineto closepath clip newpath
-55.7 171.5 translate
1 -1 scale

/cp {closepath} bind def
/ef {eofill} bind def
/gr {grestore} bind def
/gs {gsave} bind def
/sa {save} bind def
/rs {restore} bind def
/l {lineto} bind def
/m {moveto} bind def
/rm {rmoveto} bind def
/n {newpath} bind def
/s {stroke} bind def
/sh {show} bind def
/slc {setlinecap} bind def
/slj {setlinejoin} bind def
/slw {setlinewidth} bind def
/srgb {setrgbcolor} bind def
/rot {rotate} bind def
/sc {scale} bind def
/sd {setdash} bind def
/ff {findfont} bind def
/sf {setfont} bind def
/scf {scalefont} bind def
/sw {stringwidth} bind def
/tr {translate} bind def
/tnt {dup dup currentrgbcolor
  4 -2 roll dup 1 exch sub 3 -1 roll mul add
  4 -2 roll dup 1 exch sub 3 -1 roll mul add
  4 -2 roll dup 1 exch sub 3 -1 roll mul add srgb}
  bind def
/shd {dup dup currentrgbcolor 4 -2 roll mul 4 -2 roll mul
  4 -2 roll mul srgb} bind def
/reencdict 12 dict def /ReEncode { reencdict begin
/newcodesandnames exch def /newfontname exch def /basefontname exch def
/basefontdict basefontname findfont def /newfont basefontdict maxlength dict def
basefontdict { exch dup /FID ne { dup /Encoding eq
{ exch dup length array copy newfont 3 1 roll put }
{ exch newfont 3 1 roll put } ifelse } { pop pop } ifelse } forall
newfont /FontName newfontname put newcodesandnames aload pop
128 1 255 { newfont /Encoding get exch /.notdef put } for
newcodesandnames length 2 idiv { newfont /Encoding get 3 1 roll put } repeat
newfontname newfont definefont pop end } def
/isovec [
8#055 /minus 8#200 /grave 8#201 /acute 8#202 /circumflex 8#203 /tilde
8#204 /macron 8#205 /breve 8#206 /dotaccent 8#207 /dieresis
8#210 /ring 8#211 /cedilla 8#212 /hungarumlaut 8#213 /ogonek 8#214 /caron
8#220 /dotlessi 8#230 /oe 8#231 /OE
8#240 /space 8#241 /exclamdown 8#242 /cent 8#243 /sterling
8#244 /currency 8#245 /yen 8#246 /brokenbar 8#247 /section 8#250 /dieresis
8#251 /copyright 8#252 /ordfeminine 8#253 /guillemotleft 8#254 /logicalnot
8#255 /hyphen 8#256 /registered 8#257 /macron 8#260 /degree 8#261 /plusminus
8#262 /twosuperior 8#263 /threesuperior 8#264 /acute 8#265 /mu 8#266 /paragraph
8#267 /periodcentered 8#270 /cedilla 8#271 /onesuperior 8#272 /ordmasculine
8#273 /guillemotright 8#274 /onequarter 8#275 /onehalf
8#276 /threequarters 8#277 /questiondown 8#300 /Agrave 8#301 /Aacute
8#302 /Acircumflex 8#303 /Atilde 8#304 /Adieresis 8#305 /Aring
8#306 /AE 8#307 /Ccedilla 8#310 /Egrave 8#311 /Eacute
8#312 /Ecircumflex 8#313 /Edieresis 8#314 /Igrave 8#315 /Iacute
8#316 /Icircumflex 8#317 /Idieresis 8#320 /Eth 8#321 /Ntilde 8#322 /Ograve
8#323 /Oacute 8#324 /Ocircumflex 8#325 /Otilde 8#326 /Odieresis 8#327 /multiply
8#330 /Oslash 8#331 /Ugrave 8#332 /Uacute 8#333 /Ucircumflex
8#334 /Udieresis 8#335 /Yacute 8#336 /Thorn 8#337 /germandbls 8#340 /agrave
8#341 /aacute 8#342 /acircumflex 8#343 /atilde 8#344 /adieresis 8#345 /aring
8#346 /ae 8#347 /ccedilla 8#350 /egrave 8#351 /eacute
8#352 /ecircumflex 8#353 /edieresis 8#354 /igrave 8#355 /iacute
8#356 /icircumflex 8#357 /idieresis 8#360 /eth 8#361 /ntilde 8#362 /ograve
8#363 /oacute 8#364 /ocircumflex 8#365 /otilde 8#366 /odieresis 8#367 /divide
8#370 /oslash 8#371 /ugrave 8#372 /uacute 8#373 /ucircumflex
8#374 /udieresis 8#375 /yacute 8#376 /thorn 8#377 /ydieresis] def
/Times-Roman /Times-Roman-iso isovec ReEncode
 /DrawEllipse {
	/endangle exch def
	/startangle exch def
	/yrad exch def
	/xrad exch def
	/y exch def
	/x exch def
	/savematrix mtrx currentmatrix def
	x y tr xrad yrad sc 0 0 1 startangle endangle arc
	closepath
	savematrix setmatrix
	} def

/$F2psBegin {$F2psDict begin /$F2psEnteredState save def} def
/$F2psEnd {$F2psEnteredState restore end} def

$F2psBegin
10 setmiterlimit
0 slj 0 slc
 0.06299 0.06299 sc
%
% Fig objects follow
%
% 
% here starts figure with depth 50
% Ellipse
7.500 slw
n 1710 2430 90 90 0 360 DrawEllipse gs 0.00 setgray ef gr gs col0 s gr

% Ellipse
n 1710 1710 90 90 0 360 DrawEllipse gs 0.00 setgray ef gr gs col0 s gr

% Polyline
0 slj
0 slc
15.000 slw
n 1350 675 m 1350 2700 l 2025 2700 l 2025 675 l
 1350 675 l  cp gs col0 s gr 
% Polyline
7.500 slw
n 1350 1350 m
 2025 1350 l gs col0 s gr 
% Polyline
n 1350 2025 m
 2025 2025 l gs col0 s gr 
% Polyline
n 3060 1440 m 4140 1440 l 4140 1980 l 3060 1980 l
 cp gs col0 s gr 
% Polyline
n 3060 2160 m 4140 2160 l 4140 2700 l 3060 2700 l
 cp gs col0 s gr 
% Polyline
n 4860 2160 m 5940 2160 l 5940 2700 l 4860 2700 l
 cp gs col0 s gr 
% Polyline
n 4860 1440 m 5940 1440 l 5940 1980 l 4860 1980 l
 cp gs col0 s gr 
% Polyline
gs  clippath
2908 1740 m 3075 1740 l 3075 1680 l 2908 1680 l 2908 1680 l 3028 1710 l 2908 1740 l cp
eoclip
n 1710 1710 m
 3060 1710 l gs col0 s gr gr

% arrowhead
n 2908 1740 m 3028 1710 l 2908 1680 l 2908 1740 l  cp gs 0.00 setgray ef gr  col0 s
% Polyline
gs  clippath
4708 1740 m 4875 1740 l 4875 1680 l 4708 1680 l 4708 1680 l 4828 1710 l 4708 1740 l cp
eoclip
n 4140 1710 m
 4860 1710 l gs col0 s gr gr

% arrowhead
n 4708 1740 m 4828 1710 l 4708 1680 l 4708 1740 l  cp gs 0.00 setgray ef gr  col0 s
% Polyline
gs  clippath
2908 2460 m 3075 2460 l 3075 2400 l 2908 2400 l 2908 2400 l 3028 2430 l 2908 2460 l cp
eoclip
n 1710 2430 m
 3060 2430 l gs col0 s gr gr

% arrowhead
n 2908 2460 m 3028 2430 l 2908 2400 l 2908 2460 l  cp gs 0.00 setgray ef gr  col0 s
% Polyline
gs  clippath
4708 2460 m 4875 2460 l 4875 2400 l 4708 2400 l 4708 2400 l 4828 2430 l 4708 2460 l cp
eoclip
n 4140 2430 m
 4860 2430 l gs col0 s gr gr

% arrowhead
n 4708 2460 m 4828 2430 l 4708 2400 l 4708 2460 l  cp gs 0.00 setgray ef gr  col0 s
/Times-Roman-iso ff 285.75 scf sf
3150 2475 m
gs 1 -1 sc (Block 2) col0 sh gr
/Times-Roman-iso ff 285.75 scf sf
4950 2475 m
gs 1 -1 sc (Block 3) col0 sh gr
/Times-Roman-iso ff 285.75 scf sf
3150 1800 m
gs 1 -1 sc (Block 1) col0 sh gr
/Times-Roman-iso ff 285.75 scf sf
4950 1800 m
gs 1 -1 sc (Block 3) col0 sh gr
/Times-Roman-iso ff 285.75 scf sf
1620 1170 m
gs 1 -1 sc (--) col0 sh gr
/Times-Roman-iso ff 285.75 scf sf
900 1800 m
gs 1 -1 sc (A1) col0 sh gr
/Times-Roman-iso ff 285.75 scf sf
900 2520 m
gs 1 -1 sc (A2) col0 sh gr
% here ends figure;
$F2psEnd
rs
showpage
%%Trailer
%EOF
