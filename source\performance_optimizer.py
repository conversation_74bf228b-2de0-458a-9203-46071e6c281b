#!/usr/bin/env python3
##
# @file   performance_optimizer.py
# <AUTHOR> Assistant
# @date   2024
# @brief  Performance optimization utilities for DreamPlace DDP
#

import torch
import time
import gc
import psutil
import os
from typing import Dict, List, Optional
import logging

logger = logging.getLogger(__name__)

class MemoryOptimizer:
    """Memory optimization utilities"""
    
    @staticmethod
    def optimize_tensor_creation(numpy_array, device, dtype=None, non_blocking=True):
        """Optimized tensor creation from numpy array"""
        if dtype is None:
            dtype = torch.float32  # Use float32 by default for memory efficiency
        
        return torch.from_numpy(numpy_array).to(
            device=device, 
            dtype=dtype, 
            non_blocking=non_blocking
        )
    
    @staticmethod
    def clear_cache():
        """Clear memory caches"""
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
    
    @staticmethod
    def get_memory_info():
        """Get current memory usage information"""
        info = {
            'cpu_memory_mb': psutil.virtual_memory().used / 1024**2,
            'cpu_memory_percent': psutil.virtual_memory().percent
        }
        
        if torch.cuda.is_available():
            for i in range(torch.cuda.device_count()):
                allocated = torch.cuda.memory_allocated(i) / 1024**2
                reserved = torch.cuda.memory_reserved(i) / 1024**2
                total = torch.cuda.get_device_properties(i).total_memory / 1024**2
                
                info[f'gpu_{i}_allocated_mb'] = allocated
                info[f'gpu_{i}_reserved_mb'] = reserved
                info[f'gpu_{i}_total_mb'] = total
                info[f'gpu_{i}_utilization'] = allocated / total * 100
        
        return info

class PerformanceProfiler:
    """Performance profiling utilities"""
    
    def __init__(self):
        self.timers = {}
        self.counters = {}
    
    def start_timer(self, name: str):
        """Start a timer"""
        self.timers[name] = time.time()
    
    def end_timer(self, name: str) -> float:
        """End a timer and return elapsed time"""
        if name in self.timers:
            elapsed = time.time() - self.timers[name]
            del self.timers[name]
            return elapsed
        return 0.0
    
    def increment_counter(self, name: str, value: int = 1):
        """Increment a counter"""
        self.counters[name] = self.counters.get(name, 0) + value
    
    def get_stats(self) -> Dict:
        """Get performance statistics"""
        return {
            'active_timers': list(self.timers.keys()),
            'counters': self.counters.copy()
        }

class ComputeOptimizer:
    """Compute optimization utilities"""
    
    @staticmethod
    def optimize_tensor_ops():
        """Set optimal tensor operation settings"""
        # Enable optimized attention if available
        if hasattr(torch.backends.cuda, 'enable_flash_sdp'):
            torch.backends.cuda.enable_flash_sdp(True)
        
        # Enable tensor core usage
        if torch.cuda.is_available():
            torch.backends.cudnn.allow_tf32 = True
            torch.backends.cuda.matmul.allow_tf32 = True
        
        # Set optimal number of threads
        if hasattr(torch, 'set_num_threads'):
            num_cores = os.cpu_count()
            torch.set_num_threads(min(num_cores, 8))  # Optimal for most cases
    
    @staticmethod
    def create_optimized_tensor(shape, dtype=torch.float32, device='cuda', requires_grad=False):
        """Create tensor with optimal memory layout"""
        return torch.empty(
            shape, 
            dtype=dtype, 
            device=device, 
            requires_grad=requires_grad,
            memory_format=torch.contiguous_format
        )
    
    @staticmethod
    def optimize_pin_pos_computation(pos, pin2node_map, pin_offset_x, pin_offset_y, num_nodes):
        """Optimized pin position computation"""
        num_pins = len(pin2node_map)
        
        # Pre-allocate output tensor
        pin_pos = torch.empty(
            num_pins * 2, 
            dtype=pos.dtype, 
            device=pos.device,
            memory_format=torch.contiguous_format
        )
        
        # Split position tensor for better cache locality
        node_x = pos[:num_nodes]
        node_y = pos[num_nodes:]
        
        # Vectorized computation
        pin_pos[:num_pins] = node_x[pin2node_map] + pin_offset_x
        pin_pos[num_pins:] = node_y[pin2node_map] + pin_offset_y
        
        return pin_pos

class DDPOptimizer:
    """DDP-specific optimizations"""
    
    @staticmethod
    def optimize_all_reduce(tensor):
        """Optimized all-reduce operation"""
        # Ensure tensor is contiguous
        if not tensor.is_contiguous():
            tensor = tensor.contiguous()
        
        # Use appropriate data type for communication
        original_dtype = tensor.dtype
        if original_dtype == torch.float64:
            # Convert to float32 for communication, then back
            tensor_f32 = tensor.float()
            torch.distributed.all_reduce(tensor_f32, op=torch.distributed.ReduceOp.SUM)
            tensor.copy_(tensor_f32.double())
        else:
            torch.distributed.all_reduce(tensor, op=torch.distributed.ReduceOp.SUM)
        
        return tensor
    
    @staticmethod
    def setup_communication_backend():
        """Setup optimal communication backend settings"""
        if torch.cuda.is_available():
            # Set NCCL environment variables for better performance
            os.environ.setdefault('NCCL_BUFFSIZE', '8388608')  # 8MB
            os.environ.setdefault('NCCL_NTHREADS', '8')
            os.environ.setdefault('NCCL_NSOCKS_PERTHREAD', '4')

def apply_global_optimizations():
    """Apply global performance optimizations"""
    logger.info("Applying global performance optimizations...")
    
    # Memory optimizations
    MemoryOptimizer.clear_cache()
    
    # Compute optimizations
    ComputeOptimizer.optimize_tensor_ops()
    
    # DDP optimizations
    DDPOptimizer.setup_communication_backend()
    
    logger.info("Global optimizations applied successfully")

def benchmark_operation(operation, *args, num_runs=10, warmup_runs=3):
    """Benchmark an operation"""
    # Warmup runs
    for _ in range(warmup_runs):
        operation(*args)
    
    if torch.cuda.is_available():
        torch.cuda.synchronize()
    
    # Actual benchmark
    start_time = time.time()
    for _ in range(num_runs):
        operation(*args)
    
    if torch.cuda.is_available():
        torch.cuda.synchronize()
    
    end_time = time.time()
    avg_time = (end_time - start_time) / num_runs
    
    return avg_time

def memory_efficient_tensor_copy(src, dst):
    """Memory-efficient tensor copying"""
    if src.device != dst.device:
        # Use non-blocking copy for better performance
        dst.copy_(src, non_blocking=True)
    else:
        dst.copy_(src)

def optimize_gradient_computation(model_params):
    """Optimize gradient computation"""
    # Enable gradient checkpointing for large models
    for param in model_params:
        if param.requires_grad and param.grad is not None:
            # Ensure gradients are contiguous
            if not param.grad.is_contiguous():
                param.grad = param.grad.contiguous()

class PerformanceMonitor:
    """Monitor performance metrics during training"""
    
    def __init__(self):
        self.metrics = []
        self.start_time = time.time()
    
    def log_iteration(self, iteration, loss_value, memory_info=None):
        """Log performance metrics for an iteration"""
        current_time = time.time()
        elapsed = current_time - self.start_time
        
        metric = {
            'iteration': iteration,
            'timestamp': current_time,
            'elapsed_time': elapsed,
            'loss': loss_value
        }
        
        if memory_info:
            metric.update(memory_info)
        
        self.metrics.append(metric)
    
    def get_average_iteration_time(self, last_n=10):
        """Get average iteration time for last N iterations"""
        if len(self.metrics) < 2:
            return 0.0
        
        recent_metrics = self.metrics[-last_n:]
        if len(recent_metrics) < 2:
            return 0.0
        
        time_diffs = []
        for i in range(1, len(recent_metrics)):
            time_diff = recent_metrics[i]['elapsed_time'] - recent_metrics[i-1]['elapsed_time']
            time_diffs.append(time_diff)
        
        return sum(time_diffs) / len(time_diffs)
    
    def print_performance_summary(self):
        """Print performance summary"""
        if not self.metrics:
            print("No performance metrics available")
            return
        
        total_time = self.metrics[-1]['elapsed_time']
        total_iterations = len(self.metrics)
        avg_iteration_time = self.get_average_iteration_time()
        
        print(f"Performance Summary:")
        print(f"  Total time: {total_time:.2f}s")
        print(f"  Total iterations: {total_iterations}")
        print(f"  Average iteration time: {avg_iteration_time:.3f}s")
        print(f"  Iterations per second: {1.0/avg_iteration_time:.2f}")
        
        # Memory summary
        if self.metrics[-1].get('gpu_0_utilization'):
            print(f"  GPU memory utilization: {self.metrics[-1]['gpu_0_utilization']:.1f}%")

# Global instances
memory_optimizer = MemoryOptimizer()
performance_profiler = PerformanceProfiler()
compute_optimizer = ComputeOptimizer()
ddp_optimizer = DDPOptimizer()
performance_monitor = PerformanceMonitor()

# Apply optimizations on import
apply_global_optimizations()
