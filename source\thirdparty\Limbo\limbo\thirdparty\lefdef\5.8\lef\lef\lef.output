Terminals unused in grammar

   K_HISTORY
   K_ABUT
   K_COLUMNMAJOR
   K_FIXED
   K_MATCH
   K_NETS
   K_NEW
   K_ORIENTATION
   K_OVERLAPS
   K_PLACED
   K_ROWMAJOR
   K_SPECIALNETS
   K_ENDMACRO
   K_ENDMACROPIN
   K_ENDVIARULE
   K_ENDVIA
   K_ENDLAYER
   K_ENDSITE
   K_ENDEXT
   K_OPENFILE
   K_CLOSEFILE
   K_WARNING
   K_ERROR
   K_FATALERROR
   K_ROWMINSPACING
   K_ROWABUTSPACING
   K_FLIP
   K_ORIENT


Grammar

    0 $accept: lef_file $end

    1 lef_file: rules extension_opt end_library

    2 $@1: %empty

    3 version: K_VERSION $@1 T_STRING ';'

    4 int_number: NUMBER

    5 dividerchar: K_DIVIDERCHAR QSTRING ';'

    6 busbitchars: K_BUSBITCHARS QSTRING ';'

    7 rules: %empty
    8      | rules rule
    9      | error

   10 end_library: %empty
   11            | K_<PERSON><PERSON> K_LIBRARY

   12 rule: version
   13     | busbitchars
   14     | case_sensitivity
   15     | units_section
   16     | layer_rule
   17     | via
   18     | viarule
   19     | viarule_generate
   20     | dividerchar
   21     | wireextension
   22     | msg_statement
   23     | spacing_rule
   24     | dielectric
   25     | minfeature
   26     | irdrop
   27     | site
   28     | macro
   29     | array
   30     | def_statement
   31     | nondefault_rule
   32     | prop_def_section
   33     | universalnoisemargin
   34     | edgeratethreshold1
   35     | edgeratescalefactor
   36     | edgeratethreshold2
   37     | noisetable
   38     | correctiontable
   39     | input_antenna
   40     | output_antenna
   41     | inout_antenna
   42     | antenna_input
   43     | antenna_inout
   44     | antenna_output
   45     | manufacturing
   46     | fixedmask
   47     | useminspacing
   48     | clearancemeasure
   49     | maxstack_via
   50     | create_file_statement

   51 case_sensitivity: K_NAMESCASESENSITIVE K_ON ';'
   52                 | K_NAMESCASESENSITIVE K_OFF ';'

   53 wireextension: K_NOWIREEXTENSIONATPIN K_ON ';'
   54              | K_NOWIREEXTENSIONATPIN K_OFF ';'

   55 fixedmask: K_FIXEDMASK ';'

   56 manufacturing: K_MANUFACTURINGGRID int_number ';'

   57 useminspacing: K_USEMINSPACING spacing_type spacing_value ';'

   58 clearancemeasure: K_CLEARANCEMEASURE clearance_type ';'

   59 clearance_type: K_MAXXY
   60               | K_EUCLIDEAN

   61 spacing_type: K_OBS
   62             | K_PIN

   63 spacing_value: K_ON
   64              | K_OFF

   65 units_section: start_units units_rules K_END K_UNITS

   66 start_units: K_UNITS

   67 units_rules: %empty
   68            | units_rules units_rule

   69 units_rule: K_TIME K_NANOSECONDS int_number ';'
   70           | K_CAPACITANCE K_PICOFARADS int_number ';'
   71           | K_RESISTANCE K_OHMS int_number ';'
   72           | K_POWER K_MILLIWATTS int_number ';'
   73           | K_CURRENT K_MILLIAMPS int_number ';'
   74           | K_VOLTAGE K_VOLTS int_number ';'
   75           | K_DATABASE K_MICRONS int_number ';'
   76           | K_FREQUENCY K_MEGAHERTZ NUMBER ';'

   77 layer_rule: start_layer layer_options end_layer

   78 $@2: %empty

   79 start_layer: K_LAYER $@2 T_STRING

   80 $@3: %empty

   81 end_layer: K_END $@3 T_STRING

   82 layer_options: %empty
   83              | layer_options layer_option

   84 $@4: %empty

   85 $@5: %empty

   86 layer_option: K_ARRAYSPACING $@4 layer_arraySpacing_long layer_arraySpacing_width K_CUTSPACING int_number $@5 layer_arraySpacing_arraycuts ';'
   87             | K_TYPE layer_type ';'
   88             | K_MASK int_number ';'
   89             | K_PITCH int_number ';'
   90             | K_PITCH int_number int_number ';'
   91             | K_DIAGPITCH int_number ';'
   92             | K_DIAGPITCH int_number int_number ';'
   93             | K_OFFSET int_number ';'
   94             | K_OFFSET int_number int_number ';'
   95             | K_DIAGWIDTH int_number ';'
   96             | K_DIAGSPACING int_number ';'
   97             | K_WIDTH int_number ';'
   98             | K_AREA NUMBER ';'

   99 $@6: %empty

  100 layer_option: K_SPACING int_number $@6 layer_spacing_opts layer_spacing_cut_routing ';'

  101 $@7: %empty

  102 layer_option: K_SPACINGTABLE K_ORTHOGONAL K_WITHIN int_number K_SPACING int_number $@7 layer_spacingtable_opts ';'
  103             | K_DIRECTION layer_direction ';'
  104             | K_RESISTANCE K_RPERSQ int_number ';'
  105             | K_RESISTANCE K_RPERSQ K_PWL '(' res_points ')' ';'
  106             | K_CAPACITANCE K_CPERSQDIST int_number ';'
  107             | K_CAPACITANCE K_CPERSQDIST K_PWL '(' cap_points ')' ';'
  108             | K_HEIGHT int_number ';'
  109             | K_WIREEXTENSION int_number ';'
  110             | K_THICKNESS int_number ';'
  111             | K_SHRINKAGE int_number ';'
  112             | K_CAPMULTIPLIER int_number ';'
  113             | K_EDGECAPACITANCE int_number ';'
  114             | K_ANTENNALENGTHFACTOR int_number ';'
  115             | K_CURRENTDEN int_number ';'
  116             | K_CURRENTDEN K_PWL '(' current_density_pwl_list ')' ';'
  117             | K_CURRENTDEN '(' int_number int_number ')' ';'

  118 $@8: %empty

  119 layer_option: K_PROPERTY $@8 layer_prop_list ';'

  120 $@9: %empty

  121 layer_option: K_ACCURRENTDENSITY layer_table_type $@9 layer_frequency
  122             | K_ACCURRENTDENSITY layer_table_type int_number ';'
  123             | K_DCCURRENTDENSITY K_AVERAGE int_number ';'

  124 $@10: %empty

  125 $@11: %empty

  126 layer_option: K_DCCURRENTDENSITY K_AVERAGE K_CUTAREA NUMBER $@10 number_list ';' $@11 dc_layer_table

  127 $@12: %empty

  128 $@13: %empty

  129 layer_option: K_DCCURRENTDENSITY K_AVERAGE K_WIDTH int_number $@12 int_number_list ';' $@13 dc_layer_table
  130             | K_ANTENNAAREARATIO int_number ';'

  131 $@14: %empty

  132 layer_option: K_ANTENNADIFFAREARATIO $@14 layer_antenna_pwl ';'
  133             | K_ANTENNACUMAREARATIO int_number ';'

  134 $@15: %empty

  135 layer_option: K_ANTENNACUMDIFFAREARATIO $@15 layer_antenna_pwl ';'

  136 $@16: %empty

  137 layer_option: K_ANTENNAAREAFACTOR int_number $@16 layer_antenna_duo ';'
  138             | K_ANTENNASIDEAREARATIO int_number ';'

  139 $@17: %empty

  140 layer_option: K_ANTENNADIFFSIDEAREARATIO $@17 layer_antenna_pwl ';'
  141             | K_ANTENNACUMSIDEAREARATIO int_number ';'

  142 $@18: %empty

  143 layer_option: K_ANTENNACUMDIFFSIDEAREARATIO $@18 layer_antenna_pwl ';'

  144 $@19: %empty

  145 layer_option: K_ANTENNASIDEAREAFACTOR int_number $@19 layer_antenna_duo ';'

  146 $@20: %empty

  147 layer_option: K_ANTENNAMODEL $@20 layer_oxide ';'
  148             | K_ANTENNACUMROUTINGPLUSCUT ';'
  149             | K_ANTENNAGATEPLUSDIFF int_number ';'
  150             | K_ANTENNAAREAMINUSDIFF int_number ';'

  151 $@21: %empty

  152 $@22: %empty

  153 layer_option: K_ANTENNAAREADIFFREDUCEPWL '(' pt pt $@21 layer_diffusion_ratios ')' ';' $@22
  154             | K_SLOTWIREWIDTH int_number ';'
  155             | K_SLOTWIRELENGTH int_number ';'
  156             | K_SLOTWIDTH int_number ';'
  157             | K_SLOTLENGTH int_number ';'
  158             | K_MAXADJACENTSLOTSPACING int_number ';'
  159             | K_MAXCOAXIALSLOTSPACING int_number ';'
  160             | K_MAXEDGESLOTSPACING int_number ';'
  161             | K_SPLITWIREWIDTH int_number ';'
  162             | K_MINIMUMDENSITY int_number ';'
  163             | K_MAXIMUMDENSITY int_number ';'
  164             | K_DENSITYCHECKWINDOW int_number int_number ';'
  165             | K_DENSITYCHECKSTEP int_number ';'
  166             | K_FILLACTIVESPACING int_number ';'
  167             | K_MAXWIDTH int_number ';'
  168             | K_MINWIDTH int_number ';'

  169 $@23: %empty

  170 layer_option: K_MINENCLOSEDAREA NUMBER $@23 layer_minen_width ';'

  171 $@24: %empty

  172 layer_option: K_MINIMUMCUT int_number K_WIDTH int_number $@24 layer_minimumcut_within layer_minimumcut_from layer_minimumcut_length ';'

  173 $@25: %empty

  174 layer_option: K_MINSTEP int_number $@25 layer_minstep_options ';'
  175             | K_PROTRUSIONWIDTH int_number K_LENGTH int_number K_WIDTH int_number ';'

  176 $@26: %empty

  177 layer_option: K_SPACINGTABLE $@26 sp_options ';'

  178 $@27: %empty

  179 layer_option: K_ENCLOSURE layer_enclosure_type_opt int_number int_number $@27 layer_enclosure_width_opt ';'

  180 $@28: %empty

  181 layer_option: K_PREFERENCLOSURE layer_enclosure_type_opt int_number int_number $@28 layer_preferenclosure_width_opt ';'
  182             | K_RESISTANCE int_number ';'
  183             | K_DIAGMINEDGELENGTH int_number ';'

  184 $@29: %empty

  185 layer_option: K_MINSIZE $@29 firstPt otherPts ';'

  186 layer_arraySpacing_long: %empty
  187                        | K_LONGARRAY

  188 layer_arraySpacing_width: %empty
  189                         | K_WIDTH int_number

  190 layer_arraySpacing_arraycuts: %empty
  191                             | layer_arraySpacing_arraycut layer_arraySpacing_arraycuts

  192 layer_arraySpacing_arraycut: K_ARRAYCUTS int_number K_SPACING int_number

  193 $@30: %empty

  194 $@31: %empty

  195 $@32: %empty

  196 $@33: %empty

  197 sp_options: K_PARALLELRUNLENGTH int_number $@30 int_number_list $@31 K_WIDTH int_number $@32 int_number_list $@33 layer_sp_parallel_widths

  198 $@34: %empty

  199 $@35: %empty

  200 sp_options: K_TWOWIDTHS K_WIDTH int_number layer_sp_TwoWidthsPRL int_number $@34 int_number_list $@35 layer_sp_TwoWidths

  201 $@36: %empty

  202 sp_options: K_INFLUENCE K_WIDTH int_number K_WITHIN int_number K_SPACING int_number $@36 layer_sp_influence_widths

  203 layer_spacingtable_opts: %empty
  204                        | layer_spacingtable_opt layer_spacingtable_opts

  205 layer_spacingtable_opt: K_WITHIN int_number K_SPACING int_number

  206 layer_enclosure_type_opt: %empty
  207                         | K_ABOVE
  208                         | K_BELOW

  209 layer_enclosure_width_opt: %empty

  210 $@37: %empty

  211 layer_enclosure_width_opt: K_WIDTH int_number $@37 layer_enclosure_width_except_opt
  212                          | K_LENGTH int_number

  213 layer_enclosure_width_except_opt: %empty
  214                                 | K_EXCEPTEXTRACUT int_number

  215 layer_preferenclosure_width_opt: %empty
  216                                | K_WIDTH int_number

  217 layer_minimumcut_within: %empty
  218                        | K_WITHIN int_number

  219 layer_minimumcut_from: %empty
  220                      | K_FROMABOVE
  221                      | K_FROMBELOW

  222 layer_minimumcut_length: %empty
  223                        | K_LENGTH int_number K_WITHIN int_number

  224 layer_minstep_options: %empty
  225                      | layer_minstep_options layer_minstep_option

  226 layer_minstep_option: layer_minstep_type
  227                     | K_LENGTHSUM int_number
  228                     | K_MAXEDGES int_number

  229 layer_minstep_type: K_INSIDECORNER
  230                   | K_OUTSIDECORNER
  231                   | K_STEP

  232 layer_antenna_pwl: int_number

  233 $@38: %empty

  234 layer_antenna_pwl: K_PWL '(' pt pt $@38 layer_diffusion_ratios ')'

  235 layer_diffusion_ratios: %empty
  236                       | layer_diffusion_ratios layer_diffusion_ratio

  237 layer_diffusion_ratio: pt

  238 layer_antenna_duo: %empty
  239                  | K_DIFFUSEONLY

  240 layer_table_type: K_PEAK
  241                 | K_AVERAGE
  242                 | K_RMS

  243 $@39: %empty

  244 $@40: %empty

  245 $@41: %empty

  246 layer_frequency: K_FREQUENCY NUMBER $@39 number_list ';' $@40 ac_layer_table_opt K_TABLEENTRIES NUMBER $@41 number_list ';'

  247 ac_layer_table_opt: %empty

  248 $@42: %empty

  249 ac_layer_table_opt: K_CUTAREA NUMBER $@42 number_list ';'

  250 $@43: %empty

  251 ac_layer_table_opt: K_WIDTH int_number $@43 int_number_list ';'

  252 $@44: %empty

  253 dc_layer_table: K_TABLEENTRIES int_number $@44 int_number_list ';'

  254 int_number_list: %empty
  255                | int_number_list int_number

  256 number_list: %empty
  257            | number_list NUMBER

  258 layer_prop_list: layer_prop
  259                | layer_prop_list layer_prop

  260 layer_prop: T_STRING T_STRING
  261           | T_STRING QSTRING
  262           | T_STRING NUMBER

  263 current_density_pwl_list: current_density_pwl
  264                         | current_density_pwl_list current_density_pwl

  265 current_density_pwl: '(' int_number int_number ')'

  266 cap_points: cap_point
  267           | cap_points cap_point

  268 cap_point: '(' int_number int_number ')'

  269 res_points: res_point
  270           | res_points res_point

  271 res_point: '(' int_number int_number ')'

  272 layer_type: K_ROUTING
  273           | K_CUT
  274           | K_OVERLAP
  275           | K_MASTERSLICE
  276           | K_VIRTUAL
  277           | K_IMPLANT

  278 layer_direction: K_HORIZONTAL
  279                | K_VERTICAL
  280                | K_DIAG45
  281                | K_DIAG135

  282 layer_minen_width: %empty
  283                  | K_WIDTH int_number

  284 layer_oxide: K_OXIDE1
  285            | K_OXIDE2
  286            | K_OXIDE3
  287            | K_OXIDE4

  288 layer_sp_parallel_widths: %empty
  289                         | layer_sp_parallel_widths layer_sp_parallel_width

  290 $@45: %empty

  291 layer_sp_parallel_width: K_WIDTH int_number $@45 int_number_list

  292 layer_sp_TwoWidths: %empty
  293                   | layer_sp_TwoWidth layer_sp_TwoWidths

  294 $@46: %empty

  295 layer_sp_TwoWidth: K_WIDTH int_number layer_sp_TwoWidthsPRL int_number $@46 int_number_list

  296 layer_sp_TwoWidthsPRL: %empty
  297                      | K_PRL int_number

  298 layer_sp_influence_widths: %empty
  299                          | layer_sp_influence_widths layer_sp_influence_width

  300 layer_sp_influence_width: K_WIDTH int_number K_WITHIN int_number K_SPACING int_number

  301 maxstack_via: K_MAXVIASTACK int_number ';'

  302 $@47: %empty

  303 maxstack_via: K_MAXVIASTACK int_number K_RANGE $@47 T_STRING T_STRING ';'

  304 $@48: %empty

  305 via: start_via $@48 via_option end_via

  306 via_keyword: K_VIA

  307 start_via: via_keyword T_STRING
  308          | via_keyword T_STRING K_DEFAULT
  309          | via_keyword T_STRING K_GENERATED

  310 $@49: %empty

  311 $@50: %empty

  312 $@51: %empty

  313 via_viarule: K_VIARULE $@49 T_STRING ';' K_CUTSIZE int_number int_number ';' K_LAYERS $@50 T_STRING T_STRING T_STRING ';' K_CUTSPACING int_number int_number ';' K_ENCLOSURE int_number int_number int_number int_number ';' $@51 via_viarule_options

  314 via_viarule_options: %empty
  315                    | via_viarule_options via_viarule_option

  316 via_viarule_option: K_ROWCOL int_number int_number ';'
  317                   | K_ORIGIN int_number int_number ';'
  318                   | K_OFFSET int_number int_number int_number int_number ';'

  319 $@52: %empty

  320 via_viarule_option: K_PATTERN $@52 T_STRING ';'

  321 via_option: via_viarule
  322           | via_other_options

  323 via_other_options: via_other_option via_more_options

  324 via_more_options: %empty
  325                 | via_more_options via_other_option

  326 via_other_option: via_foreign
  327                 | via_layer_rule
  328                 | K_RESISTANCE int_number ';'

  329 $@53: %empty

  330 via_other_option: K_PROPERTY $@53 via_prop_list ';'
  331                 | K_TOPOFSTACKONLY

  332 via_prop_list: via_name_value_pair
  333              | via_prop_list via_name_value_pair

  334 via_name_value_pair: T_STRING NUMBER
  335                    | T_STRING QSTRING
  336                    | T_STRING T_STRING

  337 via_foreign: start_foreign ';'
  338            | start_foreign pt ';'
  339            | start_foreign pt orientation ';'
  340            | start_foreign orientation ';'

  341 $@54: %empty

  342 start_foreign: K_FOREIGN $@54 T_STRING

  343 orientation: K_N
  344            | K_W
  345            | K_S
  346            | K_E
  347            | K_FN
  348            | K_FW
  349            | K_FS
  350            | K_FE
  351            | K_R0
  352            | K_R90
  353            | K_R180
  354            | K_R270
  355            | K_MY
  356            | K_MYR90
  357            | K_MX
  358            | K_MXR90

  359 via_layer_rule: via_layer via_geometries

  360 $@55: %empty

  361 via_layer: K_LAYER $@55 T_STRING ';'

  362 via_geometries: %empty
  363               | via_geometries via_geometry

  364 via_geometry: K_RECT maskColor pt pt ';'

  365 $@56: %empty

  366 via_geometry: K_POLYGON maskColor $@56 firstPt nextPt nextPt otherPts ';'

  367 $@57: %empty

  368 end_via: K_END $@57 T_STRING

  369 $@58: %empty

  370 viarule_keyword: K_VIARULE $@58 T_STRING

  371 viarule: viarule_keyword viarule_layer_list via_names opt_viarule_props end_viarule

  372 $@59: %empty

  373 viarule_generate: viarule_keyword K_GENERATE viarule_generate_default $@59 viarule_layer_list opt_viarule_props end_viarule

  374 viarule_generate_default: %empty
  375                         | K_DEFAULT

  376 viarule_layer_list: viarule_layer
  377                   | viarule_layer_list viarule_layer

  378 opt_viarule_props: %empty
  379                  | viarule_props

  380 viarule_props: viarule_prop
  381              | viarule_props viarule_prop

  382 $@60: %empty

  383 viarule_prop: K_PROPERTY $@60 viarule_prop_list ';'

  384 viarule_prop_list: viarule_prop
  385                  | viarule_prop_list viarule_prop

  386 viarule_prop: T_STRING T_STRING
  387             | T_STRING QSTRING
  388             | T_STRING NUMBER

  389 viarule_layer: viarule_layer_name viarule_layer_options

  390 via_names: %empty
  391          | via_names via_name

  392 via_name: via_keyword T_STRING ';'

  393 $@61: %empty

  394 viarule_layer_name: K_LAYER $@61 T_STRING ';'

  395 viarule_layer_options: %empty
  396                      | viarule_layer_options viarule_layer_option

  397 viarule_layer_option: K_DIRECTION K_HORIZONTAL ';'
  398                     | K_DIRECTION K_VERTICAL ';'
  399                     | K_ENCLOSURE int_number int_number ';'
  400                     | K_WIDTH int_number K_TO int_number ';'
  401                     | K_RECT pt pt ';'
  402                     | K_SPACING int_number K_BY int_number ';'
  403                     | K_RESISTANCE int_number ';'
  404                     | K_OVERHANG int_number ';'
  405                     | K_METALOVERHANG int_number ';'

  406 $@62: %empty

  407 end_viarule: K_END $@62 T_STRING

  408 spacing_rule: start_spacing spacings end_spacing

  409 start_spacing: K_SPACING

  410 end_spacing: K_END K_SPACING

  411 spacings: %empty
  412         | spacings spacing

  413 spacing: samenet_keyword T_STRING T_STRING int_number ';'
  414        | samenet_keyword T_STRING T_STRING int_number K_STACK ';'

  415 samenet_keyword: K_SAMENET

  416 maskColor: %empty
  417          | K_MASK int_number

  418 irdrop: start_irdrop ir_tables end_irdrop

  419 start_irdrop: K_IRDROP

  420 end_irdrop: K_END K_IRDROP

  421 ir_tables: %empty
  422          | ir_tables ir_table

  423 ir_table: ir_tablename ir_table_values ';'

  424 ir_table_values: %empty
  425                | ir_table_values ir_table_value

  426 ir_table_value: int_number int_number

  427 ir_tablename: K_TABLE T_STRING

  428 minfeature: K_MINFEATURE int_number int_number ';'

  429 dielectric: K_DIELECTRIC int_number ';'

  430 $@63: %empty

  431 $@64: %empty

  432 $@65: %empty

  433 nondefault_rule: K_NONDEFAULTRULE $@63 T_STRING $@64 nd_hardspacing nd_rules $@65 end_nd_rule

  434 end_nd_rule: K_END
  435            | K_END T_STRING

  436 nd_hardspacing: %empty
  437               | K_HARDSPACING ';'

  438 nd_rules: %empty
  439         | nd_rules nd_rule

  440 nd_rule: nd_layer
  441        | via
  442        | spacing_rule
  443        | nd_prop
  444        | usevia
  445        | useviarule
  446        | mincuts

  447 usevia: K_USEVIA T_STRING ';'

  448 useviarule: K_USEVIARULE T_STRING ';'

  449 mincuts: K_MINCUTS T_STRING int_number ';'

  450 $@66: %empty

  451 nd_prop: K_PROPERTY $@66 nd_prop_list ';'

  452 nd_prop_list: nd_prop
  453             | nd_prop_list nd_prop

  454 nd_prop: T_STRING T_STRING
  455        | T_STRING QSTRING
  456        | T_STRING NUMBER

  457 $@67: %empty

  458 $@68: %empty

  459 $@69: %empty

  460 $@70: %empty

  461 nd_layer: K_LAYER $@67 T_STRING $@68 K_WIDTH int_number ';' $@69 nd_layer_stmts K_END $@70 T_STRING

  462 nd_layer_stmts: %empty
  463               | nd_layer_stmts nd_layer_stmt

  464 nd_layer_stmt: K_SPACING int_number ';'
  465              | K_WIREEXTENSION int_number ';'
  466              | K_RESISTANCE K_RPERSQ int_number ';'
  467              | K_CAPACITANCE K_CPERSQDIST int_number ';'
  468              | K_EDGECAPACITANCE int_number ';'
  469              | K_DIAGWIDTH int_number ';'

  470 site: start_site site_options end_site

  471 $@71: %empty

  472 start_site: K_SITE $@71 T_STRING

  473 $@72: %empty

  474 end_site: K_END $@72 T_STRING

  475 site_options: %empty
  476             | site_options site_option

  477 site_option: K_SIZE int_number K_BY int_number ';'
  478            | site_symmetry_statement
  479            | site_class
  480            | site_rowpattern_statement

  481 site_class: K_CLASS K_PAD ';'
  482           | K_CLASS K_CORE ';'
  483           | K_CLASS K_VIRTUAL ';'

  484 site_symmetry_statement: K_SYMMETRY site_symmetries ';'

  485 site_symmetries: %empty
  486                | site_symmetries site_symmetry

  487 site_symmetry: K_X
  488              | K_Y
  489              | K_R90

  490 $@73: %empty

  491 site_rowpattern_statement: K_ROWPATTERN $@73 site_rowpatterns ';'

  492 site_rowpatterns: %empty
  493                 | site_rowpatterns site_rowpattern

  494 $@74: %empty

  495 site_rowpattern: T_STRING orientation $@74

  496 pt: int_number int_number
  497   | '(' int_number int_number ')'

  498 $@75: %empty

  499 macro: start_macro macro_options $@75 end_macro

  500 $@76: %empty

  501 start_macro: K_MACRO $@76 T_STRING

  502 $@77: %empty

  503 end_macro: K_END $@77 T_STRING

  504 macro_options: %empty
  505              | macro_options macro_option

  506 macro_option: macro_class
  507             | macro_generator
  508             | macro_generate
  509             | macro_source
  510             | macro_symmetry_statement
  511             | macro_fixedMask
  512             | macro_origin
  513             | macro_power
  514             | macro_foreign
  515             | macro_eeq
  516             | macro_leq
  517             | macro_size
  518             | macro_site
  519             | macro_pin
  520             | K_FUNCTION K_BUFFER ';'
  521             | K_FUNCTION K_INVERTER ';'
  522             | macro_obs
  523             | macro_density
  524             | macro_clocktype
  525             | timing

  526 $@78: %empty

  527 macro_option: K_PROPERTY $@78 macro_prop_list ';'

  528 macro_prop_list: macro_name_value_pair
  529                | macro_prop_list macro_name_value_pair

  530 macro_symmetry_statement: K_SYMMETRY macro_symmetries ';'

  531 macro_symmetries: %empty
  532                 | macro_symmetries macro_symmetry

  533 macro_symmetry: K_X
  534               | K_Y
  535               | K_R90

  536 macro_name_value_pair: T_STRING NUMBER
  537                      | T_STRING QSTRING
  538                      | T_STRING T_STRING

  539 macro_class: K_CLASS class_type ';'

  540 class_type: K_COVER
  541           | K_COVER K_BUMP
  542           | K_RING
  543           | K_BLOCK
  544           | K_BLOCK K_BLACKBOX
  545           | K_BLOCK K_SOFT
  546           | K_NONE
  547           | K_BUMP
  548           | K_PAD
  549           | K_VIRTUAL
  550           | K_PAD pad_type
  551           | K_CORE
  552           | K_CORNER
  553           | K_CORE core_type
  554           | K_ENDCAP endcap_type

  555 pad_type: K_INPUT
  556         | K_OUTPUT
  557         | K_INOUT
  558         | K_POWER
  559         | K_SPACER
  560         | K_AREAIO

  561 core_type: K_FEEDTHRU
  562          | K_TIEHIGH
  563          | K_TIELOW
  564          | K_SPACER
  565          | K_ANTENNACELL
  566          | K_WELLTAP

  567 endcap_type: K_PRE
  568            | K_POST
  569            | K_TOPLEFT
  570            | K_TOPRIGHT
  571            | K_BOTTOMLEFT
  572            | K_BOTTOMRIGHT

  573 macro_generator: K_GENERATOR T_STRING ';'

  574 macro_generate: K_GENERATE T_STRING T_STRING ';'

  575 macro_source: K_SOURCE K_USER ';'
  576             | K_SOURCE K_GENERATE ';'
  577             | K_SOURCE K_BLOCK ';'

  578 macro_power: K_POWER int_number ';'

  579 macro_origin: K_ORIGIN pt ';'

  580 macro_foreign: start_foreign ';'
  581              | start_foreign pt ';'
  582              | start_foreign pt orientation ';'
  583              | start_foreign orientation ';'

  584 macro_fixedMask: K_FIXEDMASK ';'

  585 $@79: %empty

  586 macro_eeq: K_EEQ $@79 T_STRING ';'

  587 $@80: %empty

  588 macro_leq: K_LEQ $@80 T_STRING ';'

  589 macro_site: macro_site_word T_STRING ';'
  590           | macro_site_word sitePattern ';'

  591 macro_site_word: K_SITE

  592 site_word: K_SITE

  593 macro_size: K_SIZE int_number K_BY int_number ';'

  594 macro_pin: start_macro_pin macro_pin_options end_macro_pin

  595 $@81: %empty

  596 start_macro_pin: K_PIN $@81 T_STRING

  597 $@82: %empty

  598 end_macro_pin: K_END $@82 T_STRING

  599 macro_pin_options: %empty
  600                  | macro_pin_options macro_pin_option

  601 macro_pin_option: start_foreign ';'
  602                 | start_foreign pt ';'
  603                 | start_foreign pt orientation ';'
  604                 | start_foreign K_STRUCTURE ';'
  605                 | start_foreign K_STRUCTURE pt ';'
  606                 | start_foreign K_STRUCTURE pt orientation ';'

  607 $@83: %empty

  608 macro_pin_option: K_LEQ $@83 T_STRING ';'
  609                 | K_POWER int_number ';'
  610                 | electrical_direction
  611                 | K_USE macro_pin_use ';'
  612                 | K_SCANUSE macro_scan_use ';'
  613                 | K_LEAKAGE int_number ';'
  614                 | K_RISETHRESH int_number ';'
  615                 | K_FALLTHRESH int_number ';'
  616                 | K_RISESATCUR int_number ';'
  617                 | K_FALLSATCUR int_number ';'
  618                 | K_VLO int_number ';'
  619                 | K_VHI int_number ';'
  620                 | K_TIEOFFR int_number ';'
  621                 | K_SHAPE pin_shape ';'

  622 $@84: %empty

  623 macro_pin_option: K_MUSTJOIN $@84 T_STRING ';'

  624 $@85: %empty

  625 macro_pin_option: K_OUTPUTNOISEMARGIN $@85 int_number int_number ';'

  626 $@86: %empty

  627 macro_pin_option: K_OUTPUTRESISTANCE $@86 int_number int_number ';'

  628 $@87: %empty

  629 macro_pin_option: K_INPUTNOISEMARGIN $@87 int_number int_number ';'
  630                 | K_CAPACITANCE int_number ';'
  631                 | K_MAXDELAY int_number ';'
  632                 | K_MAXLOAD int_number ';'
  633                 | K_RESISTANCE int_number ';'
  634                 | K_PULLDOWNRES int_number ';'
  635                 | K_CURRENTSOURCE K_ACTIVE ';'
  636                 | K_CURRENTSOURCE K_RESISTIVE ';'
  637                 | K_RISEVOLTAGETHRESHOLD int_number ';'
  638                 | K_FALLVOLTAGETHRESHOLD int_number ';'
  639                 | K_IV_TABLES T_STRING T_STRING ';'
  640                 | K_TAPERRULE T_STRING ';'

  641 $@88: %empty

  642 macro_pin_option: K_PROPERTY $@88 pin_prop_list ';'
  643                 | start_macro_port macro_port_class_option geometries K_END
  644                 | start_macro_port K_END
  645                 | K_ANTENNASIZE int_number opt_layer_name ';'
  646                 | K_ANTENNAMETALAREA NUMBER opt_layer_name ';'
  647                 | K_ANTENNAMETALLENGTH int_number opt_layer_name ';'
  648                 | K_RISESLEWLIMIT int_number ';'
  649                 | K_FALLSLEWLIMIT int_number ';'
  650                 | K_ANTENNAPARTIALMETALAREA NUMBER opt_layer_name ';'
  651                 | K_ANTENNAPARTIALMETALSIDEAREA NUMBER opt_layer_name ';'
  652                 | K_ANTENNAPARTIALCUTAREA NUMBER opt_layer_name ';'
  653                 | K_ANTENNADIFFAREA NUMBER opt_layer_name ';'
  654                 | K_ANTENNAGATEAREA NUMBER opt_layer_name ';'
  655                 | K_ANTENNAMAXAREACAR NUMBER req_layer_name ';'
  656                 | K_ANTENNAMAXSIDEAREACAR NUMBER req_layer_name ';'
  657                 | K_ANTENNAMAXCUTCAR NUMBER req_layer_name ';'

  658 $@89: %empty

  659 macro_pin_option: K_ANTENNAMODEL $@89 pin_layer_oxide ';'

  660 $@90: %empty

  661 macro_pin_option: K_NETEXPR $@90 QSTRING ';'

  662 $@91: %empty

  663 macro_pin_option: K_SUPPLYSENSITIVITY $@91 T_STRING ';'

  664 $@92: %empty

  665 macro_pin_option: K_GROUNDSENSITIVITY $@92 T_STRING ';'

  666 pin_layer_oxide: K_OXIDE1
  667                | K_OXIDE2
  668                | K_OXIDE3
  669                | K_OXIDE4

  670 pin_prop_list: pin_name_value_pair
  671              | pin_prop_list pin_name_value_pair

  672 pin_name_value_pair: T_STRING NUMBER
  673                    | T_STRING QSTRING
  674                    | T_STRING T_STRING

  675 electrical_direction: K_DIRECTION K_INPUT ';'
  676                     | K_DIRECTION K_OUTPUT ';'
  677                     | K_DIRECTION K_OUTPUT K_TRISTATE ';'
  678                     | K_DIRECTION K_INOUT ';'
  679                     | K_DIRECTION K_FEEDTHRU ';'

  680 start_macro_port: K_PORT

  681 macro_port_class_option: %empty
  682                        | K_CLASS class_type ';'

  683 macro_pin_use: K_SIGNAL
  684              | K_ANALOG
  685              | K_POWER
  686              | K_GROUND
  687              | K_CLOCK
  688              | K_DATA

  689 macro_scan_use: K_INPUT
  690               | K_OUTPUT
  691               | K_START
  692               | K_STOP

  693 pin_shape: %empty
  694          | K_ABUTMENT
  695          | K_RING
  696          | K_FEEDTHRU

  697 geometries: geometry geometry_options

  698 $@93: %empty

  699 $@94: %empty

  700 geometry: K_LAYER $@93 T_STRING $@94 layer_exceptpgnet layer_spacing ';'
  701         | K_WIDTH int_number ';'
  702         | K_PATH maskColor firstPt otherPts ';'
  703         | K_PATH maskColor K_ITERATE firstPt otherPts stepPattern ';'
  704         | K_RECT maskColor pt pt ';'
  705         | K_RECT maskColor K_ITERATE pt pt stepPattern ';'
  706         | K_POLYGON maskColor firstPt nextPt nextPt otherPts ';'
  707         | K_POLYGON maskColor K_ITERATE firstPt nextPt nextPt otherPts stepPattern ';'
  708         | via_placement

  709 geometry_options: %empty
  710                 | geometry_options geometry

  711 layer_exceptpgnet: %empty
  712                  | K_EXCEPTPGNET

  713 layer_spacing: %empty
  714              | K_SPACING int_number
  715              | K_DESIGNRULEWIDTH int_number

  716 firstPt: pt

  717 nextPt: pt

  718 otherPts: %empty
  719         | otherPts nextPt

  720 $@95: %empty

  721 via_placement: K_VIA maskColor pt $@95 T_STRING ';'

  722 $@96: %empty

  723 via_placement: K_VIA K_ITERATE maskColor pt $@96 T_STRING stepPattern ';'

  724 stepPattern: K_DO int_number K_BY int_number K_STEP int_number int_number

  725 sitePattern: T_STRING int_number int_number orientation K_DO int_number K_BY int_number K_STEP int_number int_number
  726            | T_STRING int_number int_number orientation

  727 $@97: %empty

  728 $@98: %empty

  729 trackPattern: K_X int_number K_DO int_number K_STEP int_number $@97 K_LAYER $@98 trackLayers

  730 $@99: %empty

  731 $@100: %empty

  732 trackPattern: K_Y int_number K_DO int_number K_STEP int_number $@99 K_LAYER $@100 trackLayers
  733             | K_X int_number K_DO int_number K_STEP int_number
  734             | K_Y int_number K_DO int_number K_STEP int_number

  735 trackLayers: %empty
  736            | trackLayers layer_name

  737 layer_name: T_STRING

  738 gcellPattern: K_X int_number K_DO int_number K_STEP int_number
  739             | K_Y int_number K_DO int_number K_STEP int_number

  740 macro_obs: start_macro_obs geometries K_END
  741          | start_macro_obs K_END

  742 start_macro_obs: K_OBS

  743 macro_density: K_DENSITY density_layer density_layers K_END

  744 density_layers: %empty
  745               | density_layers density_layer

  746 $@101: %empty

  747 $@102: %empty

  748 density_layer: K_LAYER $@101 T_STRING ';' $@102 density_layer_rect density_layer_rects

  749 density_layer_rects: %empty
  750                    | density_layer_rects density_layer_rect

  751 density_layer_rect: K_RECT pt pt int_number ';'

  752 $@103: %empty

  753 macro_clocktype: K_CLOCKTYPE $@103 T_STRING ';'

  754 timing: start_timing timing_options end_timing

  755 start_timing: K_TIMING

  756 end_timing: K_END K_TIMING

  757 timing_options: %empty
  758               | timing_options timing_option

  759 $@104: %empty

  760 timing_option: K_FROMPIN $@104 list_of_from_strings ';'

  761 $@105: %empty

  762 timing_option: K_TOPIN $@105 list_of_to_strings ';'

  763 $@106: %empty

  764 timing_option: risefall K_INTRINSIC int_number int_number $@106 slew_spec K_VARIABLE int_number int_number ';'
  765              | risefall delay_or_transition K_UNATENESS unateness K_TABLEDIMENSION int_number int_number int_number ';'
  766              | K_TABLEAXIS list_of_table_axis_dnumbers ';'
  767              | K_TABLEENTRIES list_of_table_entries ';'
  768              | K_RISERS int_number int_number ';'
  769              | K_FALLRS int_number int_number ';'
  770              | K_RISECS int_number int_number ';'
  771              | K_FALLCS int_number int_number ';'
  772              | K_RISESATT1 int_number int_number ';'
  773              | K_FALLSATT1 int_number int_number ';'
  774              | K_RISET0 int_number int_number ';'
  775              | K_FALLT0 int_number int_number ';'
  776              | K_UNATENESS unateness ';'
  777              | K_STABLE K_SETUP int_number K_HOLD int_number risefall ';'
  778              | two_pin_trigger from_pin_trigger to_pin_trigger K_TABLEDIMENSION int_number int_number int_number ';'
  779              | one_pin_trigger K_TABLEDIMENSION int_number int_number int_number ';'
  780              | K_SDFCONDSTART QSTRING ';'
  781              | K_SDFCONDEND QSTRING ';'
  782              | K_SDFCOND QSTRING ';'
  783              | K_EXTENSION ';'

  784 one_pin_trigger: K_MPWH
  785                | K_MPWL
  786                | K_PERIOD

  787 two_pin_trigger: K_SETUP
  788                | K_HOLD
  789                | K_RECOVERY
  790                | K_SKEW

  791 from_pin_trigger: K_ANYEDGE
  792                 | K_POSEDGE
  793                 | K_NEGEDGE

  794 to_pin_trigger: K_ANYEDGE
  795               | K_POSEDGE
  796               | K_NEGEDGE

  797 delay_or_transition: K_DELAY
  798                    | K_TRANSITIONTIME

  799 list_of_table_entries: table_entry
  800                      | list_of_table_entries table_entry

  801 table_entry: '(' int_number int_number int_number ')'

  802 list_of_table_axis_dnumbers: int_number
  803                            | list_of_table_axis_dnumbers int_number

  804 slew_spec: %empty
  805          | int_number int_number int_number int_number
  806          | int_number int_number int_number int_number int_number int_number int_number

  807 risefall: K_RISE
  808         | K_FALL

  809 unateness: K_INVERT
  810          | K_NONINVERT
  811          | K_NONUNATE

  812 list_of_from_strings: T_STRING
  813                     | list_of_from_strings T_STRING

  814 list_of_to_strings: T_STRING
  815                   | list_of_to_strings T_STRING

  816 $@107: %empty

  817 array: start_array array_rules $@107 end_array

  818 $@108: %empty

  819 start_array: K_ARRAY $@108 T_STRING

  820 $@109: %empty

  821 end_array: K_END $@109 T_STRING

  822 array_rules: %empty
  823            | array_rules array_rule

  824 $@110: %empty

  825 array_rule: site_word $@110 sitePattern ';'

  826 $@111: %empty

  827 array_rule: K_CANPLACE $@111 sitePattern ';'

  828 $@112: %empty

  829 array_rule: K_CANNOTOCCUPY $@112 sitePattern ';'

  830 $@113: %empty

  831 array_rule: K_TRACKS $@113 trackPattern ';'
  832           | floorplan_start floorplan_list K_END T_STRING

  833 $@114: %empty

  834 array_rule: K_GCELLGRID $@114 gcellPattern ';'
  835           | K_DEFAULTCAP int_number cap_list K_END K_DEFAULTCAP
  836           | def_statement

  837 floorplan_start: K_FLOORPLAN T_STRING

  838 floorplan_list: %empty
  839               | floorplan_list floorplan_element

  840 $@115: %empty

  841 floorplan_element: K_CANPLACE $@115 sitePattern ';'

  842 $@116: %empty

  843 floorplan_element: K_CANNOTOCCUPY $@116 sitePattern ';'

  844 cap_list: %empty
  845         | cap_list one_cap

  846 one_cap: K_MINPINS int_number K_WIRECAP int_number ';'

  847 $@117: %empty

  848 msg_statement: K_MESSAGE $@117 T_STRING '=' s_expr dtrm

  849 $@118: %empty

  850 create_file_statement: K_CREATEFILE $@118 T_STRING '=' s_expr dtrm

  851 $@119: %empty

  852 def_statement: K_DEFINE $@119 T_STRING '=' expression dtrm

  853 $@120: %empty

  854 def_statement: K_DEFINES $@120 T_STRING '=' s_expr dtrm

  855 $@121: %empty

  856 def_statement: K_DEFINEB $@121 T_STRING '=' b_expr dtrm

  857 dtrm: %empty
  858     | ';'
  859     | '\n'

  860 then: K_THEN
  861     | '\n' K_THEN

  862 else: K_ELSE
  863     | '\n' K_ELSE

  864 expression: expression '+' expression
  865           | expression '-' expression
  866           | expression '*' expression
  867           | expression '/' expression
  868           | '-' expression
  869           | '(' expression ')'
  870           | K_IF b_expr then expression else expression
  871           | int_number

  872 b_expr: expression relop expression
  873       | expression K_AND expression
  874       | expression K_OR expression
  875       | s_expr relop s_expr
  876       | s_expr K_AND s_expr
  877       | s_expr K_OR s_expr
  878       | b_expr K_EQ b_expr
  879       | b_expr K_NE b_expr
  880       | b_expr K_AND b_expr
  881       | b_expr K_OR b_expr
  882       | K_NOT b_expr
  883       | '(' b_expr ')'
  884       | K_IF b_expr then b_expr else b_expr
  885       | K_TRUE
  886       | K_FALSE

  887 s_expr: s_expr '+' s_expr
  888       | '(' s_expr ')'
  889       | K_IF b_expr then s_expr else s_expr
  890       | QSTRING

  891 relop: K_LE
  892      | K_LT
  893      | K_GE
  894      | K_GT
  895      | K_EQ
  896      | K_NE
  897      | '='
  898      | '<'
  899      | '>'

  900 $@122: %empty

  901 prop_def_section: K_PROPDEF $@122 prop_stmts K_END K_PROPDEF

  902 prop_stmts: %empty
  903           | prop_stmts prop_stmt

  904 $@123: %empty

  905 prop_stmt: K_LIBRARY $@123 T_STRING prop_define ';'

  906 $@124: %empty

  907 prop_stmt: K_COMPONENTPIN $@124 T_STRING prop_define ';'

  908 $@125: %empty

  909 prop_stmt: K_PIN $@125 T_STRING prop_define ';'

  910 $@126: %empty

  911 prop_stmt: K_MACRO $@126 T_STRING prop_define ';'

  912 $@127: %empty

  913 prop_stmt: K_VIA $@127 T_STRING prop_define ';'

  914 $@128: %empty

  915 prop_stmt: K_VIARULE $@128 T_STRING prop_define ';'

  916 $@129: %empty

  917 prop_stmt: K_LAYER $@129 T_STRING prop_define ';'

  918 $@130: %empty

  919 prop_stmt: K_NONDEFAULTRULE $@130 T_STRING prop_define ';'

  920 prop_define: K_INTEGER opt_def_range opt_def_dvalue
  921            | K_REAL opt_def_range opt_def_value
  922            | K_STRING
  923            | K_STRING QSTRING
  924            | K_NAMEMAPSTRING T_STRING

  925 opt_range_second: %empty
  926                 | K_USELENGTHTHRESHOLD
  927                 | K_INFLUENCE int_number
  928                 | K_INFLUENCE int_number K_RANGE int_number int_number
  929                 | K_RANGE int_number int_number

  930 opt_endofline: %empty

  931 $@131: %empty

  932 opt_endofline: K_PARALLELEDGE int_number K_WITHIN int_number $@131 opt_endofline_twoedges

  933 opt_endofline_twoedges: %empty
  934                       | K_TWOEDGES

  935 opt_samenetPGonly: %empty
  936                  | K_PGONLY

  937 opt_def_range: %empty
  938              | K_RANGE int_number int_number

  939 opt_def_value: %empty
  940              | NUMBER

  941 opt_def_dvalue: %empty
  942               | int_number

  943 layer_spacing_opts: %empty
  944                   | layer_spacing_opt layer_spacing_opts

  945 layer_spacing_opt: K_CENTERTOCENTER

  946 $@132: %empty

  947 layer_spacing_opt: K_SAMENET $@132 opt_samenetPGonly
  948                  | K_PARALLELOVERLAP

  949 layer_spacing_cut_routing: %empty

  950 $@133: %empty

  951 $@134: %empty

  952 layer_spacing_cut_routing: K_LAYER $@133 T_STRING $@134 spacing_cut_layer_opt

  953 $@135: %empty

  954 layer_spacing_cut_routing: K_ADJACENTCUTS int_number K_WITHIN int_number $@135 opt_adjacentcuts_exceptsame
  955                          | K_AREA NUMBER

  956 $@136: %empty

  957 layer_spacing_cut_routing: K_RANGE int_number int_number $@136 opt_range_second
  958                          | K_LENGTHTHRESHOLD int_number
  959                          | K_LENGTHTHRESHOLD int_number K_RANGE int_number int_number

  960 $@137: %empty

  961 layer_spacing_cut_routing: K_ENDOFLINE int_number K_WITHIN int_number $@137 opt_endofline
  962                          | K_NOTCHLENGTH int_number
  963                          | K_ENDOFNOTCHWIDTH int_number K_NOTCHSPACING int_number K_NOTCHLENGTH int_number

  964 spacing_cut_layer_opt: %empty
  965                      | K_STACK

  966 opt_adjacentcuts_exceptsame: %empty
  967                            | K_EXCEPTSAMEPGNET

  968 opt_layer_name: %empty

  969 $@138: %empty

  970 opt_layer_name: K_LAYER $@138 T_STRING

  971 $@139: %empty

  972 req_layer_name: K_LAYER $@139 T_STRING

  973 universalnoisemargin: K_UNIVERSALNOISEMARGIN int_number int_number ';'

  974 edgeratethreshold1: K_EDGERATETHRESHOLD1 int_number ';'

  975 edgeratethreshold2: K_EDGERATETHRESHOLD2 int_number ';'

  976 edgeratescalefactor: K_EDGERATESCALEFACTOR int_number ';'

  977 $@140: %empty

  978 noisetable: K_NOISETABLE int_number $@140 ';' noise_table_list end_noisetable dtrm

  979 end_noisetable: K_END K_NOISETABLE

  980 noise_table_list: noise_table_entry
  981                 | noise_table_list noise_table_entry

  982 noise_table_entry: K_EDGERATE int_number ';'
  983                  | output_resistance_entry

  984 $@141: %empty

  985 output_resistance_entry: K_OUTPUTRESISTANCE $@141 num_list ';' victim_list

  986 num_list: int_number
  987         | num_list int_number

  988 victim_list: victim
  989            | victim_list victim

  990 $@142: %empty

  991 victim: K_VICTIMLENGTH int_number ';' $@142 K_VICTIMNOISE vnoiselist ';'

  992 vnoiselist: int_number
  993           | vnoiselist int_number

  994 $@143: %empty

  995 correctiontable: K_CORRECTIONTABLE int_number ';' $@143 correction_table_list end_correctiontable dtrm

  996 end_correctiontable: K_END K_CORRECTIONTABLE

  997 correction_table_list: correction_table_item
  998                      | correction_table_list correction_table_item

  999 correction_table_item: K_EDGERATE int_number ';'
  1000                      | output_list

  1001 $@144: %empty

  1002 output_list: K_OUTPUTRESISTANCE $@144 numo_list ';' corr_victim_list

  1003 numo_list: int_number
  1004          | numo_list int_number

  1005 corr_victim_list: corr_victim
  1006                 | corr_victim_list corr_victim

  1007 $@145: %empty

  1008 corr_victim: K_VICTIMLENGTH int_number ';' $@145 K_CORRECTIONFACTOR corr_list ';'

  1009 corr_list: int_number
  1010          | corr_list int_number

  1011 input_antenna: K_INPUTPINANTENNASIZE int_number ';'

  1012 output_antenna: K_OUTPUTPINANTENNASIZE int_number ';'

  1013 inout_antenna: K_INOUTPINANTENNASIZE int_number ';'

  1014 antenna_input: K_ANTENNAINPUTGATEAREA NUMBER ';'

  1015 antenna_inout: K_ANTENNAINOUTDIFFAREA NUMBER ';'

  1016 antenna_output: K_ANTENNAOUTPUTDIFFAREA NUMBER ';'

  1017 extension_opt: %empty
  1018              | extension

  1019 extension: K_BEGINEXT


Terminals, with rules where they appear

$end (0) 0
'\n' (10) 859 861 863
'(' (40) 105 107 116 117 153 234 265 268 271 497 801 869 883 888
')' (41) 105 107 116 117 153 234 265 268 271 497 801 869 883 888
'*' (42) 866
'+' (43) 864 887
'-' (45) 865 868
'/' (47) 867
';' (59) 3 5 6 51 52 53 54 55 56 57 58 69 70 71 72 73 74 75 76 86 87
    88 89 90 91 92 93 94 95 96 97 98 100 102 103 104 105 106 107 108
    109 110 111 112 113 114 115 116 117 119 122 123 126 129 130 132
    133 135 137 138 140 141 143 145 147 148 149 150 153 154 155 156
    157 158 159 160 161 162 163 164 165 166 167 168 170 172 174 175
    177 179 181 182 183 185 246 249 251 253 301 303 313 316 317 318
    320 328 330 337 338 339 340 361 364 366 383 392 394 397 398 399
    400 401 402 403 404 405 413 414 423 428 429 437 447 448 449 451
    461 464 465 466 467 468 469 477 481 482 483 484 491 520 521 527
    530 539 573 574 575 576 577 578 579 580 581 582 583 584 586 588
    589 590 593 601 602 603 604 605 606 608 609 611 612 613 614 615
    616 617 618 619 620 621 623 625 627 629 630 631 632 633 634 635
    636 637 638 639 640 642 645 646 647 648 649 650 651 652 653 654
    655 656 657 659 661 663 665 675 676 677 678 679 682 700 701 702
    703 704 705 706 707 721 723 748 751 753 760 762 764 765 766 767
    768 769 770 771 772 773 774 775 776 777 778 779 780 781 782 783
    825 827 829 831 834 841 843 846 858 905 907 909 911 913 915 917
    919 973 974 975 976 978 982 985 991 995 999 1002 1008 1011 1012
    1013 1014 1015 1016
'<' (60) 898
'=' (61) 848 850 852 854 856 897
'>' (62) 899
error (256) 9
K_HISTORY (258)
K_ABUT (259)
K_ABUTMENT (260) 694
K_ACTIVE (261) 635
K_ANALOG (262) 684
K_ARRAY (263) 819
K_AREA (264) 98 955
K_BLOCK (265) 543 544 545 577
K_BOTTOMLEFT (266) 571
K_BOTTOMRIGHT (267) 572
K_BY (268) 402 477 593 724 725
K_CAPACITANCE (269) 70 106 107 467 630
K_CAPMULTIPLIER (270) 112
K_CLASS (271) 481 482 483 539 682
K_CLOCK (272) 687
K_CLOCKTYPE (273) 753
K_COLUMNMAJOR (274)
K_DESIGNRULEWIDTH (275) 715
K_INFLUENCE (276) 202 927 928
K_CORE (277) 482 551 553
K_CORNER (278) 552
K_COVER (279) 540 541
K_CPERSQDIST (280) 106 107 467
K_CURRENT (281) 73
K_CURRENTSOURCE (282) 635 636
K_CUT (283) 273
K_DEFAULT (284) 308 375
K_DATABASE (285) 75
K_DATA (286) 688
K_DIELECTRIC (287) 429
K_DIRECTION (288) 103 397 398 675 676 677 678 679
K_DO (289) 724 725 729 732 733 734 738 739
K_EDGECAPACITANCE (290) 113 468
K_EEQ (291) 586
K_END (292) 11 65 81 368 407 410 420 434 435 461 474 503 598 643 644
    740 741 743 756 821 832 835 901 979 996
K_ENDCAP (293) 554
K_FALL (294) 808
K_FALLCS (295) 771
K_FALLT0 (296) 775
K_FALLSATT1 (297) 773
K_FALLRS (298) 769
K_FALLSATCUR (299) 617
K_FALLTHRESH (300) 615
K_FEEDTHRU (301) 561 679 696
K_FIXED (302)
K_FOREIGN (303) 342
K_FROMPIN (304) 760
K_GENERATE (305) 373 574 576
K_GENERATOR (306) 573
K_GROUND (307) 686
K_HEIGHT (308) 108
K_HORIZONTAL (309) 278 397
K_INOUT (310) 557 678
K_INPUT (311) 555 675 689
K_INPUTNOISEMARGIN (312) 629
K_COMPONENTPIN (313) 907
K_INTRINSIC (314) 764
K_INVERT (315) 809
K_IRDROP (316) 419 420
K_ITERATE (317) 703 705 707 723
K_IV_TABLES (318) 639
K_LAYER (319) 79 361 394 461 700 729 732 748 917 952 970 972
K_LEAKAGE (320) 613
K_LEQ (321) 588 608
K_LIBRARY (322) 11 905
K_MACRO (323) 501 911
K_MATCH (324)
K_MAXDELAY (325) 631
K_MAXLOAD (326) 632
K_METALOVERHANG (327) 405
K_MILLIAMPS (328) 73
K_MILLIWATTS (329) 72
K_MINFEATURE (330) 428
K_MUSTJOIN (331) 623
K_NAMESCASESENSITIVE (332) 51 52
K_NANOSECONDS (333) 69
K_NETS (334)
K_NEW (335)
K_NONDEFAULTRULE (336) 433 919
K_NONINVERT (337) 810
K_NONUNATE (338) 811
K_OBS (339) 61 742
K_OHMS (340) 71
K_OFFSET (341) 93 94 318
K_ORIENTATION (342)
K_ORIGIN (343) 317 579
K_OUTPUT (344) 556 676 677 690
K_OUTPUTNOISEMARGIN (345) 625
K_OVERHANG (346) 404
K_OVERLAP (347) 274
K_OFF (348) 52 54 64
K_ON (349) 51 53 63
K_OVERLAPS (350)
K_PAD (351) 481 548 550
K_PATH (352) 702 703
K_PATTERN (353) 320
K_PICOFARADS (354) 70
K_PIN (355) 62 596 909
K_PITCH (356) 89 90
K_PLACED (357)
K_POLYGON (358) 366 706 707
K_PORT (359) 680
K_POST (360) 568
K_POWER (361) 72 558 578 609 685
K_PRE (362) 567
K_PULLDOWNRES (363) 634
K_RECT (364) 364 401 704 705 751
K_RESISTANCE (365) 71 104 105 182 328 403 466 633
K_RESISTIVE (366) 636
K_RING (367) 542 695
K_RISE (368) 807
K_RISECS (369) 770
K_RISERS (370) 768
K_RISESATCUR (371) 616
K_RISETHRESH (372) 614
K_RISESATT1 (373) 772
K_RISET0 (374) 774
K_RISEVOLTAGETHRESHOLD (375) 637
K_FALLVOLTAGETHRESHOLD (376) 638
K_ROUTING (377) 272
K_ROWMAJOR (378)
K_RPERSQ (379) 104 105 466
K_SAMENET (380) 415 947
K_SCANUSE (381) 612
K_SHAPE (382) 621
K_SHRINKAGE (383) 111
K_SIGNAL (384) 683
K_SITE (385) 472 591 592
K_SIZE (386) 477 593
K_SOURCE (387) 575 576 577
K_SPACER (388) 559 564
K_SPACING (389) 100 102 192 202 205 300 402 409 410 464 714
K_SPECIALNETS (390)
K_STACK (391) 414 965
K_START (392) 691
K_STEP (393) 231 724 725 729 732 733 734 738 739
K_STOP (394) 692
K_STRUCTURE (395) 604 605 606
K_SYMMETRY (396) 484 530
K_TABLE (397) 427
K_THICKNESS (398) 110
K_TIEHIGH (399) 562
K_TIELOW (400) 563
K_TIEOFFR (401) 620
K_TIME (402) 69
K_TIMING (403) 755 756
K_TO (404) 400
K_TOPIN (405) 762
K_TOPLEFT (406) 569
K_TOPRIGHT (407) 570
K_TOPOFSTACKONLY (408) 331
K_TRISTATE (409) 677
K_TYPE (410) 87
K_UNATENESS (411) 765 776
K_UNITS (412) 65 66
K_USE (413) 611
K_VARIABLE (414) 764
K_VERTICAL (415) 279 398
K_VHI (416) 619
K_VIA (417) 306 721 723 913
K_VIARULE (418) 313 370 915
K_VLO (419) 618
K_VOLTAGE (420) 74
K_VOLTS (421) 74
K_WIDTH (422) 97 129 172 175 189 197 200 202 211 216 251 283 291 295
    300 400 461 701
K_X (423) 487 533 729 733 738
K_Y (424) 488 534 732 734 739
T_STRING (425) 3 79 81 260 261 262 303 307 308 309 313 320 334 335
    336 342 361 368 370 386 387 388 392 394 407 413 414 427 433 435
    447 448 449 454 455 456 461 472 474 495 501 503 536 537 538 573
    574 586 588 589 596 598 608 623 639 640 663 665 672 673 674 700
    721 723 725 726 737 748 753 812 813 814 815 819 821 832 837 848
    850 852 854 856 905 907 909 911 913 915 917 919 924 952 970 972
QSTRING (426) 5 6 261 335 387 455 537 661 673 780 781 782 890 923
NUMBER (427) 4 76 98 126 170 246 249 257 262 334 388 456 536 646 650
    651 652 653 654 655 656 657 672 940 955 1014 1015 1016
K_N (428) 343
K_S (429) 345
K_E (430) 346
K_W (431) 344
K_FN (432) 347
K_FS (433) 349
K_FE (434) 350
K_FW (435) 348
K_R0 (436) 351
K_R90 (437) 352 489 535
K_R180 (438) 353
K_R270 (439) 354
K_MX (440) 357
K_MY (441) 355
K_MXR90 (442) 358
K_MYR90 (443) 356
K_USER (444) 575
K_MASTERSLICE (445) 275
K_ENDMACRO (446)
K_ENDMACROPIN (447)
K_ENDVIARULE (448)
K_ENDVIA (449)
K_ENDLAYER (450)
K_ENDSITE (451)
K_CANPLACE (452) 827 841
K_CANNOTOCCUPY (453) 829 843
K_TRACKS (454) 831
K_FLOORPLAN (455) 837
K_GCELLGRID (456) 834
K_DEFAULTCAP (457) 835
K_MINPINS (458) 846
K_WIRECAP (459) 846
K_STABLE (460) 777
K_SETUP (461) 777 787
K_HOLD (462) 777 788
K_DEFINE (463) 852
K_DEFINES (464) 854
K_DEFINEB (465) 856
K_IF (466) 870 884 889
K_THEN (467) 860 861
K_ELSE (468) 862 863
K_FALSE (469) 886
K_TRUE (470) 885
K_EQ (471) 878 895
K_NE (472) 879 896
K_LE (473) 891
K_LT (474) 892
K_GE (475) 893
K_GT (476) 894
K_OR (477) 874 877 881
K_AND (478) 873 876 880
K_NOT (479) 882
K_DELAY (480) 797
K_TABLEDIMENSION (481) 765 778 779
K_TABLEAXIS (482) 766
K_TABLEENTRIES (483) 246 253 767
K_TRANSITIONTIME (484) 798
K_EXTENSION (485) 783
K_PROPDEF (486) 901
K_STRING (487) 922 923
K_INTEGER (488) 920
K_REAL (489) 921
K_RANGE (490) 303 928 929 938 957 959
K_PROPERTY (491) 119 330 383 451 527 642
K_VIRTUAL (492) 276 483 549
K_BUSBITCHARS (493) 6
K_VERSION (494) 3
K_BEGINEXT (495) 1019
K_ENDEXT (496)
K_UNIVERSALNOISEMARGIN (497) 973
K_EDGERATETHRESHOLD1 (498) 974
K_CORRECTIONTABLE (499) 995 996
K_EDGERATESCALEFACTOR (500) 976
K_EDGERATETHRESHOLD2 (501) 975
K_VICTIMNOISE (502) 991
K_NOISETABLE (503) 978 979
K_EDGERATE (504) 982 999
K_OUTPUTRESISTANCE (505) 627 985 1002
K_VICTIMLENGTH (506) 991 1008
K_CORRECTIONFACTOR (507) 1008
K_OUTPUTPINANTENNASIZE (508) 1012
K_INPUTPINANTENNASIZE (509) 1011
K_INOUTPINANTENNASIZE (510) 1013
K_CURRENTDEN (511) 115 116 117
K_PWL (512) 105 107 116 234
K_ANTENNALENGTHFACTOR (513) 114
K_TAPERRULE (514) 640
K_DIVIDERCHAR (515) 5
K_ANTENNASIZE (516) 645
K_ANTENNAMETALLENGTH (517) 647
K_ANTENNAMETALAREA (518) 646
K_RISESLEWLIMIT (519) 648
K_FALLSLEWLIMIT (520) 649
K_FUNCTION (521) 520 521
K_BUFFER (522) 520
K_INVERTER (523) 521
K_NAMEMAPSTRING (524) 924
K_NOWIREEXTENSIONATPIN (525) 53 54
K_WIREEXTENSION (526) 109 465
K_MESSAGE (527) 848
K_CREATEFILE (528) 850
K_OPENFILE (529)
K_CLOSEFILE (530)
K_WARNING (531)
K_ERROR (532)
K_FATALERROR (533)
K_RECOVERY (534) 789
K_SKEW (535) 790
K_ANYEDGE (536) 791 794
K_POSEDGE (537) 792 795
K_NEGEDGE (538) 793 796
K_SDFCONDSTART (539) 780
K_SDFCONDEND (540) 781
K_SDFCOND (541) 782
K_MPWH (542) 784
K_MPWL (543) 785
K_PERIOD (544) 786
K_ACCURRENTDENSITY (545) 121 122
K_DCCURRENTDENSITY (546) 123 126 129
K_AVERAGE (547) 123 126 129 241
K_PEAK (548) 240
K_RMS (549) 242
K_FREQUENCY (550) 76 246
K_CUTAREA (551) 126 249
K_MEGAHERTZ (552) 76
K_USELENGTHTHRESHOLD (553) 926
K_LENGTHTHRESHOLD (554) 958 959
K_ANTENNAINPUTGATEAREA (555) 1014
K_ANTENNAINOUTDIFFAREA (556) 1015
K_ANTENNAOUTPUTDIFFAREA (557) 1016
K_ANTENNAAREARATIO (558) 130
K_ANTENNADIFFAREARATIO (559) 132
K_ANTENNACUMAREARATIO (560) 133
K_ANTENNACUMDIFFAREARATIO (561) 135
K_ANTENNAAREAFACTOR (562) 137
K_ANTENNASIDEAREARATIO (563) 138
K_ANTENNADIFFSIDEAREARATIO (564) 140
K_ANTENNACUMSIDEAREARATIO (565) 141
K_ANTENNACUMDIFFSIDEAREARATIO (566) 143
K_ANTENNASIDEAREAFACTOR (567) 145
K_DIFFUSEONLY (568) 239
K_MANUFACTURINGGRID (569) 56
K_FIXEDMASK (570) 55 584
K_ANTENNACELL (571) 565
K_CLEARANCEMEASURE (572) 58
K_EUCLIDEAN (573) 60
K_MAXXY (574) 59
K_USEMINSPACING (575) 57
K_ROWMINSPACING (576)
K_ROWABUTSPACING (577)
K_FLIP (578)
K_NONE (579) 546
K_ANTENNAPARTIALMETALAREA (580) 650
K_ANTENNAPARTIALMETALSIDEAREA (581) 651
K_ANTENNAGATEAREA (582) 654
K_ANTENNADIFFAREA (583) 653
K_ANTENNAMAXAREACAR (584) 655
K_ANTENNAMAXSIDEAREACAR (585) 656
K_ANTENNAPARTIALCUTAREA (586) 652
K_ANTENNAMAXCUTCAR (587) 657
K_SLOTWIREWIDTH (588) 154
K_SLOTWIRELENGTH (589) 155
K_SLOTWIDTH (590) 156
K_SLOTLENGTH (591) 157
K_MAXADJACENTSLOTSPACING (592) 158
K_MAXCOAXIALSLOTSPACING (593) 159
K_MAXEDGESLOTSPACING (594) 160
K_SPLITWIREWIDTH (595) 161
K_MINIMUMDENSITY (596) 162
K_MAXIMUMDENSITY (597) 163
K_DENSITYCHECKWINDOW (598) 164
K_DENSITYCHECKSTEP (599) 165
K_FILLACTIVESPACING (600) 166
K_MINIMUMCUT (601) 172
K_ADJACENTCUTS (602) 954
K_ANTENNAMODEL (603) 147 659
K_BUMP (604) 541 547
K_ENCLOSURE (605) 179 313 399
K_FROMABOVE (606) 220
K_FROMBELOW (607) 221
K_IMPLANT (608) 277
K_LENGTH (609) 175 212 223
K_MAXVIASTACK (610) 301 303
K_AREAIO (611) 560
K_BLACKBOX (612) 544
K_MAXWIDTH (613) 167
K_MINENCLOSEDAREA (614) 170
K_MINSTEP (615) 174
K_ORIENT (616)
K_OXIDE1 (617) 284 666
K_OXIDE2 (618) 285 667
K_OXIDE3 (619) 286 668
K_OXIDE4 (620) 287 669
K_PARALLELRUNLENGTH (621) 197
K_MINWIDTH (622) 168
K_PROTRUSIONWIDTH (623) 175
K_SPACINGTABLE (624) 102 177
K_WITHIN (625) 102 202 205 218 223 300 932 954 961
K_ABOVE (626) 207
K_BELOW (627) 208
K_CENTERTOCENTER (628) 945
K_CUTSIZE (629) 313
K_CUTSPACING (630) 86 313
K_DENSITY (631) 743
K_DIAG45 (632) 280
K_DIAG135 (633) 281
K_MASK (634) 88 417
K_DIAGMINEDGELENGTH (635) 183
K_DIAGSPACING (636) 96
K_DIAGPITCH (637) 91 92
K_DIAGWIDTH (638) 95 469
K_GENERATED (639) 309
K_GROUNDSENSITIVITY (640) 665
K_HARDSPACING (641) 437
K_INSIDECORNER (642) 229
K_LAYERS (643) 313
K_LENGTHSUM (644) 227
K_MICRONS (645) 75
K_MINCUTS (646) 449
K_MINSIZE (647) 185
K_NETEXPR (648) 661
K_OUTSIDECORNER (649) 230
K_PREFERENCLOSURE (650) 181
K_ROWCOL (651) 316
K_ROWPATTERN (652) 491
K_SOFT (653) 545
K_SUPPLYSENSITIVITY (654) 663
K_USEVIA (655) 447
K_USEVIARULE (656) 448
K_WELLTAP (657) 566
K_ARRAYCUTS (658) 192
K_ARRAYSPACING (659) 86
K_ANTENNAAREADIFFREDUCEPWL (660) 153
K_ANTENNAAREAMINUSDIFF (661) 150
K_ANTENNACUMROUTINGPLUSCUT (662) 148
K_ANTENNAGATEPLUSDIFF (663) 149
K_ENDOFLINE (664) 961
K_ENDOFNOTCHWIDTH (665) 963
K_EXCEPTEXTRACUT (666) 214
K_EXCEPTSAMEPGNET (667) 967
K_EXCEPTPGNET (668) 712
K_LONGARRAY (669) 187
K_MAXEDGES (670) 228
K_NOTCHLENGTH (671) 962 963
K_NOTCHSPACING (672) 963
K_ORTHOGONAL (673) 102
K_PARALLELEDGE (674) 932
K_PARALLELOVERLAP (675) 948
K_PGONLY (676) 936
K_PRL (677) 297
K_TWOEDGES (678) 934
K_TWOWIDTHS (679) 200
IF (680)
LNOT (681)
UMINUS (682)


Nonterminals, with rules where they appear

$accept (439)
    on left: 0
lef_file (440)
    on left: 1, on right: 0
version (441)
    on left: 3, on right: 12
$@1 (442)
    on left: 2, on right: 3
int_number (443)
    on left: 4, on right: 56 69 70 71 72 73 74 75 86 88 89 90 91 92
    93 94 95 96 97 100 102 104 106 108 109 110 111 112 113 114 115
    117 122 123 129 130 133 137 138 141 145 149 150 154 155 156 157
    158 159 160 161 162 163 164 165 166 167 168 172 174 175 179 181
    182 183 189 192 197 200 202 205 211 212 214 216 218 223 227 228
    232 251 253 255 265 268 271 283 291 295 297 300 301 303 313 316
    317 318 328 399 400 402 403 404 405 413 414 417 426 428 429 449
    461 464 465 466 467 468 469 477 496 497 578 593 609 613 614 615
    616 617 618 619 620 625 627 629 630 631 632 633 634 637 638 645
    647 648 649 701 714 715 724 725 726 729 732 733 734 738 739 751
    764 765 768 769 770 771 772 773 774 775 777 778 779 801 802 803
    805 806 835 846 871 927 928 929 932 938 942 954 957 958 959 961
    962 963 973 974 975 976 978 982 986 987 991 992 993 995 999 1003
    1004 1008 1009 1010 1011 1012 1013
dividerchar (444)
    on left: 5, on right: 20
busbitchars (445)
    on left: 6, on right: 13
rules (446)
    on left: 7 8 9, on right: 1 8
end_library (447)
    on left: 10 11, on right: 1
rule (448)
    on left: 12 13 14 15 16 17 18 19 20 21 22 23 24 25 26 27 28 29
    30 31 32 33 34 35 36 37 38 39 40 41 42 43 44 45 46 47 48 49 50,
    on right: 8
case_sensitivity (449)
    on left: 51 52, on right: 14
wireextension (450)
    on left: 53 54, on right: 21
fixedmask (451)
    on left: 55, on right: 46
manufacturing (452)
    on left: 56, on right: 45
useminspacing (453)
    on left: 57, on right: 47
clearancemeasure (454)
    on left: 58, on right: 48
clearance_type (455)
    on left: 59 60, on right: 58
spacing_type (456)
    on left: 61 62, on right: 57
spacing_value (457)
    on left: 63 64, on right: 57
units_section (458)
    on left: 65, on right: 15
start_units (459)
    on left: 66, on right: 65
units_rules (460)
    on left: 67 68, on right: 65 68
units_rule (461)
    on left: 69 70 71 72 73 74 75 76, on right: 68
layer_rule (462)
    on left: 77, on right: 16
start_layer (463)
    on left: 79, on right: 77
$@2 (464)
    on left: 78, on right: 79
end_layer (465)
    on left: 81, on right: 77
$@3 (466)
    on left: 80, on right: 81
layer_options (467)
    on left: 82 83, on right: 77 83
layer_option (468)
    on left: 86 87 88 89 90 91 92 93 94 95 96 97 98 100 102 103 104
    105 106 107 108 109 110 111 112 113 114 115 116 117 119 121 122
    123 126 129 130 132 133 135 137 138 140 141 143 145 147 148 149
    150 153 154 155 156 157 158 159 160 161 162 163 164 165 166 167
    168 170 172 174 175 177 179 181 182 183 185, on right: 83
$@4 (469)
    on left: 84, on right: 86
$@5 (470)
    on left: 85, on right: 86
$@6 (471)
    on left: 99, on right: 100
$@7 (472)
    on left: 101, on right: 102
$@8 (473)
    on left: 118, on right: 119
$@9 (474)
    on left: 120, on right: 121
$@10 (475)
    on left: 124, on right: 126
$@11 (476)
    on left: 125, on right: 126
$@12 (477)
    on left: 127, on right: 129
$@13 (478)
    on left: 128, on right: 129
$@14 (479)
    on left: 131, on right: 132
$@15 (480)
    on left: 134, on right: 135
$@16 (481)
    on left: 136, on right: 137
$@17 (482)
    on left: 139, on right: 140
$@18 (483)
    on left: 142, on right: 143
$@19 (484)
    on left: 144, on right: 145
$@20 (485)
    on left: 146, on right: 147
$@21 (486)
    on left: 151, on right: 153
$@22 (487)
    on left: 152, on right: 153
$@23 (488)
    on left: 169, on right: 170
$@24 (489)
    on left: 171, on right: 172
$@25 (490)
    on left: 173, on right: 174
$@26 (491)
    on left: 176, on right: 177
$@27 (492)
    on left: 178, on right: 179
$@28 (493)
    on left: 180, on right: 181
$@29 (494)
    on left: 184, on right: 185
layer_arraySpacing_long (495)
    on left: 186 187, on right: 86
layer_arraySpacing_width (496)
    on left: 188 189, on right: 86
layer_arraySpacing_arraycuts (497)
    on left: 190 191, on right: 86 191
layer_arraySpacing_arraycut (498)
    on left: 192, on right: 191
sp_options (499)
    on left: 197 200 202, on right: 177
$@30 (500)
    on left: 193, on right: 197
$@31 (501)
    on left: 194, on right: 197
$@32 (502)
    on left: 195, on right: 197
$@33 (503)
    on left: 196, on right: 197
$@34 (504)
    on left: 198, on right: 200
$@35 (505)
    on left: 199, on right: 200
$@36 (506)
    on left: 201, on right: 202
layer_spacingtable_opts (507)
    on left: 203 204, on right: 102 204
layer_spacingtable_opt (508)
    on left: 205, on right: 204
layer_enclosure_type_opt (509)
    on left: 206 207 208, on right: 179 181
layer_enclosure_width_opt (510)
    on left: 209 211 212, on right: 179
$@37 (511)
    on left: 210, on right: 211
layer_enclosure_width_except_opt (512)
    on left: 213 214, on right: 211
layer_preferenclosure_width_opt (513)
    on left: 215 216, on right: 181
layer_minimumcut_within (514)
    on left: 217 218, on right: 172
layer_minimumcut_from (515)
    on left: 219 220 221, on right: 172
layer_minimumcut_length (516)
    on left: 222 223, on right: 172
layer_minstep_options (517)
    on left: 224 225, on right: 174 225
layer_minstep_option (518)
    on left: 226 227 228, on right: 225
layer_minstep_type (519)
    on left: 229 230 231, on right: 226
layer_antenna_pwl (520)
    on left: 232 234, on right: 132 135 140 143
$@38 (521)
    on left: 233, on right: 234
layer_diffusion_ratios (522)
    on left: 235 236, on right: 153 234 236
layer_diffusion_ratio (523)
    on left: 237, on right: 236
layer_antenna_duo (524)
    on left: 238 239, on right: 137 145
layer_table_type (525)
    on left: 240 241 242, on right: 121 122
layer_frequency (526)
    on left: 246, on right: 121
$@39 (527)
    on left: 243, on right: 246
$@40 (528)
    on left: 244, on right: 246
$@41 (529)
    on left: 245, on right: 246
ac_layer_table_opt (530)
    on left: 247 249 251, on right: 246
$@42 (531)
    on left: 248, on right: 249
$@43 (532)
    on left: 250, on right: 251
dc_layer_table (533)
    on left: 253, on right: 126 129
$@44 (534)
    on left: 252, on right: 253
int_number_list (535)
    on left: 254 255, on right: 129 197 200 251 253 255 291 295
number_list (536)
    on left: 256 257, on right: 126 246 249 257
layer_prop_list (537)
    on left: 258 259, on right: 119 259
layer_prop (538)
    on left: 260 261 262, on right: 258 259
current_density_pwl_list (539)
    on left: 263 264, on right: 116 264
current_density_pwl (540)
    on left: 265, on right: 263 264
cap_points (541)
    on left: 266 267, on right: 107 267
cap_point (542)
    on left: 268, on right: 266 267
res_points (543)
    on left: 269 270, on right: 105 270
res_point (544)
    on left: 271, on right: 269 270
layer_type (545)
    on left: 272 273 274 275 276 277, on right: 87
layer_direction (546)
    on left: 278 279 280 281, on right: 103
layer_minen_width (547)
    on left: 282 283, on right: 170
layer_oxide (548)
    on left: 284 285 286 287, on right: 147
layer_sp_parallel_widths (549)
    on left: 288 289, on right: 197 289
layer_sp_parallel_width (550)
    on left: 291, on right: 289
$@45 (551)
    on left: 290, on right: 291
layer_sp_TwoWidths (552)
    on left: 292 293, on right: 200 293
layer_sp_TwoWidth (553)
    on left: 295, on right: 293
$@46 (554)
    on left: 294, on right: 295
layer_sp_TwoWidthsPRL (555)
    on left: 296 297, on right: 200 295
layer_sp_influence_widths (556)
    on left: 298 299, on right: 202 299
layer_sp_influence_width (557)
    on left: 300, on right: 299
maxstack_via (558)
    on left: 301 303, on right: 49
$@47 (559)
    on left: 302, on right: 303
via (560)
    on left: 305, on right: 17 441
$@48 (561)
    on left: 304, on right: 305
via_keyword (562)
    on left: 306, on right: 307 308 309 392
start_via (563)
    on left: 307 308 309, on right: 305
via_viarule (564)
    on left: 313, on right: 321
$@49 (565)
    on left: 310, on right: 313
$@50 (566)
    on left: 311, on right: 313
$@51 (567)
    on left: 312, on right: 313
via_viarule_options (568)
    on left: 314 315, on right: 313 315
via_viarule_option (569)
    on left: 316 317 318 320, on right: 315
$@52 (570)
    on left: 319, on right: 320
via_option (571)
    on left: 321 322, on right: 305
via_other_options (572)
    on left: 323, on right: 322
via_more_options (573)
    on left: 324 325, on right: 323 325
via_other_option (574)
    on left: 326 327 328 330 331, on right: 323 325
$@53 (575)
    on left: 329, on right: 330
via_prop_list (576)
    on left: 332 333, on right: 330 333
via_name_value_pair (577)
    on left: 334 335 336, on right: 332 333
via_foreign (578)
    on left: 337 338 339 340, on right: 326
start_foreign (579)
    on left: 342, on right: 337 338 339 340 580 581 582 583 601 602
    603 604 605 606
$@54 (580)
    on left: 341, on right: 342
orientation (581)
    on left: 343 344 345 346 347 348 349 350 351 352 353 354 355 356
    357 358, on right: 339 340 495 582 583 603 606 725 726
via_layer_rule (582)
    on left: 359, on right: 327
via_layer (583)
    on left: 361, on right: 359
$@55 (584)
    on left: 360, on right: 361
via_geometries (585)
    on left: 362 363, on right: 359 363
via_geometry (586)
    on left: 364 366, on right: 363
$@56 (587)
    on left: 365, on right: 366
end_via (588)
    on left: 368, on right: 305
$@57 (589)
    on left: 367, on right: 368
viarule_keyword (590)
    on left: 370, on right: 371 373
$@58 (591)
    on left: 369, on right: 370
viarule (592)
    on left: 371, on right: 18
viarule_generate (593)
    on left: 373, on right: 19
$@59 (594)
    on left: 372, on right: 373
viarule_generate_default (595)
    on left: 374 375, on right: 373
viarule_layer_list (596)
    on left: 376 377, on right: 371 373 377
opt_viarule_props (597)
    on left: 378 379, on right: 371 373
viarule_props (598)
    on left: 380 381, on right: 379 381
viarule_prop (599)
    on left: 383 386 387 388, on right: 380 381 384 385
$@60 (600)
    on left: 382, on right: 383
viarule_prop_list (601)
    on left: 384 385, on right: 383 385
viarule_layer (602)
    on left: 389, on right: 376 377
via_names (603)
    on left: 390 391, on right: 371 391
via_name (604)
    on left: 392, on right: 391
viarule_layer_name (605)
    on left: 394, on right: 389
$@61 (606)
    on left: 393, on right: 394
viarule_layer_options (607)
    on left: 395 396, on right: 389 396
viarule_layer_option (608)
    on left: 397 398 399 400 401 402 403 404 405, on right: 396
end_viarule (609)
    on left: 407, on right: 371 373
$@62 (610)
    on left: 406, on right: 407
spacing_rule (611)
    on left: 408, on right: 23 442
start_spacing (612)
    on left: 409, on right: 408
end_spacing (613)
    on left: 410, on right: 408
spacings (614)
    on left: 411 412, on right: 408 412
spacing (615)
    on left: 413 414, on right: 412
samenet_keyword (616)
    on left: 415, on right: 413 414
maskColor (617)
    on left: 416 417, on right: 364 366 702 703 704 705 706 707 721
    723
irdrop (618)
    on left: 418, on right: 26
start_irdrop (619)
    on left: 419, on right: 418
end_irdrop (620)
    on left: 420, on right: 418
ir_tables (621)
    on left: 421 422, on right: 418 422
ir_table (622)
    on left: 423, on right: 422
ir_table_values (623)
    on left: 424 425, on right: 423 425
ir_table_value (624)
    on left: 426, on right: 425
ir_tablename (625)
    on left: 427, on right: 423
minfeature (626)
    on left: 428, on right: 25
dielectric (627)
    on left: 429, on right: 24
nondefault_rule (628)
    on left: 433, on right: 31
$@63 (629)
    on left: 430, on right: 433
$@64 (630)
    on left: 431, on right: 433
$@65 (631)
    on left: 432, on right: 433
end_nd_rule (632)
    on left: 434 435, on right: 433
nd_hardspacing (633)
    on left: 436 437, on right: 433
nd_rules (634)
    on left: 438 439, on right: 433 439
nd_rule (635)
    on left: 440 441 442 443 444 445 446, on right: 439
usevia (636)
    on left: 447, on right: 444
useviarule (637)
    on left: 448, on right: 445
mincuts (638)
    on left: 449, on right: 446
nd_prop (639)
    on left: 451 454 455 456, on right: 443 452 453
$@66 (640)
    on left: 450, on right: 451
nd_prop_list (641)
    on left: 452 453, on right: 451 453
nd_layer (642)
    on left: 461, on right: 440
$@67 (643)
    on left: 457, on right: 461
$@68 (644)
    on left: 458, on right: 461
$@69 (645)
    on left: 459, on right: 461
$@70 (646)
    on left: 460, on right: 461
nd_layer_stmts (647)
    on left: 462 463, on right: 461 463
nd_layer_stmt (648)
    on left: 464 465 466 467 468 469, on right: 463
site (649)
    on left: 470, on right: 27
start_site (650)
    on left: 472, on right: 470
$@71 (651)
    on left: 471, on right: 472
end_site (652)
    on left: 474, on right: 470
$@72 (653)
    on left: 473, on right: 474
site_options (654)
    on left: 475 476, on right: 470 476
site_option (655)
    on left: 477 478 479 480, on right: 476
site_class (656)
    on left: 481 482 483, on right: 479
site_symmetry_statement (657)
    on left: 484, on right: 478
site_symmetries (658)
    on left: 485 486, on right: 484 486
site_symmetry (659)
    on left: 487 488 489, on right: 486
site_rowpattern_statement (660)
    on left: 491, on right: 480
$@73 (661)
    on left: 490, on right: 491
site_rowpatterns (662)
    on left: 492 493, on right: 491 493
site_rowpattern (663)
    on left: 495, on right: 493
$@74 (664)
    on left: 494, on right: 495
pt (665)
    on left: 496 497, on right: 153 234 237 338 339 364 401 579 581
    582 602 603 605 606 704 705 716 717 721 723 751
macro (666)
    on left: 499, on right: 28
$@75 (667)
    on left: 498, on right: 499
start_macro (668)
    on left: 501, on right: 499
$@76 (669)
    on left: 500, on right: 501
end_macro (670)
    on left: 503, on right: 499
$@77 (671)
    on left: 502, on right: 503
macro_options (672)
    on left: 504 505, on right: 499 505
macro_option (673)
    on left: 506 507 508 509 510 511 512 513 514 515 516 517 518 519
    520 521 522 523 524 525 527, on right: 505
$@78 (674)
    on left: 526, on right: 527
macro_prop_list (675)
    on left: 528 529, on right: 527 529
macro_symmetry_statement (676)
    on left: 530, on right: 510
macro_symmetries (677)
    on left: 531 532, on right: 530 532
macro_symmetry (678)
    on left: 533 534 535, on right: 532
macro_name_value_pair (679)
    on left: 536 537 538, on right: 528 529
macro_class (680)
    on left: 539, on right: 506
class_type (681)
    on left: 540 541 542 543 544 545 546 547 548 549 550 551 552 553
    554, on right: 539 682
pad_type (682)
    on left: 555 556 557 558 559 560, on right: 550
core_type (683)
    on left: 561 562 563 564 565 566, on right: 553
endcap_type (684)
    on left: 567 568 569 570 571 572, on right: 554
macro_generator (685)
    on left: 573, on right: 507
macro_generate (686)
    on left: 574, on right: 508
macro_source (687)
    on left: 575 576 577, on right: 509
macro_power (688)
    on left: 578, on right: 513
macro_origin (689)
    on left: 579, on right: 512
macro_foreign (690)
    on left: 580 581 582 583, on right: 514
macro_fixedMask (691)
    on left: 584, on right: 511
macro_eeq (692)
    on left: 586, on right: 515
$@79 (693)
    on left: 585, on right: 586
macro_leq (694)
    on left: 588, on right: 516
$@80 (695)
    on left: 587, on right: 588
macro_site (696)
    on left: 589 590, on right: 518
macro_site_word (697)
    on left: 591, on right: 589 590
site_word (698)
    on left: 592, on right: 825
macro_size (699)
    on left: 593, on right: 517
macro_pin (700)
    on left: 594, on right: 519
start_macro_pin (701)
    on left: 596, on right: 594
$@81 (702)
    on left: 595, on right: 596
end_macro_pin (703)
    on left: 598, on right: 594
$@82 (704)
    on left: 597, on right: 598
macro_pin_options (705)
    on left: 599 600, on right: 594 600
macro_pin_option (706)
    on left: 601 602 603 604 605 606 608 609 610 611 612 613 614 615
    616 617 618 619 620 621 623 625 627 629 630 631 632 633 634 635
    636 637 638 639 640 642 643 644 645 646 647 648 649 650 651 652
    653 654 655 656 657 659 661 663 665, on right: 600
$@83 (707)
    on left: 607, on right: 608
$@84 (708)
    on left: 622, on right: 623
$@85 (709)
    on left: 624, on right: 625
$@86 (710)
    on left: 626, on right: 627
$@87 (711)
    on left: 628, on right: 629
$@88 (712)
    on left: 641, on right: 642
$@89 (713)
    on left: 658, on right: 659
$@90 (714)
    on left: 660, on right: 661
$@91 (715)
    on left: 662, on right: 663
$@92 (716)
    on left: 664, on right: 665
pin_layer_oxide (717)
    on left: 666 667 668 669, on right: 659
pin_prop_list (718)
    on left: 670 671, on right: 642 671
pin_name_value_pair (719)
    on left: 672 673 674, on right: 670 671
electrical_direction (720)
    on left: 675 676 677 678 679, on right: 610
start_macro_port (721)
    on left: 680, on right: 643 644
macro_port_class_option (722)
    on left: 681 682, on right: 643
macro_pin_use (723)
    on left: 683 684 685 686 687 688, on right: 611
macro_scan_use (724)
    on left: 689 690 691 692, on right: 612
pin_shape (725)
    on left: 693 694 695 696, on right: 621
geometries (726)
    on left: 697, on right: 643 740
geometry (727)
    on left: 700 701 702 703 704 705 706 707 708, on right: 697 710
$@93 (728)
    on left: 698, on right: 700
$@94 (729)
    on left: 699, on right: 700
geometry_options (730)
    on left: 709 710, on right: 697 710
layer_exceptpgnet (731)
    on left: 711 712, on right: 700
layer_spacing (732)
    on left: 713 714 715, on right: 700
firstPt (733)
    on left: 716, on right: 185 366 702 703 706 707
nextPt (734)
    on left: 717, on right: 366 706 707 719
otherPts (735)
    on left: 718 719, on right: 185 366 702 703 706 707 719
via_placement (736)
    on left: 721 723, on right: 708
$@95 (737)
    on left: 720, on right: 721
$@96 (738)
    on left: 722, on right: 723
stepPattern (739)
    on left: 724, on right: 703 705 707 723
sitePattern (740)
    on left: 725 726, on right: 590 825 827 829 841 843
trackPattern (741)
    on left: 729 732 733 734, on right: 831
$@97 (742)
    on left: 727, on right: 729
$@98 (743)
    on left: 728, on right: 729
$@99 (744)
    on left: 730, on right: 732
$@100 (745)
    on left: 731, on right: 732
trackLayers (746)
    on left: 735 736, on right: 729 732 736
layer_name (747)
    on left: 737, on right: 736
gcellPattern (748)
    on left: 738 739, on right: 834
macro_obs (749)
    on left: 740 741, on right: 522
start_macro_obs (750)
    on left: 742, on right: 740 741
macro_density (751)
    on left: 743, on right: 523
density_layers (752)
    on left: 744 745, on right: 743 745
density_layer (753)
    on left: 748, on right: 743 745
$@101 (754)
    on left: 746, on right: 748
$@102 (755)
    on left: 747, on right: 748
density_layer_rects (756)
    on left: 749 750, on right: 748 750
density_layer_rect (757)
    on left: 751, on right: 748 750
macro_clocktype (758)
    on left: 753, on right: 524
$@103 (759)
    on left: 752, on right: 753
timing (760)
    on left: 754, on right: 525
start_timing (761)
    on left: 755, on right: 754
end_timing (762)
    on left: 756, on right: 754
timing_options (763)
    on left: 757 758, on right: 754 758
timing_option (764)
    on left: 760 762 764 765 766 767 768 769 770 771 772 773 774 775
    776 777 778 779 780 781 782 783, on right: 758
$@104 (765)
    on left: 759, on right: 760
$@105 (766)
    on left: 761, on right: 762
$@106 (767)
    on left: 763, on right: 764
one_pin_trigger (768)
    on left: 784 785 786, on right: 779
two_pin_trigger (769)
    on left: 787 788 789 790, on right: 778
from_pin_trigger (770)
    on left: 791 792 793, on right: 778
to_pin_trigger (771)
    on left: 794 795 796, on right: 778
delay_or_transition (772)
    on left: 797 798, on right: 765
list_of_table_entries (773)
    on left: 799 800, on right: 767 800
table_entry (774)
    on left: 801, on right: 799 800
list_of_table_axis_dnumbers (775)
    on left: 802 803, on right: 766 803
slew_spec (776)
    on left: 804 805 806, on right: 764
risefall (777)
    on left: 807 808, on right: 764 765 777
unateness (778)
    on left: 809 810 811, on right: 765 776
list_of_from_strings (779)
    on left: 812 813, on right: 760 813
list_of_to_strings (780)
    on left: 814 815, on right: 762 815
array (781)
    on left: 817, on right: 29
$@107 (782)
    on left: 816, on right: 817
start_array (783)
    on left: 819, on right: 817
$@108 (784)
    on left: 818, on right: 819
end_array (785)
    on left: 821, on right: 817
$@109 (786)
    on left: 820, on right: 821
array_rules (787)
    on left: 822 823, on right: 817 823
array_rule (788)
    on left: 825 827 829 831 832 834 835 836, on right: 823
$@110 (789)
    on left: 824, on right: 825
$@111 (790)
    on left: 826, on right: 827
$@112 (791)
    on left: 828, on right: 829
$@113 (792)
    on left: 830, on right: 831
$@114 (793)
    on left: 833, on right: 834
floorplan_start (794)
    on left: 837, on right: 832
floorplan_list (795)
    on left: 838 839, on right: 832 839
floorplan_element (796)
    on left: 841 843, on right: 839
$@115 (797)
    on left: 840, on right: 841
$@116 (798)
    on left: 842, on right: 843
cap_list (799)
    on left: 844 845, on right: 835 845
one_cap (800)
    on left: 846, on right: 845
msg_statement (801)
    on left: 848, on right: 22
$@117 (802)
    on left: 847, on right: 848
create_file_statement (803)
    on left: 850, on right: 50
$@118 (804)
    on left: 849, on right: 850
def_statement (805)
    on left: 852 854 856, on right: 30 836
$@119 (806)
    on left: 851, on right: 852
$@120 (807)
    on left: 853, on right: 854
$@121 (808)
    on left: 855, on right: 856
dtrm (809)
    on left: 857 858 859, on right: 848 850 852 854 856 978 995
then (810)
    on left: 860 861, on right: 870 884 889
else (811)
    on left: 862 863, on right: 870 884 889
expression (812)
    on left: 864 865 866 867 868 869 870 871, on right: 852 864 865
    866 867 868 869 870 872 873 874
b_expr (813)
    on left: 872 873 874 875 876 877 878 879 880 881 882 883 884 885
    886, on right: 856 870 878 879 880 881 882 883 884 889
s_expr (814)
    on left: 887 888 889 890, on right: 848 850 854 875 876 877 887
    888 889
relop (815)
    on left: 891 892 893 894 895 896 897 898 899, on right: 872 875
prop_def_section (816)
    on left: 901, on right: 32
$@122 (817)
    on left: 900, on right: 901
prop_stmts (818)
    on left: 902 903, on right: 901 903
prop_stmt (819)
    on left: 905 907 909 911 913 915 917 919, on right: 903
$@123 (820)
    on left: 904, on right: 905
$@124 (821)
    on left: 906, on right: 907
$@125 (822)
    on left: 908, on right: 909
$@126 (823)
    on left: 910, on right: 911
$@127 (824)
    on left: 912, on right: 913
$@128 (825)
    on left: 914, on right: 915
$@129 (826)
    on left: 916, on right: 917
$@130 (827)
    on left: 918, on right: 919
prop_define (828)
    on left: 920 921 922 923 924, on right: 905 907 909 911 913 915
    917 919
opt_range_second (829)
    on left: 925 926 927 928 929, on right: 957
opt_endofline (830)
    on left: 930 932, on right: 961
$@131 (831)
    on left: 931, on right: 932
opt_endofline_twoedges (832)
    on left: 933 934, on right: 932
opt_samenetPGonly (833)
    on left: 935 936, on right: 947
opt_def_range (834)
    on left: 937 938, on right: 920 921
opt_def_value (835)
    on left: 939 940, on right: 921
opt_def_dvalue (836)
    on left: 941 942, on right: 920
layer_spacing_opts (837)
    on left: 943 944, on right: 100 944
layer_spacing_opt (838)
    on left: 945 947 948, on right: 944
$@132 (839)
    on left: 946, on right: 947
layer_spacing_cut_routing (840)
    on left: 949 952 954 955 957 958 959 961 962 963, on right: 100
$@133 (841)
    on left: 950, on right: 952
$@134 (842)
    on left: 951, on right: 952
$@135 (843)
    on left: 953, on right: 954
$@136 (844)
    on left: 956, on right: 957
$@137 (845)
    on left: 960, on right: 961
spacing_cut_layer_opt (846)
    on left: 964 965, on right: 952
opt_adjacentcuts_exceptsame (847)
    on left: 966 967, on right: 954
opt_layer_name (848)
    on left: 968 970, on right: 645 646 647 650 651 652 653 654
$@138 (849)
    on left: 969, on right: 970
req_layer_name (850)
    on left: 972, on right: 655 656 657
$@139 (851)
    on left: 971, on right: 972
universalnoisemargin (852)
    on left: 973, on right: 33
edgeratethreshold1 (853)
    on left: 974, on right: 34
edgeratethreshold2 (854)
    on left: 975, on right: 36
edgeratescalefactor (855)
    on left: 976, on right: 35
noisetable (856)
    on left: 978, on right: 37
$@140 (857)
    on left: 977, on right: 978
end_noisetable (858)
    on left: 979, on right: 978
noise_table_list (859)
    on left: 980 981, on right: 978 981
noise_table_entry (860)
    on left: 982 983, on right: 980 981
output_resistance_entry (861)
    on left: 985, on right: 983
$@141 (862)
    on left: 984, on right: 985
num_list (863)
    on left: 986 987, on right: 985 987
victim_list (864)
    on left: 988 989, on right: 985 989
victim (865)
    on left: 991, on right: 988 989
$@142 (866)
    on left: 990, on right: 991
vnoiselist (867)
    on left: 992 993, on right: 991 993
correctiontable (868)
    on left: 995, on right: 38
$@143 (869)
    on left: 994, on right: 995
end_correctiontable (870)
    on left: 996, on right: 995
correction_table_list (871)
    on left: 997 998, on right: 995 998
correction_table_item (872)
    on left: 999 1000, on right: 997 998
output_list (873)
    on left: 1002, on right: 1000
$@144 (874)
    on left: 1001, on right: 1002
numo_list (875)
    on left: 1003 1004, on right: 1002 1004
corr_victim_list (876)
    on left: 1005 1006, on right: 1002 1006
corr_victim (877)
    on left: 1008, on right: 1005 1006
$@145 (878)
    on left: 1007, on right: 1008
corr_list (879)
    on left: 1009 1010, on right: 1008 1010
input_antenna (880)
    on left: 1011, on right: 39
output_antenna (881)
    on left: 1012, on right: 40
inout_antenna (882)
    on left: 1013, on right: 41
antenna_input (883)
    on left: 1014, on right: 42
antenna_inout (884)
    on left: 1015, on right: 43
antenna_output (885)
    on left: 1016, on right: 44
extension_opt (886)
    on left: 1017 1018, on right: 1
extension (887)
    on left: 1019, on right: 1018


State 0

    0 $accept: . lef_file $end

    error  shift, and go to state 1

    $end                     reduce using rule 7 (rules)
    K_ARRAY                  reduce using rule 7 (rules)
    K_DIELECTRIC             reduce using rule 7 (rules)
    K_END                    reduce using rule 7 (rules)
    K_IRDROP                 reduce using rule 7 (rules)
    K_LAYER                  reduce using rule 7 (rules)
    K_MACRO                  reduce using rule 7 (rules)
    K_MINFEATURE             reduce using rule 7 (rules)
    K_NAMESCASESENSITIVE     reduce using rule 7 (rules)
    K_NONDEFAULTRULE         reduce using rule 7 (rules)
    K_SITE                   reduce using rule 7 (rules)
    K_SPACING                reduce using rule 7 (rules)
    K_UNITS                  reduce using rule 7 (rules)
    K_VIA                    reduce using rule 7 (rules)
    K_VIARULE                reduce using rule 7 (rules)
    K_DEFINE                 reduce using rule 7 (rules)
    K_DEFINES                reduce using rule 7 (rules)
    K_DEFINEB                reduce using rule 7 (rules)
    K_PROPDEF                reduce using rule 7 (rules)
    K_BUSBITCHARS            reduce using rule 7 (rules)
    K_VERSION                reduce using rule 7 (rules)
    K_BEGINEXT               reduce using rule 7 (rules)
    K_UNIVERSALNOISEMARGIN   reduce using rule 7 (rules)
    K_EDGERATETHRESHOLD1     reduce using rule 7 (rules)
    K_CORRECTIONTABLE        reduce using rule 7 (rules)
    K_EDGERATESCALEFACTOR    reduce using rule 7 (rules)
    K_EDGERATETHRESHOLD2     reduce using rule 7 (rules)
    K_NOISETABLE             reduce using rule 7 (rules)
    K_OUTPUTPINANTENNASIZE   reduce using rule 7 (rules)
    K_INPUTPINANTENNASIZE    reduce using rule 7 (rules)
    K_INOUTPINANTENNASIZE    reduce using rule 7 (rules)
    K_DIVIDERCHAR            reduce using rule 7 (rules)
    K_NOWIREEXTENSIONATPIN   reduce using rule 7 (rules)
    K_MESSAGE                reduce using rule 7 (rules)
    K_CREATEFILE             reduce using rule 7 (rules)
    K_ANTENNAINPUTGATEAREA   reduce using rule 7 (rules)
    K_ANTENNAINOUTDIFFAREA   reduce using rule 7 (rules)
    K_ANTENNAOUTPUTDIFFAREA  reduce using rule 7 (rules)
    K_MANUFACTURINGGRID      reduce using rule 7 (rules)
    K_FIXEDMASK              reduce using rule 7 (rules)
    K_CLEARANCEMEASURE       reduce using rule 7 (rules)
    K_USEMINSPACING          reduce using rule 7 (rules)
    K_MAXVIASTACK            reduce using rule 7 (rules)

    lef_file  go to state 2
    rules     go to state 3


State 1

    9 rules: error .

    $default  reduce using rule 9 (rules)


State 2

    0 $accept: lef_file . $end

    $end  shift, and go to state 4


State 3

    1 lef_file: rules . extension_opt end_library
    8 rules: rules . rule

    K_ARRAY                  shift, and go to state 5
    K_DIELECTRIC             shift, and go to state 6
    K_IRDROP                 shift, and go to state 7
    K_LAYER                  shift, and go to state 8
    K_MACRO                  shift, and go to state 9
    K_MINFEATURE             shift, and go to state 10
    K_NAMESCASESENSITIVE     shift, and go to state 11
    K_NONDEFAULTRULE         shift, and go to state 12
    K_SITE                   shift, and go to state 13
    K_SPACING                shift, and go to state 14
    K_UNITS                  shift, and go to state 15
    K_VIA                    shift, and go to state 16
    K_VIARULE                shift, and go to state 17
    K_DEFINE                 shift, and go to state 18
    K_DEFINES                shift, and go to state 19
    K_DEFINEB                shift, and go to state 20
    K_PROPDEF                shift, and go to state 21
    K_BUSBITCHARS            shift, and go to state 22
    K_VERSION                shift, and go to state 23
    K_BEGINEXT               shift, and go to state 24
    K_UNIVERSALNOISEMARGIN   shift, and go to state 25
    K_EDGERATETHRESHOLD1     shift, and go to state 26
    K_CORRECTIONTABLE        shift, and go to state 27
    K_EDGERATESCALEFACTOR    shift, and go to state 28
    K_EDGERATETHRESHOLD2     shift, and go to state 29
    K_NOISETABLE             shift, and go to state 30
    K_OUTPUTPINANTENNASIZE   shift, and go to state 31
    K_INPUTPINANTENNASIZE    shift, and go to state 32
    K_INOUTPINANTENNASIZE    shift, and go to state 33
    K_DIVIDERCHAR            shift, and go to state 34
    K_NOWIREEXTENSIONATPIN   shift, and go to state 35
    K_MESSAGE                shift, and go to state 36
    K_CREATEFILE             shift, and go to state 37
    K_ANTENNAINPUTGATEAREA   shift, and go to state 38
    K_ANTENNAINOUTDIFFAREA   shift, and go to state 39
    K_ANTENNAOUTPUTDIFFAREA  shift, and go to state 40
    K_MANUFACTURINGGRID      shift, and go to state 41
    K_FIXEDMASK              shift, and go to state 42
    K_CLEARANCEMEASURE       shift, and go to state 43
    K_USEMINSPACING          shift, and go to state 44
    K_MAXVIASTACK            shift, and go to state 45

    $default  reduce using rule 1017 (extension_opt)

    version                go to state 46
    dividerchar            go to state 47
    busbitchars            go to state 48
    rule                   go to state 49
    case_sensitivity       go to state 50
    wireextension          go to state 51
    fixedmask              go to state 52
    manufacturing          go to state 53
    useminspacing          go to state 54
    clearancemeasure       go to state 55
    units_section          go to state 56
    start_units            go to state 57
    layer_rule             go to state 58
    start_layer            go to state 59
    maxstack_via           go to state 60
    via                    go to state 61
    via_keyword            go to state 62
    start_via              go to state 63
    viarule_keyword        go to state 64
    viarule                go to state 65
    viarule_generate       go to state 66
    spacing_rule           go to state 67
    start_spacing          go to state 68
    irdrop                 go to state 69
    start_irdrop           go to state 70
    minfeature             go to state 71
    dielectric             go to state 72
    nondefault_rule        go to state 73
    site                   go to state 74
    start_site             go to state 75
    macro                  go to state 76
    start_macro            go to state 77
    array                  go to state 78
    start_array            go to state 79
    msg_statement          go to state 80
    create_file_statement  go to state 81
    def_statement          go to state 82
    prop_def_section       go to state 83
    universalnoisemargin   go to state 84
    edgeratethreshold1     go to state 85
    edgeratethreshold2     go to state 86
    edgeratescalefactor    go to state 87
    noisetable             go to state 88
    correctiontable        go to state 89
    input_antenna          go to state 90
    output_antenna         go to state 91
    inout_antenna          go to state 92
    antenna_input          go to state 93
    antenna_inout          go to state 94
    antenna_output         go to state 95
    extension_opt          go to state 96
    extension              go to state 97


State 4

    0 $accept: lef_file $end .

    $default  accept


State 5

  819 start_array: K_ARRAY . $@108 T_STRING

    $default  reduce using rule 818 ($@108)

    $@108  go to state 98


State 6

  429 dielectric: K_DIELECTRIC . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 100


State 7

  419 start_irdrop: K_IRDROP .

    $default  reduce using rule 419 (start_irdrop)


State 8

   79 start_layer: K_LAYER . $@2 T_STRING

    $default  reduce using rule 78 ($@2)

    $@2  go to state 101


State 9

  501 start_macro: K_MACRO . $@76 T_STRING

    $default  reduce using rule 500 ($@76)

    $@76  go to state 102


State 10

  428 minfeature: K_MINFEATURE . int_number int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 103


State 11

   51 case_sensitivity: K_NAMESCASESENSITIVE . K_ON ';'
   52                 | K_NAMESCASESENSITIVE . K_OFF ';'

    K_OFF  shift, and go to state 104
    K_ON   shift, and go to state 105


State 12

  433 nondefault_rule: K_NONDEFAULTRULE . $@63 T_STRING $@64 nd_hardspacing nd_rules $@65 end_nd_rule

    $default  reduce using rule 430 ($@63)

    $@63  go to state 106


State 13

  472 start_site: K_SITE . $@71 T_STRING

    $default  reduce using rule 471 ($@71)

    $@71  go to state 107


State 14

  409 start_spacing: K_SPACING .

    $default  reduce using rule 409 (start_spacing)


State 15

   66 start_units: K_UNITS .

    $default  reduce using rule 66 (start_units)


State 16

  306 via_keyword: K_VIA .

    $default  reduce using rule 306 (via_keyword)


State 17

  370 viarule_keyword: K_VIARULE . $@58 T_STRING

    $default  reduce using rule 369 ($@58)

    $@58  go to state 108


State 18

  852 def_statement: K_DEFINE . $@119 T_STRING '=' expression dtrm

    $default  reduce using rule 851 ($@119)

    $@119  go to state 109


State 19

  854 def_statement: K_DEFINES . $@120 T_STRING '=' s_expr dtrm

    $default  reduce using rule 853 ($@120)

    $@120  go to state 110


State 20

  856 def_statement: K_DEFINEB . $@121 T_STRING '=' b_expr dtrm

    $default  reduce using rule 855 ($@121)

    $@121  go to state 111


State 21

  901 prop_def_section: K_PROPDEF . $@122 prop_stmts K_END K_PROPDEF

    $default  reduce using rule 900 ($@122)

    $@122  go to state 112


State 22

    6 busbitchars: K_BUSBITCHARS . QSTRING ';'

    QSTRING  shift, and go to state 113


State 23

    3 version: K_VERSION . $@1 T_STRING ';'

    $default  reduce using rule 2 ($@1)

    $@1  go to state 114


State 24

  1019 extension: K_BEGINEXT .

    $default  reduce using rule 1019 (extension)


State 25

  973 universalnoisemargin: K_UNIVERSALNOISEMARGIN . int_number int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 115


State 26

  974 edgeratethreshold1: K_EDGERATETHRESHOLD1 . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 116


State 27

  995 correctiontable: K_CORRECTIONTABLE . int_number ';' $@143 correction_table_list end_correctiontable dtrm

    NUMBER  shift, and go to state 99

    int_number  go to state 117


State 28

  976 edgeratescalefactor: K_EDGERATESCALEFACTOR . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 118


State 29

  975 edgeratethreshold2: K_EDGERATETHRESHOLD2 . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 119


State 30

  978 noisetable: K_NOISETABLE . int_number $@140 ';' noise_table_list end_noisetable dtrm

    NUMBER  shift, and go to state 99

    int_number  go to state 120


State 31

  1012 output_antenna: K_OUTPUTPINANTENNASIZE . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 121


State 32

  1011 input_antenna: K_INPUTPINANTENNASIZE . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 122


State 33

  1013 inout_antenna: K_INOUTPINANTENNASIZE . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 123


State 34

    5 dividerchar: K_DIVIDERCHAR . QSTRING ';'

    QSTRING  shift, and go to state 124


State 35

   53 wireextension: K_NOWIREEXTENSIONATPIN . K_ON ';'
   54              | K_NOWIREEXTENSIONATPIN . K_OFF ';'

    K_OFF  shift, and go to state 125
    K_ON   shift, and go to state 126


State 36

  848 msg_statement: K_MESSAGE . $@117 T_STRING '=' s_expr dtrm

    $default  reduce using rule 847 ($@117)

    $@117  go to state 127


State 37

  850 create_file_statement: K_CREATEFILE . $@118 T_STRING '=' s_expr dtrm

    $default  reduce using rule 849 ($@118)

    $@118  go to state 128


State 38

  1014 antenna_input: K_ANTENNAINPUTGATEAREA . NUMBER ';'

    NUMBER  shift, and go to state 129


State 39

  1015 antenna_inout: K_ANTENNAINOUTDIFFAREA . NUMBER ';'

    NUMBER  shift, and go to state 130


State 40

  1016 antenna_output: K_ANTENNAOUTPUTDIFFAREA . NUMBER ';'

    NUMBER  shift, and go to state 131


State 41

   56 manufacturing: K_MANUFACTURINGGRID . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 132


State 42

   55 fixedmask: K_FIXEDMASK . ';'

    ';'  shift, and go to state 133


State 43

   58 clearancemeasure: K_CLEARANCEMEASURE . clearance_type ';'

    K_EUCLIDEAN  shift, and go to state 134
    K_MAXXY      shift, and go to state 135

    clearance_type  go to state 136


State 44

   57 useminspacing: K_USEMINSPACING . spacing_type spacing_value ';'

    K_OBS  shift, and go to state 137
    K_PIN  shift, and go to state 138

    spacing_type  go to state 139


State 45

  301 maxstack_via: K_MAXVIASTACK . int_number ';'
  303             | K_MAXVIASTACK . int_number K_RANGE $@47 T_STRING T_STRING ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 140


State 46

   12 rule: version .

    $default  reduce using rule 12 (rule)


State 47

   20 rule: dividerchar .

    $default  reduce using rule 20 (rule)


State 48

   13 rule: busbitchars .

    $default  reduce using rule 13 (rule)


State 49

    8 rules: rules rule .

    $default  reduce using rule 8 (rules)


State 50

   14 rule: case_sensitivity .

    $default  reduce using rule 14 (rule)


State 51

   21 rule: wireextension .

    $default  reduce using rule 21 (rule)


State 52

   46 rule: fixedmask .

    $default  reduce using rule 46 (rule)


State 53

   45 rule: manufacturing .

    $default  reduce using rule 45 (rule)


State 54

   47 rule: useminspacing .

    $default  reduce using rule 47 (rule)


State 55

   48 rule: clearancemeasure .

    $default  reduce using rule 48 (rule)


State 56

   15 rule: units_section .

    $default  reduce using rule 15 (rule)


State 57

   65 units_section: start_units . units_rules K_END K_UNITS

    $default  reduce using rule 67 (units_rules)

    units_rules  go to state 141


State 58

   16 rule: layer_rule .

    $default  reduce using rule 16 (rule)


State 59

   77 layer_rule: start_layer . layer_options end_layer

    $default  reduce using rule 82 (layer_options)

    layer_options  go to state 142


State 60

   49 rule: maxstack_via .

    $default  reduce using rule 49 (rule)


State 61

   17 rule: via .

    $default  reduce using rule 17 (rule)


State 62

  307 start_via: via_keyword . T_STRING
  308          | via_keyword . T_STRING K_DEFAULT
  309          | via_keyword . T_STRING K_GENERATED

    T_STRING  shift, and go to state 143


State 63

  305 via: start_via . $@48 via_option end_via

    $default  reduce using rule 304 ($@48)

    $@48  go to state 144


State 64

  371 viarule: viarule_keyword . viarule_layer_list via_names opt_viarule_props end_viarule
  373 viarule_generate: viarule_keyword . K_GENERATE viarule_generate_default $@59 viarule_layer_list opt_viarule_props end_viarule

    K_GENERATE  shift, and go to state 145
    K_LAYER     shift, and go to state 146

    viarule_layer_list  go to state 147
    viarule_layer       go to state 148
    viarule_layer_name  go to state 149


State 65

   18 rule: viarule .

    $default  reduce using rule 18 (rule)


State 66

   19 rule: viarule_generate .

    $default  reduce using rule 19 (rule)


State 67

   23 rule: spacing_rule .

    $default  reduce using rule 23 (rule)


State 68

  408 spacing_rule: start_spacing . spacings end_spacing

    $default  reduce using rule 411 (spacings)

    spacings  go to state 150


State 69

   26 rule: irdrop .

    $default  reduce using rule 26 (rule)


State 70

  418 irdrop: start_irdrop . ir_tables end_irdrop

    $default  reduce using rule 421 (ir_tables)

    ir_tables  go to state 151


State 71

   25 rule: minfeature .

    $default  reduce using rule 25 (rule)


State 72

   24 rule: dielectric .

    $default  reduce using rule 24 (rule)


State 73

   31 rule: nondefault_rule .

    $default  reduce using rule 31 (rule)


State 74

   27 rule: site .

    $default  reduce using rule 27 (rule)


State 75

  470 site: start_site . site_options end_site

    $default  reduce using rule 475 (site_options)

    site_options  go to state 152


State 76

   28 rule: macro .

    $default  reduce using rule 28 (rule)


State 77

  499 macro: start_macro . macro_options $@75 end_macro

    $default  reduce using rule 504 (macro_options)

    macro_options  go to state 153


State 78

   29 rule: array .

    $default  reduce using rule 29 (rule)


State 79

  817 array: start_array . array_rules $@107 end_array

    $default  reduce using rule 822 (array_rules)

    array_rules  go to state 154


State 80

   22 rule: msg_statement .

    $default  reduce using rule 22 (rule)


State 81

   50 rule: create_file_statement .

    $default  reduce using rule 50 (rule)


State 82

   30 rule: def_statement .

    $default  reduce using rule 30 (rule)


State 83

   32 rule: prop_def_section .

    $default  reduce using rule 32 (rule)


State 84

   33 rule: universalnoisemargin .

    $default  reduce using rule 33 (rule)


State 85

   34 rule: edgeratethreshold1 .

    $default  reduce using rule 34 (rule)


State 86

   36 rule: edgeratethreshold2 .

    $default  reduce using rule 36 (rule)


State 87

   35 rule: edgeratescalefactor .

    $default  reduce using rule 35 (rule)


State 88

   37 rule: noisetable .

    $default  reduce using rule 37 (rule)


State 89

   38 rule: correctiontable .

    $default  reduce using rule 38 (rule)


State 90

   39 rule: input_antenna .

    $default  reduce using rule 39 (rule)


State 91

   40 rule: output_antenna .

    $default  reduce using rule 40 (rule)


State 92

   41 rule: inout_antenna .

    $default  reduce using rule 41 (rule)


State 93

   42 rule: antenna_input .

    $default  reduce using rule 42 (rule)


State 94

   43 rule: antenna_inout .

    $default  reduce using rule 43 (rule)


State 95

   44 rule: antenna_output .

    $default  reduce using rule 44 (rule)


State 96

    1 lef_file: rules extension_opt . end_library

    K_END  shift, and go to state 155

    $default  reduce using rule 10 (end_library)

    end_library  go to state 156


State 97

  1018 extension_opt: extension .

    $default  reduce using rule 1018 (extension_opt)


State 98

  819 start_array: K_ARRAY $@108 . T_STRING

    T_STRING  shift, and go to state 157


State 99

    4 int_number: NUMBER .

    $default  reduce using rule 4 (int_number)


State 100

  429 dielectric: K_DIELECTRIC int_number . ';'

    ';'  shift, and go to state 158


State 101

   79 start_layer: K_LAYER $@2 . T_STRING

    T_STRING  shift, and go to state 159


State 102

  501 start_macro: K_MACRO $@76 . T_STRING

    T_STRING  shift, and go to state 160


State 103

  428 minfeature: K_MINFEATURE int_number . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 161


State 104

   52 case_sensitivity: K_NAMESCASESENSITIVE K_OFF . ';'

    ';'  shift, and go to state 162


State 105

   51 case_sensitivity: K_NAMESCASESENSITIVE K_ON . ';'

    ';'  shift, and go to state 163


State 106

  433 nondefault_rule: K_NONDEFAULTRULE $@63 . T_STRING $@64 nd_hardspacing nd_rules $@65 end_nd_rule

    T_STRING  shift, and go to state 164


State 107

  472 start_site: K_SITE $@71 . T_STRING

    T_STRING  shift, and go to state 165


State 108

  370 viarule_keyword: K_VIARULE $@58 . T_STRING

    T_STRING  shift, and go to state 166


State 109

  852 def_statement: K_DEFINE $@119 . T_STRING '=' expression dtrm

    T_STRING  shift, and go to state 167


State 110

  854 def_statement: K_DEFINES $@120 . T_STRING '=' s_expr dtrm

    T_STRING  shift, and go to state 168


State 111

  856 def_statement: K_DEFINEB $@121 . T_STRING '=' b_expr dtrm

    T_STRING  shift, and go to state 169


State 112

  901 prop_def_section: K_PROPDEF $@122 . prop_stmts K_END K_PROPDEF

    $default  reduce using rule 902 (prop_stmts)

    prop_stmts  go to state 170


State 113

    6 busbitchars: K_BUSBITCHARS QSTRING . ';'

    ';'  shift, and go to state 171


State 114

    3 version: K_VERSION $@1 . T_STRING ';'

    T_STRING  shift, and go to state 172


State 115

  973 universalnoisemargin: K_UNIVERSALNOISEMARGIN int_number . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 173


State 116

  974 edgeratethreshold1: K_EDGERATETHRESHOLD1 int_number . ';'

    ';'  shift, and go to state 174


State 117

  995 correctiontable: K_CORRECTIONTABLE int_number . ';' $@143 correction_table_list end_correctiontable dtrm

    ';'  shift, and go to state 175


State 118

  976 edgeratescalefactor: K_EDGERATESCALEFACTOR int_number . ';'

    ';'  shift, and go to state 176


State 119

  975 edgeratethreshold2: K_EDGERATETHRESHOLD2 int_number . ';'

    ';'  shift, and go to state 177


State 120

  978 noisetable: K_NOISETABLE int_number . $@140 ';' noise_table_list end_noisetable dtrm

    $default  reduce using rule 977 ($@140)

    $@140  go to state 178


State 121

  1012 output_antenna: K_OUTPUTPINANTENNASIZE int_number . ';'

    ';'  shift, and go to state 179


State 122

  1011 input_antenna: K_INPUTPINANTENNASIZE int_number . ';'

    ';'  shift, and go to state 180


State 123

  1013 inout_antenna: K_INOUTPINANTENNASIZE int_number . ';'

    ';'  shift, and go to state 181


State 124

    5 dividerchar: K_DIVIDERCHAR QSTRING . ';'

    ';'  shift, and go to state 182


State 125

   54 wireextension: K_NOWIREEXTENSIONATPIN K_OFF . ';'

    ';'  shift, and go to state 183


State 126

   53 wireextension: K_NOWIREEXTENSIONATPIN K_ON . ';'

    ';'  shift, and go to state 184


State 127

  848 msg_statement: K_MESSAGE $@117 . T_STRING '=' s_expr dtrm

    T_STRING  shift, and go to state 185


State 128

  850 create_file_statement: K_CREATEFILE $@118 . T_STRING '=' s_expr dtrm

    T_STRING  shift, and go to state 186


State 129

  1014 antenna_input: K_ANTENNAINPUTGATEAREA NUMBER . ';'

    ';'  shift, and go to state 187


State 130

  1015 antenna_inout: K_ANTENNAINOUTDIFFAREA NUMBER . ';'

    ';'  shift, and go to state 188


State 131

  1016 antenna_output: K_ANTENNAOUTPUTDIFFAREA NUMBER . ';'

    ';'  shift, and go to state 189


State 132

   56 manufacturing: K_MANUFACTURINGGRID int_number . ';'

    ';'  shift, and go to state 190


State 133

   55 fixedmask: K_FIXEDMASK ';' .

    $default  reduce using rule 55 (fixedmask)


State 134

   60 clearance_type: K_EUCLIDEAN .

    $default  reduce using rule 60 (clearance_type)


State 135

   59 clearance_type: K_MAXXY .

    $default  reduce using rule 59 (clearance_type)


State 136

   58 clearancemeasure: K_CLEARANCEMEASURE clearance_type . ';'

    ';'  shift, and go to state 191


State 137

   61 spacing_type: K_OBS .

    $default  reduce using rule 61 (spacing_type)


State 138

   62 spacing_type: K_PIN .

    $default  reduce using rule 62 (spacing_type)


State 139

   57 useminspacing: K_USEMINSPACING spacing_type . spacing_value ';'

    K_OFF  shift, and go to state 192
    K_ON   shift, and go to state 193

    spacing_value  go to state 194


State 140

  301 maxstack_via: K_MAXVIASTACK int_number . ';'
  303             | K_MAXVIASTACK int_number . K_RANGE $@47 T_STRING T_STRING ';'

    K_RANGE  shift, and go to state 195
    ';'      shift, and go to state 196


State 141

   65 units_section: start_units units_rules . K_END K_UNITS
   68 units_rules: units_rules . units_rule

    K_CAPACITANCE  shift, and go to state 197
    K_CURRENT      shift, and go to state 198
    K_DATABASE     shift, and go to state 199
    K_END          shift, and go to state 200
    K_POWER        shift, and go to state 201
    K_RESISTANCE   shift, and go to state 202
    K_TIME         shift, and go to state 203
    K_VOLTAGE      shift, and go to state 204
    K_FREQUENCY    shift, and go to state 205

    units_rule  go to state 206


State 142

   77 layer_rule: start_layer layer_options . end_layer
   83 layer_options: layer_options . layer_option

    K_AREA                         shift, and go to state 207
    K_CAPACITANCE                  shift, and go to state 208
    K_CAPMULTIPLIER                shift, and go to state 209
    K_DIRECTION                    shift, and go to state 210
    K_EDGECAPACITANCE              shift, and go to state 211
    K_END                          shift, and go to state 212
    K_HEIGHT                       shift, and go to state 213
    K_OFFSET                       shift, and go to state 214
    K_PITCH                        shift, and go to state 215
    K_RESISTANCE                   shift, and go to state 216
    K_SHRINKAGE                    shift, and go to state 217
    K_SPACING                      shift, and go to state 218
    K_THICKNESS                    shift, and go to state 219
    K_TYPE                         shift, and go to state 220
    K_WIDTH                        shift, and go to state 221
    K_PROPERTY                     shift, and go to state 222
    K_CURRENTDEN                   shift, and go to state 223
    K_ANTENNALENGTHFACTOR          shift, and go to state 224
    K_WIREEXTENSION                shift, and go to state 225
    K_ACCURRENTDENSITY             shift, and go to state 226
    K_DCCURRENTDENSITY             shift, and go to state 227
    K_ANTENNAAREARATIO             shift, and go to state 228
    K_ANTENNADIFFAREARATIO         shift, and go to state 229
    K_ANTENNACUMAREARATIO          shift, and go to state 230
    K_ANTENNACUMDIFFAREARATIO      shift, and go to state 231
    K_ANTENNAAREAFACTOR            shift, and go to state 232
    K_ANTENNASIDEAREARATIO         shift, and go to state 233
    K_ANTENNADIFFSIDEAREARATIO     shift, and go to state 234
    K_ANTENNACUMSIDEAREARATIO      shift, and go to state 235
    K_ANTENNACUMDIFFSIDEAREARATIO  shift, and go to state 236
    K_ANTENNASIDEAREAFACTOR        shift, and go to state 237
    K_SLOTWIREWIDTH                shift, and go to state 238
    K_SLOTWIRELENGTH               shift, and go to state 239
    K_SLOTWIDTH                    shift, and go to state 240
    K_SLOTLENGTH                   shift, and go to state 241
    K_MAXADJACENTSLOTSPACING       shift, and go to state 242
    K_MAXCOAXIALSLOTSPACING        shift, and go to state 243
    K_MAXEDGESLOTSPACING           shift, and go to state 244
    K_SPLITWIREWIDTH               shift, and go to state 245
    K_MINIMUMDENSITY               shift, and go to state 246
    K_MAXIMUMDENSITY               shift, and go to state 247
    K_DENSITYCHECKWINDOW           shift, and go to state 248
    K_DENSITYCHECKSTEP             shift, and go to state 249
    K_FILLACTIVESPACING            shift, and go to state 250
    K_MINIMUMCUT                   shift, and go to state 251
    K_ANTENNAMODEL                 shift, and go to state 252
    K_ENCLOSURE                    shift, and go to state 253
    K_MAXWIDTH                     shift, and go to state 254
    K_MINENCLOSEDAREA              shift, and go to state 255
    K_MINSTEP                      shift, and go to state 256
    K_MINWIDTH                     shift, and go to state 257
    K_PROTRUSIONWIDTH              shift, and go to state 258
    K_SPACINGTABLE                 shift, and go to state 259
    K_MASK                         shift, and go to state 260
    K_DIAGMINEDGELENGTH            shift, and go to state 261
    K_DIAGSPACING                  shift, and go to state 262
    K_DIAGPITCH                    shift, and go to state 263
    K_DIAGWIDTH                    shift, and go to state 264
    K_MINSIZE                      shift, and go to state 265
    K_PREFERENCLOSURE              shift, and go to state 266
    K_ARRAYSPACING                 shift, and go to state 267
    K_ANTENNAAREADIFFREDUCEPWL     shift, and go to state 268
    K_ANTENNAAREAMINUSDIFF         shift, and go to state 269
    K_ANTENNACUMROUTINGPLUSCUT     shift, and go to state 270
    K_ANTENNAGATEPLUSDIFF          shift, and go to state 271

    end_layer     go to state 272
    layer_option  go to state 273


State 143

  307 start_via: via_keyword T_STRING .
  308          | via_keyword T_STRING . K_DEFAULT
  309          | via_keyword T_STRING . K_GENERATED

    K_DEFAULT    shift, and go to state 274
    K_GENERATED  shift, and go to state 275

    $default  reduce using rule 307 (start_via)


State 144

  305 via: start_via $@48 . via_option end_via

    K_FOREIGN         shift, and go to state 276
    K_LAYER           shift, and go to state 277
    K_RESISTANCE      shift, and go to state 278
    K_TOPOFSTACKONLY  shift, and go to state 279
    K_VIARULE         shift, and go to state 280
    K_PROPERTY        shift, and go to state 281

    via_viarule        go to state 282
    via_option         go to state 283
    via_other_options  go to state 284
    via_other_option   go to state 285
    via_foreign        go to state 286
    start_foreign      go to state 287
    via_layer_rule     go to state 288
    via_layer          go to state 289


State 145

  373 viarule_generate: viarule_keyword K_GENERATE . viarule_generate_default $@59 viarule_layer_list opt_viarule_props end_viarule

    K_DEFAULT  shift, and go to state 290

    $default  reduce using rule 374 (viarule_generate_default)

    viarule_generate_default  go to state 291


State 146

  394 viarule_layer_name: K_LAYER . $@61 T_STRING ';'

    $default  reduce using rule 393 ($@61)

    $@61  go to state 292


State 147

  371 viarule: viarule_keyword viarule_layer_list . via_names opt_viarule_props end_viarule
  377 viarule_layer_list: viarule_layer_list . viarule_layer

    K_LAYER  shift, and go to state 146

    $default  reduce using rule 390 (via_names)

    viarule_layer       go to state 293
    via_names           go to state 294
    viarule_layer_name  go to state 149


State 148

  376 viarule_layer_list: viarule_layer .

    $default  reduce using rule 376 (viarule_layer_list)


State 149

  389 viarule_layer: viarule_layer_name . viarule_layer_options

    $default  reduce using rule 395 (viarule_layer_options)

    viarule_layer_options  go to state 295


State 150

  408 spacing_rule: start_spacing spacings . end_spacing
  412 spacings: spacings . spacing

    K_END      shift, and go to state 296
    K_SAMENET  shift, and go to state 297

    end_spacing      go to state 298
    spacing          go to state 299
    samenet_keyword  go to state 300


State 151

  418 irdrop: start_irdrop ir_tables . end_irdrop
  422 ir_tables: ir_tables . ir_table

    K_END    shift, and go to state 301
    K_TABLE  shift, and go to state 302

    end_irdrop    go to state 303
    ir_table      go to state 304
    ir_tablename  go to state 305


State 152

  470 site: start_site site_options . end_site
  476 site_options: site_options . site_option

    K_CLASS       shift, and go to state 306
    K_END         shift, and go to state 307
    K_SIZE        shift, and go to state 308
    K_SYMMETRY    shift, and go to state 309
    K_ROWPATTERN  shift, and go to state 310

    end_site                   go to state 311
    site_option                go to state 312
    site_class                 go to state 313
    site_symmetry_statement    go to state 314
    site_rowpattern_statement  go to state 315


State 153

  499 macro: start_macro macro_options . $@75 end_macro
  505 macro_options: macro_options . macro_option

    K_CLASS      shift, and go to state 316
    K_CLOCKTYPE  shift, and go to state 317
    K_EEQ        shift, and go to state 318
    K_FOREIGN    shift, and go to state 276
    K_GENERATE   shift, and go to state 319
    K_GENERATOR  shift, and go to state 320
    K_LEQ        shift, and go to state 321
    K_OBS        shift, and go to state 322
    K_ORIGIN     shift, and go to state 323
    K_PIN        shift, and go to state 324
    K_POWER      shift, and go to state 325
    K_SITE       shift, and go to state 326
    K_SIZE       shift, and go to state 327
    K_SOURCE     shift, and go to state 328
    K_SYMMETRY   shift, and go to state 329
    K_TIMING     shift, and go to state 330
    K_PROPERTY   shift, and go to state 331
    K_FUNCTION   shift, and go to state 332
    K_FIXEDMASK  shift, and go to state 333
    K_DENSITY    shift, and go to state 334

    $default  reduce using rule 498 ($@75)

    start_foreign             go to state 335
    $@75                      go to state 336
    macro_option              go to state 337
    macro_symmetry_statement  go to state 338
    macro_class               go to state 339
    macro_generator           go to state 340
    macro_generate            go to state 341
    macro_source              go to state 342
    macro_power               go to state 343
    macro_origin              go to state 344
    macro_foreign             go to state 345
    macro_fixedMask           go to state 346
    macro_eeq                 go to state 347
    macro_leq                 go to state 348
    macro_site                go to state 349
    macro_site_word           go to state 350
    macro_size                go to state 351
    macro_pin                 go to state 352
    start_macro_pin           go to state 353
    macro_obs                 go to state 354
    start_macro_obs           go to state 355
    macro_density             go to state 356
    macro_clocktype           go to state 357
    timing                    go to state 358
    start_timing              go to state 359


State 154

  817 array: start_array array_rules . $@107 end_array
  823 array_rules: array_rules . array_rule

    K_SITE          shift, and go to state 360
    K_CANPLACE      shift, and go to state 361
    K_CANNOTOCCUPY  shift, and go to state 362
    K_TRACKS        shift, and go to state 363
    K_FLOORPLAN     shift, and go to state 364
    K_GCELLGRID     shift, and go to state 365
    K_DEFAULTCAP    shift, and go to state 366
    K_DEFINE        shift, and go to state 18
    K_DEFINES       shift, and go to state 19
    K_DEFINEB       shift, and go to state 20

    $default  reduce using rule 816 ($@107)

    site_word        go to state 367
    $@107            go to state 368
    array_rule       go to state 369
    floorplan_start  go to state 370
    def_statement    go to state 371


State 155

   11 end_library: K_END . K_LIBRARY

    K_LIBRARY  shift, and go to state 372


State 156

    1 lef_file: rules extension_opt end_library .

    $default  reduce using rule 1 (lef_file)


State 157

  819 start_array: K_ARRAY $@108 T_STRING .

    $default  reduce using rule 819 (start_array)


State 158

  429 dielectric: K_DIELECTRIC int_number ';' .

    $default  reduce using rule 429 (dielectric)


State 159

   79 start_layer: K_LAYER $@2 T_STRING .

    $default  reduce using rule 79 (start_layer)


State 160

  501 start_macro: K_MACRO $@76 T_STRING .

    $default  reduce using rule 501 (start_macro)


State 161

  428 minfeature: K_MINFEATURE int_number int_number . ';'

    ';'  shift, and go to state 373


State 162

   52 case_sensitivity: K_NAMESCASESENSITIVE K_OFF ';' .

    $default  reduce using rule 52 (case_sensitivity)


State 163

   51 case_sensitivity: K_NAMESCASESENSITIVE K_ON ';' .

    $default  reduce using rule 51 (case_sensitivity)


State 164

  433 nondefault_rule: K_NONDEFAULTRULE $@63 T_STRING . $@64 nd_hardspacing nd_rules $@65 end_nd_rule

    $default  reduce using rule 431 ($@64)

    $@64  go to state 374


State 165

  472 start_site: K_SITE $@71 T_STRING .

    $default  reduce using rule 472 (start_site)


State 166

  370 viarule_keyword: K_VIARULE $@58 T_STRING .

    $default  reduce using rule 370 (viarule_keyword)


State 167

  852 def_statement: K_DEFINE $@119 T_STRING . '=' expression dtrm

    '='  shift, and go to state 375


State 168

  854 def_statement: K_DEFINES $@120 T_STRING . '=' s_expr dtrm

    '='  shift, and go to state 376


State 169

  856 def_statement: K_DEFINEB $@121 T_STRING . '=' b_expr dtrm

    '='  shift, and go to state 377


State 170

  901 prop_def_section: K_PROPDEF $@122 prop_stmts . K_END K_PROPDEF
  903 prop_stmts: prop_stmts . prop_stmt

    K_END             shift, and go to state 378
    K_COMPONENTPIN    shift, and go to state 379
    K_LAYER           shift, and go to state 380
    K_LIBRARY         shift, and go to state 381
    K_MACRO           shift, and go to state 382
    K_NONDEFAULTRULE  shift, and go to state 383
    K_PIN             shift, and go to state 384
    K_VIA             shift, and go to state 385
    K_VIARULE         shift, and go to state 386

    prop_stmt  go to state 387


State 171

    6 busbitchars: K_BUSBITCHARS QSTRING ';' .

    $default  reduce using rule 6 (busbitchars)


State 172

    3 version: K_VERSION $@1 T_STRING . ';'

    ';'  shift, and go to state 388


State 173

  973 universalnoisemargin: K_UNIVERSALNOISEMARGIN int_number int_number . ';'

    ';'  shift, and go to state 389


State 174

  974 edgeratethreshold1: K_EDGERATETHRESHOLD1 int_number ';' .

    $default  reduce using rule 974 (edgeratethreshold1)


State 175

  995 correctiontable: K_CORRECTIONTABLE int_number ';' . $@143 correction_table_list end_correctiontable dtrm

    $default  reduce using rule 994 ($@143)

    $@143  go to state 390


State 176

  976 edgeratescalefactor: K_EDGERATESCALEFACTOR int_number ';' .

    $default  reduce using rule 976 (edgeratescalefactor)


State 177

  975 edgeratethreshold2: K_EDGERATETHRESHOLD2 int_number ';' .

    $default  reduce using rule 975 (edgeratethreshold2)


State 178

  978 noisetable: K_NOISETABLE int_number $@140 . ';' noise_table_list end_noisetable dtrm

    ';'  shift, and go to state 391


State 179

  1012 output_antenna: K_OUTPUTPINANTENNASIZE int_number ';' .

    $default  reduce using rule 1012 (output_antenna)


State 180

  1011 input_antenna: K_INPUTPINANTENNASIZE int_number ';' .

    $default  reduce using rule 1011 (input_antenna)


State 181

  1013 inout_antenna: K_INOUTPINANTENNASIZE int_number ';' .

    $default  reduce using rule 1013 (inout_antenna)


State 182

    5 dividerchar: K_DIVIDERCHAR QSTRING ';' .

    $default  reduce using rule 5 (dividerchar)


State 183

   54 wireextension: K_NOWIREEXTENSIONATPIN K_OFF ';' .

    $default  reduce using rule 54 (wireextension)


State 184

   53 wireextension: K_NOWIREEXTENSIONATPIN K_ON ';' .

    $default  reduce using rule 53 (wireextension)


State 185

  848 msg_statement: K_MESSAGE $@117 T_STRING . '=' s_expr dtrm

    '='  shift, and go to state 392


State 186

  850 create_file_statement: K_CREATEFILE $@118 T_STRING . '=' s_expr dtrm

    '='  shift, and go to state 393


State 187

  1014 antenna_input: K_ANTENNAINPUTGATEAREA NUMBER ';' .

    $default  reduce using rule 1014 (antenna_input)


State 188

  1015 antenna_inout: K_ANTENNAINOUTDIFFAREA NUMBER ';' .

    $default  reduce using rule 1015 (antenna_inout)


State 189

  1016 antenna_output: K_ANTENNAOUTPUTDIFFAREA NUMBER ';' .

    $default  reduce using rule 1016 (antenna_output)


State 190

   56 manufacturing: K_MANUFACTURINGGRID int_number ';' .

    $default  reduce using rule 56 (manufacturing)


State 191

   58 clearancemeasure: K_CLEARANCEMEASURE clearance_type ';' .

    $default  reduce using rule 58 (clearancemeasure)


State 192

   64 spacing_value: K_OFF .

    $default  reduce using rule 64 (spacing_value)


State 193

   63 spacing_value: K_ON .

    $default  reduce using rule 63 (spacing_value)


State 194

   57 useminspacing: K_USEMINSPACING spacing_type spacing_value . ';'

    ';'  shift, and go to state 394


State 195

  303 maxstack_via: K_MAXVIASTACK int_number K_RANGE . $@47 T_STRING T_STRING ';'

    $default  reduce using rule 302 ($@47)

    $@47  go to state 395


State 196

  301 maxstack_via: K_MAXVIASTACK int_number ';' .

    $default  reduce using rule 301 (maxstack_via)


State 197

   70 units_rule: K_CAPACITANCE . K_PICOFARADS int_number ';'

    K_PICOFARADS  shift, and go to state 396


State 198

   73 units_rule: K_CURRENT . K_MILLIAMPS int_number ';'

    K_MILLIAMPS  shift, and go to state 397


State 199

   75 units_rule: K_DATABASE . K_MICRONS int_number ';'

    K_MICRONS  shift, and go to state 398


State 200

   65 units_section: start_units units_rules K_END . K_UNITS

    K_UNITS  shift, and go to state 399


State 201

   72 units_rule: K_POWER . K_MILLIWATTS int_number ';'

    K_MILLIWATTS  shift, and go to state 400


State 202

   71 units_rule: K_RESISTANCE . K_OHMS int_number ';'

    K_OHMS  shift, and go to state 401


State 203

   69 units_rule: K_TIME . K_NANOSECONDS int_number ';'

    K_NANOSECONDS  shift, and go to state 402


State 204

   74 units_rule: K_VOLTAGE . K_VOLTS int_number ';'

    K_VOLTS  shift, and go to state 403


State 205

   76 units_rule: K_FREQUENCY . K_MEGAHERTZ NUMBER ';'

    K_MEGAHERTZ  shift, and go to state 404


State 206

   68 units_rules: units_rules units_rule .

    $default  reduce using rule 68 (units_rules)


State 207

   98 layer_option: K_AREA . NUMBER ';'

    NUMBER  shift, and go to state 405


State 208

  106 layer_option: K_CAPACITANCE . K_CPERSQDIST int_number ';'
  107             | K_CAPACITANCE . K_CPERSQDIST K_PWL '(' cap_points ')' ';'

    K_CPERSQDIST  shift, and go to state 406


State 209

  112 layer_option: K_CAPMULTIPLIER . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 407


State 210

  103 layer_option: K_DIRECTION . layer_direction ';'

    K_HORIZONTAL  shift, and go to state 408
    K_VERTICAL    shift, and go to state 409
    K_DIAG45      shift, and go to state 410
    K_DIAG135     shift, and go to state 411

    layer_direction  go to state 412


State 211

  113 layer_option: K_EDGECAPACITANCE . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 413


State 212

   81 end_layer: K_END . $@3 T_STRING

    $default  reduce using rule 80 ($@3)

    $@3  go to state 414


State 213

  108 layer_option: K_HEIGHT . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 415


State 214

   93 layer_option: K_OFFSET . int_number ';'
   94             | K_OFFSET . int_number int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 416


State 215

   89 layer_option: K_PITCH . int_number ';'
   90             | K_PITCH . int_number int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 417


State 216

  104 layer_option: K_RESISTANCE . K_RPERSQ int_number ';'
  105             | K_RESISTANCE . K_RPERSQ K_PWL '(' res_points ')' ';'
  182             | K_RESISTANCE . int_number ';'

    K_RPERSQ  shift, and go to state 418
    NUMBER    shift, and go to state 99

    int_number  go to state 419


State 217

  111 layer_option: K_SHRINKAGE . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 420


State 218

  100 layer_option: K_SPACING . int_number $@6 layer_spacing_opts layer_spacing_cut_routing ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 421


State 219

  110 layer_option: K_THICKNESS . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 422


State 220

   87 layer_option: K_TYPE . layer_type ';'

    K_CUT          shift, and go to state 423
    K_OVERLAP      shift, and go to state 424
    K_ROUTING      shift, and go to state 425
    K_MASTERSLICE  shift, and go to state 426
    K_VIRTUAL      shift, and go to state 427
    K_IMPLANT      shift, and go to state 428

    layer_type  go to state 429


State 221

   97 layer_option: K_WIDTH . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 430


State 222

  119 layer_option: K_PROPERTY . $@8 layer_prop_list ';'

    $default  reduce using rule 118 ($@8)

    $@8  go to state 431


State 223

  115 layer_option: K_CURRENTDEN . int_number ';'
  116             | K_CURRENTDEN . K_PWL '(' current_density_pwl_list ')' ';'
  117             | K_CURRENTDEN . '(' int_number int_number ')' ';'

    NUMBER  shift, and go to state 99
    K_PWL   shift, and go to state 432
    '('     shift, and go to state 433

    int_number  go to state 434


State 224

  114 layer_option: K_ANTENNALENGTHFACTOR . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 435


State 225

  109 layer_option: K_WIREEXTENSION . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 436


State 226

  121 layer_option: K_ACCURRENTDENSITY . layer_table_type $@9 layer_frequency
  122             | K_ACCURRENTDENSITY . layer_table_type int_number ';'

    K_AVERAGE  shift, and go to state 437
    K_PEAK     shift, and go to state 438
    K_RMS      shift, and go to state 439

    layer_table_type  go to state 440


State 227

  123 layer_option: K_DCCURRENTDENSITY . K_AVERAGE int_number ';'
  126             | K_DCCURRENTDENSITY . K_AVERAGE K_CUTAREA NUMBER $@10 number_list ';' $@11 dc_layer_table
  129             | K_DCCURRENTDENSITY . K_AVERAGE K_WIDTH int_number $@12 int_number_list ';' $@13 dc_layer_table

    K_AVERAGE  shift, and go to state 441


State 228

  130 layer_option: K_ANTENNAAREARATIO . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 442


State 229

  132 layer_option: K_ANTENNADIFFAREARATIO . $@14 layer_antenna_pwl ';'

    $default  reduce using rule 131 ($@14)

    $@14  go to state 443


State 230

  133 layer_option: K_ANTENNACUMAREARATIO . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 444


State 231

  135 layer_option: K_ANTENNACUMDIFFAREARATIO . $@15 layer_antenna_pwl ';'

    $default  reduce using rule 134 ($@15)

    $@15  go to state 445


State 232

  137 layer_option: K_ANTENNAAREAFACTOR . int_number $@16 layer_antenna_duo ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 446


State 233

  138 layer_option: K_ANTENNASIDEAREARATIO . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 447


State 234

  140 layer_option: K_ANTENNADIFFSIDEAREARATIO . $@17 layer_antenna_pwl ';'

    $default  reduce using rule 139 ($@17)

    $@17  go to state 448


State 235

  141 layer_option: K_ANTENNACUMSIDEAREARATIO . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 449


State 236

  143 layer_option: K_ANTENNACUMDIFFSIDEAREARATIO . $@18 layer_antenna_pwl ';'

    $default  reduce using rule 142 ($@18)

    $@18  go to state 450


State 237

  145 layer_option: K_ANTENNASIDEAREAFACTOR . int_number $@19 layer_antenna_duo ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 451


State 238

  154 layer_option: K_SLOTWIREWIDTH . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 452


State 239

  155 layer_option: K_SLOTWIRELENGTH . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 453


State 240

  156 layer_option: K_SLOTWIDTH . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 454


State 241

  157 layer_option: K_SLOTLENGTH . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 455


State 242

  158 layer_option: K_MAXADJACENTSLOTSPACING . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 456


State 243

  159 layer_option: K_MAXCOAXIALSLOTSPACING . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 457


State 244

  160 layer_option: K_MAXEDGESLOTSPACING . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 458


State 245

  161 layer_option: K_SPLITWIREWIDTH . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 459


State 246

  162 layer_option: K_MINIMUMDENSITY . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 460


State 247

  163 layer_option: K_MAXIMUMDENSITY . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 461


State 248

  164 layer_option: K_DENSITYCHECKWINDOW . int_number int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 462


State 249

  165 layer_option: K_DENSITYCHECKSTEP . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 463


State 250

  166 layer_option: K_FILLACTIVESPACING . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 464


State 251

  172 layer_option: K_MINIMUMCUT . int_number K_WIDTH int_number $@24 layer_minimumcut_within layer_minimumcut_from layer_minimumcut_length ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 465


State 252

  147 layer_option: K_ANTENNAMODEL . $@20 layer_oxide ';'

    $default  reduce using rule 146 ($@20)

    $@20  go to state 466


State 253

  179 layer_option: K_ENCLOSURE . layer_enclosure_type_opt int_number int_number $@27 layer_enclosure_width_opt ';'

    K_ABOVE  shift, and go to state 467
    K_BELOW  shift, and go to state 468

    $default  reduce using rule 206 (layer_enclosure_type_opt)

    layer_enclosure_type_opt  go to state 469


State 254

  167 layer_option: K_MAXWIDTH . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 470


State 255

  170 layer_option: K_MINENCLOSEDAREA . NUMBER $@23 layer_minen_width ';'

    NUMBER  shift, and go to state 471


State 256

  174 layer_option: K_MINSTEP . int_number $@25 layer_minstep_options ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 472


State 257

  168 layer_option: K_MINWIDTH . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 473


State 258

  175 layer_option: K_PROTRUSIONWIDTH . int_number K_LENGTH int_number K_WIDTH int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 474


State 259

  102 layer_option: K_SPACINGTABLE . K_ORTHOGONAL K_WITHIN int_number K_SPACING int_number $@7 layer_spacingtable_opts ';'
  177             | K_SPACINGTABLE . $@26 sp_options ';'

    K_ORTHOGONAL  shift, and go to state 475

    $default  reduce using rule 176 ($@26)

    $@26  go to state 476


State 260

   88 layer_option: K_MASK . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 477


State 261

  183 layer_option: K_DIAGMINEDGELENGTH . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 478


State 262

   96 layer_option: K_DIAGSPACING . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 479


State 263

   91 layer_option: K_DIAGPITCH . int_number ';'
   92             | K_DIAGPITCH . int_number int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 480


State 264

   95 layer_option: K_DIAGWIDTH . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 481


State 265

  185 layer_option: K_MINSIZE . $@29 firstPt otherPts ';'

    $default  reduce using rule 184 ($@29)

    $@29  go to state 482


State 266

  181 layer_option: K_PREFERENCLOSURE . layer_enclosure_type_opt int_number int_number $@28 layer_preferenclosure_width_opt ';'

    K_ABOVE  shift, and go to state 467
    K_BELOW  shift, and go to state 468

    $default  reduce using rule 206 (layer_enclosure_type_opt)

    layer_enclosure_type_opt  go to state 483


State 267

   86 layer_option: K_ARRAYSPACING . $@4 layer_arraySpacing_long layer_arraySpacing_width K_CUTSPACING int_number $@5 layer_arraySpacing_arraycuts ';'

    $default  reduce using rule 84 ($@4)

    $@4  go to state 484


State 268

  153 layer_option: K_ANTENNAAREADIFFREDUCEPWL . '(' pt pt $@21 layer_diffusion_ratios ')' ';' $@22

    '('  shift, and go to state 485


State 269

  150 layer_option: K_ANTENNAAREAMINUSDIFF . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 486


State 270

  148 layer_option: K_ANTENNACUMROUTINGPLUSCUT . ';'

    ';'  shift, and go to state 487


State 271

  149 layer_option: K_ANTENNAGATEPLUSDIFF . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 488


State 272

   77 layer_rule: start_layer layer_options end_layer .

    $default  reduce using rule 77 (layer_rule)


State 273

   83 layer_options: layer_options layer_option .

    $default  reduce using rule 83 (layer_options)


State 274

  308 start_via: via_keyword T_STRING K_DEFAULT .

    $default  reduce using rule 308 (start_via)


State 275

  309 start_via: via_keyword T_STRING K_GENERATED .

    $default  reduce using rule 309 (start_via)


State 276

  342 start_foreign: K_FOREIGN . $@54 T_STRING

    $default  reduce using rule 341 ($@54)

    $@54  go to state 489


State 277

  361 via_layer: K_LAYER . $@55 T_STRING ';'

    $default  reduce using rule 360 ($@55)

    $@55  go to state 490


State 278

  328 via_other_option: K_RESISTANCE . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 491


State 279

  331 via_other_option: K_TOPOFSTACKONLY .

    $default  reduce using rule 331 (via_other_option)


State 280

  313 via_viarule: K_VIARULE . $@49 T_STRING ';' K_CUTSIZE int_number int_number ';' K_LAYERS $@50 T_STRING T_STRING T_STRING ';' K_CUTSPACING int_number int_number ';' K_ENCLOSURE int_number int_number int_number int_number ';' $@51 via_viarule_options

    $default  reduce using rule 310 ($@49)

    $@49  go to state 492


State 281

  330 via_other_option: K_PROPERTY . $@53 via_prop_list ';'

    $default  reduce using rule 329 ($@53)

    $@53  go to state 493


State 282

  321 via_option: via_viarule .

    $default  reduce using rule 321 (via_option)


State 283

  305 via: start_via $@48 via_option . end_via

    K_END  shift, and go to state 494

    end_via  go to state 495


State 284

  322 via_option: via_other_options .

    $default  reduce using rule 322 (via_option)


State 285

  323 via_other_options: via_other_option . via_more_options

    $default  reduce using rule 324 (via_more_options)

    via_more_options  go to state 496


State 286

  326 via_other_option: via_foreign .

    $default  reduce using rule 326 (via_other_option)


State 287

  337 via_foreign: start_foreign . ';'
  338            | start_foreign . pt ';'
  339            | start_foreign . pt orientation ';'
  340            | start_foreign . orientation ';'

    NUMBER   shift, and go to state 99
    K_N      shift, and go to state 497
    K_S      shift, and go to state 498
    K_E      shift, and go to state 499
    K_W      shift, and go to state 500
    K_FN     shift, and go to state 501
    K_FS     shift, and go to state 502
    K_FE     shift, and go to state 503
    K_FW     shift, and go to state 504
    K_R0     shift, and go to state 505
    K_R90    shift, and go to state 506
    K_R180   shift, and go to state 507
    K_R270   shift, and go to state 508
    K_MX     shift, and go to state 509
    K_MY     shift, and go to state 510
    K_MXR90  shift, and go to state 511
    K_MYR90  shift, and go to state 512
    ';'      shift, and go to state 513
    '('      shift, and go to state 514

    int_number   go to state 515
    orientation  go to state 516
    pt           go to state 517


State 288

  327 via_other_option: via_layer_rule .

    $default  reduce using rule 327 (via_other_option)


State 289

  359 via_layer_rule: via_layer . via_geometries

    $default  reduce using rule 362 (via_geometries)

    via_geometries  go to state 518


State 290

  375 viarule_generate_default: K_DEFAULT .

    $default  reduce using rule 375 (viarule_generate_default)


State 291

  373 viarule_generate: viarule_keyword K_GENERATE viarule_generate_default . $@59 viarule_layer_list opt_viarule_props end_viarule

    $default  reduce using rule 372 ($@59)

    $@59  go to state 519


State 292

  394 viarule_layer_name: K_LAYER $@61 . T_STRING ';'

    T_STRING  shift, and go to state 520


State 293

  377 viarule_layer_list: viarule_layer_list viarule_layer .

    $default  reduce using rule 377 (viarule_layer_list)


State 294

  371 viarule: viarule_keyword viarule_layer_list via_names . opt_viarule_props end_viarule
  391 via_names: via_names . via_name

    K_VIA       shift, and go to state 16
    T_STRING    shift, and go to state 521
    K_PROPERTY  shift, and go to state 522

    $default  reduce using rule 378 (opt_viarule_props)

    via_keyword        go to state 523
    opt_viarule_props  go to state 524
    viarule_props      go to state 525
    viarule_prop       go to state 526
    via_name           go to state 527


State 295

  389 viarule_layer: viarule_layer_name viarule_layer_options .
  396 viarule_layer_options: viarule_layer_options . viarule_layer_option

    K_DIRECTION      shift, and go to state 528
    K_METALOVERHANG  shift, and go to state 529
    K_OVERHANG       shift, and go to state 530
    K_RECT           shift, and go to state 531
    K_RESISTANCE     shift, and go to state 532
    K_SPACING        shift, and go to state 533
    K_WIDTH          shift, and go to state 534
    K_ENCLOSURE      shift, and go to state 535

    $default  reduce using rule 389 (viarule_layer)

    viarule_layer_option  go to state 536


State 296

  410 end_spacing: K_END . K_SPACING

    K_SPACING  shift, and go to state 537


State 297

  415 samenet_keyword: K_SAMENET .

    $default  reduce using rule 415 (samenet_keyword)


State 298

  408 spacing_rule: start_spacing spacings end_spacing .

    $default  reduce using rule 408 (spacing_rule)


State 299

  412 spacings: spacings spacing .

    $default  reduce using rule 412 (spacings)


State 300

  413 spacing: samenet_keyword . T_STRING T_STRING int_number ';'
  414        | samenet_keyword . T_STRING T_STRING int_number K_STACK ';'

    T_STRING  shift, and go to state 538


State 301

  420 end_irdrop: K_END . K_IRDROP

    K_IRDROP  shift, and go to state 539


State 302

  427 ir_tablename: K_TABLE . T_STRING

    T_STRING  shift, and go to state 540


State 303

  418 irdrop: start_irdrop ir_tables end_irdrop .

    $default  reduce using rule 418 (irdrop)


State 304

  422 ir_tables: ir_tables ir_table .

    $default  reduce using rule 422 (ir_tables)


State 305

  423 ir_table: ir_tablename . ir_table_values ';'

    $default  reduce using rule 424 (ir_table_values)

    ir_table_values  go to state 541


State 306

  481 site_class: K_CLASS . K_PAD ';'
  482           | K_CLASS . K_CORE ';'
  483           | K_CLASS . K_VIRTUAL ';'

    K_CORE     shift, and go to state 542
    K_PAD      shift, and go to state 543
    K_VIRTUAL  shift, and go to state 544


State 307

  474 end_site: K_END . $@72 T_STRING

    $default  reduce using rule 473 ($@72)

    $@72  go to state 545


State 308

  477 site_option: K_SIZE . int_number K_BY int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 546


State 309

  484 site_symmetry_statement: K_SYMMETRY . site_symmetries ';'

    $default  reduce using rule 485 (site_symmetries)

    site_symmetries  go to state 547


State 310

  491 site_rowpattern_statement: K_ROWPATTERN . $@73 site_rowpatterns ';'

    $default  reduce using rule 490 ($@73)

    $@73  go to state 548


State 311

  470 site: start_site site_options end_site .

    $default  reduce using rule 470 (site)


State 312

  476 site_options: site_options site_option .

    $default  reduce using rule 476 (site_options)


State 313

  479 site_option: site_class .

    $default  reduce using rule 479 (site_option)


State 314

  478 site_option: site_symmetry_statement .

    $default  reduce using rule 478 (site_option)


State 315

  480 site_option: site_rowpattern_statement .

    $default  reduce using rule 480 (site_option)


State 316

  539 macro_class: K_CLASS . class_type ';'

    K_BLOCK    shift, and go to state 549
    K_CORE     shift, and go to state 550
    K_CORNER   shift, and go to state 551
    K_COVER    shift, and go to state 552
    K_ENDCAP   shift, and go to state 553
    K_PAD      shift, and go to state 554
    K_RING     shift, and go to state 555
    K_VIRTUAL  shift, and go to state 556
    K_NONE     shift, and go to state 557
    K_BUMP     shift, and go to state 558

    class_type  go to state 559


State 317

  753 macro_clocktype: K_CLOCKTYPE . $@103 T_STRING ';'

    $default  reduce using rule 752 ($@103)

    $@103  go to state 560


State 318

  586 macro_eeq: K_EEQ . $@79 T_STRING ';'

    $default  reduce using rule 585 ($@79)

    $@79  go to state 561


State 319

  574 macro_generate: K_GENERATE . T_STRING T_STRING ';'

    T_STRING  shift, and go to state 562


State 320

  573 macro_generator: K_GENERATOR . T_STRING ';'

    T_STRING  shift, and go to state 563


State 321

  588 macro_leq: K_LEQ . $@80 T_STRING ';'

    $default  reduce using rule 587 ($@80)

    $@80  go to state 564


State 322

  742 start_macro_obs: K_OBS .

    $default  reduce using rule 742 (start_macro_obs)


State 323

  579 macro_origin: K_ORIGIN . pt ';'

    NUMBER  shift, and go to state 99
    '('     shift, and go to state 514

    int_number  go to state 515
    pt          go to state 565


State 324

  596 start_macro_pin: K_PIN . $@81 T_STRING

    $default  reduce using rule 595 ($@81)

    $@81  go to state 566


State 325

  578 macro_power: K_POWER . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 567


State 326

  591 macro_site_word: K_SITE .

    $default  reduce using rule 591 (macro_site_word)


State 327

  593 macro_size: K_SIZE . int_number K_BY int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 568


State 328

  575 macro_source: K_SOURCE . K_USER ';'
  576             | K_SOURCE . K_GENERATE ';'
  577             | K_SOURCE . K_BLOCK ';'

    K_BLOCK     shift, and go to state 569
    K_GENERATE  shift, and go to state 570
    K_USER      shift, and go to state 571


State 329

  530 macro_symmetry_statement: K_SYMMETRY . macro_symmetries ';'

    $default  reduce using rule 531 (macro_symmetries)

    macro_symmetries  go to state 572


State 330

  755 start_timing: K_TIMING .

    $default  reduce using rule 755 (start_timing)


State 331

  527 macro_option: K_PROPERTY . $@78 macro_prop_list ';'

    $default  reduce using rule 526 ($@78)

    $@78  go to state 573


State 332

  520 macro_option: K_FUNCTION . K_BUFFER ';'
  521             | K_FUNCTION . K_INVERTER ';'

    K_BUFFER    shift, and go to state 574
    K_INVERTER  shift, and go to state 575


State 333

  584 macro_fixedMask: K_FIXEDMASK . ';'

    ';'  shift, and go to state 576


State 334

  743 macro_density: K_DENSITY . density_layer density_layers K_END

    K_LAYER  shift, and go to state 577

    density_layer  go to state 578


State 335

  580 macro_foreign: start_foreign . ';'
  581              | start_foreign . pt ';'
  582              | start_foreign . pt orientation ';'
  583              | start_foreign . orientation ';'

    NUMBER   shift, and go to state 99
    K_N      shift, and go to state 497
    K_S      shift, and go to state 498
    K_E      shift, and go to state 499
    K_W      shift, and go to state 500
    K_FN     shift, and go to state 501
    K_FS     shift, and go to state 502
    K_FE     shift, and go to state 503
    K_FW     shift, and go to state 504
    K_R0     shift, and go to state 505
    K_R90    shift, and go to state 506
    K_R180   shift, and go to state 507
    K_R270   shift, and go to state 508
    K_MX     shift, and go to state 509
    K_MY     shift, and go to state 510
    K_MXR90  shift, and go to state 511
    K_MYR90  shift, and go to state 512
    ';'      shift, and go to state 579
    '('      shift, and go to state 514

    int_number   go to state 515
    orientation  go to state 580
    pt           go to state 581


State 336

  499 macro: start_macro macro_options $@75 . end_macro

    K_END  shift, and go to state 582

    end_macro  go to state 583


State 337

  505 macro_options: macro_options macro_option .

    $default  reduce using rule 505 (macro_options)


State 338

  510 macro_option: macro_symmetry_statement .

    $default  reduce using rule 510 (macro_option)


State 339

  506 macro_option: macro_class .

    $default  reduce using rule 506 (macro_option)


State 340

  507 macro_option: macro_generator .

    $default  reduce using rule 507 (macro_option)


State 341

  508 macro_option: macro_generate .

    $default  reduce using rule 508 (macro_option)


State 342

  509 macro_option: macro_source .

    $default  reduce using rule 509 (macro_option)


State 343

  513 macro_option: macro_power .

    $default  reduce using rule 513 (macro_option)


State 344

  512 macro_option: macro_origin .

    $default  reduce using rule 512 (macro_option)


State 345

  514 macro_option: macro_foreign .

    $default  reduce using rule 514 (macro_option)


State 346

  511 macro_option: macro_fixedMask .

    $default  reduce using rule 511 (macro_option)


State 347

  515 macro_option: macro_eeq .

    $default  reduce using rule 515 (macro_option)


State 348

  516 macro_option: macro_leq .

    $default  reduce using rule 516 (macro_option)


State 349

  518 macro_option: macro_site .

    $default  reduce using rule 518 (macro_option)


State 350

  589 macro_site: macro_site_word . T_STRING ';'
  590           | macro_site_word . sitePattern ';'

    T_STRING  shift, and go to state 584

    sitePattern  go to state 585


State 351

  517 macro_option: macro_size .

    $default  reduce using rule 517 (macro_option)


State 352

  519 macro_option: macro_pin .

    $default  reduce using rule 519 (macro_option)


State 353

  594 macro_pin: start_macro_pin . macro_pin_options end_macro_pin

    $default  reduce using rule 599 (macro_pin_options)

    macro_pin_options  go to state 586


State 354

  522 macro_option: macro_obs .

    $default  reduce using rule 522 (macro_option)


State 355

  740 macro_obs: start_macro_obs . geometries K_END
  741          | start_macro_obs . K_END

    K_END      shift, and go to state 587
    K_LAYER    shift, and go to state 588
    K_PATH     shift, and go to state 589
    K_POLYGON  shift, and go to state 590
    K_RECT     shift, and go to state 591
    K_VIA      shift, and go to state 592
    K_WIDTH    shift, and go to state 593

    geometries     go to state 594
    geometry       go to state 595
    via_placement  go to state 596


State 356

  523 macro_option: macro_density .

    $default  reduce using rule 523 (macro_option)


State 357

  524 macro_option: macro_clocktype .

    $default  reduce using rule 524 (macro_option)


State 358

  525 macro_option: timing .

    $default  reduce using rule 525 (macro_option)


State 359

  754 timing: start_timing . timing_options end_timing

    $default  reduce using rule 757 (timing_options)

    timing_options  go to state 597


State 360

  592 site_word: K_SITE .

    $default  reduce using rule 592 (site_word)


State 361

  827 array_rule: K_CANPLACE . $@111 sitePattern ';'

    $default  reduce using rule 826 ($@111)

    $@111  go to state 598


State 362

  829 array_rule: K_CANNOTOCCUPY . $@112 sitePattern ';'

    $default  reduce using rule 828 ($@112)

    $@112  go to state 599


State 363

  831 array_rule: K_TRACKS . $@113 trackPattern ';'

    $default  reduce using rule 830 ($@113)

    $@113  go to state 600


State 364

  837 floorplan_start: K_FLOORPLAN . T_STRING

    T_STRING  shift, and go to state 601


State 365

  834 array_rule: K_GCELLGRID . $@114 gcellPattern ';'

    $default  reduce using rule 833 ($@114)

    $@114  go to state 602


State 366

  835 array_rule: K_DEFAULTCAP . int_number cap_list K_END K_DEFAULTCAP

    NUMBER  shift, and go to state 99

    int_number  go to state 603


State 367

  825 array_rule: site_word . $@110 sitePattern ';'

    $default  reduce using rule 824 ($@110)

    $@110  go to state 604


State 368

  817 array: start_array array_rules $@107 . end_array

    K_END  shift, and go to state 605

    end_array  go to state 606


State 369

  823 array_rules: array_rules array_rule .

    $default  reduce using rule 823 (array_rules)


State 370

  832 array_rule: floorplan_start . floorplan_list K_END T_STRING

    $default  reduce using rule 838 (floorplan_list)

    floorplan_list  go to state 607


State 371

  836 array_rule: def_statement .

    $default  reduce using rule 836 (array_rule)


State 372

   11 end_library: K_END K_LIBRARY .

    $default  reduce using rule 11 (end_library)


State 373

  428 minfeature: K_MINFEATURE int_number int_number ';' .

    $default  reduce using rule 428 (minfeature)


State 374

  433 nondefault_rule: K_NONDEFAULTRULE $@63 T_STRING $@64 . nd_hardspacing nd_rules $@65 end_nd_rule

    K_HARDSPACING  shift, and go to state 608

    $default  reduce using rule 436 (nd_hardspacing)

    nd_hardspacing  go to state 609


State 375

  852 def_statement: K_DEFINE $@119 T_STRING '=' . expression dtrm

    NUMBER  shift, and go to state 99
    K_IF    shift, and go to state 610
    '-'     shift, and go to state 611
    '('     shift, and go to state 612

    int_number  go to state 613
    expression  go to state 614


State 376

  854 def_statement: K_DEFINES $@120 T_STRING '=' . s_expr dtrm

    QSTRING  shift, and go to state 615
    K_IF     shift, and go to state 616
    '('      shift, and go to state 617

    s_expr  go to state 618


State 377

  856 def_statement: K_DEFINEB $@121 T_STRING '=' . b_expr dtrm

    QSTRING  shift, and go to state 615
    NUMBER   shift, and go to state 99
    K_IF     shift, and go to state 619
    K_FALSE  shift, and go to state 620
    K_TRUE   shift, and go to state 621
    K_NOT    shift, and go to state 622
    '-'      shift, and go to state 611
    '('      shift, and go to state 623

    int_number  go to state 613
    expression  go to state 624
    b_expr      go to state 625
    s_expr      go to state 626


State 378

  901 prop_def_section: K_PROPDEF $@122 prop_stmts K_END . K_PROPDEF

    K_PROPDEF  shift, and go to state 627


State 379

  907 prop_stmt: K_COMPONENTPIN . $@124 T_STRING prop_define ';'

    $default  reduce using rule 906 ($@124)

    $@124  go to state 628


State 380

  917 prop_stmt: K_LAYER . $@129 T_STRING prop_define ';'

    $default  reduce using rule 916 ($@129)

    $@129  go to state 629


State 381

  905 prop_stmt: K_LIBRARY . $@123 T_STRING prop_define ';'

    $default  reduce using rule 904 ($@123)

    $@123  go to state 630


State 382

  911 prop_stmt: K_MACRO . $@126 T_STRING prop_define ';'

    $default  reduce using rule 910 ($@126)

    $@126  go to state 631


State 383

  919 prop_stmt: K_NONDEFAULTRULE . $@130 T_STRING prop_define ';'

    $default  reduce using rule 918 ($@130)

    $@130  go to state 632


State 384

  909 prop_stmt: K_PIN . $@125 T_STRING prop_define ';'

    $default  reduce using rule 908 ($@125)

    $@125  go to state 633


State 385

  913 prop_stmt: K_VIA . $@127 T_STRING prop_define ';'

    $default  reduce using rule 912 ($@127)

    $@127  go to state 634


State 386

  915 prop_stmt: K_VIARULE . $@128 T_STRING prop_define ';'

    $default  reduce using rule 914 ($@128)

    $@128  go to state 635


State 387

  903 prop_stmts: prop_stmts prop_stmt .

    $default  reduce using rule 903 (prop_stmts)


State 388

    3 version: K_VERSION $@1 T_STRING ';' .

    $default  reduce using rule 3 (version)


State 389

  973 universalnoisemargin: K_UNIVERSALNOISEMARGIN int_number int_number ';' .

    $default  reduce using rule 973 (universalnoisemargin)


State 390

  995 correctiontable: K_CORRECTIONTABLE int_number ';' $@143 . correction_table_list end_correctiontable dtrm

    K_EDGERATE          shift, and go to state 636
    K_OUTPUTRESISTANCE  shift, and go to state 637

    correction_table_list  go to state 638
    correction_table_item  go to state 639
    output_list            go to state 640


State 391

  978 noisetable: K_NOISETABLE int_number $@140 ';' . noise_table_list end_noisetable dtrm

    K_EDGERATE          shift, and go to state 641
    K_OUTPUTRESISTANCE  shift, and go to state 642

    noise_table_list         go to state 643
    noise_table_entry        go to state 644
    output_resistance_entry  go to state 645


State 392

  848 msg_statement: K_MESSAGE $@117 T_STRING '=' . s_expr dtrm

    QSTRING  shift, and go to state 615
    K_IF     shift, and go to state 616
    '('      shift, and go to state 617

    s_expr  go to state 646


State 393

  850 create_file_statement: K_CREATEFILE $@118 T_STRING '=' . s_expr dtrm

    QSTRING  shift, and go to state 615
    K_IF     shift, and go to state 616
    '('      shift, and go to state 617

    s_expr  go to state 647


State 394

   57 useminspacing: K_USEMINSPACING spacing_type spacing_value ';' .

    $default  reduce using rule 57 (useminspacing)


State 395

  303 maxstack_via: K_MAXVIASTACK int_number K_RANGE $@47 . T_STRING T_STRING ';'

    T_STRING  shift, and go to state 648


State 396

   70 units_rule: K_CAPACITANCE K_PICOFARADS . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 649


State 397

   73 units_rule: K_CURRENT K_MILLIAMPS . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 650


State 398

   75 units_rule: K_DATABASE K_MICRONS . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 651


State 399

   65 units_section: start_units units_rules K_END K_UNITS .

    $default  reduce using rule 65 (units_section)


State 400

   72 units_rule: K_POWER K_MILLIWATTS . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 652


State 401

   71 units_rule: K_RESISTANCE K_OHMS . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 653


State 402

   69 units_rule: K_TIME K_NANOSECONDS . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 654


State 403

   74 units_rule: K_VOLTAGE K_VOLTS . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 655


State 404

   76 units_rule: K_FREQUENCY K_MEGAHERTZ . NUMBER ';'

    NUMBER  shift, and go to state 656


State 405

   98 layer_option: K_AREA NUMBER . ';'

    ';'  shift, and go to state 657


State 406

  106 layer_option: K_CAPACITANCE K_CPERSQDIST . int_number ';'
  107             | K_CAPACITANCE K_CPERSQDIST . K_PWL '(' cap_points ')' ';'

    NUMBER  shift, and go to state 99
    K_PWL   shift, and go to state 658

    int_number  go to state 659


State 407

  112 layer_option: K_CAPMULTIPLIER int_number . ';'

    ';'  shift, and go to state 660


State 408

  278 layer_direction: K_HORIZONTAL .

    $default  reduce using rule 278 (layer_direction)


State 409

  279 layer_direction: K_VERTICAL .

    $default  reduce using rule 279 (layer_direction)


State 410

  280 layer_direction: K_DIAG45 .

    $default  reduce using rule 280 (layer_direction)


State 411

  281 layer_direction: K_DIAG135 .

    $default  reduce using rule 281 (layer_direction)


State 412

  103 layer_option: K_DIRECTION layer_direction . ';'

    ';'  shift, and go to state 661


State 413

  113 layer_option: K_EDGECAPACITANCE int_number . ';'

    ';'  shift, and go to state 662


State 414

   81 end_layer: K_END $@3 . T_STRING

    T_STRING  shift, and go to state 663


State 415

  108 layer_option: K_HEIGHT int_number . ';'

    ';'  shift, and go to state 664


State 416

   93 layer_option: K_OFFSET int_number . ';'
   94             | K_OFFSET int_number . int_number ';'

    NUMBER  shift, and go to state 99
    ';'     shift, and go to state 665

    int_number  go to state 666


State 417

   89 layer_option: K_PITCH int_number . ';'
   90             | K_PITCH int_number . int_number ';'

    NUMBER  shift, and go to state 99
    ';'     shift, and go to state 667

    int_number  go to state 668


State 418

  104 layer_option: K_RESISTANCE K_RPERSQ . int_number ';'
  105             | K_RESISTANCE K_RPERSQ . K_PWL '(' res_points ')' ';'

    NUMBER  shift, and go to state 99
    K_PWL   shift, and go to state 669

    int_number  go to state 670


State 419

  182 layer_option: K_RESISTANCE int_number . ';'

    ';'  shift, and go to state 671


State 420

  111 layer_option: K_SHRINKAGE int_number . ';'

    ';'  shift, and go to state 672


State 421

  100 layer_option: K_SPACING int_number . $@6 layer_spacing_opts layer_spacing_cut_routing ';'

    $default  reduce using rule 99 ($@6)

    $@6  go to state 673


State 422

  110 layer_option: K_THICKNESS int_number . ';'

    ';'  shift, and go to state 674


State 423

  273 layer_type: K_CUT .

    $default  reduce using rule 273 (layer_type)


State 424

  274 layer_type: K_OVERLAP .

    $default  reduce using rule 274 (layer_type)


State 425

  272 layer_type: K_ROUTING .

    $default  reduce using rule 272 (layer_type)


State 426

  275 layer_type: K_MASTERSLICE .

    $default  reduce using rule 275 (layer_type)


State 427

  276 layer_type: K_VIRTUAL .

    $default  reduce using rule 276 (layer_type)


State 428

  277 layer_type: K_IMPLANT .

    $default  reduce using rule 277 (layer_type)


State 429

   87 layer_option: K_TYPE layer_type . ';'

    ';'  shift, and go to state 675


State 430

   97 layer_option: K_WIDTH int_number . ';'

    ';'  shift, and go to state 676


State 431

  119 layer_option: K_PROPERTY $@8 . layer_prop_list ';'

    T_STRING  shift, and go to state 677

    layer_prop_list  go to state 678
    layer_prop       go to state 679


State 432

  116 layer_option: K_CURRENTDEN K_PWL . '(' current_density_pwl_list ')' ';'

    '('  shift, and go to state 680


State 433

  117 layer_option: K_CURRENTDEN '(' . int_number int_number ')' ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 681


State 434

  115 layer_option: K_CURRENTDEN int_number . ';'

    ';'  shift, and go to state 682


State 435

  114 layer_option: K_ANTENNALENGTHFACTOR int_number . ';'

    ';'  shift, and go to state 683


State 436

  109 layer_option: K_WIREEXTENSION int_number . ';'

    ';'  shift, and go to state 684


State 437

  241 layer_table_type: K_AVERAGE .

    $default  reduce using rule 241 (layer_table_type)


State 438

  240 layer_table_type: K_PEAK .

    $default  reduce using rule 240 (layer_table_type)


State 439

  242 layer_table_type: K_RMS .

    $default  reduce using rule 242 (layer_table_type)


State 440

  121 layer_option: K_ACCURRENTDENSITY layer_table_type . $@9 layer_frequency
  122             | K_ACCURRENTDENSITY layer_table_type . int_number ';'

    NUMBER  shift, and go to state 99

    $default  reduce using rule 120 ($@9)

    int_number  go to state 685
    $@9         go to state 686


State 441

  123 layer_option: K_DCCURRENTDENSITY K_AVERAGE . int_number ';'
  126             | K_DCCURRENTDENSITY K_AVERAGE . K_CUTAREA NUMBER $@10 number_list ';' $@11 dc_layer_table
  129             | K_DCCURRENTDENSITY K_AVERAGE . K_WIDTH int_number $@12 int_number_list ';' $@13 dc_layer_table

    K_WIDTH    shift, and go to state 687
    NUMBER     shift, and go to state 99
    K_CUTAREA  shift, and go to state 688

    int_number  go to state 689


State 442

  130 layer_option: K_ANTENNAAREARATIO int_number . ';'

    ';'  shift, and go to state 690


State 443

  132 layer_option: K_ANTENNADIFFAREARATIO $@14 . layer_antenna_pwl ';'

    NUMBER  shift, and go to state 99
    K_PWL   shift, and go to state 691

    int_number         go to state 692
    layer_antenna_pwl  go to state 693


State 444

  133 layer_option: K_ANTENNACUMAREARATIO int_number . ';'

    ';'  shift, and go to state 694


State 445

  135 layer_option: K_ANTENNACUMDIFFAREARATIO $@15 . layer_antenna_pwl ';'

    NUMBER  shift, and go to state 99
    K_PWL   shift, and go to state 691

    int_number         go to state 692
    layer_antenna_pwl  go to state 695


State 446

  137 layer_option: K_ANTENNAAREAFACTOR int_number . $@16 layer_antenna_duo ';'

    $default  reduce using rule 136 ($@16)

    $@16  go to state 696


State 447

  138 layer_option: K_ANTENNASIDEAREARATIO int_number . ';'

    ';'  shift, and go to state 697


State 448

  140 layer_option: K_ANTENNADIFFSIDEAREARATIO $@17 . layer_antenna_pwl ';'

    NUMBER  shift, and go to state 99
    K_PWL   shift, and go to state 691

    int_number         go to state 692
    layer_antenna_pwl  go to state 698


State 449

  141 layer_option: K_ANTENNACUMSIDEAREARATIO int_number . ';'

    ';'  shift, and go to state 699


State 450

  143 layer_option: K_ANTENNACUMDIFFSIDEAREARATIO $@18 . layer_antenna_pwl ';'

    NUMBER  shift, and go to state 99
    K_PWL   shift, and go to state 691

    int_number         go to state 692
    layer_antenna_pwl  go to state 700


State 451

  145 layer_option: K_ANTENNASIDEAREAFACTOR int_number . $@19 layer_antenna_duo ';'

    $default  reduce using rule 144 ($@19)

    $@19  go to state 701


State 452

  154 layer_option: K_SLOTWIREWIDTH int_number . ';'

    ';'  shift, and go to state 702


State 453

  155 layer_option: K_SLOTWIRELENGTH int_number . ';'

    ';'  shift, and go to state 703


State 454

  156 layer_option: K_SLOTWIDTH int_number . ';'

    ';'  shift, and go to state 704


State 455

  157 layer_option: K_SLOTLENGTH int_number . ';'

    ';'  shift, and go to state 705


State 456

  158 layer_option: K_MAXADJACENTSLOTSPACING int_number . ';'

    ';'  shift, and go to state 706


State 457

  159 layer_option: K_MAXCOAXIALSLOTSPACING int_number . ';'

    ';'  shift, and go to state 707


State 458

  160 layer_option: K_MAXEDGESLOTSPACING int_number . ';'

    ';'  shift, and go to state 708


State 459

  161 layer_option: K_SPLITWIREWIDTH int_number . ';'

    ';'  shift, and go to state 709


State 460

  162 layer_option: K_MINIMUMDENSITY int_number . ';'

    ';'  shift, and go to state 710


State 461

  163 layer_option: K_MAXIMUMDENSITY int_number . ';'

    ';'  shift, and go to state 711


State 462

  164 layer_option: K_DENSITYCHECKWINDOW int_number . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 712


State 463

  165 layer_option: K_DENSITYCHECKSTEP int_number . ';'

    ';'  shift, and go to state 713


State 464

  166 layer_option: K_FILLACTIVESPACING int_number . ';'

    ';'  shift, and go to state 714


State 465

  172 layer_option: K_MINIMUMCUT int_number . K_WIDTH int_number $@24 layer_minimumcut_within layer_minimumcut_from layer_minimumcut_length ';'

    K_WIDTH  shift, and go to state 715


State 466

  147 layer_option: K_ANTENNAMODEL $@20 . layer_oxide ';'

    K_OXIDE1  shift, and go to state 716
    K_OXIDE2  shift, and go to state 717
    K_OXIDE3  shift, and go to state 718
    K_OXIDE4  shift, and go to state 719

    layer_oxide  go to state 720


State 467

  207 layer_enclosure_type_opt: K_ABOVE .

    $default  reduce using rule 207 (layer_enclosure_type_opt)


State 468

  208 layer_enclosure_type_opt: K_BELOW .

    $default  reduce using rule 208 (layer_enclosure_type_opt)


State 469

  179 layer_option: K_ENCLOSURE layer_enclosure_type_opt . int_number int_number $@27 layer_enclosure_width_opt ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 721


State 470

  167 layer_option: K_MAXWIDTH int_number . ';'

    ';'  shift, and go to state 722


State 471

  170 layer_option: K_MINENCLOSEDAREA NUMBER . $@23 layer_minen_width ';'

    $default  reduce using rule 169 ($@23)

    $@23  go to state 723


State 472

  174 layer_option: K_MINSTEP int_number . $@25 layer_minstep_options ';'

    $default  reduce using rule 173 ($@25)

    $@25  go to state 724


State 473

  168 layer_option: K_MINWIDTH int_number . ';'

    ';'  shift, and go to state 725


State 474

  175 layer_option: K_PROTRUSIONWIDTH int_number . K_LENGTH int_number K_WIDTH int_number ';'

    K_LENGTH  shift, and go to state 726


State 475

  102 layer_option: K_SPACINGTABLE K_ORTHOGONAL . K_WITHIN int_number K_SPACING int_number $@7 layer_spacingtable_opts ';'

    K_WITHIN  shift, and go to state 727


State 476

  177 layer_option: K_SPACINGTABLE $@26 . sp_options ';'

    K_INFLUENCE          shift, and go to state 728
    K_PARALLELRUNLENGTH  shift, and go to state 729
    K_TWOWIDTHS          shift, and go to state 730

    sp_options  go to state 731


State 477

   88 layer_option: K_MASK int_number . ';'

    ';'  shift, and go to state 732


State 478

  183 layer_option: K_DIAGMINEDGELENGTH int_number . ';'

    ';'  shift, and go to state 733


State 479

   96 layer_option: K_DIAGSPACING int_number . ';'

    ';'  shift, and go to state 734


State 480

   91 layer_option: K_DIAGPITCH int_number . ';'
   92             | K_DIAGPITCH int_number . int_number ';'

    NUMBER  shift, and go to state 99
    ';'     shift, and go to state 735

    int_number  go to state 736


State 481

   95 layer_option: K_DIAGWIDTH int_number . ';'

    ';'  shift, and go to state 737


State 482

  185 layer_option: K_MINSIZE $@29 . firstPt otherPts ';'

    NUMBER  shift, and go to state 99
    '('     shift, and go to state 514

    int_number  go to state 515
    pt          go to state 738
    firstPt     go to state 739


State 483

  181 layer_option: K_PREFERENCLOSURE layer_enclosure_type_opt . int_number int_number $@28 layer_preferenclosure_width_opt ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 740


State 484

   86 layer_option: K_ARRAYSPACING $@4 . layer_arraySpacing_long layer_arraySpacing_width K_CUTSPACING int_number $@5 layer_arraySpacing_arraycuts ';'

    K_LONGARRAY  shift, and go to state 741

    $default  reduce using rule 186 (layer_arraySpacing_long)

    layer_arraySpacing_long  go to state 742


State 485

  153 layer_option: K_ANTENNAAREADIFFREDUCEPWL '(' . pt pt $@21 layer_diffusion_ratios ')' ';' $@22

    NUMBER  shift, and go to state 99
    '('     shift, and go to state 514

    int_number  go to state 515
    pt          go to state 743


State 486

  150 layer_option: K_ANTENNAAREAMINUSDIFF int_number . ';'

    ';'  shift, and go to state 744


State 487

  148 layer_option: K_ANTENNACUMROUTINGPLUSCUT ';' .

    $default  reduce using rule 148 (layer_option)


State 488

  149 layer_option: K_ANTENNAGATEPLUSDIFF int_number . ';'

    ';'  shift, and go to state 745


State 489

  342 start_foreign: K_FOREIGN $@54 . T_STRING

    T_STRING  shift, and go to state 746


State 490

  361 via_layer: K_LAYER $@55 . T_STRING ';'

    T_STRING  shift, and go to state 747


State 491

  328 via_other_option: K_RESISTANCE int_number . ';'

    ';'  shift, and go to state 748


State 492

  313 via_viarule: K_VIARULE $@49 . T_STRING ';' K_CUTSIZE int_number int_number ';' K_LAYERS $@50 T_STRING T_STRING T_STRING ';' K_CUTSPACING int_number int_number ';' K_ENCLOSURE int_number int_number int_number int_number ';' $@51 via_viarule_options

    T_STRING  shift, and go to state 749


State 493

  330 via_other_option: K_PROPERTY $@53 . via_prop_list ';'

    T_STRING  shift, and go to state 750

    via_prop_list        go to state 751
    via_name_value_pair  go to state 752


State 494

  368 end_via: K_END . $@57 T_STRING

    $default  reduce using rule 367 ($@57)

    $@57  go to state 753


State 495

  305 via: start_via $@48 via_option end_via .

    $default  reduce using rule 305 (via)


State 496

  323 via_other_options: via_other_option via_more_options .
  325 via_more_options: via_more_options . via_other_option

    K_FOREIGN         shift, and go to state 276
    K_LAYER           shift, and go to state 277
    K_RESISTANCE      shift, and go to state 278
    K_TOPOFSTACKONLY  shift, and go to state 279
    K_PROPERTY        shift, and go to state 281

    $default  reduce using rule 323 (via_other_options)

    via_other_option  go to state 754
    via_foreign       go to state 286
    start_foreign     go to state 287
    via_layer_rule    go to state 288
    via_layer         go to state 289


State 497

  343 orientation: K_N .

    $default  reduce using rule 343 (orientation)


State 498

  345 orientation: K_S .

    $default  reduce using rule 345 (orientation)


State 499

  346 orientation: K_E .

    $default  reduce using rule 346 (orientation)


State 500

  344 orientation: K_W .

    $default  reduce using rule 344 (orientation)


State 501

  347 orientation: K_FN .

    $default  reduce using rule 347 (orientation)


State 502

  349 orientation: K_FS .

    $default  reduce using rule 349 (orientation)


State 503

  350 orientation: K_FE .

    $default  reduce using rule 350 (orientation)


State 504

  348 orientation: K_FW .

    $default  reduce using rule 348 (orientation)


State 505

  351 orientation: K_R0 .

    $default  reduce using rule 351 (orientation)


State 506

  352 orientation: K_R90 .

    $default  reduce using rule 352 (orientation)


State 507

  353 orientation: K_R180 .

    $default  reduce using rule 353 (orientation)


State 508

  354 orientation: K_R270 .

    $default  reduce using rule 354 (orientation)


State 509

  357 orientation: K_MX .

    $default  reduce using rule 357 (orientation)


State 510

  355 orientation: K_MY .

    $default  reduce using rule 355 (orientation)


State 511

  358 orientation: K_MXR90 .

    $default  reduce using rule 358 (orientation)


State 512

  356 orientation: K_MYR90 .

    $default  reduce using rule 356 (orientation)


State 513

  337 via_foreign: start_foreign ';' .

    $default  reduce using rule 337 (via_foreign)


State 514

  497 pt: '(' . int_number int_number ')'

    NUMBER  shift, and go to state 99

    int_number  go to state 755


State 515

  496 pt: int_number . int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 756


State 516

  340 via_foreign: start_foreign orientation . ';'

    ';'  shift, and go to state 757


State 517

  338 via_foreign: start_foreign pt . ';'
  339            | start_foreign pt . orientation ';'

    K_N      shift, and go to state 497
    K_S      shift, and go to state 498
    K_E      shift, and go to state 499
    K_W      shift, and go to state 500
    K_FN     shift, and go to state 501
    K_FS     shift, and go to state 502
    K_FE     shift, and go to state 503
    K_FW     shift, and go to state 504
    K_R0     shift, and go to state 505
    K_R90    shift, and go to state 506
    K_R180   shift, and go to state 507
    K_R270   shift, and go to state 508
    K_MX     shift, and go to state 509
    K_MY     shift, and go to state 510
    K_MXR90  shift, and go to state 511
    K_MYR90  shift, and go to state 512
    ';'      shift, and go to state 758

    orientation  go to state 759


State 518

  359 via_layer_rule: via_layer via_geometries .
  363 via_geometries: via_geometries . via_geometry

    K_POLYGON  shift, and go to state 760
    K_RECT     shift, and go to state 761

    $default  reduce using rule 359 (via_layer_rule)

    via_geometry  go to state 762


State 519

  373 viarule_generate: viarule_keyword K_GENERATE viarule_generate_default $@59 . viarule_layer_list opt_viarule_props end_viarule

    K_LAYER  shift, and go to state 146

    viarule_layer_list  go to state 763
    viarule_layer       go to state 148
    viarule_layer_name  go to state 149


State 520

  394 viarule_layer_name: K_LAYER $@61 T_STRING . ';'

    ';'  shift, and go to state 764


State 521

  386 viarule_prop: T_STRING . T_STRING
  387             | T_STRING . QSTRING
  388             | T_STRING . NUMBER

    T_STRING  shift, and go to state 765
    QSTRING   shift, and go to state 766
    NUMBER    shift, and go to state 767


State 522

  383 viarule_prop: K_PROPERTY . $@60 viarule_prop_list ';'

    $default  reduce using rule 382 ($@60)

    $@60  go to state 768


State 523

  392 via_name: via_keyword . T_STRING ';'

    T_STRING  shift, and go to state 769


State 524

  371 viarule: viarule_keyword viarule_layer_list via_names opt_viarule_props . end_viarule

    K_END  shift, and go to state 770

    end_viarule  go to state 771


State 525

  379 opt_viarule_props: viarule_props .
  381 viarule_props: viarule_props . viarule_prop

    T_STRING    shift, and go to state 521
    K_PROPERTY  shift, and go to state 522

    $default  reduce using rule 379 (opt_viarule_props)

    viarule_prop  go to state 772


State 526

  380 viarule_props: viarule_prop .

    $default  reduce using rule 380 (viarule_props)


State 527

  391 via_names: via_names via_name .

    $default  reduce using rule 391 (via_names)


State 528

  397 viarule_layer_option: K_DIRECTION . K_HORIZONTAL ';'
  398                     | K_DIRECTION . K_VERTICAL ';'

    K_HORIZONTAL  shift, and go to state 773
    K_VERTICAL    shift, and go to state 774


State 529

  405 viarule_layer_option: K_METALOVERHANG . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 775


State 530

  404 viarule_layer_option: K_OVERHANG . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 776


State 531

  401 viarule_layer_option: K_RECT . pt pt ';'

    NUMBER  shift, and go to state 99
    '('     shift, and go to state 514

    int_number  go to state 515
    pt          go to state 777


State 532

  403 viarule_layer_option: K_RESISTANCE . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 778


State 533

  402 viarule_layer_option: K_SPACING . int_number K_BY int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 779


State 534

  400 viarule_layer_option: K_WIDTH . int_number K_TO int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 780


State 535

  399 viarule_layer_option: K_ENCLOSURE . int_number int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 781


State 536

  396 viarule_layer_options: viarule_layer_options viarule_layer_option .

    $default  reduce using rule 396 (viarule_layer_options)


State 537

  410 end_spacing: K_END K_SPACING .

    $default  reduce using rule 410 (end_spacing)


State 538

  413 spacing: samenet_keyword T_STRING . T_STRING int_number ';'
  414        | samenet_keyword T_STRING . T_STRING int_number K_STACK ';'

    T_STRING  shift, and go to state 782


State 539

  420 end_irdrop: K_END K_IRDROP .

    $default  reduce using rule 420 (end_irdrop)


State 540

  427 ir_tablename: K_TABLE T_STRING .

    $default  reduce using rule 427 (ir_tablename)


State 541

  423 ir_table: ir_tablename ir_table_values . ';'
  425 ir_table_values: ir_table_values . ir_table_value

    NUMBER  shift, and go to state 99
    ';'     shift, and go to state 783

    int_number      go to state 784
    ir_table_value  go to state 785


State 542

  482 site_class: K_CLASS K_CORE . ';'

    ';'  shift, and go to state 786


State 543

  481 site_class: K_CLASS K_PAD . ';'

    ';'  shift, and go to state 787


State 544

  483 site_class: K_CLASS K_VIRTUAL . ';'

    ';'  shift, and go to state 788


State 545

  474 end_site: K_END $@72 . T_STRING

    T_STRING  shift, and go to state 789


State 546

  477 site_option: K_SIZE int_number . K_BY int_number ';'

    K_BY  shift, and go to state 790


State 547

  484 site_symmetry_statement: K_SYMMETRY site_symmetries . ';'
  486 site_symmetries: site_symmetries . site_symmetry

    K_X    shift, and go to state 791
    K_Y    shift, and go to state 792
    K_R90  shift, and go to state 793
    ';'    shift, and go to state 794

    site_symmetry  go to state 795


State 548

  491 site_rowpattern_statement: K_ROWPATTERN $@73 . site_rowpatterns ';'

    $default  reduce using rule 492 (site_rowpatterns)

    site_rowpatterns  go to state 796


State 549

  543 class_type: K_BLOCK .
  544           | K_BLOCK . K_BLACKBOX
  545           | K_BLOCK . K_SOFT

    K_BLACKBOX  shift, and go to state 797
    K_SOFT      shift, and go to state 798

    $default  reduce using rule 543 (class_type)


State 550

  551 class_type: K_CORE .
  553           | K_CORE . core_type

    K_FEEDTHRU     shift, and go to state 799
    K_SPACER       shift, and go to state 800
    K_TIEHIGH      shift, and go to state 801
    K_TIELOW       shift, and go to state 802
    K_ANTENNACELL  shift, and go to state 803
    K_WELLTAP      shift, and go to state 804

    $default  reduce using rule 551 (class_type)

    core_type  go to state 805


State 551

  552 class_type: K_CORNER .

    $default  reduce using rule 552 (class_type)


State 552

  540 class_type: K_COVER .
  541           | K_COVER . K_BUMP

    K_BUMP  shift, and go to state 806

    $default  reduce using rule 540 (class_type)


State 553

  554 class_type: K_ENDCAP . endcap_type

    K_BOTTOMLEFT   shift, and go to state 807
    K_BOTTOMRIGHT  shift, and go to state 808
    K_POST         shift, and go to state 809
    K_PRE          shift, and go to state 810
    K_TOPLEFT      shift, and go to state 811
    K_TOPRIGHT     shift, and go to state 812

    endcap_type  go to state 813


State 554

  548 class_type: K_PAD .
  550           | K_PAD . pad_type

    K_INOUT   shift, and go to state 814
    K_INPUT   shift, and go to state 815
    K_OUTPUT  shift, and go to state 816
    K_POWER   shift, and go to state 817
    K_SPACER  shift, and go to state 818
    K_AREAIO  shift, and go to state 819

    $default  reduce using rule 548 (class_type)

    pad_type  go to state 820


State 555

  542 class_type: K_RING .

    $default  reduce using rule 542 (class_type)


State 556

  549 class_type: K_VIRTUAL .

    $default  reduce using rule 549 (class_type)


State 557

  546 class_type: K_NONE .

    $default  reduce using rule 546 (class_type)


State 558

  547 class_type: K_BUMP .

    $default  reduce using rule 547 (class_type)


State 559

  539 macro_class: K_CLASS class_type . ';'

    ';'  shift, and go to state 821


State 560

  753 macro_clocktype: K_CLOCKTYPE $@103 . T_STRING ';'

    T_STRING  shift, and go to state 822


State 561

  586 macro_eeq: K_EEQ $@79 . T_STRING ';'

    T_STRING  shift, and go to state 823


State 562

  574 macro_generate: K_GENERATE T_STRING . T_STRING ';'

    T_STRING  shift, and go to state 824


State 563

  573 macro_generator: K_GENERATOR T_STRING . ';'

    ';'  shift, and go to state 825


State 564

  588 macro_leq: K_LEQ $@80 . T_STRING ';'

    T_STRING  shift, and go to state 826


State 565

  579 macro_origin: K_ORIGIN pt . ';'

    ';'  shift, and go to state 827


State 566

  596 start_macro_pin: K_PIN $@81 . T_STRING

    T_STRING  shift, and go to state 828


State 567

  578 macro_power: K_POWER int_number . ';'

    ';'  shift, and go to state 829


State 568

  593 macro_size: K_SIZE int_number . K_BY int_number ';'

    K_BY  shift, and go to state 830


State 569

  577 macro_source: K_SOURCE K_BLOCK . ';'

    ';'  shift, and go to state 831


State 570

  576 macro_source: K_SOURCE K_GENERATE . ';'

    ';'  shift, and go to state 832


State 571

  575 macro_source: K_SOURCE K_USER . ';'

    ';'  shift, and go to state 833


State 572

  530 macro_symmetry_statement: K_SYMMETRY macro_symmetries . ';'
  532 macro_symmetries: macro_symmetries . macro_symmetry

    K_X    shift, and go to state 834
    K_Y    shift, and go to state 835
    K_R90  shift, and go to state 836
    ';'    shift, and go to state 837

    macro_symmetry  go to state 838


State 573

  527 macro_option: K_PROPERTY $@78 . macro_prop_list ';'

    T_STRING  shift, and go to state 839

    macro_prop_list        go to state 840
    macro_name_value_pair  go to state 841


State 574

  520 macro_option: K_FUNCTION K_BUFFER . ';'

    ';'  shift, and go to state 842


State 575

  521 macro_option: K_FUNCTION K_INVERTER . ';'

    ';'  shift, and go to state 843


State 576

  584 macro_fixedMask: K_FIXEDMASK ';' .

    $default  reduce using rule 584 (macro_fixedMask)


State 577

  748 density_layer: K_LAYER . $@101 T_STRING ';' $@102 density_layer_rect density_layer_rects

    $default  reduce using rule 746 ($@101)

    $@101  go to state 844


State 578

  743 macro_density: K_DENSITY density_layer . density_layers K_END

    $default  reduce using rule 744 (density_layers)

    density_layers  go to state 845


State 579

  580 macro_foreign: start_foreign ';' .

    $default  reduce using rule 580 (macro_foreign)


State 580

  583 macro_foreign: start_foreign orientation . ';'

    ';'  shift, and go to state 846


State 581

  581 macro_foreign: start_foreign pt . ';'
  582              | start_foreign pt . orientation ';'

    K_N      shift, and go to state 497
    K_S      shift, and go to state 498
    K_E      shift, and go to state 499
    K_W      shift, and go to state 500
    K_FN     shift, and go to state 501
    K_FS     shift, and go to state 502
    K_FE     shift, and go to state 503
    K_FW     shift, and go to state 504
    K_R0     shift, and go to state 505
    K_R90    shift, and go to state 506
    K_R180   shift, and go to state 507
    K_R270   shift, and go to state 508
    K_MX     shift, and go to state 509
    K_MY     shift, and go to state 510
    K_MXR90  shift, and go to state 511
    K_MYR90  shift, and go to state 512
    ';'      shift, and go to state 847

    orientation  go to state 848


State 582

  503 end_macro: K_END . $@77 T_STRING

    $default  reduce using rule 502 ($@77)

    $@77  go to state 849


State 583

  499 macro: start_macro macro_options $@75 end_macro .

    $default  reduce using rule 499 (macro)


State 584

  589 macro_site: macro_site_word T_STRING . ';'
  725 sitePattern: T_STRING . int_number int_number orientation K_DO int_number K_BY int_number K_STEP int_number int_number
  726            | T_STRING . int_number int_number orientation

    NUMBER  shift, and go to state 99
    ';'     shift, and go to state 850

    int_number  go to state 851


State 585

  590 macro_site: macro_site_word sitePattern . ';'

    ';'  shift, and go to state 852


State 586

  594 macro_pin: start_macro_pin macro_pin_options . end_macro_pin
  600 macro_pin_options: macro_pin_options . macro_pin_option

    K_CAPACITANCE                  shift, and go to state 853
    K_CURRENTSOURCE                shift, and go to state 854
    K_DIRECTION                    shift, and go to state 855
    K_END                          shift, and go to state 856
    K_FALLSATCUR                   shift, and go to state 857
    K_FALLTHRESH                   shift, and go to state 858
    K_FOREIGN                      shift, and go to state 276
    K_INPUTNOISEMARGIN             shift, and go to state 859
    K_IV_TABLES                    shift, and go to state 860
    K_LEAKAGE                      shift, and go to state 861
    K_LEQ                          shift, and go to state 862
    K_MAXDELAY                     shift, and go to state 863
    K_MAXLOAD                      shift, and go to state 864
    K_MUSTJOIN                     shift, and go to state 865
    K_OUTPUTNOISEMARGIN            shift, and go to state 866
    K_PORT                         shift, and go to state 867
    K_POWER                        shift, and go to state 868
    K_PULLDOWNRES                  shift, and go to state 869
    K_RESISTANCE                   shift, and go to state 870
    K_RISESATCUR                   shift, and go to state 871
    K_RISETHRESH                   shift, and go to state 872
    K_RISEVOLTAGETHRESHOLD         shift, and go to state 873
    K_FALLVOLTAGETHRESHOLD         shift, and go to state 874
    K_SCANUSE                      shift, and go to state 875
    K_SHAPE                        shift, and go to state 876
    K_TIEOFFR                      shift, and go to state 877
    K_USE                          shift, and go to state 878
    K_VHI                          shift, and go to state 879
    K_VLO                          shift, and go to state 880
    K_PROPERTY                     shift, and go to state 881
    K_OUTPUTRESISTANCE             shift, and go to state 882
    K_TAPERRULE                    shift, and go to state 883
    K_ANTENNASIZE                  shift, and go to state 884
    K_ANTENNAMETALLENGTH           shift, and go to state 885
    K_ANTENNAMETALAREA             shift, and go to state 886
    K_RISESLEWLIMIT                shift, and go to state 887
    K_FALLSLEWLIMIT                shift, and go to state 888
    K_ANTENNAPARTIALMETALAREA      shift, and go to state 889
    K_ANTENNAPARTIALMETALSIDEAREA  shift, and go to state 890
    K_ANTENNAGATEAREA              shift, and go to state 891
    K_ANTENNADIFFAREA              shift, and go to state 892
    K_ANTENNAMAXAREACAR            shift, and go to state 893
    K_ANTENNAMAXSIDEAREACAR        shift, and go to state 894
    K_ANTENNAPARTIALCUTAREA        shift, and go to state 895
    K_ANTENNAMAXCUTCAR             shift, and go to state 896
    K_ANTENNAMODEL                 shift, and go to state 897
    K_GROUNDSENSITIVITY            shift, and go to state 898
    K_NETEXPR                      shift, and go to state 899
    K_SUPPLYSENSITIVITY            shift, and go to state 900

    start_foreign         go to state 901
    end_macro_pin         go to state 902
    macro_pin_option      go to state 903
    electrical_direction  go to state 904
    start_macro_port      go to state 905


State 587

  741 macro_obs: start_macro_obs K_END .

    $default  reduce using rule 741 (macro_obs)


State 588

  700 geometry: K_LAYER . $@93 T_STRING $@94 layer_exceptpgnet layer_spacing ';'

    $default  reduce using rule 698 ($@93)

    $@93  go to state 906


State 589

  702 geometry: K_PATH . maskColor firstPt otherPts ';'
  703         | K_PATH . maskColor K_ITERATE firstPt otherPts stepPattern ';'

    K_MASK  shift, and go to state 907

    $default  reduce using rule 416 (maskColor)

    maskColor  go to state 908


State 590

  706 geometry: K_POLYGON . maskColor firstPt nextPt nextPt otherPts ';'
  707         | K_POLYGON . maskColor K_ITERATE firstPt nextPt nextPt otherPts stepPattern ';'

    K_MASK  shift, and go to state 907

    $default  reduce using rule 416 (maskColor)

    maskColor  go to state 909


State 591

  704 geometry: K_RECT . maskColor pt pt ';'
  705         | K_RECT . maskColor K_ITERATE pt pt stepPattern ';'

    K_MASK  shift, and go to state 907

    $default  reduce using rule 416 (maskColor)

    maskColor  go to state 910


State 592

  721 via_placement: K_VIA . maskColor pt $@95 T_STRING ';'
  723              | K_VIA . K_ITERATE maskColor pt $@96 T_STRING stepPattern ';'

    K_ITERATE  shift, and go to state 911
    K_MASK     shift, and go to state 907

    $default  reduce using rule 416 (maskColor)

    maskColor  go to state 912


State 593

  701 geometry: K_WIDTH . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 913


State 594

  740 macro_obs: start_macro_obs geometries . K_END

    K_END  shift, and go to state 914


State 595

  697 geometries: geometry . geometry_options

    $default  reduce using rule 709 (geometry_options)

    geometry_options  go to state 915


State 596

  708 geometry: via_placement .

    $default  reduce using rule 708 (geometry)


State 597

  754 timing: start_timing timing_options . end_timing
  758 timing_options: timing_options . timing_option

    K_END           shift, and go to state 916
    K_FALL          shift, and go to state 917
    K_FALLCS        shift, and go to state 918
    K_FALLT0        shift, and go to state 919
    K_FALLSATT1     shift, and go to state 920
    K_FALLRS        shift, and go to state 921
    K_FROMPIN       shift, and go to state 922
    K_RISE          shift, and go to state 923
    K_RISECS        shift, and go to state 924
    K_RISERS        shift, and go to state 925
    K_RISESATT1     shift, and go to state 926
    K_RISET0        shift, and go to state 927
    K_TOPIN         shift, and go to state 928
    K_UNATENESS     shift, and go to state 929
    K_STABLE        shift, and go to state 930
    K_SETUP         shift, and go to state 931
    K_HOLD          shift, and go to state 932
    K_TABLEAXIS     shift, and go to state 933
    K_TABLEENTRIES  shift, and go to state 934
    K_EXTENSION     shift, and go to state 935
    K_RECOVERY      shift, and go to state 936
    K_SKEW          shift, and go to state 937
    K_SDFCONDSTART  shift, and go to state 938
    K_SDFCONDEND    shift, and go to state 939
    K_SDFCOND       shift, and go to state 940
    K_MPWH          shift, and go to state 941
    K_MPWL          shift, and go to state 942
    K_PERIOD        shift, and go to state 943

    end_timing       go to state 944
    timing_option    go to state 945
    one_pin_trigger  go to state 946
    two_pin_trigger  go to state 947
    risefall         go to state 948


State 598

  827 array_rule: K_CANPLACE $@111 . sitePattern ';'

    T_STRING  shift, and go to state 949

    sitePattern  go to state 950


State 599

  829 array_rule: K_CANNOTOCCUPY $@112 . sitePattern ';'

    T_STRING  shift, and go to state 949

    sitePattern  go to state 951


State 600

  831 array_rule: K_TRACKS $@113 . trackPattern ';'

    K_X  shift, and go to state 952
    K_Y  shift, and go to state 953

    trackPattern  go to state 954


State 601

  837 floorplan_start: K_FLOORPLAN T_STRING .

    $default  reduce using rule 837 (floorplan_start)


State 602

  834 array_rule: K_GCELLGRID $@114 . gcellPattern ';'

    K_X  shift, and go to state 955
    K_Y  shift, and go to state 956

    gcellPattern  go to state 957


State 603

  835 array_rule: K_DEFAULTCAP int_number . cap_list K_END K_DEFAULTCAP

    $default  reduce using rule 844 (cap_list)

    cap_list  go to state 958


State 604

  825 array_rule: site_word $@110 . sitePattern ';'

    T_STRING  shift, and go to state 949

    sitePattern  go to state 959


State 605

  821 end_array: K_END . $@109 T_STRING

    $default  reduce using rule 820 ($@109)

    $@109  go to state 960


State 606

  817 array: start_array array_rules $@107 end_array .

    $default  reduce using rule 817 (array)


State 607

  832 array_rule: floorplan_start floorplan_list . K_END T_STRING
  839 floorplan_list: floorplan_list . floorplan_element

    K_END           shift, and go to state 961
    K_CANPLACE      shift, and go to state 962
    K_CANNOTOCCUPY  shift, and go to state 963

    floorplan_element  go to state 964


State 608

  437 nd_hardspacing: K_HARDSPACING . ';'

    ';'  shift, and go to state 965


State 609

  433 nondefault_rule: K_NONDEFAULTRULE $@63 T_STRING $@64 nd_hardspacing . nd_rules $@65 end_nd_rule

    $default  reduce using rule 438 (nd_rules)

    nd_rules  go to state 966


State 610

  870 expression: K_IF . b_expr then expression else expression

    QSTRING  shift, and go to state 615
    NUMBER   shift, and go to state 99
    K_IF     shift, and go to state 619
    K_FALSE  shift, and go to state 620
    K_TRUE   shift, and go to state 621
    K_NOT    shift, and go to state 622
    '-'      shift, and go to state 611
    '('      shift, and go to state 623

    int_number  go to state 613
    expression  go to state 624
    b_expr      go to state 967
    s_expr      go to state 626


State 611

  868 expression: '-' . expression

    NUMBER  shift, and go to state 99
    K_IF    shift, and go to state 610
    '-'     shift, and go to state 611
    '('     shift, and go to state 612

    int_number  go to state 613
    expression  go to state 968


State 612

  869 expression: '(' . expression ')'

    NUMBER  shift, and go to state 99
    K_IF    shift, and go to state 610
    '-'     shift, and go to state 611
    '('     shift, and go to state 612

    int_number  go to state 613
    expression  go to state 969


State 613

  871 expression: int_number .

    $default  reduce using rule 871 (expression)


State 614

  852 def_statement: K_DEFINE $@119 T_STRING '=' expression . dtrm
  864 expression: expression . '+' expression
  865           | expression . '-' expression
  866           | expression . '*' expression
  867           | expression . '/' expression

    '-'   shift, and go to state 970
    '+'   shift, and go to state 971
    '*'   shift, and go to state 972
    '/'   shift, and go to state 973
    ';'   shift, and go to state 974
    '\n'  shift, and go to state 975

    $default  reduce using rule 857 (dtrm)

    dtrm  go to state 976


State 615

  890 s_expr: QSTRING .

    $default  reduce using rule 890 (s_expr)


State 616

  889 s_expr: K_IF . b_expr then s_expr else s_expr

    QSTRING  shift, and go to state 615
    NUMBER   shift, and go to state 99
    K_IF     shift, and go to state 619
    K_FALSE  shift, and go to state 620
    K_TRUE   shift, and go to state 621
    K_NOT    shift, and go to state 622
    '-'      shift, and go to state 611
    '('      shift, and go to state 623

    int_number  go to state 613
    expression  go to state 624
    b_expr      go to state 977
    s_expr      go to state 626


State 617

  888 s_expr: '(' . s_expr ')'

    QSTRING  shift, and go to state 615
    K_IF     shift, and go to state 616
    '('      shift, and go to state 617

    s_expr  go to state 978


State 618

  854 def_statement: K_DEFINES $@120 T_STRING '=' s_expr . dtrm
  887 s_expr: s_expr . '+' s_expr

    '+'   shift, and go to state 979
    ';'   shift, and go to state 974
    '\n'  shift, and go to state 975

    $default  reduce using rule 857 (dtrm)

    dtrm  go to state 980


State 619

  870 expression: K_IF . b_expr then expression else expression
  884 b_expr: K_IF . b_expr then b_expr else b_expr
  889 s_expr: K_IF . b_expr then s_expr else s_expr

    QSTRING  shift, and go to state 615
    NUMBER   shift, and go to state 99
    K_IF     shift, and go to state 619
    K_FALSE  shift, and go to state 620
    K_TRUE   shift, and go to state 621
    K_NOT    shift, and go to state 622
    '-'      shift, and go to state 611
    '('      shift, and go to state 623

    int_number  go to state 613
    expression  go to state 624
    b_expr      go to state 981
    s_expr      go to state 626


State 620

  886 b_expr: K_FALSE .

    $default  reduce using rule 886 (b_expr)


State 621

  885 b_expr: K_TRUE .

    $default  reduce using rule 885 (b_expr)


State 622

  882 b_expr: K_NOT . b_expr

    QSTRING  shift, and go to state 615
    NUMBER   shift, and go to state 99
    K_IF     shift, and go to state 619
    K_FALSE  shift, and go to state 620
    K_TRUE   shift, and go to state 621
    K_NOT    shift, and go to state 622
    '-'      shift, and go to state 611
    '('      shift, and go to state 623

    int_number  go to state 613
    expression  go to state 624
    b_expr      go to state 982
    s_expr      go to state 626


State 623

  869 expression: '(' . expression ')'
  883 b_expr: '(' . b_expr ')'
  888 s_expr: '(' . s_expr ')'

    QSTRING  shift, and go to state 615
    NUMBER   shift, and go to state 99
    K_IF     shift, and go to state 619
    K_FALSE  shift, and go to state 620
    K_TRUE   shift, and go to state 621
    K_NOT    shift, and go to state 622
    '-'      shift, and go to state 611
    '('      shift, and go to state 623

    int_number  go to state 613
    expression  go to state 983
    b_expr      go to state 984
    s_expr      go to state 985


State 624

  864 expression: expression . '+' expression
  865           | expression . '-' expression
  866           | expression . '*' expression
  867           | expression . '/' expression
  872 b_expr: expression . relop expression
  873       | expression . K_AND expression
  874       | expression . K_OR expression

    K_EQ   shift, and go to state 986
    K_NE   shift, and go to state 987
    K_LE   shift, and go to state 988
    K_LT   shift, and go to state 989
    K_GE   shift, and go to state 990
    K_GT   shift, and go to state 991
    K_OR   shift, and go to state 992
    K_AND  shift, and go to state 993
    '-'    shift, and go to state 970
    '+'    shift, and go to state 971
    '*'    shift, and go to state 972
    '/'    shift, and go to state 973
    '='    shift, and go to state 994
    '<'    shift, and go to state 995
    '>'    shift, and go to state 996

    relop  go to state 997


State 625

  856 def_statement: K_DEFINEB $@121 T_STRING '=' b_expr . dtrm
  878 b_expr: b_expr . K_EQ b_expr
  879       | b_expr . K_NE b_expr
  880       | b_expr . K_AND b_expr
  881       | b_expr . K_OR b_expr

    K_EQ   shift, and go to state 998
    K_NE   shift, and go to state 999
    K_OR   shift, and go to state 1000
    K_AND  shift, and go to state 1001
    ';'    shift, and go to state 974
    '\n'   shift, and go to state 975

    $default  reduce using rule 857 (dtrm)

    dtrm  go to state 1002


State 626

  875 b_expr: s_expr . relop s_expr
  876       | s_expr . K_AND s_expr
  877       | s_expr . K_OR s_expr
  887 s_expr: s_expr . '+' s_expr

    K_EQ   shift, and go to state 986
    K_NE   shift, and go to state 987
    K_LE   shift, and go to state 988
    K_LT   shift, and go to state 989
    K_GE   shift, and go to state 990
    K_GT   shift, and go to state 991
    K_OR   shift, and go to state 1003
    K_AND  shift, and go to state 1004
    '+'    shift, and go to state 979
    '='    shift, and go to state 994
    '<'    shift, and go to state 995
    '>'    shift, and go to state 996

    relop  go to state 1005


State 627

  901 prop_def_section: K_PROPDEF $@122 prop_stmts K_END K_PROPDEF .

    $default  reduce using rule 901 (prop_def_section)


State 628

  907 prop_stmt: K_COMPONENTPIN $@124 . T_STRING prop_define ';'

    T_STRING  shift, and go to state 1006


State 629

  917 prop_stmt: K_LAYER $@129 . T_STRING prop_define ';'

    T_STRING  shift, and go to state 1007


State 630

  905 prop_stmt: K_LIBRARY $@123 . T_STRING prop_define ';'

    T_STRING  shift, and go to state 1008


State 631

  911 prop_stmt: K_MACRO $@126 . T_STRING prop_define ';'

    T_STRING  shift, and go to state 1009


State 632

  919 prop_stmt: K_NONDEFAULTRULE $@130 . T_STRING prop_define ';'

    T_STRING  shift, and go to state 1010


State 633

  909 prop_stmt: K_PIN $@125 . T_STRING prop_define ';'

    T_STRING  shift, and go to state 1011


State 634

  913 prop_stmt: K_VIA $@127 . T_STRING prop_define ';'

    T_STRING  shift, and go to state 1012


State 635

  915 prop_stmt: K_VIARULE $@128 . T_STRING prop_define ';'

    T_STRING  shift, and go to state 1013


State 636

  999 correction_table_item: K_EDGERATE . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1014


State 637

  1002 output_list: K_OUTPUTRESISTANCE . $@144 numo_list ';' corr_victim_list

    $default  reduce using rule 1001 ($@144)

    $@144  go to state 1015


State 638

  995 correctiontable: K_CORRECTIONTABLE int_number ';' $@143 correction_table_list . end_correctiontable dtrm
  998 correction_table_list: correction_table_list . correction_table_item

    K_END               shift, and go to state 1016
    K_EDGERATE          shift, and go to state 636
    K_OUTPUTRESISTANCE  shift, and go to state 637

    end_correctiontable    go to state 1017
    correction_table_item  go to state 1018
    output_list            go to state 640


State 639

  997 correction_table_list: correction_table_item .

    $default  reduce using rule 997 (correction_table_list)


State 640

  1000 correction_table_item: output_list .

    $default  reduce using rule 1000 (correction_table_item)


State 641

  982 noise_table_entry: K_EDGERATE . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1019


State 642

  985 output_resistance_entry: K_OUTPUTRESISTANCE . $@141 num_list ';' victim_list

    $default  reduce using rule 984 ($@141)

    $@141  go to state 1020


State 643

  978 noisetable: K_NOISETABLE int_number $@140 ';' noise_table_list . end_noisetable dtrm
  981 noise_table_list: noise_table_list . noise_table_entry

    K_END               shift, and go to state 1021
    K_EDGERATE          shift, and go to state 641
    K_OUTPUTRESISTANCE  shift, and go to state 642

    end_noisetable           go to state 1022
    noise_table_entry        go to state 1023
    output_resistance_entry  go to state 645


State 644

  980 noise_table_list: noise_table_entry .

    $default  reduce using rule 980 (noise_table_list)


State 645

  983 noise_table_entry: output_resistance_entry .

    $default  reduce using rule 983 (noise_table_entry)


State 646

  848 msg_statement: K_MESSAGE $@117 T_STRING '=' s_expr . dtrm
  887 s_expr: s_expr . '+' s_expr

    '+'   shift, and go to state 979
    ';'   shift, and go to state 974
    '\n'  shift, and go to state 975

    $default  reduce using rule 857 (dtrm)

    dtrm  go to state 1024


State 647

  850 create_file_statement: K_CREATEFILE $@118 T_STRING '=' s_expr . dtrm
  887 s_expr: s_expr . '+' s_expr

    '+'   shift, and go to state 979
    ';'   shift, and go to state 974
    '\n'  shift, and go to state 975

    $default  reduce using rule 857 (dtrm)

    dtrm  go to state 1025


State 648

  303 maxstack_via: K_MAXVIASTACK int_number K_RANGE $@47 T_STRING . T_STRING ';'

    T_STRING  shift, and go to state 1026


State 649

   70 units_rule: K_CAPACITANCE K_PICOFARADS int_number . ';'

    ';'  shift, and go to state 1027


State 650

   73 units_rule: K_CURRENT K_MILLIAMPS int_number . ';'

    ';'  shift, and go to state 1028


State 651

   75 units_rule: K_DATABASE K_MICRONS int_number . ';'

    ';'  shift, and go to state 1029


State 652

   72 units_rule: K_POWER K_MILLIWATTS int_number . ';'

    ';'  shift, and go to state 1030


State 653

   71 units_rule: K_RESISTANCE K_OHMS int_number . ';'

    ';'  shift, and go to state 1031


State 654

   69 units_rule: K_TIME K_NANOSECONDS int_number . ';'

    ';'  shift, and go to state 1032


State 655

   74 units_rule: K_VOLTAGE K_VOLTS int_number . ';'

    ';'  shift, and go to state 1033


State 656

   76 units_rule: K_FREQUENCY K_MEGAHERTZ NUMBER . ';'

    ';'  shift, and go to state 1034


State 657

   98 layer_option: K_AREA NUMBER ';' .

    $default  reduce using rule 98 (layer_option)


State 658

  107 layer_option: K_CAPACITANCE K_CPERSQDIST K_PWL . '(' cap_points ')' ';'

    '('  shift, and go to state 1035


State 659

  106 layer_option: K_CAPACITANCE K_CPERSQDIST int_number . ';'

    ';'  shift, and go to state 1036


State 660

  112 layer_option: K_CAPMULTIPLIER int_number ';' .

    $default  reduce using rule 112 (layer_option)


State 661

  103 layer_option: K_DIRECTION layer_direction ';' .

    $default  reduce using rule 103 (layer_option)


State 662

  113 layer_option: K_EDGECAPACITANCE int_number ';' .

    $default  reduce using rule 113 (layer_option)


State 663

   81 end_layer: K_END $@3 T_STRING .

    $default  reduce using rule 81 (end_layer)


State 664

  108 layer_option: K_HEIGHT int_number ';' .

    $default  reduce using rule 108 (layer_option)


State 665

   93 layer_option: K_OFFSET int_number ';' .

    $default  reduce using rule 93 (layer_option)


State 666

   94 layer_option: K_OFFSET int_number int_number . ';'

    ';'  shift, and go to state 1037


State 667

   89 layer_option: K_PITCH int_number ';' .

    $default  reduce using rule 89 (layer_option)


State 668

   90 layer_option: K_PITCH int_number int_number . ';'

    ';'  shift, and go to state 1038


State 669

  105 layer_option: K_RESISTANCE K_RPERSQ K_PWL . '(' res_points ')' ';'

    '('  shift, and go to state 1039


State 670

  104 layer_option: K_RESISTANCE K_RPERSQ int_number . ';'

    ';'  shift, and go to state 1040


State 671

  182 layer_option: K_RESISTANCE int_number ';' .

    $default  reduce using rule 182 (layer_option)


State 672

  111 layer_option: K_SHRINKAGE int_number ';' .

    $default  reduce using rule 111 (layer_option)


State 673

  100 layer_option: K_SPACING int_number $@6 . layer_spacing_opts layer_spacing_cut_routing ';'

    K_SAMENET          shift, and go to state 1041
    K_CENTERTOCENTER   shift, and go to state 1042
    K_PARALLELOVERLAP  shift, and go to state 1043

    $default  reduce using rule 943 (layer_spacing_opts)

    layer_spacing_opts  go to state 1044
    layer_spacing_opt   go to state 1045


State 674

  110 layer_option: K_THICKNESS int_number ';' .

    $default  reduce using rule 110 (layer_option)


State 675

   87 layer_option: K_TYPE layer_type ';' .

    $default  reduce using rule 87 (layer_option)


State 676

   97 layer_option: K_WIDTH int_number ';' .

    $default  reduce using rule 97 (layer_option)


State 677

  260 layer_prop: T_STRING . T_STRING
  261           | T_STRING . QSTRING
  262           | T_STRING . NUMBER

    T_STRING  shift, and go to state 1046
    QSTRING   shift, and go to state 1047
    NUMBER    shift, and go to state 1048


State 678

  119 layer_option: K_PROPERTY $@8 layer_prop_list . ';'
  259 layer_prop_list: layer_prop_list . layer_prop

    T_STRING  shift, and go to state 677
    ';'       shift, and go to state 1049

    layer_prop  go to state 1050


State 679

  258 layer_prop_list: layer_prop .

    $default  reduce using rule 258 (layer_prop_list)


State 680

  116 layer_option: K_CURRENTDEN K_PWL '(' . current_density_pwl_list ')' ';'

    '('  shift, and go to state 1051

    current_density_pwl_list  go to state 1052
    current_density_pwl       go to state 1053


State 681

  117 layer_option: K_CURRENTDEN '(' int_number . int_number ')' ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1054


State 682

  115 layer_option: K_CURRENTDEN int_number ';' .

    $default  reduce using rule 115 (layer_option)


State 683

  114 layer_option: K_ANTENNALENGTHFACTOR int_number ';' .

    $default  reduce using rule 114 (layer_option)


State 684

  109 layer_option: K_WIREEXTENSION int_number ';' .

    $default  reduce using rule 109 (layer_option)


State 685

  122 layer_option: K_ACCURRENTDENSITY layer_table_type int_number . ';'

    ';'  shift, and go to state 1055


State 686

  121 layer_option: K_ACCURRENTDENSITY layer_table_type $@9 . layer_frequency

    K_FREQUENCY  shift, and go to state 1056

    layer_frequency  go to state 1057


State 687

  129 layer_option: K_DCCURRENTDENSITY K_AVERAGE K_WIDTH . int_number $@12 int_number_list ';' $@13 dc_layer_table

    NUMBER  shift, and go to state 99

    int_number  go to state 1058


State 688

  126 layer_option: K_DCCURRENTDENSITY K_AVERAGE K_CUTAREA . NUMBER $@10 number_list ';' $@11 dc_layer_table

    NUMBER  shift, and go to state 1059


State 689

  123 layer_option: K_DCCURRENTDENSITY K_AVERAGE int_number . ';'

    ';'  shift, and go to state 1060


State 690

  130 layer_option: K_ANTENNAAREARATIO int_number ';' .

    $default  reduce using rule 130 (layer_option)


State 691

  234 layer_antenna_pwl: K_PWL . '(' pt pt $@38 layer_diffusion_ratios ')'

    '('  shift, and go to state 1061


State 692

  232 layer_antenna_pwl: int_number .

    $default  reduce using rule 232 (layer_antenna_pwl)


State 693

  132 layer_option: K_ANTENNADIFFAREARATIO $@14 layer_antenna_pwl . ';'

    ';'  shift, and go to state 1062


State 694

  133 layer_option: K_ANTENNACUMAREARATIO int_number ';' .

    $default  reduce using rule 133 (layer_option)


State 695

  135 layer_option: K_ANTENNACUMDIFFAREARATIO $@15 layer_antenna_pwl . ';'

    ';'  shift, and go to state 1063


State 696

  137 layer_option: K_ANTENNAAREAFACTOR int_number $@16 . layer_antenna_duo ';'

    K_DIFFUSEONLY  shift, and go to state 1064

    $default  reduce using rule 238 (layer_antenna_duo)

    layer_antenna_duo  go to state 1065


State 697

  138 layer_option: K_ANTENNASIDEAREARATIO int_number ';' .

    $default  reduce using rule 138 (layer_option)


State 698

  140 layer_option: K_ANTENNADIFFSIDEAREARATIO $@17 layer_antenna_pwl . ';'

    ';'  shift, and go to state 1066


State 699

  141 layer_option: K_ANTENNACUMSIDEAREARATIO int_number ';' .

    $default  reduce using rule 141 (layer_option)


State 700

  143 layer_option: K_ANTENNACUMDIFFSIDEAREARATIO $@18 layer_antenna_pwl . ';'

    ';'  shift, and go to state 1067


State 701

  145 layer_option: K_ANTENNASIDEAREAFACTOR int_number $@19 . layer_antenna_duo ';'

    K_DIFFUSEONLY  shift, and go to state 1064

    $default  reduce using rule 238 (layer_antenna_duo)

    layer_antenna_duo  go to state 1068


State 702

  154 layer_option: K_SLOTWIREWIDTH int_number ';' .

    $default  reduce using rule 154 (layer_option)


State 703

  155 layer_option: K_SLOTWIRELENGTH int_number ';' .

    $default  reduce using rule 155 (layer_option)


State 704

  156 layer_option: K_SLOTWIDTH int_number ';' .

    $default  reduce using rule 156 (layer_option)


State 705

  157 layer_option: K_SLOTLENGTH int_number ';' .

    $default  reduce using rule 157 (layer_option)


State 706

  158 layer_option: K_MAXADJACENTSLOTSPACING int_number ';' .

    $default  reduce using rule 158 (layer_option)


State 707

  159 layer_option: K_MAXCOAXIALSLOTSPACING int_number ';' .

    $default  reduce using rule 159 (layer_option)


State 708

  160 layer_option: K_MAXEDGESLOTSPACING int_number ';' .

    $default  reduce using rule 160 (layer_option)


State 709

  161 layer_option: K_SPLITWIREWIDTH int_number ';' .

    $default  reduce using rule 161 (layer_option)


State 710

  162 layer_option: K_MINIMUMDENSITY int_number ';' .

    $default  reduce using rule 162 (layer_option)


State 711

  163 layer_option: K_MAXIMUMDENSITY int_number ';' .

    $default  reduce using rule 163 (layer_option)


State 712

  164 layer_option: K_DENSITYCHECKWINDOW int_number int_number . ';'

    ';'  shift, and go to state 1069


State 713

  165 layer_option: K_DENSITYCHECKSTEP int_number ';' .

    $default  reduce using rule 165 (layer_option)


State 714

  166 layer_option: K_FILLACTIVESPACING int_number ';' .

    $default  reduce using rule 166 (layer_option)


State 715

  172 layer_option: K_MINIMUMCUT int_number K_WIDTH . int_number $@24 layer_minimumcut_within layer_minimumcut_from layer_minimumcut_length ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1070


State 716

  284 layer_oxide: K_OXIDE1 .

    $default  reduce using rule 284 (layer_oxide)


State 717

  285 layer_oxide: K_OXIDE2 .

    $default  reduce using rule 285 (layer_oxide)


State 718

  286 layer_oxide: K_OXIDE3 .

    $default  reduce using rule 286 (layer_oxide)


State 719

  287 layer_oxide: K_OXIDE4 .

    $default  reduce using rule 287 (layer_oxide)


State 720

  147 layer_option: K_ANTENNAMODEL $@20 layer_oxide . ';'

    ';'  shift, and go to state 1071


State 721

  179 layer_option: K_ENCLOSURE layer_enclosure_type_opt int_number . int_number $@27 layer_enclosure_width_opt ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1072


State 722

  167 layer_option: K_MAXWIDTH int_number ';' .

    $default  reduce using rule 167 (layer_option)


State 723

  170 layer_option: K_MINENCLOSEDAREA NUMBER $@23 . layer_minen_width ';'

    K_WIDTH  shift, and go to state 1073

    $default  reduce using rule 282 (layer_minen_width)

    layer_minen_width  go to state 1074


State 724

  174 layer_option: K_MINSTEP int_number $@25 . layer_minstep_options ';'

    $default  reduce using rule 224 (layer_minstep_options)

    layer_minstep_options  go to state 1075


State 725

  168 layer_option: K_MINWIDTH int_number ';' .

    $default  reduce using rule 168 (layer_option)


State 726

  175 layer_option: K_PROTRUSIONWIDTH int_number K_LENGTH . int_number K_WIDTH int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1076


State 727

  102 layer_option: K_SPACINGTABLE K_ORTHOGONAL K_WITHIN . int_number K_SPACING int_number $@7 layer_spacingtable_opts ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1077


State 728

  202 sp_options: K_INFLUENCE . K_WIDTH int_number K_WITHIN int_number K_SPACING int_number $@36 layer_sp_influence_widths

    K_WIDTH  shift, and go to state 1078


State 729

  197 sp_options: K_PARALLELRUNLENGTH . int_number $@30 int_number_list $@31 K_WIDTH int_number $@32 int_number_list $@33 layer_sp_parallel_widths

    NUMBER  shift, and go to state 99

    int_number  go to state 1079


State 730

  200 sp_options: K_TWOWIDTHS . K_WIDTH int_number layer_sp_TwoWidthsPRL int_number $@34 int_number_list $@35 layer_sp_TwoWidths

    K_WIDTH  shift, and go to state 1080


State 731

  177 layer_option: K_SPACINGTABLE $@26 sp_options . ';'

    ';'  shift, and go to state 1081


State 732

   88 layer_option: K_MASK int_number ';' .

    $default  reduce using rule 88 (layer_option)


State 733

  183 layer_option: K_DIAGMINEDGELENGTH int_number ';' .

    $default  reduce using rule 183 (layer_option)


State 734

   96 layer_option: K_DIAGSPACING int_number ';' .

    $default  reduce using rule 96 (layer_option)


State 735

   91 layer_option: K_DIAGPITCH int_number ';' .

    $default  reduce using rule 91 (layer_option)


State 736

   92 layer_option: K_DIAGPITCH int_number int_number . ';'

    ';'  shift, and go to state 1082


State 737

   95 layer_option: K_DIAGWIDTH int_number ';' .

    $default  reduce using rule 95 (layer_option)


State 738

  716 firstPt: pt .

    $default  reduce using rule 716 (firstPt)


State 739

  185 layer_option: K_MINSIZE $@29 firstPt . otherPts ';'

    $default  reduce using rule 718 (otherPts)

    otherPts  go to state 1083


State 740

  181 layer_option: K_PREFERENCLOSURE layer_enclosure_type_opt int_number . int_number $@28 layer_preferenclosure_width_opt ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1084


State 741

  187 layer_arraySpacing_long: K_LONGARRAY .

    $default  reduce using rule 187 (layer_arraySpacing_long)


State 742

   86 layer_option: K_ARRAYSPACING $@4 layer_arraySpacing_long . layer_arraySpacing_width K_CUTSPACING int_number $@5 layer_arraySpacing_arraycuts ';'

    K_WIDTH  shift, and go to state 1085

    $default  reduce using rule 188 (layer_arraySpacing_width)

    layer_arraySpacing_width  go to state 1086


State 743

  153 layer_option: K_ANTENNAAREADIFFREDUCEPWL '(' pt . pt $@21 layer_diffusion_ratios ')' ';' $@22

    NUMBER  shift, and go to state 99
    '('     shift, and go to state 514

    int_number  go to state 515
    pt          go to state 1087


State 744

  150 layer_option: K_ANTENNAAREAMINUSDIFF int_number ';' .

    $default  reduce using rule 150 (layer_option)


State 745

  149 layer_option: K_ANTENNAGATEPLUSDIFF int_number ';' .

    $default  reduce using rule 149 (layer_option)


State 746

  342 start_foreign: K_FOREIGN $@54 T_STRING .

    $default  reduce using rule 342 (start_foreign)


State 747

  361 via_layer: K_LAYER $@55 T_STRING . ';'

    ';'  shift, and go to state 1088


State 748

  328 via_other_option: K_RESISTANCE int_number ';' .

    $default  reduce using rule 328 (via_other_option)


State 749

  313 via_viarule: K_VIARULE $@49 T_STRING . ';' K_CUTSIZE int_number int_number ';' K_LAYERS $@50 T_STRING T_STRING T_STRING ';' K_CUTSPACING int_number int_number ';' K_ENCLOSURE int_number int_number int_number int_number ';' $@51 via_viarule_options

    ';'  shift, and go to state 1089


State 750

  334 via_name_value_pair: T_STRING . NUMBER
  335                    | T_STRING . QSTRING
  336                    | T_STRING . T_STRING

    T_STRING  shift, and go to state 1090
    QSTRING   shift, and go to state 1091
    NUMBER    shift, and go to state 1092


State 751

  330 via_other_option: K_PROPERTY $@53 via_prop_list . ';'
  333 via_prop_list: via_prop_list . via_name_value_pair

    T_STRING  shift, and go to state 750
    ';'       shift, and go to state 1093

    via_name_value_pair  go to state 1094


State 752

  332 via_prop_list: via_name_value_pair .

    $default  reduce using rule 332 (via_prop_list)


State 753

  368 end_via: K_END $@57 . T_STRING

    T_STRING  shift, and go to state 1095


State 754

  325 via_more_options: via_more_options via_other_option .

    $default  reduce using rule 325 (via_more_options)


State 755

  497 pt: '(' int_number . int_number ')'

    NUMBER  shift, and go to state 99

    int_number  go to state 1096


State 756

  496 pt: int_number int_number .

    $default  reduce using rule 496 (pt)


State 757

  340 via_foreign: start_foreign orientation ';' .

    $default  reduce using rule 340 (via_foreign)


State 758

  338 via_foreign: start_foreign pt ';' .

    $default  reduce using rule 338 (via_foreign)


State 759

  339 via_foreign: start_foreign pt orientation . ';'

    ';'  shift, and go to state 1097


State 760

  366 via_geometry: K_POLYGON . maskColor $@56 firstPt nextPt nextPt otherPts ';'

    K_MASK  shift, and go to state 907

    $default  reduce using rule 416 (maskColor)

    maskColor  go to state 1098


State 761

  364 via_geometry: K_RECT . maskColor pt pt ';'

    K_MASK  shift, and go to state 907

    $default  reduce using rule 416 (maskColor)

    maskColor  go to state 1099


State 762

  363 via_geometries: via_geometries via_geometry .

    $default  reduce using rule 363 (via_geometries)


State 763

  373 viarule_generate: viarule_keyword K_GENERATE viarule_generate_default $@59 viarule_layer_list . opt_viarule_props end_viarule
  377 viarule_layer_list: viarule_layer_list . viarule_layer

    K_LAYER     shift, and go to state 146
    T_STRING    shift, and go to state 521
    K_PROPERTY  shift, and go to state 522

    $default  reduce using rule 378 (opt_viarule_props)

    opt_viarule_props   go to state 1100
    viarule_props       go to state 525
    viarule_prop        go to state 526
    viarule_layer       go to state 293
    viarule_layer_name  go to state 149


State 764

  394 viarule_layer_name: K_LAYER $@61 T_STRING ';' .

    $default  reduce using rule 394 (viarule_layer_name)


State 765

  386 viarule_prop: T_STRING T_STRING .

    $default  reduce using rule 386 (viarule_prop)


State 766

  387 viarule_prop: T_STRING QSTRING .

    $default  reduce using rule 387 (viarule_prop)


State 767

  388 viarule_prop: T_STRING NUMBER .

    $default  reduce using rule 388 (viarule_prop)


State 768

  383 viarule_prop: K_PROPERTY $@60 . viarule_prop_list ';'

    T_STRING    shift, and go to state 521
    K_PROPERTY  shift, and go to state 522

    viarule_prop       go to state 1101
    viarule_prop_list  go to state 1102


State 769

  392 via_name: via_keyword T_STRING . ';'

    ';'  shift, and go to state 1103


State 770

  407 end_viarule: K_END . $@62 T_STRING

    $default  reduce using rule 406 ($@62)

    $@62  go to state 1104


State 771

  371 viarule: viarule_keyword viarule_layer_list via_names opt_viarule_props end_viarule .

    $default  reduce using rule 371 (viarule)


State 772

  381 viarule_props: viarule_props viarule_prop .

    $default  reduce using rule 381 (viarule_props)


State 773

  397 viarule_layer_option: K_DIRECTION K_HORIZONTAL . ';'

    ';'  shift, and go to state 1105


State 774

  398 viarule_layer_option: K_DIRECTION K_VERTICAL . ';'

    ';'  shift, and go to state 1106


State 775

  405 viarule_layer_option: K_METALOVERHANG int_number . ';'

    ';'  shift, and go to state 1107


State 776

  404 viarule_layer_option: K_OVERHANG int_number . ';'

    ';'  shift, and go to state 1108


State 777

  401 viarule_layer_option: K_RECT pt . pt ';'

    NUMBER  shift, and go to state 99
    '('     shift, and go to state 514

    int_number  go to state 515
    pt          go to state 1109


State 778

  403 viarule_layer_option: K_RESISTANCE int_number . ';'

    ';'  shift, and go to state 1110


State 779

  402 viarule_layer_option: K_SPACING int_number . K_BY int_number ';'

    K_BY  shift, and go to state 1111


State 780

  400 viarule_layer_option: K_WIDTH int_number . K_TO int_number ';'

    K_TO  shift, and go to state 1112


State 781

  399 viarule_layer_option: K_ENCLOSURE int_number . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1113


State 782

  413 spacing: samenet_keyword T_STRING T_STRING . int_number ';'
  414        | samenet_keyword T_STRING T_STRING . int_number K_STACK ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1114


State 783

  423 ir_table: ir_tablename ir_table_values ';' .

    $default  reduce using rule 423 (ir_table)


State 784

  426 ir_table_value: int_number . int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1115


State 785

  425 ir_table_values: ir_table_values ir_table_value .

    $default  reduce using rule 425 (ir_table_values)


State 786

  482 site_class: K_CLASS K_CORE ';' .

    $default  reduce using rule 482 (site_class)


State 787

  481 site_class: K_CLASS K_PAD ';' .

    $default  reduce using rule 481 (site_class)


State 788

  483 site_class: K_CLASS K_VIRTUAL ';' .

    $default  reduce using rule 483 (site_class)


State 789

  474 end_site: K_END $@72 T_STRING .

    $default  reduce using rule 474 (end_site)


State 790

  477 site_option: K_SIZE int_number K_BY . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1116


State 791

  487 site_symmetry: K_X .

    $default  reduce using rule 487 (site_symmetry)


State 792

  488 site_symmetry: K_Y .

    $default  reduce using rule 488 (site_symmetry)


State 793

  489 site_symmetry: K_R90 .

    $default  reduce using rule 489 (site_symmetry)


State 794

  484 site_symmetry_statement: K_SYMMETRY site_symmetries ';' .

    $default  reduce using rule 484 (site_symmetry_statement)


State 795

  486 site_symmetries: site_symmetries site_symmetry .

    $default  reduce using rule 486 (site_symmetries)


State 796

  491 site_rowpattern_statement: K_ROWPATTERN $@73 site_rowpatterns . ';'
  493 site_rowpatterns: site_rowpatterns . site_rowpattern

    T_STRING  shift, and go to state 1117
    ';'       shift, and go to state 1118

    site_rowpattern  go to state 1119


State 797

  544 class_type: K_BLOCK K_BLACKBOX .

    $default  reduce using rule 544 (class_type)


State 798

  545 class_type: K_BLOCK K_SOFT .

    $default  reduce using rule 545 (class_type)


State 799

  561 core_type: K_FEEDTHRU .

    $default  reduce using rule 561 (core_type)


State 800

  564 core_type: K_SPACER .

    $default  reduce using rule 564 (core_type)


State 801

  562 core_type: K_TIEHIGH .

    $default  reduce using rule 562 (core_type)


State 802

  563 core_type: K_TIELOW .

    $default  reduce using rule 563 (core_type)


State 803

  565 core_type: K_ANTENNACELL .

    $default  reduce using rule 565 (core_type)


State 804

  566 core_type: K_WELLTAP .

    $default  reduce using rule 566 (core_type)


State 805

  553 class_type: K_CORE core_type .

    $default  reduce using rule 553 (class_type)


State 806

  541 class_type: K_COVER K_BUMP .

    $default  reduce using rule 541 (class_type)


State 807

  571 endcap_type: K_BOTTOMLEFT .

    $default  reduce using rule 571 (endcap_type)


State 808

  572 endcap_type: K_BOTTOMRIGHT .

    $default  reduce using rule 572 (endcap_type)


State 809

  568 endcap_type: K_POST .

    $default  reduce using rule 568 (endcap_type)


State 810

  567 endcap_type: K_PRE .

    $default  reduce using rule 567 (endcap_type)


State 811

  569 endcap_type: K_TOPLEFT .

    $default  reduce using rule 569 (endcap_type)


State 812

  570 endcap_type: K_TOPRIGHT .

    $default  reduce using rule 570 (endcap_type)


State 813

  554 class_type: K_ENDCAP endcap_type .

    $default  reduce using rule 554 (class_type)


State 814

  557 pad_type: K_INOUT .

    $default  reduce using rule 557 (pad_type)


State 815

  555 pad_type: K_INPUT .

    $default  reduce using rule 555 (pad_type)


State 816

  556 pad_type: K_OUTPUT .

    $default  reduce using rule 556 (pad_type)


State 817

  558 pad_type: K_POWER .

    $default  reduce using rule 558 (pad_type)


State 818

  559 pad_type: K_SPACER .

    $default  reduce using rule 559 (pad_type)


State 819

  560 pad_type: K_AREAIO .

    $default  reduce using rule 560 (pad_type)


State 820

  550 class_type: K_PAD pad_type .

    $default  reduce using rule 550 (class_type)


State 821

  539 macro_class: K_CLASS class_type ';' .

    $default  reduce using rule 539 (macro_class)


State 822

  753 macro_clocktype: K_CLOCKTYPE $@103 T_STRING . ';'

    ';'  shift, and go to state 1120


State 823

  586 macro_eeq: K_EEQ $@79 T_STRING . ';'

    ';'  shift, and go to state 1121


State 824

  574 macro_generate: K_GENERATE T_STRING T_STRING . ';'

    ';'  shift, and go to state 1122


State 825

  573 macro_generator: K_GENERATOR T_STRING ';' .

    $default  reduce using rule 573 (macro_generator)


State 826

  588 macro_leq: K_LEQ $@80 T_STRING . ';'

    ';'  shift, and go to state 1123


State 827

  579 macro_origin: K_ORIGIN pt ';' .

    $default  reduce using rule 579 (macro_origin)


State 828

  596 start_macro_pin: K_PIN $@81 T_STRING .

    $default  reduce using rule 596 (start_macro_pin)


State 829

  578 macro_power: K_POWER int_number ';' .

    $default  reduce using rule 578 (macro_power)


State 830

  593 macro_size: K_SIZE int_number K_BY . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1124


State 831

  577 macro_source: K_SOURCE K_BLOCK ';' .

    $default  reduce using rule 577 (macro_source)


State 832

  576 macro_source: K_SOURCE K_GENERATE ';' .

    $default  reduce using rule 576 (macro_source)


State 833

  575 macro_source: K_SOURCE K_USER ';' .

    $default  reduce using rule 575 (macro_source)


State 834

  533 macro_symmetry: K_X .

    $default  reduce using rule 533 (macro_symmetry)


State 835

  534 macro_symmetry: K_Y .

    $default  reduce using rule 534 (macro_symmetry)


State 836

  535 macro_symmetry: K_R90 .

    $default  reduce using rule 535 (macro_symmetry)


State 837

  530 macro_symmetry_statement: K_SYMMETRY macro_symmetries ';' .

    $default  reduce using rule 530 (macro_symmetry_statement)


State 838

  532 macro_symmetries: macro_symmetries macro_symmetry .

    $default  reduce using rule 532 (macro_symmetries)


State 839

  536 macro_name_value_pair: T_STRING . NUMBER
  537                      | T_STRING . QSTRING
  538                      | T_STRING . T_STRING

    T_STRING  shift, and go to state 1125
    QSTRING   shift, and go to state 1126
    NUMBER    shift, and go to state 1127


State 840

  527 macro_option: K_PROPERTY $@78 macro_prop_list . ';'
  529 macro_prop_list: macro_prop_list . macro_name_value_pair

    T_STRING  shift, and go to state 839
    ';'       shift, and go to state 1128

    macro_name_value_pair  go to state 1129


State 841

  528 macro_prop_list: macro_name_value_pair .

    $default  reduce using rule 528 (macro_prop_list)


State 842

  520 macro_option: K_FUNCTION K_BUFFER ';' .

    $default  reduce using rule 520 (macro_option)


State 843

  521 macro_option: K_FUNCTION K_INVERTER ';' .

    $default  reduce using rule 521 (macro_option)


State 844

  748 density_layer: K_LAYER $@101 . T_STRING ';' $@102 density_layer_rect density_layer_rects

    T_STRING  shift, and go to state 1130


State 845

  743 macro_density: K_DENSITY density_layer density_layers . K_END
  745 density_layers: density_layers . density_layer

    K_END    shift, and go to state 1131
    K_LAYER  shift, and go to state 577

    density_layer  go to state 1132


State 846

  583 macro_foreign: start_foreign orientation ';' .

    $default  reduce using rule 583 (macro_foreign)


State 847

  581 macro_foreign: start_foreign pt ';' .

    $default  reduce using rule 581 (macro_foreign)


State 848

  582 macro_foreign: start_foreign pt orientation . ';'

    ';'  shift, and go to state 1133


State 849

  503 end_macro: K_END $@77 . T_STRING

    T_STRING  shift, and go to state 1134


State 850

  589 macro_site: macro_site_word T_STRING ';' .

    $default  reduce using rule 589 (macro_site)


State 851

  725 sitePattern: T_STRING int_number . int_number orientation K_DO int_number K_BY int_number K_STEP int_number int_number
  726            | T_STRING int_number . int_number orientation

    NUMBER  shift, and go to state 99

    int_number  go to state 1135


State 852

  590 macro_site: macro_site_word sitePattern ';' .

    $default  reduce using rule 590 (macro_site)


State 853

  630 macro_pin_option: K_CAPACITANCE . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1136


State 854

  635 macro_pin_option: K_CURRENTSOURCE . K_ACTIVE ';'
  636                 | K_CURRENTSOURCE . K_RESISTIVE ';'

    K_ACTIVE     shift, and go to state 1137
    K_RESISTIVE  shift, and go to state 1138


State 855

  675 electrical_direction: K_DIRECTION . K_INPUT ';'
  676                     | K_DIRECTION . K_OUTPUT ';'
  677                     | K_DIRECTION . K_OUTPUT K_TRISTATE ';'
  678                     | K_DIRECTION . K_INOUT ';'
  679                     | K_DIRECTION . K_FEEDTHRU ';'

    K_FEEDTHRU  shift, and go to state 1139
    K_INOUT     shift, and go to state 1140
    K_INPUT     shift, and go to state 1141
    K_OUTPUT    shift, and go to state 1142


State 856

  598 end_macro_pin: K_END . $@82 T_STRING

    $default  reduce using rule 597 ($@82)

    $@82  go to state 1143


State 857

  617 macro_pin_option: K_FALLSATCUR . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1144


State 858

  615 macro_pin_option: K_FALLTHRESH . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1145


State 859

  629 macro_pin_option: K_INPUTNOISEMARGIN . $@87 int_number int_number ';'

    $default  reduce using rule 628 ($@87)

    $@87  go to state 1146


State 860

  639 macro_pin_option: K_IV_TABLES . T_STRING T_STRING ';'

    T_STRING  shift, and go to state 1147


State 861

  613 macro_pin_option: K_LEAKAGE . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1148


State 862

  608 macro_pin_option: K_LEQ . $@83 T_STRING ';'

    $default  reduce using rule 607 ($@83)

    $@83  go to state 1149


State 863

  631 macro_pin_option: K_MAXDELAY . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1150


State 864

  632 macro_pin_option: K_MAXLOAD . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1151


State 865

  623 macro_pin_option: K_MUSTJOIN . $@84 T_STRING ';'

    $default  reduce using rule 622 ($@84)

    $@84  go to state 1152


State 866

  625 macro_pin_option: K_OUTPUTNOISEMARGIN . $@85 int_number int_number ';'

    $default  reduce using rule 624 ($@85)

    $@85  go to state 1153


State 867

  680 start_macro_port: K_PORT .

    $default  reduce using rule 680 (start_macro_port)


State 868

  609 macro_pin_option: K_POWER . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1154


State 869

  634 macro_pin_option: K_PULLDOWNRES . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1155


State 870

  633 macro_pin_option: K_RESISTANCE . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1156


State 871

  616 macro_pin_option: K_RISESATCUR . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1157


State 872

  614 macro_pin_option: K_RISETHRESH . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1158


State 873

  637 macro_pin_option: K_RISEVOLTAGETHRESHOLD . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1159


State 874

  638 macro_pin_option: K_FALLVOLTAGETHRESHOLD . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1160


State 875

  612 macro_pin_option: K_SCANUSE . macro_scan_use ';'

    K_INPUT   shift, and go to state 1161
    K_OUTPUT  shift, and go to state 1162
    K_START   shift, and go to state 1163
    K_STOP    shift, and go to state 1164

    macro_scan_use  go to state 1165


State 876

  621 macro_pin_option: K_SHAPE . pin_shape ';'

    K_ABUTMENT  shift, and go to state 1166
    K_FEEDTHRU  shift, and go to state 1167
    K_RING      shift, and go to state 1168

    $default  reduce using rule 693 (pin_shape)

    pin_shape  go to state 1169


State 877

  620 macro_pin_option: K_TIEOFFR . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1170


State 878

  611 macro_pin_option: K_USE . macro_pin_use ';'

    K_ANALOG  shift, and go to state 1171
    K_CLOCK   shift, and go to state 1172
    K_DATA    shift, and go to state 1173
    K_GROUND  shift, and go to state 1174
    K_POWER   shift, and go to state 1175
    K_SIGNAL  shift, and go to state 1176

    macro_pin_use  go to state 1177


State 879

  619 macro_pin_option: K_VHI . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1178


State 880

  618 macro_pin_option: K_VLO . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1179


State 881

  642 macro_pin_option: K_PROPERTY . $@88 pin_prop_list ';'

    $default  reduce using rule 641 ($@88)

    $@88  go to state 1180


State 882

  627 macro_pin_option: K_OUTPUTRESISTANCE . $@86 int_number int_number ';'

    $default  reduce using rule 626 ($@86)

    $@86  go to state 1181


State 883

  640 macro_pin_option: K_TAPERRULE . T_STRING ';'

    T_STRING  shift, and go to state 1182


State 884

  645 macro_pin_option: K_ANTENNASIZE . int_number opt_layer_name ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1183


State 885

  647 macro_pin_option: K_ANTENNAMETALLENGTH . int_number opt_layer_name ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1184


State 886

  646 macro_pin_option: K_ANTENNAMETALAREA . NUMBER opt_layer_name ';'

    NUMBER  shift, and go to state 1185


State 887

  648 macro_pin_option: K_RISESLEWLIMIT . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1186


State 888

  649 macro_pin_option: K_FALLSLEWLIMIT . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1187


State 889

  650 macro_pin_option: K_ANTENNAPARTIALMETALAREA . NUMBER opt_layer_name ';'

    NUMBER  shift, and go to state 1188


State 890

  651 macro_pin_option: K_ANTENNAPARTIALMETALSIDEAREA . NUMBER opt_layer_name ';'

    NUMBER  shift, and go to state 1189


State 891

  654 macro_pin_option: K_ANTENNAGATEAREA . NUMBER opt_layer_name ';'

    NUMBER  shift, and go to state 1190


State 892

  653 macro_pin_option: K_ANTENNADIFFAREA . NUMBER opt_layer_name ';'

    NUMBER  shift, and go to state 1191


State 893

  655 macro_pin_option: K_ANTENNAMAXAREACAR . NUMBER req_layer_name ';'

    NUMBER  shift, and go to state 1192


State 894

  656 macro_pin_option: K_ANTENNAMAXSIDEAREACAR . NUMBER req_layer_name ';'

    NUMBER  shift, and go to state 1193


State 895

  652 macro_pin_option: K_ANTENNAPARTIALCUTAREA . NUMBER opt_layer_name ';'

    NUMBER  shift, and go to state 1194


State 896

  657 macro_pin_option: K_ANTENNAMAXCUTCAR . NUMBER req_layer_name ';'

    NUMBER  shift, and go to state 1195


State 897

  659 macro_pin_option: K_ANTENNAMODEL . $@89 pin_layer_oxide ';'

    $default  reduce using rule 658 ($@89)

    $@89  go to state 1196


State 898

  665 macro_pin_option: K_GROUNDSENSITIVITY . $@92 T_STRING ';'

    $default  reduce using rule 664 ($@92)

    $@92  go to state 1197


State 899

  661 macro_pin_option: K_NETEXPR . $@90 QSTRING ';'

    $default  reduce using rule 660 ($@90)

    $@90  go to state 1198


State 900

  663 macro_pin_option: K_SUPPLYSENSITIVITY . $@91 T_STRING ';'

    $default  reduce using rule 662 ($@91)

    $@91  go to state 1199


State 901

  601 macro_pin_option: start_foreign . ';'
  602                 | start_foreign . pt ';'
  603                 | start_foreign . pt orientation ';'
  604                 | start_foreign . K_STRUCTURE ';'
  605                 | start_foreign . K_STRUCTURE pt ';'
  606                 | start_foreign . K_STRUCTURE pt orientation ';'

    K_STRUCTURE  shift, and go to state 1200
    NUMBER       shift, and go to state 99
    ';'          shift, and go to state 1201
    '('          shift, and go to state 514

    int_number  go to state 515
    pt          go to state 1202


State 902

  594 macro_pin: start_macro_pin macro_pin_options end_macro_pin .

    $default  reduce using rule 594 (macro_pin)


State 903

  600 macro_pin_options: macro_pin_options macro_pin_option .

    $default  reduce using rule 600 (macro_pin_options)


State 904

  610 macro_pin_option: electrical_direction .

    $default  reduce using rule 610 (macro_pin_option)


State 905

  643 macro_pin_option: start_macro_port . macro_port_class_option geometries K_END
  644                 | start_macro_port . K_END

    K_CLASS  shift, and go to state 1203
    K_END    shift, and go to state 1204

    $default  reduce using rule 681 (macro_port_class_option)

    macro_port_class_option  go to state 1205


State 906

  700 geometry: K_LAYER $@93 . T_STRING $@94 layer_exceptpgnet layer_spacing ';'

    T_STRING  shift, and go to state 1206


State 907

  417 maskColor: K_MASK . int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1207


State 908

  702 geometry: K_PATH maskColor . firstPt otherPts ';'
  703         | K_PATH maskColor . K_ITERATE firstPt otherPts stepPattern ';'

    K_ITERATE  shift, and go to state 1208
    NUMBER     shift, and go to state 99
    '('        shift, and go to state 514

    int_number  go to state 515
    pt          go to state 738
    firstPt     go to state 1209


State 909

  706 geometry: K_POLYGON maskColor . firstPt nextPt nextPt otherPts ';'
  707         | K_POLYGON maskColor . K_ITERATE firstPt nextPt nextPt otherPts stepPattern ';'

    K_ITERATE  shift, and go to state 1210
    NUMBER     shift, and go to state 99
    '('        shift, and go to state 514

    int_number  go to state 515
    pt          go to state 738
    firstPt     go to state 1211


State 910

  704 geometry: K_RECT maskColor . pt pt ';'
  705         | K_RECT maskColor . K_ITERATE pt pt stepPattern ';'

    K_ITERATE  shift, and go to state 1212
    NUMBER     shift, and go to state 99
    '('        shift, and go to state 514

    int_number  go to state 515
    pt          go to state 1213


State 911

  723 via_placement: K_VIA K_ITERATE . maskColor pt $@96 T_STRING stepPattern ';'

    K_MASK  shift, and go to state 907

    $default  reduce using rule 416 (maskColor)

    maskColor  go to state 1214


State 912

  721 via_placement: K_VIA maskColor . pt $@95 T_STRING ';'

    NUMBER  shift, and go to state 99
    '('     shift, and go to state 514

    int_number  go to state 515
    pt          go to state 1215


State 913

  701 geometry: K_WIDTH int_number . ';'

    ';'  shift, and go to state 1216


State 914

  740 macro_obs: start_macro_obs geometries K_END .

    $default  reduce using rule 740 (macro_obs)


State 915

  697 geometries: geometry geometry_options .
  710 geometry_options: geometry_options . geometry

    K_LAYER    shift, and go to state 588
    K_PATH     shift, and go to state 589
    K_POLYGON  shift, and go to state 590
    K_RECT     shift, and go to state 591
    K_VIA      shift, and go to state 592
    K_WIDTH    shift, and go to state 593

    $default  reduce using rule 697 (geometries)

    geometry       go to state 1217
    via_placement  go to state 596


State 916

  756 end_timing: K_END . K_TIMING

    K_TIMING  shift, and go to state 1218


State 917

  808 risefall: K_FALL .

    $default  reduce using rule 808 (risefall)


State 918

  771 timing_option: K_FALLCS . int_number int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1219


State 919

  775 timing_option: K_FALLT0 . int_number int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1220


State 920

  773 timing_option: K_FALLSATT1 . int_number int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1221


State 921

  769 timing_option: K_FALLRS . int_number int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1222


State 922

  760 timing_option: K_FROMPIN . $@104 list_of_from_strings ';'

    $default  reduce using rule 759 ($@104)

    $@104  go to state 1223


State 923

  807 risefall: K_RISE .

    $default  reduce using rule 807 (risefall)


State 924

  770 timing_option: K_RISECS . int_number int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1224


State 925

  768 timing_option: K_RISERS . int_number int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1225


State 926

  772 timing_option: K_RISESATT1 . int_number int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1226


State 927

  774 timing_option: K_RISET0 . int_number int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1227


State 928

  762 timing_option: K_TOPIN . $@105 list_of_to_strings ';'

    $default  reduce using rule 761 ($@105)

    $@105  go to state 1228


State 929

  776 timing_option: K_UNATENESS . unateness ';'

    K_INVERT     shift, and go to state 1229
    K_NONINVERT  shift, and go to state 1230
    K_NONUNATE   shift, and go to state 1231

    unateness  go to state 1232


State 930

  777 timing_option: K_STABLE . K_SETUP int_number K_HOLD int_number risefall ';'

    K_SETUP  shift, and go to state 1233


State 931

  787 two_pin_trigger: K_SETUP .

    $default  reduce using rule 787 (two_pin_trigger)


State 932

  788 two_pin_trigger: K_HOLD .

    $default  reduce using rule 788 (two_pin_trigger)


State 933

  766 timing_option: K_TABLEAXIS . list_of_table_axis_dnumbers ';'

    NUMBER  shift, and go to state 99

    int_number                   go to state 1234
    list_of_table_axis_dnumbers  go to state 1235


State 934

  767 timing_option: K_TABLEENTRIES . list_of_table_entries ';'

    '('  shift, and go to state 1236

    list_of_table_entries  go to state 1237
    table_entry            go to state 1238


State 935

  783 timing_option: K_EXTENSION . ';'

    ';'  shift, and go to state 1239


State 936

  789 two_pin_trigger: K_RECOVERY .

    $default  reduce using rule 789 (two_pin_trigger)


State 937

  790 two_pin_trigger: K_SKEW .

    $default  reduce using rule 790 (two_pin_trigger)


State 938

  780 timing_option: K_SDFCONDSTART . QSTRING ';'

    QSTRING  shift, and go to state 1240


State 939

  781 timing_option: K_SDFCONDEND . QSTRING ';'

    QSTRING  shift, and go to state 1241


State 940

  782 timing_option: K_SDFCOND . QSTRING ';'

    QSTRING  shift, and go to state 1242


State 941

  784 one_pin_trigger: K_MPWH .

    $default  reduce using rule 784 (one_pin_trigger)


State 942

  785 one_pin_trigger: K_MPWL .

    $default  reduce using rule 785 (one_pin_trigger)


State 943

  786 one_pin_trigger: K_PERIOD .

    $default  reduce using rule 786 (one_pin_trigger)


State 944

  754 timing: start_timing timing_options end_timing .

    $default  reduce using rule 754 (timing)


State 945

  758 timing_options: timing_options timing_option .

    $default  reduce using rule 758 (timing_options)


State 946

  779 timing_option: one_pin_trigger . K_TABLEDIMENSION int_number int_number int_number ';'

    K_TABLEDIMENSION  shift, and go to state 1243


State 947

  778 timing_option: two_pin_trigger . from_pin_trigger to_pin_trigger K_TABLEDIMENSION int_number int_number int_number ';'

    K_ANYEDGE  shift, and go to state 1244
    K_POSEDGE  shift, and go to state 1245
    K_NEGEDGE  shift, and go to state 1246

    from_pin_trigger  go to state 1247


State 948

  764 timing_option: risefall . K_INTRINSIC int_number int_number $@106 slew_spec K_VARIABLE int_number int_number ';'
  765              | risefall . delay_or_transition K_UNATENESS unateness K_TABLEDIMENSION int_number int_number int_number ';'

    K_INTRINSIC       shift, and go to state 1248
    K_DELAY           shift, and go to state 1249
    K_TRANSITIONTIME  shift, and go to state 1250

    delay_or_transition  go to state 1251


State 949

  725 sitePattern: T_STRING . int_number int_number orientation K_DO int_number K_BY int_number K_STEP int_number int_number
  726            | T_STRING . int_number int_number orientation

    NUMBER  shift, and go to state 99

    int_number  go to state 851


State 950

  827 array_rule: K_CANPLACE $@111 sitePattern . ';'

    ';'  shift, and go to state 1252


State 951

  829 array_rule: K_CANNOTOCCUPY $@112 sitePattern . ';'

    ';'  shift, and go to state 1253


State 952

  729 trackPattern: K_X . int_number K_DO int_number K_STEP int_number $@97 K_LAYER $@98 trackLayers
  733             | K_X . int_number K_DO int_number K_STEP int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1254


State 953

  732 trackPattern: K_Y . int_number K_DO int_number K_STEP int_number $@99 K_LAYER $@100 trackLayers
  734             | K_Y . int_number K_DO int_number K_STEP int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1255


State 954

  831 array_rule: K_TRACKS $@113 trackPattern . ';'

    ';'  shift, and go to state 1256


State 955

  738 gcellPattern: K_X . int_number K_DO int_number K_STEP int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1257


State 956

  739 gcellPattern: K_Y . int_number K_DO int_number K_STEP int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1258


State 957

  834 array_rule: K_GCELLGRID $@114 gcellPattern . ';'

    ';'  shift, and go to state 1259


State 958

  835 array_rule: K_DEFAULTCAP int_number cap_list . K_END K_DEFAULTCAP
  845 cap_list: cap_list . one_cap

    K_END      shift, and go to state 1260
    K_MINPINS  shift, and go to state 1261

    one_cap  go to state 1262


State 959

  825 array_rule: site_word $@110 sitePattern . ';'

    ';'  shift, and go to state 1263


State 960

  821 end_array: K_END $@109 . T_STRING

    T_STRING  shift, and go to state 1264


State 961

  832 array_rule: floorplan_start floorplan_list K_END . T_STRING

    T_STRING  shift, and go to state 1265


State 962

  841 floorplan_element: K_CANPLACE . $@115 sitePattern ';'

    $default  reduce using rule 840 ($@115)

    $@115  go to state 1266


State 963

  843 floorplan_element: K_CANNOTOCCUPY . $@116 sitePattern ';'

    $default  reduce using rule 842 ($@116)

    $@116  go to state 1267


State 964

  839 floorplan_list: floorplan_list floorplan_element .

    $default  reduce using rule 839 (floorplan_list)


State 965

  437 nd_hardspacing: K_HARDSPACING ';' .

    $default  reduce using rule 437 (nd_hardspacing)


State 966

  433 nondefault_rule: K_NONDEFAULTRULE $@63 T_STRING $@64 nd_hardspacing nd_rules . $@65 end_nd_rule
  439 nd_rules: nd_rules . nd_rule

    K_LAYER       shift, and go to state 1268
    K_SPACING     shift, and go to state 14
    K_VIA         shift, and go to state 16
    T_STRING      shift, and go to state 1269
    K_PROPERTY    shift, and go to state 1270
    K_MINCUTS     shift, and go to state 1271
    K_USEVIA      shift, and go to state 1272
    K_USEVIARULE  shift, and go to state 1273

    $default  reduce using rule 432 ($@65)

    via            go to state 1274
    via_keyword    go to state 62
    start_via      go to state 63
    spacing_rule   go to state 1275
    start_spacing  go to state 68
    $@65           go to state 1276
    nd_rule        go to state 1277
    usevia         go to state 1278
    useviarule     go to state 1279
    mincuts        go to state 1280
    nd_prop        go to state 1281
    nd_layer       go to state 1282


State 967

  870 expression: K_IF b_expr . then expression else expression
  878 b_expr: b_expr . K_EQ b_expr
  879       | b_expr . K_NE b_expr
  880       | b_expr . K_AND b_expr
  881       | b_expr . K_OR b_expr

    K_THEN  shift, and go to state 1283
    K_EQ    shift, and go to state 998
    K_NE    shift, and go to state 999
    K_OR    shift, and go to state 1000
    K_AND   shift, and go to state 1001
    '\n'    shift, and go to state 1284

    then  go to state 1285


State 968

  864 expression: expression . '+' expression
  865           | expression . '-' expression
  866           | expression . '*' expression
  867           | expression . '/' expression
  868           | '-' expression .

    $default  reduce using rule 868 (expression)


State 969

  864 expression: expression . '+' expression
  865           | expression . '-' expression
  866           | expression . '*' expression
  867           | expression . '/' expression
  869           | '(' expression . ')'

    '-'  shift, and go to state 970
    '+'  shift, and go to state 971
    '*'  shift, and go to state 972
    '/'  shift, and go to state 973
    ')'  shift, and go to state 1286


State 970

  865 expression: expression '-' . expression

    NUMBER  shift, and go to state 99
    K_IF    shift, and go to state 610
    '-'     shift, and go to state 611
    '('     shift, and go to state 612

    int_number  go to state 613
    expression  go to state 1287


State 971

  864 expression: expression '+' . expression

    NUMBER  shift, and go to state 99
    K_IF    shift, and go to state 610
    '-'     shift, and go to state 611
    '('     shift, and go to state 612

    int_number  go to state 613
    expression  go to state 1288


State 972

  866 expression: expression '*' . expression

    NUMBER  shift, and go to state 99
    K_IF    shift, and go to state 610
    '-'     shift, and go to state 611
    '('     shift, and go to state 612

    int_number  go to state 613
    expression  go to state 1289


State 973

  867 expression: expression '/' . expression

    NUMBER  shift, and go to state 99
    K_IF    shift, and go to state 610
    '-'     shift, and go to state 611
    '('     shift, and go to state 612

    int_number  go to state 613
    expression  go to state 1290


State 974

  858 dtrm: ';' .

    $default  reduce using rule 858 (dtrm)


State 975

  859 dtrm: '\n' .

    $default  reduce using rule 859 (dtrm)


State 976

  852 def_statement: K_DEFINE $@119 T_STRING '=' expression dtrm .

    $default  reduce using rule 852 (def_statement)


State 977

  878 b_expr: b_expr . K_EQ b_expr
  879       | b_expr . K_NE b_expr
  880       | b_expr . K_AND b_expr
  881       | b_expr . K_OR b_expr
  889 s_expr: K_IF b_expr . then s_expr else s_expr

    K_THEN  shift, and go to state 1283
    K_EQ    shift, and go to state 998
    K_NE    shift, and go to state 999
    K_OR    shift, and go to state 1000
    K_AND   shift, and go to state 1001
    '\n'    shift, and go to state 1284

    then  go to state 1291


State 978

  887 s_expr: s_expr . '+' s_expr
  888       | '(' s_expr . ')'

    '+'  shift, and go to state 979
    ')'  shift, and go to state 1292


State 979

  887 s_expr: s_expr '+' . s_expr

    QSTRING  shift, and go to state 615
    K_IF     shift, and go to state 616
    '('      shift, and go to state 617

    s_expr  go to state 1293


State 980

  854 def_statement: K_DEFINES $@120 T_STRING '=' s_expr dtrm .

    $default  reduce using rule 854 (def_statement)


State 981

  870 expression: K_IF b_expr . then expression else expression
  878 b_expr: b_expr . K_EQ b_expr
  879       | b_expr . K_NE b_expr
  880       | b_expr . K_AND b_expr
  881       | b_expr . K_OR b_expr
  884       | K_IF b_expr . then b_expr else b_expr
  889 s_expr: K_IF b_expr . then s_expr else s_expr

    K_THEN  shift, and go to state 1283
    K_EQ    shift, and go to state 998
    K_NE    shift, and go to state 999
    K_OR    shift, and go to state 1000
    K_AND   shift, and go to state 1001
    '\n'    shift, and go to state 1284

    then  go to state 1294


State 982

  878 b_expr: b_expr . K_EQ b_expr
  879       | b_expr . K_NE b_expr
  880       | b_expr . K_AND b_expr
  881       | b_expr . K_OR b_expr
  882       | K_NOT b_expr .

    $default  reduce using rule 882 (b_expr)


State 983

  864 expression: expression . '+' expression
  865           | expression . '-' expression
  866           | expression . '*' expression
  867           | expression . '/' expression
  869           | '(' expression . ')'
  872 b_expr: expression . relop expression
  873       | expression . K_AND expression
  874       | expression . K_OR expression

    K_EQ   shift, and go to state 986
    K_NE   shift, and go to state 987
    K_LE   shift, and go to state 988
    K_LT   shift, and go to state 989
    K_GE   shift, and go to state 990
    K_GT   shift, and go to state 991
    K_OR   shift, and go to state 992
    K_AND  shift, and go to state 993
    '-'    shift, and go to state 970
    '+'    shift, and go to state 971
    '*'    shift, and go to state 972
    '/'    shift, and go to state 973
    ')'    shift, and go to state 1286
    '='    shift, and go to state 994
    '<'    shift, and go to state 995
    '>'    shift, and go to state 996

    relop  go to state 997


State 984

  878 b_expr: b_expr . K_EQ b_expr
  879       | b_expr . K_NE b_expr
  880       | b_expr . K_AND b_expr
  881       | b_expr . K_OR b_expr
  883       | '(' b_expr . ')'

    K_EQ   shift, and go to state 998
    K_NE   shift, and go to state 999
    K_OR   shift, and go to state 1000
    K_AND  shift, and go to state 1001
    ')'    shift, and go to state 1295


State 985

  875 b_expr: s_expr . relop s_expr
  876       | s_expr . K_AND s_expr
  877       | s_expr . K_OR s_expr
  887 s_expr: s_expr . '+' s_expr
  888       | '(' s_expr . ')'

    K_EQ   shift, and go to state 986
    K_NE   shift, and go to state 987
    K_LE   shift, and go to state 988
    K_LT   shift, and go to state 989
    K_GE   shift, and go to state 990
    K_GT   shift, and go to state 991
    K_OR   shift, and go to state 1003
    K_AND  shift, and go to state 1004
    '+'    shift, and go to state 979
    ')'    shift, and go to state 1292
    '='    shift, and go to state 994
    '<'    shift, and go to state 995
    '>'    shift, and go to state 996

    relop  go to state 1005


State 986

  895 relop: K_EQ .

    $default  reduce using rule 895 (relop)


State 987

  896 relop: K_NE .

    $default  reduce using rule 896 (relop)


State 988

  891 relop: K_LE .

    $default  reduce using rule 891 (relop)


State 989

  892 relop: K_LT .

    $default  reduce using rule 892 (relop)


State 990

  893 relop: K_GE .

    $default  reduce using rule 893 (relop)


State 991

  894 relop: K_GT .

    $default  reduce using rule 894 (relop)


State 992

  874 b_expr: expression K_OR . expression

    NUMBER  shift, and go to state 99
    K_IF    shift, and go to state 610
    '-'     shift, and go to state 611
    '('     shift, and go to state 612

    int_number  go to state 613
    expression  go to state 1296


State 993

  873 b_expr: expression K_AND . expression

    NUMBER  shift, and go to state 99
    K_IF    shift, and go to state 610
    '-'     shift, and go to state 611
    '('     shift, and go to state 612

    int_number  go to state 613
    expression  go to state 1297


State 994

  897 relop: '=' .

    $default  reduce using rule 897 (relop)


State 995

  898 relop: '<' .

    $default  reduce using rule 898 (relop)


State 996

  899 relop: '>' .

    $default  reduce using rule 899 (relop)


State 997

  872 b_expr: expression relop . expression

    NUMBER  shift, and go to state 99
    K_IF    shift, and go to state 610
    '-'     shift, and go to state 611
    '('     shift, and go to state 612

    int_number  go to state 613
    expression  go to state 1298


State 998

  878 b_expr: b_expr K_EQ . b_expr

    QSTRING  shift, and go to state 615
    NUMBER   shift, and go to state 99
    K_IF     shift, and go to state 619
    K_FALSE  shift, and go to state 620
    K_TRUE   shift, and go to state 621
    K_NOT    shift, and go to state 622
    '-'      shift, and go to state 611
    '('      shift, and go to state 623

    int_number  go to state 613
    expression  go to state 624
    b_expr      go to state 1299
    s_expr      go to state 626


State 999

  879 b_expr: b_expr K_NE . b_expr

    QSTRING  shift, and go to state 615
    NUMBER   shift, and go to state 99
    K_IF     shift, and go to state 619
    K_FALSE  shift, and go to state 620
    K_TRUE   shift, and go to state 621
    K_NOT    shift, and go to state 622
    '-'      shift, and go to state 611
    '('      shift, and go to state 623

    int_number  go to state 613
    expression  go to state 624
    b_expr      go to state 1300
    s_expr      go to state 626


State 1000

  881 b_expr: b_expr K_OR . b_expr

    QSTRING  shift, and go to state 615
    NUMBER   shift, and go to state 99
    K_IF     shift, and go to state 619
    K_FALSE  shift, and go to state 620
    K_TRUE   shift, and go to state 621
    K_NOT    shift, and go to state 622
    '-'      shift, and go to state 611
    '('      shift, and go to state 623

    int_number  go to state 613
    expression  go to state 624
    b_expr      go to state 1301
    s_expr      go to state 626


State 1001

  880 b_expr: b_expr K_AND . b_expr

    QSTRING  shift, and go to state 615
    NUMBER   shift, and go to state 99
    K_IF     shift, and go to state 619
    K_FALSE  shift, and go to state 620
    K_TRUE   shift, and go to state 621
    K_NOT    shift, and go to state 622
    '-'      shift, and go to state 611
    '('      shift, and go to state 623

    int_number  go to state 613
    expression  go to state 624
    b_expr      go to state 1302
    s_expr      go to state 626


State 1002

  856 def_statement: K_DEFINEB $@121 T_STRING '=' b_expr dtrm .

    $default  reduce using rule 856 (def_statement)


State 1003

  877 b_expr: s_expr K_OR . s_expr

    QSTRING  shift, and go to state 615
    K_IF     shift, and go to state 616
    '('      shift, and go to state 617

    s_expr  go to state 1303


State 1004

  876 b_expr: s_expr K_AND . s_expr

    QSTRING  shift, and go to state 615
    K_IF     shift, and go to state 616
    '('      shift, and go to state 617

    s_expr  go to state 1304


State 1005

  875 b_expr: s_expr relop . s_expr

    QSTRING  shift, and go to state 615
    K_IF     shift, and go to state 616
    '('      shift, and go to state 617

    s_expr  go to state 1305


State 1006

  907 prop_stmt: K_COMPONENTPIN $@124 T_STRING . prop_define ';'

    K_STRING         shift, and go to state 1306
    K_INTEGER        shift, and go to state 1307
    K_REAL           shift, and go to state 1308
    K_NAMEMAPSTRING  shift, and go to state 1309

    prop_define  go to state 1310


State 1007

  917 prop_stmt: K_LAYER $@129 T_STRING . prop_define ';'

    K_STRING         shift, and go to state 1306
    K_INTEGER        shift, and go to state 1307
    K_REAL           shift, and go to state 1308
    K_NAMEMAPSTRING  shift, and go to state 1309

    prop_define  go to state 1311


State 1008

  905 prop_stmt: K_LIBRARY $@123 T_STRING . prop_define ';'

    K_STRING         shift, and go to state 1306
    K_INTEGER        shift, and go to state 1307
    K_REAL           shift, and go to state 1308
    K_NAMEMAPSTRING  shift, and go to state 1309

    prop_define  go to state 1312


State 1009

  911 prop_stmt: K_MACRO $@126 T_STRING . prop_define ';'

    K_STRING         shift, and go to state 1306
    K_INTEGER        shift, and go to state 1307
    K_REAL           shift, and go to state 1308
    K_NAMEMAPSTRING  shift, and go to state 1309

    prop_define  go to state 1313


State 1010

  919 prop_stmt: K_NONDEFAULTRULE $@130 T_STRING . prop_define ';'

    K_STRING         shift, and go to state 1306
    K_INTEGER        shift, and go to state 1307
    K_REAL           shift, and go to state 1308
    K_NAMEMAPSTRING  shift, and go to state 1309

    prop_define  go to state 1314


State 1011

  909 prop_stmt: K_PIN $@125 T_STRING . prop_define ';'

    K_STRING         shift, and go to state 1306
    K_INTEGER        shift, and go to state 1307
    K_REAL           shift, and go to state 1308
    K_NAMEMAPSTRING  shift, and go to state 1309

    prop_define  go to state 1315


State 1012

  913 prop_stmt: K_VIA $@127 T_STRING . prop_define ';'

    K_STRING         shift, and go to state 1306
    K_INTEGER        shift, and go to state 1307
    K_REAL           shift, and go to state 1308
    K_NAMEMAPSTRING  shift, and go to state 1309

    prop_define  go to state 1316


State 1013

  915 prop_stmt: K_VIARULE $@128 T_STRING . prop_define ';'

    K_STRING         shift, and go to state 1306
    K_INTEGER        shift, and go to state 1307
    K_REAL           shift, and go to state 1308
    K_NAMEMAPSTRING  shift, and go to state 1309

    prop_define  go to state 1317


State 1014

  999 correction_table_item: K_EDGERATE int_number . ';'

    ';'  shift, and go to state 1318


State 1015

  1002 output_list: K_OUTPUTRESISTANCE $@144 . numo_list ';' corr_victim_list

    NUMBER  shift, and go to state 99

    int_number  go to state 1319
    numo_list   go to state 1320


State 1016

  996 end_correctiontable: K_END . K_CORRECTIONTABLE

    K_CORRECTIONTABLE  shift, and go to state 1321


State 1017

  995 correctiontable: K_CORRECTIONTABLE int_number ';' $@143 correction_table_list end_correctiontable . dtrm

    ';'   shift, and go to state 974
    '\n'  shift, and go to state 975

    $default  reduce using rule 857 (dtrm)

    dtrm  go to state 1322


State 1018

  998 correction_table_list: correction_table_list correction_table_item .

    $default  reduce using rule 998 (correction_table_list)


State 1019

  982 noise_table_entry: K_EDGERATE int_number . ';'

    ';'  shift, and go to state 1323


State 1020

  985 output_resistance_entry: K_OUTPUTRESISTANCE $@141 . num_list ';' victim_list

    NUMBER  shift, and go to state 99

    int_number  go to state 1324
    num_list    go to state 1325


State 1021

  979 end_noisetable: K_END . K_NOISETABLE

    K_NOISETABLE  shift, and go to state 1326


State 1022

  978 noisetable: K_NOISETABLE int_number $@140 ';' noise_table_list end_noisetable . dtrm

    ';'   shift, and go to state 974
    '\n'  shift, and go to state 975

    $default  reduce using rule 857 (dtrm)

    dtrm  go to state 1327


State 1023

  981 noise_table_list: noise_table_list noise_table_entry .

    $default  reduce using rule 981 (noise_table_list)


State 1024

  848 msg_statement: K_MESSAGE $@117 T_STRING '=' s_expr dtrm .

    $default  reduce using rule 848 (msg_statement)


State 1025

  850 create_file_statement: K_CREATEFILE $@118 T_STRING '=' s_expr dtrm .

    $default  reduce using rule 850 (create_file_statement)


State 1026

  303 maxstack_via: K_MAXVIASTACK int_number K_RANGE $@47 T_STRING T_STRING . ';'

    ';'  shift, and go to state 1328


State 1027

   70 units_rule: K_CAPACITANCE K_PICOFARADS int_number ';' .

    $default  reduce using rule 70 (units_rule)


State 1028

   73 units_rule: K_CURRENT K_MILLIAMPS int_number ';' .

    $default  reduce using rule 73 (units_rule)


State 1029

   75 units_rule: K_DATABASE K_MICRONS int_number ';' .

    $default  reduce using rule 75 (units_rule)


State 1030

   72 units_rule: K_POWER K_MILLIWATTS int_number ';' .

    $default  reduce using rule 72 (units_rule)


State 1031

   71 units_rule: K_RESISTANCE K_OHMS int_number ';' .

    $default  reduce using rule 71 (units_rule)


State 1032

   69 units_rule: K_TIME K_NANOSECONDS int_number ';' .

    $default  reduce using rule 69 (units_rule)


State 1033

   74 units_rule: K_VOLTAGE K_VOLTS int_number ';' .

    $default  reduce using rule 74 (units_rule)


State 1034

   76 units_rule: K_FREQUENCY K_MEGAHERTZ NUMBER ';' .

    $default  reduce using rule 76 (units_rule)


State 1035

  107 layer_option: K_CAPACITANCE K_CPERSQDIST K_PWL '(' . cap_points ')' ';'

    '('  shift, and go to state 1329

    cap_points  go to state 1330
    cap_point   go to state 1331


State 1036

  106 layer_option: K_CAPACITANCE K_CPERSQDIST int_number ';' .

    $default  reduce using rule 106 (layer_option)


State 1037

   94 layer_option: K_OFFSET int_number int_number ';' .

    $default  reduce using rule 94 (layer_option)


State 1038

   90 layer_option: K_PITCH int_number int_number ';' .

    $default  reduce using rule 90 (layer_option)


State 1039

  105 layer_option: K_RESISTANCE K_RPERSQ K_PWL '(' . res_points ')' ';'

    '('  shift, and go to state 1332

    res_points  go to state 1333
    res_point   go to state 1334


State 1040

  104 layer_option: K_RESISTANCE K_RPERSQ int_number ';' .

    $default  reduce using rule 104 (layer_option)


State 1041

  947 layer_spacing_opt: K_SAMENET . $@132 opt_samenetPGonly

    $default  reduce using rule 946 ($@132)

    $@132  go to state 1335


State 1042

  945 layer_spacing_opt: K_CENTERTOCENTER .

    $default  reduce using rule 945 (layer_spacing_opt)


State 1043

  948 layer_spacing_opt: K_PARALLELOVERLAP .

    $default  reduce using rule 948 (layer_spacing_opt)


State 1044

  100 layer_option: K_SPACING int_number $@6 layer_spacing_opts . layer_spacing_cut_routing ';'

    K_AREA             shift, and go to state 1336
    K_LAYER            shift, and go to state 1337
    K_RANGE            shift, and go to state 1338
    K_LENGTHTHRESHOLD  shift, and go to state 1339
    K_ADJACENTCUTS     shift, and go to state 1340
    K_ENDOFLINE        shift, and go to state 1341
    K_ENDOFNOTCHWIDTH  shift, and go to state 1342
    K_NOTCHLENGTH      shift, and go to state 1343

    $default  reduce using rule 949 (layer_spacing_cut_routing)

    layer_spacing_cut_routing  go to state 1344


State 1045

  944 layer_spacing_opts: layer_spacing_opt . layer_spacing_opts

    K_SAMENET          shift, and go to state 1041
    K_CENTERTOCENTER   shift, and go to state 1042
    K_PARALLELOVERLAP  shift, and go to state 1043

    $default  reduce using rule 943 (layer_spacing_opts)

    layer_spacing_opts  go to state 1345
    layer_spacing_opt   go to state 1045


State 1046

  260 layer_prop: T_STRING T_STRING .

    $default  reduce using rule 260 (layer_prop)


State 1047

  261 layer_prop: T_STRING QSTRING .

    $default  reduce using rule 261 (layer_prop)


State 1048

  262 layer_prop: T_STRING NUMBER .

    $default  reduce using rule 262 (layer_prop)


State 1049

  119 layer_option: K_PROPERTY $@8 layer_prop_list ';' .

    $default  reduce using rule 119 (layer_option)


State 1050

  259 layer_prop_list: layer_prop_list layer_prop .

    $default  reduce using rule 259 (layer_prop_list)


State 1051

  265 current_density_pwl: '(' . int_number int_number ')'

    NUMBER  shift, and go to state 99

    int_number  go to state 1346


State 1052

  116 layer_option: K_CURRENTDEN K_PWL '(' current_density_pwl_list . ')' ';'
  264 current_density_pwl_list: current_density_pwl_list . current_density_pwl

    '('  shift, and go to state 1051
    ')'  shift, and go to state 1347

    current_density_pwl  go to state 1348


State 1053

  263 current_density_pwl_list: current_density_pwl .

    $default  reduce using rule 263 (current_density_pwl_list)


State 1054

  117 layer_option: K_CURRENTDEN '(' int_number int_number . ')' ';'

    ')'  shift, and go to state 1349


State 1055

  122 layer_option: K_ACCURRENTDENSITY layer_table_type int_number ';' .

    $default  reduce using rule 122 (layer_option)


State 1056

  246 layer_frequency: K_FREQUENCY . NUMBER $@39 number_list ';' $@40 ac_layer_table_opt K_TABLEENTRIES NUMBER $@41 number_list ';'

    NUMBER  shift, and go to state 1350


State 1057

  121 layer_option: K_ACCURRENTDENSITY layer_table_type $@9 layer_frequency .

    $default  reduce using rule 121 (layer_option)


State 1058

  129 layer_option: K_DCCURRENTDENSITY K_AVERAGE K_WIDTH int_number . $@12 int_number_list ';' $@13 dc_layer_table

    $default  reduce using rule 127 ($@12)

    $@12  go to state 1351


State 1059

  126 layer_option: K_DCCURRENTDENSITY K_AVERAGE K_CUTAREA NUMBER . $@10 number_list ';' $@11 dc_layer_table

    $default  reduce using rule 124 ($@10)

    $@10  go to state 1352


State 1060

  123 layer_option: K_DCCURRENTDENSITY K_AVERAGE int_number ';' .

    $default  reduce using rule 123 (layer_option)


State 1061

  234 layer_antenna_pwl: K_PWL '(' . pt pt $@38 layer_diffusion_ratios ')'

    NUMBER  shift, and go to state 99
    '('     shift, and go to state 514

    int_number  go to state 515
    pt          go to state 1353


State 1062

  132 layer_option: K_ANTENNADIFFAREARATIO $@14 layer_antenna_pwl ';' .

    $default  reduce using rule 132 (layer_option)


State 1063

  135 layer_option: K_ANTENNACUMDIFFAREARATIO $@15 layer_antenna_pwl ';' .

    $default  reduce using rule 135 (layer_option)


State 1064

  239 layer_antenna_duo: K_DIFFUSEONLY .

    $default  reduce using rule 239 (layer_antenna_duo)


State 1065

  137 layer_option: K_ANTENNAAREAFACTOR int_number $@16 layer_antenna_duo . ';'

    ';'  shift, and go to state 1354


State 1066

  140 layer_option: K_ANTENNADIFFSIDEAREARATIO $@17 layer_antenna_pwl ';' .

    $default  reduce using rule 140 (layer_option)


State 1067

  143 layer_option: K_ANTENNACUMDIFFSIDEAREARATIO $@18 layer_antenna_pwl ';' .

    $default  reduce using rule 143 (layer_option)


State 1068

  145 layer_option: K_ANTENNASIDEAREAFACTOR int_number $@19 layer_antenna_duo . ';'

    ';'  shift, and go to state 1355


State 1069

  164 layer_option: K_DENSITYCHECKWINDOW int_number int_number ';' .

    $default  reduce using rule 164 (layer_option)


State 1070

  172 layer_option: K_MINIMUMCUT int_number K_WIDTH int_number . $@24 layer_minimumcut_within layer_minimumcut_from layer_minimumcut_length ';'

    $default  reduce using rule 171 ($@24)

    $@24  go to state 1356


State 1071

  147 layer_option: K_ANTENNAMODEL $@20 layer_oxide ';' .

    $default  reduce using rule 147 (layer_option)


State 1072

  179 layer_option: K_ENCLOSURE layer_enclosure_type_opt int_number int_number . $@27 layer_enclosure_width_opt ';'

    $default  reduce using rule 178 ($@27)

    $@27  go to state 1357


State 1073

  283 layer_minen_width: K_WIDTH . int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1358


State 1074

  170 layer_option: K_MINENCLOSEDAREA NUMBER $@23 layer_minen_width . ';'

    ';'  shift, and go to state 1359


State 1075

  174 layer_option: K_MINSTEP int_number $@25 layer_minstep_options . ';'
  225 layer_minstep_options: layer_minstep_options . layer_minstep_option

    K_STEP           shift, and go to state 1360
    K_INSIDECORNER   shift, and go to state 1361
    K_LENGTHSUM      shift, and go to state 1362
    K_OUTSIDECORNER  shift, and go to state 1363
    K_MAXEDGES       shift, and go to state 1364
    ';'              shift, and go to state 1365

    layer_minstep_option  go to state 1366
    layer_minstep_type    go to state 1367


State 1076

  175 layer_option: K_PROTRUSIONWIDTH int_number K_LENGTH int_number . K_WIDTH int_number ';'

    K_WIDTH  shift, and go to state 1368


State 1077

  102 layer_option: K_SPACINGTABLE K_ORTHOGONAL K_WITHIN int_number . K_SPACING int_number $@7 layer_spacingtable_opts ';'

    K_SPACING  shift, and go to state 1369


State 1078

  202 sp_options: K_INFLUENCE K_WIDTH . int_number K_WITHIN int_number K_SPACING int_number $@36 layer_sp_influence_widths

    NUMBER  shift, and go to state 99

    int_number  go to state 1370


State 1079

  197 sp_options: K_PARALLELRUNLENGTH int_number . $@30 int_number_list $@31 K_WIDTH int_number $@32 int_number_list $@33 layer_sp_parallel_widths

    $default  reduce using rule 193 ($@30)

    $@30  go to state 1371


State 1080

  200 sp_options: K_TWOWIDTHS K_WIDTH . int_number layer_sp_TwoWidthsPRL int_number $@34 int_number_list $@35 layer_sp_TwoWidths

    NUMBER  shift, and go to state 99

    int_number  go to state 1372


State 1081

  177 layer_option: K_SPACINGTABLE $@26 sp_options ';' .

    $default  reduce using rule 177 (layer_option)


State 1082

   92 layer_option: K_DIAGPITCH int_number int_number ';' .

    $default  reduce using rule 92 (layer_option)


State 1083

  185 layer_option: K_MINSIZE $@29 firstPt otherPts . ';'
  719 otherPts: otherPts . nextPt

    NUMBER  shift, and go to state 99
    ';'     shift, and go to state 1373
    '('     shift, and go to state 514

    int_number  go to state 515
    pt          go to state 1374
    nextPt      go to state 1375


State 1084

  181 layer_option: K_PREFERENCLOSURE layer_enclosure_type_opt int_number int_number . $@28 layer_preferenclosure_width_opt ';'

    $default  reduce using rule 180 ($@28)

    $@28  go to state 1376


State 1085

  189 layer_arraySpacing_width: K_WIDTH . int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1377


State 1086

   86 layer_option: K_ARRAYSPACING $@4 layer_arraySpacing_long layer_arraySpacing_width . K_CUTSPACING int_number $@5 layer_arraySpacing_arraycuts ';'

    K_CUTSPACING  shift, and go to state 1378


State 1087

  153 layer_option: K_ANTENNAAREADIFFREDUCEPWL '(' pt pt . $@21 layer_diffusion_ratios ')' ';' $@22

    $default  reduce using rule 151 ($@21)

    $@21  go to state 1379


State 1088

  361 via_layer: K_LAYER $@55 T_STRING ';' .

    $default  reduce using rule 361 (via_layer)


State 1089

  313 via_viarule: K_VIARULE $@49 T_STRING ';' . K_CUTSIZE int_number int_number ';' K_LAYERS $@50 T_STRING T_STRING T_STRING ';' K_CUTSPACING int_number int_number ';' K_ENCLOSURE int_number int_number int_number int_number ';' $@51 via_viarule_options

    K_CUTSIZE  shift, and go to state 1380


State 1090

  336 via_name_value_pair: T_STRING T_STRING .

    $default  reduce using rule 336 (via_name_value_pair)


State 1091

  335 via_name_value_pair: T_STRING QSTRING .

    $default  reduce using rule 335 (via_name_value_pair)


State 1092

  334 via_name_value_pair: T_STRING NUMBER .

    $default  reduce using rule 334 (via_name_value_pair)


State 1093

  330 via_other_option: K_PROPERTY $@53 via_prop_list ';' .

    $default  reduce using rule 330 (via_other_option)


State 1094

  333 via_prop_list: via_prop_list via_name_value_pair .

    $default  reduce using rule 333 (via_prop_list)


State 1095

  368 end_via: K_END $@57 T_STRING .

    $default  reduce using rule 368 (end_via)


State 1096

  497 pt: '(' int_number int_number . ')'

    ')'  shift, and go to state 1381


State 1097

  339 via_foreign: start_foreign pt orientation ';' .

    $default  reduce using rule 339 (via_foreign)


State 1098

  366 via_geometry: K_POLYGON maskColor . $@56 firstPt nextPt nextPt otherPts ';'

    $default  reduce using rule 365 ($@56)

    $@56  go to state 1382


State 1099

  364 via_geometry: K_RECT maskColor . pt pt ';'

    NUMBER  shift, and go to state 99
    '('     shift, and go to state 514

    int_number  go to state 515
    pt          go to state 1383


State 1100

  373 viarule_generate: viarule_keyword K_GENERATE viarule_generate_default $@59 viarule_layer_list opt_viarule_props . end_viarule

    K_END  shift, and go to state 770

    end_viarule  go to state 1384


State 1101

  384 viarule_prop_list: viarule_prop .

    $default  reduce using rule 384 (viarule_prop_list)


State 1102

  383 viarule_prop: K_PROPERTY $@60 viarule_prop_list . ';'
  385 viarule_prop_list: viarule_prop_list . viarule_prop

    T_STRING    shift, and go to state 521
    K_PROPERTY  shift, and go to state 522
    ';'         shift, and go to state 1385

    viarule_prop  go to state 1386


State 1103

  392 via_name: via_keyword T_STRING ';' .

    $default  reduce using rule 392 (via_name)


State 1104

  407 end_viarule: K_END $@62 . T_STRING

    T_STRING  shift, and go to state 1387


State 1105

  397 viarule_layer_option: K_DIRECTION K_HORIZONTAL ';' .

    $default  reduce using rule 397 (viarule_layer_option)


State 1106

  398 viarule_layer_option: K_DIRECTION K_VERTICAL ';' .

    $default  reduce using rule 398 (viarule_layer_option)


State 1107

  405 viarule_layer_option: K_METALOVERHANG int_number ';' .

    $default  reduce using rule 405 (viarule_layer_option)


State 1108

  404 viarule_layer_option: K_OVERHANG int_number ';' .

    $default  reduce using rule 404 (viarule_layer_option)


State 1109

  401 viarule_layer_option: K_RECT pt pt . ';'

    ';'  shift, and go to state 1388


State 1110

  403 viarule_layer_option: K_RESISTANCE int_number ';' .

    $default  reduce using rule 403 (viarule_layer_option)


State 1111

  402 viarule_layer_option: K_SPACING int_number K_BY . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1389


State 1112

  400 viarule_layer_option: K_WIDTH int_number K_TO . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1390


State 1113

  399 viarule_layer_option: K_ENCLOSURE int_number int_number . ';'

    ';'  shift, and go to state 1391


State 1114

  413 spacing: samenet_keyword T_STRING T_STRING int_number . ';'
  414        | samenet_keyword T_STRING T_STRING int_number . K_STACK ';'

    K_STACK  shift, and go to state 1392
    ';'      shift, and go to state 1393


State 1115

  426 ir_table_value: int_number int_number .

    $default  reduce using rule 426 (ir_table_value)


State 1116

  477 site_option: K_SIZE int_number K_BY int_number . ';'

    ';'  shift, and go to state 1394


State 1117

  495 site_rowpattern: T_STRING . orientation $@74

    K_N      shift, and go to state 497
    K_S      shift, and go to state 498
    K_E      shift, and go to state 499
    K_W      shift, and go to state 500
    K_FN     shift, and go to state 501
    K_FS     shift, and go to state 502
    K_FE     shift, and go to state 503
    K_FW     shift, and go to state 504
    K_R0     shift, and go to state 505
    K_R90    shift, and go to state 506
    K_R180   shift, and go to state 507
    K_R270   shift, and go to state 508
    K_MX     shift, and go to state 509
    K_MY     shift, and go to state 510
    K_MXR90  shift, and go to state 511
    K_MYR90  shift, and go to state 512

    orientation  go to state 1395


State 1118

  491 site_rowpattern_statement: K_ROWPATTERN $@73 site_rowpatterns ';' .

    $default  reduce using rule 491 (site_rowpattern_statement)


State 1119

  493 site_rowpatterns: site_rowpatterns site_rowpattern .

    $default  reduce using rule 493 (site_rowpatterns)


State 1120

  753 macro_clocktype: K_CLOCKTYPE $@103 T_STRING ';' .

    $default  reduce using rule 753 (macro_clocktype)


State 1121

  586 macro_eeq: K_EEQ $@79 T_STRING ';' .

    $default  reduce using rule 586 (macro_eeq)


State 1122

  574 macro_generate: K_GENERATE T_STRING T_STRING ';' .

    $default  reduce using rule 574 (macro_generate)


State 1123

  588 macro_leq: K_LEQ $@80 T_STRING ';' .

    $default  reduce using rule 588 (macro_leq)


State 1124

  593 macro_size: K_SIZE int_number K_BY int_number . ';'

    ';'  shift, and go to state 1396


State 1125

  538 macro_name_value_pair: T_STRING T_STRING .

    $default  reduce using rule 538 (macro_name_value_pair)


State 1126

  537 macro_name_value_pair: T_STRING QSTRING .

    $default  reduce using rule 537 (macro_name_value_pair)


State 1127

  536 macro_name_value_pair: T_STRING NUMBER .

    $default  reduce using rule 536 (macro_name_value_pair)


State 1128

  527 macro_option: K_PROPERTY $@78 macro_prop_list ';' .

    $default  reduce using rule 527 (macro_option)


State 1129

  529 macro_prop_list: macro_prop_list macro_name_value_pair .

    $default  reduce using rule 529 (macro_prop_list)


State 1130

  748 density_layer: K_LAYER $@101 T_STRING . ';' $@102 density_layer_rect density_layer_rects

    ';'  shift, and go to state 1397


State 1131

  743 macro_density: K_DENSITY density_layer density_layers K_END .

    $default  reduce using rule 743 (macro_density)


State 1132

  745 density_layers: density_layers density_layer .

    $default  reduce using rule 745 (density_layers)


State 1133

  582 macro_foreign: start_foreign pt orientation ';' .

    $default  reduce using rule 582 (macro_foreign)


State 1134

  503 end_macro: K_END $@77 T_STRING .

    $default  reduce using rule 503 (end_macro)


State 1135

  725 sitePattern: T_STRING int_number int_number . orientation K_DO int_number K_BY int_number K_STEP int_number int_number
  726            | T_STRING int_number int_number . orientation

    K_N      shift, and go to state 497
    K_S      shift, and go to state 498
    K_E      shift, and go to state 499
    K_W      shift, and go to state 500
    K_FN     shift, and go to state 501
    K_FS     shift, and go to state 502
    K_FE     shift, and go to state 503
    K_FW     shift, and go to state 504
    K_R0     shift, and go to state 505
    K_R90    shift, and go to state 506
    K_R180   shift, and go to state 507
    K_R270   shift, and go to state 508
    K_MX     shift, and go to state 509
    K_MY     shift, and go to state 510
    K_MXR90  shift, and go to state 511
    K_MYR90  shift, and go to state 512

    orientation  go to state 1398


State 1136

  630 macro_pin_option: K_CAPACITANCE int_number . ';'

    ';'  shift, and go to state 1399


State 1137

  635 macro_pin_option: K_CURRENTSOURCE K_ACTIVE . ';'

    ';'  shift, and go to state 1400


State 1138

  636 macro_pin_option: K_CURRENTSOURCE K_RESISTIVE . ';'

    ';'  shift, and go to state 1401


State 1139

  679 electrical_direction: K_DIRECTION K_FEEDTHRU . ';'

    ';'  shift, and go to state 1402


State 1140

  678 electrical_direction: K_DIRECTION K_INOUT . ';'

    ';'  shift, and go to state 1403


State 1141

  675 electrical_direction: K_DIRECTION K_INPUT . ';'

    ';'  shift, and go to state 1404


State 1142

  676 electrical_direction: K_DIRECTION K_OUTPUT . ';'
  677                     | K_DIRECTION K_OUTPUT . K_TRISTATE ';'

    K_TRISTATE  shift, and go to state 1405
    ';'         shift, and go to state 1406


State 1143

  598 end_macro_pin: K_END $@82 . T_STRING

    T_STRING  shift, and go to state 1407


State 1144

  617 macro_pin_option: K_FALLSATCUR int_number . ';'

    ';'  shift, and go to state 1408


State 1145

  615 macro_pin_option: K_FALLTHRESH int_number . ';'

    ';'  shift, and go to state 1409


State 1146

  629 macro_pin_option: K_INPUTNOISEMARGIN $@87 . int_number int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1410


State 1147

  639 macro_pin_option: K_IV_TABLES T_STRING . T_STRING ';'

    T_STRING  shift, and go to state 1411


State 1148

  613 macro_pin_option: K_LEAKAGE int_number . ';'

    ';'  shift, and go to state 1412


State 1149

  608 macro_pin_option: K_LEQ $@83 . T_STRING ';'

    T_STRING  shift, and go to state 1413


State 1150

  631 macro_pin_option: K_MAXDELAY int_number . ';'

    ';'  shift, and go to state 1414


State 1151

  632 macro_pin_option: K_MAXLOAD int_number . ';'

    ';'  shift, and go to state 1415


State 1152

  623 macro_pin_option: K_MUSTJOIN $@84 . T_STRING ';'

    T_STRING  shift, and go to state 1416


State 1153

  625 macro_pin_option: K_OUTPUTNOISEMARGIN $@85 . int_number int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1417


State 1154

  609 macro_pin_option: K_POWER int_number . ';'

    ';'  shift, and go to state 1418


State 1155

  634 macro_pin_option: K_PULLDOWNRES int_number . ';'

    ';'  shift, and go to state 1419


State 1156

  633 macro_pin_option: K_RESISTANCE int_number . ';'

    ';'  shift, and go to state 1420


State 1157

  616 macro_pin_option: K_RISESATCUR int_number . ';'

    ';'  shift, and go to state 1421


State 1158

  614 macro_pin_option: K_RISETHRESH int_number . ';'

    ';'  shift, and go to state 1422


State 1159

  637 macro_pin_option: K_RISEVOLTAGETHRESHOLD int_number . ';'

    ';'  shift, and go to state 1423


State 1160

  638 macro_pin_option: K_FALLVOLTAGETHRESHOLD int_number . ';'

    ';'  shift, and go to state 1424


State 1161

  689 macro_scan_use: K_INPUT .

    $default  reduce using rule 689 (macro_scan_use)


State 1162

  690 macro_scan_use: K_OUTPUT .

    $default  reduce using rule 690 (macro_scan_use)


State 1163

  691 macro_scan_use: K_START .

    $default  reduce using rule 691 (macro_scan_use)


State 1164

  692 macro_scan_use: K_STOP .

    $default  reduce using rule 692 (macro_scan_use)


State 1165

  612 macro_pin_option: K_SCANUSE macro_scan_use . ';'

    ';'  shift, and go to state 1425


State 1166

  694 pin_shape: K_ABUTMENT .

    $default  reduce using rule 694 (pin_shape)


State 1167

  696 pin_shape: K_FEEDTHRU .

    $default  reduce using rule 696 (pin_shape)


State 1168

  695 pin_shape: K_RING .

    $default  reduce using rule 695 (pin_shape)


State 1169

  621 macro_pin_option: K_SHAPE pin_shape . ';'

    ';'  shift, and go to state 1426


State 1170

  620 macro_pin_option: K_TIEOFFR int_number . ';'

    ';'  shift, and go to state 1427


State 1171

  684 macro_pin_use: K_ANALOG .

    $default  reduce using rule 684 (macro_pin_use)


State 1172

  687 macro_pin_use: K_CLOCK .

    $default  reduce using rule 687 (macro_pin_use)


State 1173

  688 macro_pin_use: K_DATA .

    $default  reduce using rule 688 (macro_pin_use)


State 1174

  686 macro_pin_use: K_GROUND .

    $default  reduce using rule 686 (macro_pin_use)


State 1175

  685 macro_pin_use: K_POWER .

    $default  reduce using rule 685 (macro_pin_use)


State 1176

  683 macro_pin_use: K_SIGNAL .

    $default  reduce using rule 683 (macro_pin_use)


State 1177

  611 macro_pin_option: K_USE macro_pin_use . ';'

    ';'  shift, and go to state 1428


State 1178

  619 macro_pin_option: K_VHI int_number . ';'

    ';'  shift, and go to state 1429


State 1179

  618 macro_pin_option: K_VLO int_number . ';'

    ';'  shift, and go to state 1430


State 1180

  642 macro_pin_option: K_PROPERTY $@88 . pin_prop_list ';'

    T_STRING  shift, and go to state 1431

    pin_prop_list        go to state 1432
    pin_name_value_pair  go to state 1433


State 1181

  627 macro_pin_option: K_OUTPUTRESISTANCE $@86 . int_number int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1434


State 1182

  640 macro_pin_option: K_TAPERRULE T_STRING . ';'

    ';'  shift, and go to state 1435


State 1183

  645 macro_pin_option: K_ANTENNASIZE int_number . opt_layer_name ';'

    K_LAYER  shift, and go to state 1436

    $default  reduce using rule 968 (opt_layer_name)

    opt_layer_name  go to state 1437


State 1184

  647 macro_pin_option: K_ANTENNAMETALLENGTH int_number . opt_layer_name ';'

    K_LAYER  shift, and go to state 1436

    $default  reduce using rule 968 (opt_layer_name)

    opt_layer_name  go to state 1438


State 1185

  646 macro_pin_option: K_ANTENNAMETALAREA NUMBER . opt_layer_name ';'

    K_LAYER  shift, and go to state 1436

    $default  reduce using rule 968 (opt_layer_name)

    opt_layer_name  go to state 1439


State 1186

  648 macro_pin_option: K_RISESLEWLIMIT int_number . ';'

    ';'  shift, and go to state 1440


State 1187

  649 macro_pin_option: K_FALLSLEWLIMIT int_number . ';'

    ';'  shift, and go to state 1441


State 1188

  650 macro_pin_option: K_ANTENNAPARTIALMETALAREA NUMBER . opt_layer_name ';'

    K_LAYER  shift, and go to state 1436

    $default  reduce using rule 968 (opt_layer_name)

    opt_layer_name  go to state 1442


State 1189

  651 macro_pin_option: K_ANTENNAPARTIALMETALSIDEAREA NUMBER . opt_layer_name ';'

    K_LAYER  shift, and go to state 1436

    $default  reduce using rule 968 (opt_layer_name)

    opt_layer_name  go to state 1443


State 1190

  654 macro_pin_option: K_ANTENNAGATEAREA NUMBER . opt_layer_name ';'

    K_LAYER  shift, and go to state 1436

    $default  reduce using rule 968 (opt_layer_name)

    opt_layer_name  go to state 1444


State 1191

  653 macro_pin_option: K_ANTENNADIFFAREA NUMBER . opt_layer_name ';'

    K_LAYER  shift, and go to state 1436

    $default  reduce using rule 968 (opt_layer_name)

    opt_layer_name  go to state 1445


State 1192

  655 macro_pin_option: K_ANTENNAMAXAREACAR NUMBER . req_layer_name ';'

    K_LAYER  shift, and go to state 1446

    req_layer_name  go to state 1447


State 1193

  656 macro_pin_option: K_ANTENNAMAXSIDEAREACAR NUMBER . req_layer_name ';'

    K_LAYER  shift, and go to state 1446

    req_layer_name  go to state 1448


State 1194

  652 macro_pin_option: K_ANTENNAPARTIALCUTAREA NUMBER . opt_layer_name ';'

    K_LAYER  shift, and go to state 1436

    $default  reduce using rule 968 (opt_layer_name)

    opt_layer_name  go to state 1449


State 1195

  657 macro_pin_option: K_ANTENNAMAXCUTCAR NUMBER . req_layer_name ';'

    K_LAYER  shift, and go to state 1446

    req_layer_name  go to state 1450


State 1196

  659 macro_pin_option: K_ANTENNAMODEL $@89 . pin_layer_oxide ';'

    K_OXIDE1  shift, and go to state 1451
    K_OXIDE2  shift, and go to state 1452
    K_OXIDE3  shift, and go to state 1453
    K_OXIDE4  shift, and go to state 1454

    pin_layer_oxide  go to state 1455


State 1197

  665 macro_pin_option: K_GROUNDSENSITIVITY $@92 . T_STRING ';'

    T_STRING  shift, and go to state 1456


State 1198

  661 macro_pin_option: K_NETEXPR $@90 . QSTRING ';'

    QSTRING  shift, and go to state 1457


State 1199

  663 macro_pin_option: K_SUPPLYSENSITIVITY $@91 . T_STRING ';'

    T_STRING  shift, and go to state 1458


State 1200

  604 macro_pin_option: start_foreign K_STRUCTURE . ';'
  605                 | start_foreign K_STRUCTURE . pt ';'
  606                 | start_foreign K_STRUCTURE . pt orientation ';'

    NUMBER  shift, and go to state 99
    ';'     shift, and go to state 1459
    '('     shift, and go to state 514

    int_number  go to state 515
    pt          go to state 1460


State 1201

  601 macro_pin_option: start_foreign ';' .

    $default  reduce using rule 601 (macro_pin_option)


State 1202

  602 macro_pin_option: start_foreign pt . ';'
  603                 | start_foreign pt . orientation ';'

    K_N      shift, and go to state 497
    K_S      shift, and go to state 498
    K_E      shift, and go to state 499
    K_W      shift, and go to state 500
    K_FN     shift, and go to state 501
    K_FS     shift, and go to state 502
    K_FE     shift, and go to state 503
    K_FW     shift, and go to state 504
    K_R0     shift, and go to state 505
    K_R90    shift, and go to state 506
    K_R180   shift, and go to state 507
    K_R270   shift, and go to state 508
    K_MX     shift, and go to state 509
    K_MY     shift, and go to state 510
    K_MXR90  shift, and go to state 511
    K_MYR90  shift, and go to state 512
    ';'      shift, and go to state 1461

    orientation  go to state 1462


State 1203

  682 macro_port_class_option: K_CLASS . class_type ';'

    K_BLOCK    shift, and go to state 549
    K_CORE     shift, and go to state 550
    K_CORNER   shift, and go to state 551
    K_COVER    shift, and go to state 552
    K_ENDCAP   shift, and go to state 553
    K_PAD      shift, and go to state 554
    K_RING     shift, and go to state 555
    K_VIRTUAL  shift, and go to state 556
    K_NONE     shift, and go to state 557
    K_BUMP     shift, and go to state 558

    class_type  go to state 1463


State 1204

  644 macro_pin_option: start_macro_port K_END .

    $default  reduce using rule 644 (macro_pin_option)


State 1205

  643 macro_pin_option: start_macro_port macro_port_class_option . geometries K_END

    K_LAYER    shift, and go to state 588
    K_PATH     shift, and go to state 589
    K_POLYGON  shift, and go to state 590
    K_RECT     shift, and go to state 591
    K_VIA      shift, and go to state 592
    K_WIDTH    shift, and go to state 593

    geometries     go to state 1464
    geometry       go to state 595
    via_placement  go to state 596


State 1206

  700 geometry: K_LAYER $@93 T_STRING . $@94 layer_exceptpgnet layer_spacing ';'

    $default  reduce using rule 699 ($@94)

    $@94  go to state 1465


State 1207

  417 maskColor: K_MASK int_number .

    $default  reduce using rule 417 (maskColor)


State 1208

  703 geometry: K_PATH maskColor K_ITERATE . firstPt otherPts stepPattern ';'

    NUMBER  shift, and go to state 99
    '('     shift, and go to state 514

    int_number  go to state 515
    pt          go to state 738
    firstPt     go to state 1466


State 1209

  702 geometry: K_PATH maskColor firstPt . otherPts ';'

    $default  reduce using rule 718 (otherPts)

    otherPts  go to state 1467


State 1210

  707 geometry: K_POLYGON maskColor K_ITERATE . firstPt nextPt nextPt otherPts stepPattern ';'

    NUMBER  shift, and go to state 99
    '('     shift, and go to state 514

    int_number  go to state 515
    pt          go to state 738
    firstPt     go to state 1468


State 1211

  706 geometry: K_POLYGON maskColor firstPt . nextPt nextPt otherPts ';'

    NUMBER  shift, and go to state 99
    '('     shift, and go to state 514

    int_number  go to state 515
    pt          go to state 1374
    nextPt      go to state 1469


State 1212

  705 geometry: K_RECT maskColor K_ITERATE . pt pt stepPattern ';'

    NUMBER  shift, and go to state 99
    '('     shift, and go to state 514

    int_number  go to state 515
    pt          go to state 1470


State 1213

  704 geometry: K_RECT maskColor pt . pt ';'

    NUMBER  shift, and go to state 99
    '('     shift, and go to state 514

    int_number  go to state 515
    pt          go to state 1471


State 1214

  723 via_placement: K_VIA K_ITERATE maskColor . pt $@96 T_STRING stepPattern ';'

    NUMBER  shift, and go to state 99
    '('     shift, and go to state 514

    int_number  go to state 515
    pt          go to state 1472


State 1215

  721 via_placement: K_VIA maskColor pt . $@95 T_STRING ';'

    $default  reduce using rule 720 ($@95)

    $@95  go to state 1473


State 1216

  701 geometry: K_WIDTH int_number ';' .

    $default  reduce using rule 701 (geometry)


State 1217

  710 geometry_options: geometry_options geometry .

    $default  reduce using rule 710 (geometry_options)


State 1218

  756 end_timing: K_END K_TIMING .

    $default  reduce using rule 756 (end_timing)


State 1219

  771 timing_option: K_FALLCS int_number . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1474


State 1220

  775 timing_option: K_FALLT0 int_number . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1475


State 1221

  773 timing_option: K_FALLSATT1 int_number . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1476


State 1222

  769 timing_option: K_FALLRS int_number . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1477


State 1223

  760 timing_option: K_FROMPIN $@104 . list_of_from_strings ';'

    T_STRING  shift, and go to state 1478

    list_of_from_strings  go to state 1479


State 1224

  770 timing_option: K_RISECS int_number . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1480


State 1225

  768 timing_option: K_RISERS int_number . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1481


State 1226

  772 timing_option: K_RISESATT1 int_number . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1482


State 1227

  774 timing_option: K_RISET0 int_number . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1483


State 1228

  762 timing_option: K_TOPIN $@105 . list_of_to_strings ';'

    T_STRING  shift, and go to state 1484

    list_of_to_strings  go to state 1485


State 1229

  809 unateness: K_INVERT .

    $default  reduce using rule 809 (unateness)


State 1230

  810 unateness: K_NONINVERT .

    $default  reduce using rule 810 (unateness)


State 1231

  811 unateness: K_NONUNATE .

    $default  reduce using rule 811 (unateness)


State 1232

  776 timing_option: K_UNATENESS unateness . ';'

    ';'  shift, and go to state 1486


State 1233

  777 timing_option: K_STABLE K_SETUP . int_number K_HOLD int_number risefall ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1487


State 1234

  802 list_of_table_axis_dnumbers: int_number .

    $default  reduce using rule 802 (list_of_table_axis_dnumbers)


State 1235

  766 timing_option: K_TABLEAXIS list_of_table_axis_dnumbers . ';'
  803 list_of_table_axis_dnumbers: list_of_table_axis_dnumbers . int_number

    NUMBER  shift, and go to state 99
    ';'     shift, and go to state 1488

    int_number  go to state 1489


State 1236

  801 table_entry: '(' . int_number int_number int_number ')'

    NUMBER  shift, and go to state 99

    int_number  go to state 1490


State 1237

  767 timing_option: K_TABLEENTRIES list_of_table_entries . ';'
  800 list_of_table_entries: list_of_table_entries . table_entry

    ';'  shift, and go to state 1491
    '('  shift, and go to state 1236

    table_entry  go to state 1492


State 1238

  799 list_of_table_entries: table_entry .

    $default  reduce using rule 799 (list_of_table_entries)


State 1239

  783 timing_option: K_EXTENSION ';' .

    $default  reduce using rule 783 (timing_option)


State 1240

  780 timing_option: K_SDFCONDSTART QSTRING . ';'

    ';'  shift, and go to state 1493


State 1241

  781 timing_option: K_SDFCONDEND QSTRING . ';'

    ';'  shift, and go to state 1494


State 1242

  782 timing_option: K_SDFCOND QSTRING . ';'

    ';'  shift, and go to state 1495


State 1243

  779 timing_option: one_pin_trigger K_TABLEDIMENSION . int_number int_number int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1496


State 1244

  791 from_pin_trigger: K_ANYEDGE .

    $default  reduce using rule 791 (from_pin_trigger)


State 1245

  792 from_pin_trigger: K_POSEDGE .

    $default  reduce using rule 792 (from_pin_trigger)


State 1246

  793 from_pin_trigger: K_NEGEDGE .

    $default  reduce using rule 793 (from_pin_trigger)


State 1247

  778 timing_option: two_pin_trigger from_pin_trigger . to_pin_trigger K_TABLEDIMENSION int_number int_number int_number ';'

    K_ANYEDGE  shift, and go to state 1497
    K_POSEDGE  shift, and go to state 1498
    K_NEGEDGE  shift, and go to state 1499

    to_pin_trigger  go to state 1500


State 1248

  764 timing_option: risefall K_INTRINSIC . int_number int_number $@106 slew_spec K_VARIABLE int_number int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1501


State 1249

  797 delay_or_transition: K_DELAY .

    $default  reduce using rule 797 (delay_or_transition)


State 1250

  798 delay_or_transition: K_TRANSITIONTIME .

    $default  reduce using rule 798 (delay_or_transition)


State 1251

  765 timing_option: risefall delay_or_transition . K_UNATENESS unateness K_TABLEDIMENSION int_number int_number int_number ';'

    K_UNATENESS  shift, and go to state 1502


State 1252

  827 array_rule: K_CANPLACE $@111 sitePattern ';' .

    $default  reduce using rule 827 (array_rule)


State 1253

  829 array_rule: K_CANNOTOCCUPY $@112 sitePattern ';' .

    $default  reduce using rule 829 (array_rule)


State 1254

  729 trackPattern: K_X int_number . K_DO int_number K_STEP int_number $@97 K_LAYER $@98 trackLayers
  733             | K_X int_number . K_DO int_number K_STEP int_number

    K_DO  shift, and go to state 1503


State 1255

  732 trackPattern: K_Y int_number . K_DO int_number K_STEP int_number $@99 K_LAYER $@100 trackLayers
  734             | K_Y int_number . K_DO int_number K_STEP int_number

    K_DO  shift, and go to state 1504


State 1256

  831 array_rule: K_TRACKS $@113 trackPattern ';' .

    $default  reduce using rule 831 (array_rule)


State 1257

  738 gcellPattern: K_X int_number . K_DO int_number K_STEP int_number

    K_DO  shift, and go to state 1505


State 1258

  739 gcellPattern: K_Y int_number . K_DO int_number K_STEP int_number

    K_DO  shift, and go to state 1506


State 1259

  834 array_rule: K_GCELLGRID $@114 gcellPattern ';' .

    $default  reduce using rule 834 (array_rule)


State 1260

  835 array_rule: K_DEFAULTCAP int_number cap_list K_END . K_DEFAULTCAP

    K_DEFAULTCAP  shift, and go to state 1507


State 1261

  846 one_cap: K_MINPINS . int_number K_WIRECAP int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1508


State 1262

  845 cap_list: cap_list one_cap .

    $default  reduce using rule 845 (cap_list)


State 1263

  825 array_rule: site_word $@110 sitePattern ';' .

    $default  reduce using rule 825 (array_rule)


State 1264

  821 end_array: K_END $@109 T_STRING .

    $default  reduce using rule 821 (end_array)


State 1265

  832 array_rule: floorplan_start floorplan_list K_END T_STRING .

    $default  reduce using rule 832 (array_rule)


State 1266

  841 floorplan_element: K_CANPLACE $@115 . sitePattern ';'

    T_STRING  shift, and go to state 949

    sitePattern  go to state 1509


State 1267

  843 floorplan_element: K_CANNOTOCCUPY $@116 . sitePattern ';'

    T_STRING  shift, and go to state 949

    sitePattern  go to state 1510


State 1268

  461 nd_layer: K_LAYER . $@67 T_STRING $@68 K_WIDTH int_number ';' $@69 nd_layer_stmts K_END $@70 T_STRING

    $default  reduce using rule 457 ($@67)

    $@67  go to state 1511


State 1269

  454 nd_prop: T_STRING . T_STRING
  455        | T_STRING . QSTRING
  456        | T_STRING . NUMBER

    T_STRING  shift, and go to state 1512
    QSTRING   shift, and go to state 1513
    NUMBER    shift, and go to state 1514


State 1270

  451 nd_prop: K_PROPERTY . $@66 nd_prop_list ';'

    $default  reduce using rule 450 ($@66)

    $@66  go to state 1515


State 1271

  449 mincuts: K_MINCUTS . T_STRING int_number ';'

    T_STRING  shift, and go to state 1516


State 1272

  447 usevia: K_USEVIA . T_STRING ';'

    T_STRING  shift, and go to state 1517


State 1273

  448 useviarule: K_USEVIARULE . T_STRING ';'

    T_STRING  shift, and go to state 1518


State 1274

  441 nd_rule: via .

    $default  reduce using rule 441 (nd_rule)


State 1275

  442 nd_rule: spacing_rule .

    $default  reduce using rule 442 (nd_rule)


State 1276

  433 nondefault_rule: K_NONDEFAULTRULE $@63 T_STRING $@64 nd_hardspacing nd_rules $@65 . end_nd_rule

    K_END  shift, and go to state 1519

    end_nd_rule  go to state 1520


State 1277

  439 nd_rules: nd_rules nd_rule .

    $default  reduce using rule 439 (nd_rules)


State 1278

  444 nd_rule: usevia .

    $default  reduce using rule 444 (nd_rule)


State 1279

  445 nd_rule: useviarule .

    $default  reduce using rule 445 (nd_rule)


State 1280

  446 nd_rule: mincuts .

    $default  reduce using rule 446 (nd_rule)


State 1281

  443 nd_rule: nd_prop .

    $default  reduce using rule 443 (nd_rule)


State 1282

  440 nd_rule: nd_layer .

    $default  reduce using rule 440 (nd_rule)


State 1283

  860 then: K_THEN .

    $default  reduce using rule 860 (then)


State 1284

  861 then: '\n' . K_THEN

    K_THEN  shift, and go to state 1521


State 1285

  870 expression: K_IF b_expr then . expression else expression

    NUMBER  shift, and go to state 99
    K_IF    shift, and go to state 610
    '-'     shift, and go to state 611
    '('     shift, and go to state 612

    int_number  go to state 613
    expression  go to state 1522


State 1286

  869 expression: '(' expression ')' .

    $default  reduce using rule 869 (expression)


State 1287

  864 expression: expression . '+' expression
  865           | expression . '-' expression
  865           | expression '-' expression .
  866           | expression . '*' expression
  867           | expression . '/' expression

    '*'  shift, and go to state 972
    '/'  shift, and go to state 973

    $default  reduce using rule 865 (expression)


State 1288

  864 expression: expression . '+' expression
  864           | expression '+' expression .
  865           | expression . '-' expression
  866           | expression . '*' expression
  867           | expression . '/' expression

    '*'  shift, and go to state 972
    '/'  shift, and go to state 973

    $default  reduce using rule 864 (expression)


State 1289

  864 expression: expression . '+' expression
  865           | expression . '-' expression
  866           | expression . '*' expression
  866           | expression '*' expression .
  867           | expression . '/' expression

    $default  reduce using rule 866 (expression)


State 1290

  864 expression: expression . '+' expression
  865           | expression . '-' expression
  866           | expression . '*' expression
  867           | expression . '/' expression
  867           | expression '/' expression .

    $default  reduce using rule 867 (expression)


State 1291

  889 s_expr: K_IF b_expr then . s_expr else s_expr

    QSTRING  shift, and go to state 615
    K_IF     shift, and go to state 616
    '('      shift, and go to state 617

    s_expr  go to state 1523


State 1292

  888 s_expr: '(' s_expr ')' .

    $default  reduce using rule 888 (s_expr)


State 1293

  887 s_expr: s_expr . '+' s_expr
  887       | s_expr '+' s_expr .

    $default  reduce using rule 887 (s_expr)


State 1294

  870 expression: K_IF b_expr then . expression else expression
  884 b_expr: K_IF b_expr then . b_expr else b_expr
  889 s_expr: K_IF b_expr then . s_expr else s_expr

    QSTRING  shift, and go to state 615
    NUMBER   shift, and go to state 99
    K_IF     shift, and go to state 619
    K_FALSE  shift, and go to state 620
    K_TRUE   shift, and go to state 621
    K_NOT    shift, and go to state 622
    '-'      shift, and go to state 611
    '('      shift, and go to state 623

    int_number  go to state 613
    expression  go to state 1524
    b_expr      go to state 1525
    s_expr      go to state 1526


State 1295

  883 b_expr: '(' b_expr ')' .

    $default  reduce using rule 883 (b_expr)


State 1296

  864 expression: expression . '+' expression
  865           | expression . '-' expression
  866           | expression . '*' expression
  867           | expression . '/' expression
  874 b_expr: expression K_OR expression .

    '-'  shift, and go to state 970
    '+'  shift, and go to state 971
    '*'  shift, and go to state 972
    '/'  shift, and go to state 973

    $default  reduce using rule 874 (b_expr)


State 1297

  864 expression: expression . '+' expression
  865           | expression . '-' expression
  866           | expression . '*' expression
  867           | expression . '/' expression
  873 b_expr: expression K_AND expression .

    '-'  shift, and go to state 970
    '+'  shift, and go to state 971
    '*'  shift, and go to state 972
    '/'  shift, and go to state 973

    $default  reduce using rule 873 (b_expr)


State 1298

  864 expression: expression . '+' expression
  865           | expression . '-' expression
  866           | expression . '*' expression
  867           | expression . '/' expression
  872 b_expr: expression relop expression .

    '-'  shift, and go to state 970
    '+'  shift, and go to state 971
    '*'  shift, and go to state 972
    '/'  shift, and go to state 973

    $default  reduce using rule 872 (b_expr)


State 1299

  878 b_expr: b_expr . K_EQ b_expr
  878       | b_expr K_EQ b_expr .
  879       | b_expr . K_NE b_expr
  880       | b_expr . K_AND b_expr
  881       | b_expr . K_OR b_expr

    $default  reduce using rule 878 (b_expr)


State 1300

  878 b_expr: b_expr . K_EQ b_expr
  879       | b_expr . K_NE b_expr
  879       | b_expr K_NE b_expr .
  880       | b_expr . K_AND b_expr
  881       | b_expr . K_OR b_expr

    $default  reduce using rule 879 (b_expr)


State 1301

  878 b_expr: b_expr . K_EQ b_expr
  879       | b_expr . K_NE b_expr
  880       | b_expr . K_AND b_expr
  881       | b_expr . K_OR b_expr
  881       | b_expr K_OR b_expr .

    K_EQ  shift, and go to state 998
    K_NE  shift, and go to state 999

    $default  reduce using rule 881 (b_expr)


State 1302

  878 b_expr: b_expr . K_EQ b_expr
  879       | b_expr . K_NE b_expr
  880       | b_expr . K_AND b_expr
  880       | b_expr K_AND b_expr .
  881       | b_expr . K_OR b_expr

    K_EQ  shift, and go to state 998
    K_NE  shift, and go to state 999
    K_OR  shift, and go to state 1000

    $default  reduce using rule 880 (b_expr)


State 1303

  877 b_expr: s_expr K_OR s_expr .
  887 s_expr: s_expr . '+' s_expr

    '+'  shift, and go to state 979

    $default  reduce using rule 877 (b_expr)


State 1304

  876 b_expr: s_expr K_AND s_expr .
  887 s_expr: s_expr . '+' s_expr

    '+'  shift, and go to state 979

    $default  reduce using rule 876 (b_expr)


State 1305

  875 b_expr: s_expr relop s_expr .
  887 s_expr: s_expr . '+' s_expr

    '+'  shift, and go to state 979

    $default  reduce using rule 875 (b_expr)


State 1306

  922 prop_define: K_STRING .
  923            | K_STRING . QSTRING

    QSTRING  shift, and go to state 1527

    $default  reduce using rule 922 (prop_define)


State 1307

  920 prop_define: K_INTEGER . opt_def_range opt_def_dvalue

    K_RANGE  shift, and go to state 1528

    $default  reduce using rule 937 (opt_def_range)

    opt_def_range  go to state 1529


State 1308

  921 prop_define: K_REAL . opt_def_range opt_def_value

    K_RANGE  shift, and go to state 1528

    $default  reduce using rule 937 (opt_def_range)

    opt_def_range  go to state 1530


State 1309

  924 prop_define: K_NAMEMAPSTRING . T_STRING

    T_STRING  shift, and go to state 1531


State 1310

  907 prop_stmt: K_COMPONENTPIN $@124 T_STRING prop_define . ';'

    ';'  shift, and go to state 1532


State 1311

  917 prop_stmt: K_LAYER $@129 T_STRING prop_define . ';'

    ';'  shift, and go to state 1533


State 1312

  905 prop_stmt: K_LIBRARY $@123 T_STRING prop_define . ';'

    ';'  shift, and go to state 1534


State 1313

  911 prop_stmt: K_MACRO $@126 T_STRING prop_define . ';'

    ';'  shift, and go to state 1535


State 1314

  919 prop_stmt: K_NONDEFAULTRULE $@130 T_STRING prop_define . ';'

    ';'  shift, and go to state 1536


State 1315

  909 prop_stmt: K_PIN $@125 T_STRING prop_define . ';'

    ';'  shift, and go to state 1537


State 1316

  913 prop_stmt: K_VIA $@127 T_STRING prop_define . ';'

    ';'  shift, and go to state 1538


State 1317

  915 prop_stmt: K_VIARULE $@128 T_STRING prop_define . ';'

    ';'  shift, and go to state 1539


State 1318

  999 correction_table_item: K_EDGERATE int_number ';' .

    $default  reduce using rule 999 (correction_table_item)


State 1319

  1003 numo_list: int_number .

    $default  reduce using rule 1003 (numo_list)


State 1320

  1002 output_list: K_OUTPUTRESISTANCE $@144 numo_list . ';' corr_victim_list
  1004 numo_list: numo_list . int_number

    NUMBER  shift, and go to state 99
    ';'     shift, and go to state 1540

    int_number  go to state 1541


State 1321

  996 end_correctiontable: K_END K_CORRECTIONTABLE .

    $default  reduce using rule 996 (end_correctiontable)


State 1322

  995 correctiontable: K_CORRECTIONTABLE int_number ';' $@143 correction_table_list end_correctiontable dtrm .

    $default  reduce using rule 995 (correctiontable)


State 1323

  982 noise_table_entry: K_EDGERATE int_number ';' .

    $default  reduce using rule 982 (noise_table_entry)


State 1324

  986 num_list: int_number .

    $default  reduce using rule 986 (num_list)


State 1325

  985 output_resistance_entry: K_OUTPUTRESISTANCE $@141 num_list . ';' victim_list
  987 num_list: num_list . int_number

    NUMBER  shift, and go to state 99
    ';'     shift, and go to state 1542

    int_number  go to state 1543


State 1326

  979 end_noisetable: K_END K_NOISETABLE .

    $default  reduce using rule 979 (end_noisetable)


State 1327

  978 noisetable: K_NOISETABLE int_number $@140 ';' noise_table_list end_noisetable dtrm .

    $default  reduce using rule 978 (noisetable)


State 1328

  303 maxstack_via: K_MAXVIASTACK int_number K_RANGE $@47 T_STRING T_STRING ';' .

    $default  reduce using rule 303 (maxstack_via)


State 1329

  268 cap_point: '(' . int_number int_number ')'

    NUMBER  shift, and go to state 99

    int_number  go to state 1544


State 1330

  107 layer_option: K_CAPACITANCE K_CPERSQDIST K_PWL '(' cap_points . ')' ';'
  267 cap_points: cap_points . cap_point

    '('  shift, and go to state 1329
    ')'  shift, and go to state 1545

    cap_point  go to state 1546


State 1331

  266 cap_points: cap_point .

    $default  reduce using rule 266 (cap_points)


State 1332

  271 res_point: '(' . int_number int_number ')'

    NUMBER  shift, and go to state 99

    int_number  go to state 1547


State 1333

  105 layer_option: K_RESISTANCE K_RPERSQ K_PWL '(' res_points . ')' ';'
  270 res_points: res_points . res_point

    '('  shift, and go to state 1332
    ')'  shift, and go to state 1548

    res_point  go to state 1549


State 1334

  269 res_points: res_point .

    $default  reduce using rule 269 (res_points)


State 1335

  947 layer_spacing_opt: K_SAMENET $@132 . opt_samenetPGonly

    K_PGONLY  shift, and go to state 1550

    $default  reduce using rule 935 (opt_samenetPGonly)

    opt_samenetPGonly  go to state 1551


State 1336

  955 layer_spacing_cut_routing: K_AREA . NUMBER

    NUMBER  shift, and go to state 1552


State 1337

  952 layer_spacing_cut_routing: K_LAYER . $@133 T_STRING $@134 spacing_cut_layer_opt

    $default  reduce using rule 950 ($@133)

    $@133  go to state 1553


State 1338

  957 layer_spacing_cut_routing: K_RANGE . int_number int_number $@136 opt_range_second

    NUMBER  shift, and go to state 99

    int_number  go to state 1554


State 1339

  958 layer_spacing_cut_routing: K_LENGTHTHRESHOLD . int_number
  959                          | K_LENGTHTHRESHOLD . int_number K_RANGE int_number int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1555


State 1340

  954 layer_spacing_cut_routing: K_ADJACENTCUTS . int_number K_WITHIN int_number $@135 opt_adjacentcuts_exceptsame

    NUMBER  shift, and go to state 99

    int_number  go to state 1556


State 1341

  961 layer_spacing_cut_routing: K_ENDOFLINE . int_number K_WITHIN int_number $@137 opt_endofline

    NUMBER  shift, and go to state 99

    int_number  go to state 1557


State 1342

  963 layer_spacing_cut_routing: K_ENDOFNOTCHWIDTH . int_number K_NOTCHSPACING int_number K_NOTCHLENGTH int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1558


State 1343

  962 layer_spacing_cut_routing: K_NOTCHLENGTH . int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1559


State 1344

  100 layer_option: K_SPACING int_number $@6 layer_spacing_opts layer_spacing_cut_routing . ';'

    ';'  shift, and go to state 1560


State 1345

  944 layer_spacing_opts: layer_spacing_opt layer_spacing_opts .

    $default  reduce using rule 944 (layer_spacing_opts)


State 1346

  265 current_density_pwl: '(' int_number . int_number ')'

    NUMBER  shift, and go to state 99

    int_number  go to state 1561


State 1347

  116 layer_option: K_CURRENTDEN K_PWL '(' current_density_pwl_list ')' . ';'

    ';'  shift, and go to state 1562


State 1348

  264 current_density_pwl_list: current_density_pwl_list current_density_pwl .

    $default  reduce using rule 264 (current_density_pwl_list)


State 1349

  117 layer_option: K_CURRENTDEN '(' int_number int_number ')' . ';'

    ';'  shift, and go to state 1563


State 1350

  246 layer_frequency: K_FREQUENCY NUMBER . $@39 number_list ';' $@40 ac_layer_table_opt K_TABLEENTRIES NUMBER $@41 number_list ';'

    $default  reduce using rule 243 ($@39)

    $@39  go to state 1564


State 1351

  129 layer_option: K_DCCURRENTDENSITY K_AVERAGE K_WIDTH int_number $@12 . int_number_list ';' $@13 dc_layer_table

    $default  reduce using rule 254 (int_number_list)

    int_number_list  go to state 1565


State 1352

  126 layer_option: K_DCCURRENTDENSITY K_AVERAGE K_CUTAREA NUMBER $@10 . number_list ';' $@11 dc_layer_table

    $default  reduce using rule 256 (number_list)

    number_list  go to state 1566


State 1353

  234 layer_antenna_pwl: K_PWL '(' pt . pt $@38 layer_diffusion_ratios ')'

    NUMBER  shift, and go to state 99
    '('     shift, and go to state 514

    int_number  go to state 515
    pt          go to state 1567


State 1354

  137 layer_option: K_ANTENNAAREAFACTOR int_number $@16 layer_antenna_duo ';' .

    $default  reduce using rule 137 (layer_option)


State 1355

  145 layer_option: K_ANTENNASIDEAREAFACTOR int_number $@19 layer_antenna_duo ';' .

    $default  reduce using rule 145 (layer_option)


State 1356

  172 layer_option: K_MINIMUMCUT int_number K_WIDTH int_number $@24 . layer_minimumcut_within layer_minimumcut_from layer_minimumcut_length ';'

    K_WITHIN  shift, and go to state 1568

    $default  reduce using rule 217 (layer_minimumcut_within)

    layer_minimumcut_within  go to state 1569


State 1357

  179 layer_option: K_ENCLOSURE layer_enclosure_type_opt int_number int_number $@27 . layer_enclosure_width_opt ';'

    K_WIDTH   shift, and go to state 1570
    K_LENGTH  shift, and go to state 1571

    $default  reduce using rule 209 (layer_enclosure_width_opt)

    layer_enclosure_width_opt  go to state 1572


State 1358

  283 layer_minen_width: K_WIDTH int_number .

    $default  reduce using rule 283 (layer_minen_width)


State 1359

  170 layer_option: K_MINENCLOSEDAREA NUMBER $@23 layer_minen_width ';' .

    $default  reduce using rule 170 (layer_option)


State 1360

  231 layer_minstep_type: K_STEP .

    $default  reduce using rule 231 (layer_minstep_type)


State 1361

  229 layer_minstep_type: K_INSIDECORNER .

    $default  reduce using rule 229 (layer_minstep_type)


State 1362

  227 layer_minstep_option: K_LENGTHSUM . int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1573


State 1363

  230 layer_minstep_type: K_OUTSIDECORNER .

    $default  reduce using rule 230 (layer_minstep_type)


State 1364

  228 layer_minstep_option: K_MAXEDGES . int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1574


State 1365

  174 layer_option: K_MINSTEP int_number $@25 layer_minstep_options ';' .

    $default  reduce using rule 174 (layer_option)


State 1366

  225 layer_minstep_options: layer_minstep_options layer_minstep_option .

    $default  reduce using rule 225 (layer_minstep_options)


State 1367

  226 layer_minstep_option: layer_minstep_type .

    $default  reduce using rule 226 (layer_minstep_option)


State 1368

  175 layer_option: K_PROTRUSIONWIDTH int_number K_LENGTH int_number K_WIDTH . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1575


State 1369

  102 layer_option: K_SPACINGTABLE K_ORTHOGONAL K_WITHIN int_number K_SPACING . int_number $@7 layer_spacingtable_opts ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1576


State 1370

  202 sp_options: K_INFLUENCE K_WIDTH int_number . K_WITHIN int_number K_SPACING int_number $@36 layer_sp_influence_widths

    K_WITHIN  shift, and go to state 1577


State 1371

  197 sp_options: K_PARALLELRUNLENGTH int_number $@30 . int_number_list $@31 K_WIDTH int_number $@32 int_number_list $@33 layer_sp_parallel_widths

    $default  reduce using rule 254 (int_number_list)

    int_number_list  go to state 1578


State 1372

  200 sp_options: K_TWOWIDTHS K_WIDTH int_number . layer_sp_TwoWidthsPRL int_number $@34 int_number_list $@35 layer_sp_TwoWidths

    K_PRL  shift, and go to state 1579

    $default  reduce using rule 296 (layer_sp_TwoWidthsPRL)

    layer_sp_TwoWidthsPRL  go to state 1580


State 1373

  185 layer_option: K_MINSIZE $@29 firstPt otherPts ';' .

    $default  reduce using rule 185 (layer_option)


State 1374

  717 nextPt: pt .

    $default  reduce using rule 717 (nextPt)


State 1375

  719 otherPts: otherPts nextPt .

    $default  reduce using rule 719 (otherPts)


State 1376

  181 layer_option: K_PREFERENCLOSURE layer_enclosure_type_opt int_number int_number $@28 . layer_preferenclosure_width_opt ';'

    K_WIDTH  shift, and go to state 1581

    $default  reduce using rule 215 (layer_preferenclosure_width_opt)

    layer_preferenclosure_width_opt  go to state 1582


State 1377

  189 layer_arraySpacing_width: K_WIDTH int_number .

    $default  reduce using rule 189 (layer_arraySpacing_width)


State 1378

   86 layer_option: K_ARRAYSPACING $@4 layer_arraySpacing_long layer_arraySpacing_width K_CUTSPACING . int_number $@5 layer_arraySpacing_arraycuts ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1583


State 1379

  153 layer_option: K_ANTENNAAREADIFFREDUCEPWL '(' pt pt $@21 . layer_diffusion_ratios ')' ';' $@22

    $default  reduce using rule 235 (layer_diffusion_ratios)

    layer_diffusion_ratios  go to state 1584


State 1380

  313 via_viarule: K_VIARULE $@49 T_STRING ';' K_CUTSIZE . int_number int_number ';' K_LAYERS $@50 T_STRING T_STRING T_STRING ';' K_CUTSPACING int_number int_number ';' K_ENCLOSURE int_number int_number int_number int_number ';' $@51 via_viarule_options

    NUMBER  shift, and go to state 99

    int_number  go to state 1585


State 1381

  497 pt: '(' int_number int_number ')' .

    $default  reduce using rule 497 (pt)


State 1382

  366 via_geometry: K_POLYGON maskColor $@56 . firstPt nextPt nextPt otherPts ';'

    NUMBER  shift, and go to state 99
    '('     shift, and go to state 514

    int_number  go to state 515
    pt          go to state 738
    firstPt     go to state 1586


State 1383

  364 via_geometry: K_RECT maskColor pt . pt ';'

    NUMBER  shift, and go to state 99
    '('     shift, and go to state 514

    int_number  go to state 515
    pt          go to state 1587


State 1384

  373 viarule_generate: viarule_keyword K_GENERATE viarule_generate_default $@59 viarule_layer_list opt_viarule_props end_viarule .

    $default  reduce using rule 373 (viarule_generate)


State 1385

  383 viarule_prop: K_PROPERTY $@60 viarule_prop_list ';' .

    $default  reduce using rule 383 (viarule_prop)


State 1386

  385 viarule_prop_list: viarule_prop_list viarule_prop .

    $default  reduce using rule 385 (viarule_prop_list)


State 1387

  407 end_viarule: K_END $@62 T_STRING .

    $default  reduce using rule 407 (end_viarule)


State 1388

  401 viarule_layer_option: K_RECT pt pt ';' .

    $default  reduce using rule 401 (viarule_layer_option)


State 1389

  402 viarule_layer_option: K_SPACING int_number K_BY int_number . ';'

    ';'  shift, and go to state 1588


State 1390

  400 viarule_layer_option: K_WIDTH int_number K_TO int_number . ';'

    ';'  shift, and go to state 1589


State 1391

  399 viarule_layer_option: K_ENCLOSURE int_number int_number ';' .

    $default  reduce using rule 399 (viarule_layer_option)


State 1392

  414 spacing: samenet_keyword T_STRING T_STRING int_number K_STACK . ';'

    ';'  shift, and go to state 1590


State 1393

  413 spacing: samenet_keyword T_STRING T_STRING int_number ';' .

    $default  reduce using rule 413 (spacing)


State 1394

  477 site_option: K_SIZE int_number K_BY int_number ';' .

    $default  reduce using rule 477 (site_option)


State 1395

  495 site_rowpattern: T_STRING orientation . $@74

    $default  reduce using rule 494 ($@74)

    $@74  go to state 1591


State 1396

  593 macro_size: K_SIZE int_number K_BY int_number ';' .

    $default  reduce using rule 593 (macro_size)


State 1397

  748 density_layer: K_LAYER $@101 T_STRING ';' . $@102 density_layer_rect density_layer_rects

    $default  reduce using rule 747 ($@102)

    $@102  go to state 1592


State 1398

  725 sitePattern: T_STRING int_number int_number orientation . K_DO int_number K_BY int_number K_STEP int_number int_number
  726            | T_STRING int_number int_number orientation .

    K_DO  shift, and go to state 1593

    $default  reduce using rule 726 (sitePattern)


State 1399

  630 macro_pin_option: K_CAPACITANCE int_number ';' .

    $default  reduce using rule 630 (macro_pin_option)


State 1400

  635 macro_pin_option: K_CURRENTSOURCE K_ACTIVE ';' .

    $default  reduce using rule 635 (macro_pin_option)


State 1401

  636 macro_pin_option: K_CURRENTSOURCE K_RESISTIVE ';' .

    $default  reduce using rule 636 (macro_pin_option)


State 1402

  679 electrical_direction: K_DIRECTION K_FEEDTHRU ';' .

    $default  reduce using rule 679 (electrical_direction)


State 1403

  678 electrical_direction: K_DIRECTION K_INOUT ';' .

    $default  reduce using rule 678 (electrical_direction)


State 1404

  675 electrical_direction: K_DIRECTION K_INPUT ';' .

    $default  reduce using rule 675 (electrical_direction)


State 1405

  677 electrical_direction: K_DIRECTION K_OUTPUT K_TRISTATE . ';'

    ';'  shift, and go to state 1594


State 1406

  676 electrical_direction: K_DIRECTION K_OUTPUT ';' .

    $default  reduce using rule 676 (electrical_direction)


State 1407

  598 end_macro_pin: K_END $@82 T_STRING .

    $default  reduce using rule 598 (end_macro_pin)


State 1408

  617 macro_pin_option: K_FALLSATCUR int_number ';' .

    $default  reduce using rule 617 (macro_pin_option)


State 1409

  615 macro_pin_option: K_FALLTHRESH int_number ';' .

    $default  reduce using rule 615 (macro_pin_option)


State 1410

  629 macro_pin_option: K_INPUTNOISEMARGIN $@87 int_number . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1595


State 1411

  639 macro_pin_option: K_IV_TABLES T_STRING T_STRING . ';'

    ';'  shift, and go to state 1596


State 1412

  613 macro_pin_option: K_LEAKAGE int_number ';' .

    $default  reduce using rule 613 (macro_pin_option)


State 1413

  608 macro_pin_option: K_LEQ $@83 T_STRING . ';'

    ';'  shift, and go to state 1597


State 1414

  631 macro_pin_option: K_MAXDELAY int_number ';' .

    $default  reduce using rule 631 (macro_pin_option)


State 1415

  632 macro_pin_option: K_MAXLOAD int_number ';' .

    $default  reduce using rule 632 (macro_pin_option)


State 1416

  623 macro_pin_option: K_MUSTJOIN $@84 T_STRING . ';'

    ';'  shift, and go to state 1598


State 1417

  625 macro_pin_option: K_OUTPUTNOISEMARGIN $@85 int_number . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1599


State 1418

  609 macro_pin_option: K_POWER int_number ';' .

    $default  reduce using rule 609 (macro_pin_option)


State 1419

  634 macro_pin_option: K_PULLDOWNRES int_number ';' .

    $default  reduce using rule 634 (macro_pin_option)


State 1420

  633 macro_pin_option: K_RESISTANCE int_number ';' .

    $default  reduce using rule 633 (macro_pin_option)


State 1421

  616 macro_pin_option: K_RISESATCUR int_number ';' .

    $default  reduce using rule 616 (macro_pin_option)


State 1422

  614 macro_pin_option: K_RISETHRESH int_number ';' .

    $default  reduce using rule 614 (macro_pin_option)


State 1423

  637 macro_pin_option: K_RISEVOLTAGETHRESHOLD int_number ';' .

    $default  reduce using rule 637 (macro_pin_option)


State 1424

  638 macro_pin_option: K_FALLVOLTAGETHRESHOLD int_number ';' .

    $default  reduce using rule 638 (macro_pin_option)


State 1425

  612 macro_pin_option: K_SCANUSE macro_scan_use ';' .

    $default  reduce using rule 612 (macro_pin_option)


State 1426

  621 macro_pin_option: K_SHAPE pin_shape ';' .

    $default  reduce using rule 621 (macro_pin_option)


State 1427

  620 macro_pin_option: K_TIEOFFR int_number ';' .

    $default  reduce using rule 620 (macro_pin_option)


State 1428

  611 macro_pin_option: K_USE macro_pin_use ';' .

    $default  reduce using rule 611 (macro_pin_option)


State 1429

  619 macro_pin_option: K_VHI int_number ';' .

    $default  reduce using rule 619 (macro_pin_option)


State 1430

  618 macro_pin_option: K_VLO int_number ';' .

    $default  reduce using rule 618 (macro_pin_option)


State 1431

  672 pin_name_value_pair: T_STRING . NUMBER
  673                    | T_STRING . QSTRING
  674                    | T_STRING . T_STRING

    T_STRING  shift, and go to state 1600
    QSTRING   shift, and go to state 1601
    NUMBER    shift, and go to state 1602


State 1432

  642 macro_pin_option: K_PROPERTY $@88 pin_prop_list . ';'
  671 pin_prop_list: pin_prop_list . pin_name_value_pair

    T_STRING  shift, and go to state 1431
    ';'       shift, and go to state 1603

    pin_name_value_pair  go to state 1604


State 1433

  670 pin_prop_list: pin_name_value_pair .

    $default  reduce using rule 670 (pin_prop_list)


State 1434

  627 macro_pin_option: K_OUTPUTRESISTANCE $@86 int_number . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1605


State 1435

  640 macro_pin_option: K_TAPERRULE T_STRING ';' .

    $default  reduce using rule 640 (macro_pin_option)


State 1436

  970 opt_layer_name: K_LAYER . $@138 T_STRING

    $default  reduce using rule 969 ($@138)

    $@138  go to state 1606


State 1437

  645 macro_pin_option: K_ANTENNASIZE int_number opt_layer_name . ';'

    ';'  shift, and go to state 1607


State 1438

  647 macro_pin_option: K_ANTENNAMETALLENGTH int_number opt_layer_name . ';'

    ';'  shift, and go to state 1608


State 1439

  646 macro_pin_option: K_ANTENNAMETALAREA NUMBER opt_layer_name . ';'

    ';'  shift, and go to state 1609


State 1440

  648 macro_pin_option: K_RISESLEWLIMIT int_number ';' .

    $default  reduce using rule 648 (macro_pin_option)


State 1441

  649 macro_pin_option: K_FALLSLEWLIMIT int_number ';' .

    $default  reduce using rule 649 (macro_pin_option)


State 1442

  650 macro_pin_option: K_ANTENNAPARTIALMETALAREA NUMBER opt_layer_name . ';'

    ';'  shift, and go to state 1610


State 1443

  651 macro_pin_option: K_ANTENNAPARTIALMETALSIDEAREA NUMBER opt_layer_name . ';'

    ';'  shift, and go to state 1611


State 1444

  654 macro_pin_option: K_ANTENNAGATEAREA NUMBER opt_layer_name . ';'

    ';'  shift, and go to state 1612


State 1445

  653 macro_pin_option: K_ANTENNADIFFAREA NUMBER opt_layer_name . ';'

    ';'  shift, and go to state 1613


State 1446

  972 req_layer_name: K_LAYER . $@139 T_STRING

    $default  reduce using rule 971 ($@139)

    $@139  go to state 1614


State 1447

  655 macro_pin_option: K_ANTENNAMAXAREACAR NUMBER req_layer_name . ';'

    ';'  shift, and go to state 1615


State 1448

  656 macro_pin_option: K_ANTENNAMAXSIDEAREACAR NUMBER req_layer_name . ';'

    ';'  shift, and go to state 1616


State 1449

  652 macro_pin_option: K_ANTENNAPARTIALCUTAREA NUMBER opt_layer_name . ';'

    ';'  shift, and go to state 1617


State 1450

  657 macro_pin_option: K_ANTENNAMAXCUTCAR NUMBER req_layer_name . ';'

    ';'  shift, and go to state 1618


State 1451

  666 pin_layer_oxide: K_OXIDE1 .

    $default  reduce using rule 666 (pin_layer_oxide)


State 1452

  667 pin_layer_oxide: K_OXIDE2 .

    $default  reduce using rule 667 (pin_layer_oxide)


State 1453

  668 pin_layer_oxide: K_OXIDE3 .

    $default  reduce using rule 668 (pin_layer_oxide)


State 1454

  669 pin_layer_oxide: K_OXIDE4 .

    $default  reduce using rule 669 (pin_layer_oxide)


State 1455

  659 macro_pin_option: K_ANTENNAMODEL $@89 pin_layer_oxide . ';'

    ';'  shift, and go to state 1619


State 1456

  665 macro_pin_option: K_GROUNDSENSITIVITY $@92 T_STRING . ';'

    ';'  shift, and go to state 1620


State 1457

  661 macro_pin_option: K_NETEXPR $@90 QSTRING . ';'

    ';'  shift, and go to state 1621


State 1458

  663 macro_pin_option: K_SUPPLYSENSITIVITY $@91 T_STRING . ';'

    ';'  shift, and go to state 1622


State 1459

  604 macro_pin_option: start_foreign K_STRUCTURE ';' .

    $default  reduce using rule 604 (macro_pin_option)


State 1460

  605 macro_pin_option: start_foreign K_STRUCTURE pt . ';'
  606                 | start_foreign K_STRUCTURE pt . orientation ';'

    K_N      shift, and go to state 497
    K_S      shift, and go to state 498
    K_E      shift, and go to state 499
    K_W      shift, and go to state 500
    K_FN     shift, and go to state 501
    K_FS     shift, and go to state 502
    K_FE     shift, and go to state 503
    K_FW     shift, and go to state 504
    K_R0     shift, and go to state 505
    K_R90    shift, and go to state 506
    K_R180   shift, and go to state 507
    K_R270   shift, and go to state 508
    K_MX     shift, and go to state 509
    K_MY     shift, and go to state 510
    K_MXR90  shift, and go to state 511
    K_MYR90  shift, and go to state 512
    ';'      shift, and go to state 1623

    orientation  go to state 1624


State 1461

  602 macro_pin_option: start_foreign pt ';' .

    $default  reduce using rule 602 (macro_pin_option)


State 1462

  603 macro_pin_option: start_foreign pt orientation . ';'

    ';'  shift, and go to state 1625


State 1463

  682 macro_port_class_option: K_CLASS class_type . ';'

    ';'  shift, and go to state 1626


State 1464

  643 macro_pin_option: start_macro_port macro_port_class_option geometries . K_END

    K_END  shift, and go to state 1627


State 1465

  700 geometry: K_LAYER $@93 T_STRING $@94 . layer_exceptpgnet layer_spacing ';'

    K_EXCEPTPGNET  shift, and go to state 1628

    $default  reduce using rule 711 (layer_exceptpgnet)

    layer_exceptpgnet  go to state 1629


State 1466

  703 geometry: K_PATH maskColor K_ITERATE firstPt . otherPts stepPattern ';'

    $default  reduce using rule 718 (otherPts)

    otherPts  go to state 1630


State 1467

  702 geometry: K_PATH maskColor firstPt otherPts . ';'
  719 otherPts: otherPts . nextPt

    NUMBER  shift, and go to state 99
    ';'     shift, and go to state 1631
    '('     shift, and go to state 514

    int_number  go to state 515
    pt          go to state 1374
    nextPt      go to state 1375


State 1468

  707 geometry: K_POLYGON maskColor K_ITERATE firstPt . nextPt nextPt otherPts stepPattern ';'

    NUMBER  shift, and go to state 99
    '('     shift, and go to state 514

    int_number  go to state 515
    pt          go to state 1374
    nextPt      go to state 1632


State 1469

  706 geometry: K_POLYGON maskColor firstPt nextPt . nextPt otherPts ';'

    NUMBER  shift, and go to state 99
    '('     shift, and go to state 514

    int_number  go to state 515
    pt          go to state 1374
    nextPt      go to state 1633


State 1470

  705 geometry: K_RECT maskColor K_ITERATE pt . pt stepPattern ';'

    NUMBER  shift, and go to state 99
    '('     shift, and go to state 514

    int_number  go to state 515
    pt          go to state 1634


State 1471

  704 geometry: K_RECT maskColor pt pt . ';'

    ';'  shift, and go to state 1635


State 1472

  723 via_placement: K_VIA K_ITERATE maskColor pt . $@96 T_STRING stepPattern ';'

    $default  reduce using rule 722 ($@96)

    $@96  go to state 1636


State 1473

  721 via_placement: K_VIA maskColor pt $@95 . T_STRING ';'

    T_STRING  shift, and go to state 1637


State 1474

  771 timing_option: K_FALLCS int_number int_number . ';'

    ';'  shift, and go to state 1638


State 1475

  775 timing_option: K_FALLT0 int_number int_number . ';'

    ';'  shift, and go to state 1639


State 1476

  773 timing_option: K_FALLSATT1 int_number int_number . ';'

    ';'  shift, and go to state 1640


State 1477

  769 timing_option: K_FALLRS int_number int_number . ';'

    ';'  shift, and go to state 1641


State 1478

  812 list_of_from_strings: T_STRING .

    $default  reduce using rule 812 (list_of_from_strings)


State 1479

  760 timing_option: K_FROMPIN $@104 list_of_from_strings . ';'
  813 list_of_from_strings: list_of_from_strings . T_STRING

    T_STRING  shift, and go to state 1642
    ';'       shift, and go to state 1643


State 1480

  770 timing_option: K_RISECS int_number int_number . ';'

    ';'  shift, and go to state 1644


State 1481

  768 timing_option: K_RISERS int_number int_number . ';'

    ';'  shift, and go to state 1645


State 1482

  772 timing_option: K_RISESATT1 int_number int_number . ';'

    ';'  shift, and go to state 1646


State 1483

  774 timing_option: K_RISET0 int_number int_number . ';'

    ';'  shift, and go to state 1647


State 1484

  814 list_of_to_strings: T_STRING .

    $default  reduce using rule 814 (list_of_to_strings)


State 1485

  762 timing_option: K_TOPIN $@105 list_of_to_strings . ';'
  815 list_of_to_strings: list_of_to_strings . T_STRING

    T_STRING  shift, and go to state 1648
    ';'       shift, and go to state 1649


State 1486

  776 timing_option: K_UNATENESS unateness ';' .

    $default  reduce using rule 776 (timing_option)


State 1487

  777 timing_option: K_STABLE K_SETUP int_number . K_HOLD int_number risefall ';'

    K_HOLD  shift, and go to state 1650


State 1488

  766 timing_option: K_TABLEAXIS list_of_table_axis_dnumbers ';' .

    $default  reduce using rule 766 (timing_option)


State 1489

  803 list_of_table_axis_dnumbers: list_of_table_axis_dnumbers int_number .

    $default  reduce using rule 803 (list_of_table_axis_dnumbers)


State 1490

  801 table_entry: '(' int_number . int_number int_number ')'

    NUMBER  shift, and go to state 99

    int_number  go to state 1651


State 1491

  767 timing_option: K_TABLEENTRIES list_of_table_entries ';' .

    $default  reduce using rule 767 (timing_option)


State 1492

  800 list_of_table_entries: list_of_table_entries table_entry .

    $default  reduce using rule 800 (list_of_table_entries)


State 1493

  780 timing_option: K_SDFCONDSTART QSTRING ';' .

    $default  reduce using rule 780 (timing_option)


State 1494

  781 timing_option: K_SDFCONDEND QSTRING ';' .

    $default  reduce using rule 781 (timing_option)


State 1495

  782 timing_option: K_SDFCOND QSTRING ';' .

    $default  reduce using rule 782 (timing_option)


State 1496

  779 timing_option: one_pin_trigger K_TABLEDIMENSION int_number . int_number int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1652


State 1497

  794 to_pin_trigger: K_ANYEDGE .

    $default  reduce using rule 794 (to_pin_trigger)


State 1498

  795 to_pin_trigger: K_POSEDGE .

    $default  reduce using rule 795 (to_pin_trigger)


State 1499

  796 to_pin_trigger: K_NEGEDGE .

    $default  reduce using rule 796 (to_pin_trigger)


State 1500

  778 timing_option: two_pin_trigger from_pin_trigger to_pin_trigger . K_TABLEDIMENSION int_number int_number int_number ';'

    K_TABLEDIMENSION  shift, and go to state 1653


State 1501

  764 timing_option: risefall K_INTRINSIC int_number . int_number $@106 slew_spec K_VARIABLE int_number int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1654


State 1502

  765 timing_option: risefall delay_or_transition K_UNATENESS . unateness K_TABLEDIMENSION int_number int_number int_number ';'

    K_INVERT     shift, and go to state 1229
    K_NONINVERT  shift, and go to state 1230
    K_NONUNATE   shift, and go to state 1231

    unateness  go to state 1655


State 1503

  729 trackPattern: K_X int_number K_DO . int_number K_STEP int_number $@97 K_LAYER $@98 trackLayers
  733             | K_X int_number K_DO . int_number K_STEP int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1656


State 1504

  732 trackPattern: K_Y int_number K_DO . int_number K_STEP int_number $@99 K_LAYER $@100 trackLayers
  734             | K_Y int_number K_DO . int_number K_STEP int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1657


State 1505

  738 gcellPattern: K_X int_number K_DO . int_number K_STEP int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1658


State 1506

  739 gcellPattern: K_Y int_number K_DO . int_number K_STEP int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1659


State 1507

  835 array_rule: K_DEFAULTCAP int_number cap_list K_END K_DEFAULTCAP .

    $default  reduce using rule 835 (array_rule)


State 1508

  846 one_cap: K_MINPINS int_number . K_WIRECAP int_number ';'

    K_WIRECAP  shift, and go to state 1660


State 1509

  841 floorplan_element: K_CANPLACE $@115 sitePattern . ';'

    ';'  shift, and go to state 1661


State 1510

  843 floorplan_element: K_CANNOTOCCUPY $@116 sitePattern . ';'

    ';'  shift, and go to state 1662


State 1511

  461 nd_layer: K_LAYER $@67 . T_STRING $@68 K_WIDTH int_number ';' $@69 nd_layer_stmts K_END $@70 T_STRING

    T_STRING  shift, and go to state 1663


State 1512

  454 nd_prop: T_STRING T_STRING .

    $default  reduce using rule 454 (nd_prop)


State 1513

  455 nd_prop: T_STRING QSTRING .

    $default  reduce using rule 455 (nd_prop)


State 1514

  456 nd_prop: T_STRING NUMBER .

    $default  reduce using rule 456 (nd_prop)


State 1515

  451 nd_prop: K_PROPERTY $@66 . nd_prop_list ';'

    T_STRING    shift, and go to state 1269
    K_PROPERTY  shift, and go to state 1270

    nd_prop       go to state 1664
    nd_prop_list  go to state 1665


State 1516

  449 mincuts: K_MINCUTS T_STRING . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1666


State 1517

  447 usevia: K_USEVIA T_STRING . ';'

    ';'  shift, and go to state 1667


State 1518

  448 useviarule: K_USEVIARULE T_STRING . ';'

    ';'  shift, and go to state 1668


State 1519

  434 end_nd_rule: K_END .
  435            | K_END . T_STRING

    T_STRING  shift, and go to state 1669

    $default  reduce using rule 434 (end_nd_rule)


State 1520

  433 nondefault_rule: K_NONDEFAULTRULE $@63 T_STRING $@64 nd_hardspacing nd_rules $@65 end_nd_rule .

    $default  reduce using rule 433 (nondefault_rule)


State 1521

  861 then: '\n' K_THEN .

    $default  reduce using rule 861 (then)


State 1522

  864 expression: expression . '+' expression
  865           | expression . '-' expression
  866           | expression . '*' expression
  867           | expression . '/' expression
  870           | K_IF b_expr then expression . else expression

    K_ELSE  shift, and go to state 1670
    '-'     shift, and go to state 970
    '+'     shift, and go to state 971
    '*'     shift, and go to state 972
    '/'     shift, and go to state 973
    '\n'    shift, and go to state 1671

    else  go to state 1672


State 1523

  887 s_expr: s_expr . '+' s_expr
  889       | K_IF b_expr then s_expr . else s_expr

    K_ELSE  shift, and go to state 1670
    '+'     shift, and go to state 979
    '\n'    shift, and go to state 1671

    else  go to state 1673


State 1524

  864 expression: expression . '+' expression
  865           | expression . '-' expression
  866           | expression . '*' expression
  867           | expression . '/' expression
  870           | K_IF b_expr then expression . else expression
  872 b_expr: expression . relop expression
  873       | expression . K_AND expression
  874       | expression . K_OR expression

    K_ELSE  shift, and go to state 1670
    K_EQ    shift, and go to state 986
    K_NE    shift, and go to state 987
    K_LE    shift, and go to state 988
    K_LT    shift, and go to state 989
    K_GE    shift, and go to state 990
    K_GT    shift, and go to state 991
    K_OR    shift, and go to state 992
    K_AND   shift, and go to state 993
    '-'     shift, and go to state 970
    '+'     shift, and go to state 971
    '*'     shift, and go to state 972
    '/'     shift, and go to state 973
    '='     shift, and go to state 994
    '\n'    shift, and go to state 1671
    '<'     shift, and go to state 995
    '>'     shift, and go to state 996

    else   go to state 1672
    relop  go to state 997


State 1525

  878 b_expr: b_expr . K_EQ b_expr
  879       | b_expr . K_NE b_expr
  880       | b_expr . K_AND b_expr
  881       | b_expr . K_OR b_expr
  884       | K_IF b_expr then b_expr . else b_expr

    K_ELSE  shift, and go to state 1670
    K_EQ    shift, and go to state 998
    K_NE    shift, and go to state 999
    K_OR    shift, and go to state 1000
    K_AND   shift, and go to state 1001
    '\n'    shift, and go to state 1671

    else  go to state 1674


State 1526

  875 b_expr: s_expr . relop s_expr
  876       | s_expr . K_AND s_expr
  877       | s_expr . K_OR s_expr
  887 s_expr: s_expr . '+' s_expr
  889       | K_IF b_expr then s_expr . else s_expr

    K_ELSE  shift, and go to state 1670
    K_EQ    shift, and go to state 986
    K_NE    shift, and go to state 987
    K_LE    shift, and go to state 988
    K_LT    shift, and go to state 989
    K_GE    shift, and go to state 990
    K_GT    shift, and go to state 991
    K_OR    shift, and go to state 1003
    K_AND   shift, and go to state 1004
    '+'     shift, and go to state 979
    '='     shift, and go to state 994
    '\n'    shift, and go to state 1671
    '<'     shift, and go to state 995
    '>'     shift, and go to state 996

    else   go to state 1673
    relop  go to state 1005


State 1527

  923 prop_define: K_STRING QSTRING .

    $default  reduce using rule 923 (prop_define)


State 1528

  938 opt_def_range: K_RANGE . int_number int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1675


State 1529

  920 prop_define: K_INTEGER opt_def_range . opt_def_dvalue

    NUMBER  shift, and go to state 99

    $default  reduce using rule 941 (opt_def_dvalue)

    int_number      go to state 1676
    opt_def_dvalue  go to state 1677


State 1530

  921 prop_define: K_REAL opt_def_range . opt_def_value

    NUMBER  shift, and go to state 1678

    $default  reduce using rule 939 (opt_def_value)

    opt_def_value  go to state 1679


State 1531

  924 prop_define: K_NAMEMAPSTRING T_STRING .

    $default  reduce using rule 924 (prop_define)


State 1532

  907 prop_stmt: K_COMPONENTPIN $@124 T_STRING prop_define ';' .

    $default  reduce using rule 907 (prop_stmt)


State 1533

  917 prop_stmt: K_LAYER $@129 T_STRING prop_define ';' .

    $default  reduce using rule 917 (prop_stmt)


State 1534

  905 prop_stmt: K_LIBRARY $@123 T_STRING prop_define ';' .

    $default  reduce using rule 905 (prop_stmt)


State 1535

  911 prop_stmt: K_MACRO $@126 T_STRING prop_define ';' .

    $default  reduce using rule 911 (prop_stmt)


State 1536

  919 prop_stmt: K_NONDEFAULTRULE $@130 T_STRING prop_define ';' .

    $default  reduce using rule 919 (prop_stmt)


State 1537

  909 prop_stmt: K_PIN $@125 T_STRING prop_define ';' .

    $default  reduce using rule 909 (prop_stmt)


State 1538

  913 prop_stmt: K_VIA $@127 T_STRING prop_define ';' .

    $default  reduce using rule 913 (prop_stmt)


State 1539

  915 prop_stmt: K_VIARULE $@128 T_STRING prop_define ';' .

    $default  reduce using rule 915 (prop_stmt)


State 1540

  1002 output_list: K_OUTPUTRESISTANCE $@144 numo_list ';' . corr_victim_list

    K_VICTIMLENGTH  shift, and go to state 1680

    corr_victim_list  go to state 1681
    corr_victim       go to state 1682


State 1541

  1004 numo_list: numo_list int_number .

    $default  reduce using rule 1004 (numo_list)


State 1542

  985 output_resistance_entry: K_OUTPUTRESISTANCE $@141 num_list ';' . victim_list

    K_VICTIMLENGTH  shift, and go to state 1683

    victim_list  go to state 1684
    victim       go to state 1685


State 1543

  987 num_list: num_list int_number .

    $default  reduce using rule 987 (num_list)


State 1544

  268 cap_point: '(' int_number . int_number ')'

    NUMBER  shift, and go to state 99

    int_number  go to state 1686


State 1545

  107 layer_option: K_CAPACITANCE K_CPERSQDIST K_PWL '(' cap_points ')' . ';'

    ';'  shift, and go to state 1687


State 1546

  267 cap_points: cap_points cap_point .

    $default  reduce using rule 267 (cap_points)


State 1547

  271 res_point: '(' int_number . int_number ')'

    NUMBER  shift, and go to state 99

    int_number  go to state 1688


State 1548

  105 layer_option: K_RESISTANCE K_RPERSQ K_PWL '(' res_points ')' . ';'

    ';'  shift, and go to state 1689


State 1549

  270 res_points: res_points res_point .

    $default  reduce using rule 270 (res_points)


State 1550

  936 opt_samenetPGonly: K_PGONLY .

    $default  reduce using rule 936 (opt_samenetPGonly)


State 1551

  947 layer_spacing_opt: K_SAMENET $@132 opt_samenetPGonly .

    $default  reduce using rule 947 (layer_spacing_opt)


State 1552

  955 layer_spacing_cut_routing: K_AREA NUMBER .

    $default  reduce using rule 955 (layer_spacing_cut_routing)


State 1553

  952 layer_spacing_cut_routing: K_LAYER $@133 . T_STRING $@134 spacing_cut_layer_opt

    T_STRING  shift, and go to state 1690


State 1554

  957 layer_spacing_cut_routing: K_RANGE int_number . int_number $@136 opt_range_second

    NUMBER  shift, and go to state 99

    int_number  go to state 1691


State 1555

  958 layer_spacing_cut_routing: K_LENGTHTHRESHOLD int_number .
  959                          | K_LENGTHTHRESHOLD int_number . K_RANGE int_number int_number

    K_RANGE  shift, and go to state 1692

    $default  reduce using rule 958 (layer_spacing_cut_routing)


State 1556

  954 layer_spacing_cut_routing: K_ADJACENTCUTS int_number . K_WITHIN int_number $@135 opt_adjacentcuts_exceptsame

    K_WITHIN  shift, and go to state 1693


State 1557

  961 layer_spacing_cut_routing: K_ENDOFLINE int_number . K_WITHIN int_number $@137 opt_endofline

    K_WITHIN  shift, and go to state 1694


State 1558

  963 layer_spacing_cut_routing: K_ENDOFNOTCHWIDTH int_number . K_NOTCHSPACING int_number K_NOTCHLENGTH int_number

    K_NOTCHSPACING  shift, and go to state 1695


State 1559

  962 layer_spacing_cut_routing: K_NOTCHLENGTH int_number .

    $default  reduce using rule 962 (layer_spacing_cut_routing)


State 1560

  100 layer_option: K_SPACING int_number $@6 layer_spacing_opts layer_spacing_cut_routing ';' .

    $default  reduce using rule 100 (layer_option)


State 1561

  265 current_density_pwl: '(' int_number int_number . ')'

    ')'  shift, and go to state 1696


State 1562

  116 layer_option: K_CURRENTDEN K_PWL '(' current_density_pwl_list ')' ';' .

    $default  reduce using rule 116 (layer_option)


State 1563

  117 layer_option: K_CURRENTDEN '(' int_number int_number ')' ';' .

    $default  reduce using rule 117 (layer_option)


State 1564

  246 layer_frequency: K_FREQUENCY NUMBER $@39 . number_list ';' $@40 ac_layer_table_opt K_TABLEENTRIES NUMBER $@41 number_list ';'

    $default  reduce using rule 256 (number_list)

    number_list  go to state 1697


State 1565

  129 layer_option: K_DCCURRENTDENSITY K_AVERAGE K_WIDTH int_number $@12 int_number_list . ';' $@13 dc_layer_table
  255 int_number_list: int_number_list . int_number

    NUMBER  shift, and go to state 99
    ';'     shift, and go to state 1698

    int_number  go to state 1699


State 1566

  126 layer_option: K_DCCURRENTDENSITY K_AVERAGE K_CUTAREA NUMBER $@10 number_list . ';' $@11 dc_layer_table
  257 number_list: number_list . NUMBER

    NUMBER  shift, and go to state 1700
    ';'     shift, and go to state 1701


State 1567

  234 layer_antenna_pwl: K_PWL '(' pt pt . $@38 layer_diffusion_ratios ')'

    $default  reduce using rule 233 ($@38)

    $@38  go to state 1702


State 1568

  218 layer_minimumcut_within: K_WITHIN . int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1703


State 1569

  172 layer_option: K_MINIMUMCUT int_number K_WIDTH int_number $@24 layer_minimumcut_within . layer_minimumcut_from layer_minimumcut_length ';'

    K_FROMABOVE  shift, and go to state 1704
    K_FROMBELOW  shift, and go to state 1705

    $default  reduce using rule 219 (layer_minimumcut_from)

    layer_minimumcut_from  go to state 1706


State 1570

  211 layer_enclosure_width_opt: K_WIDTH . int_number $@37 layer_enclosure_width_except_opt

    NUMBER  shift, and go to state 99

    int_number  go to state 1707


State 1571

  212 layer_enclosure_width_opt: K_LENGTH . int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1708


State 1572

  179 layer_option: K_ENCLOSURE layer_enclosure_type_opt int_number int_number $@27 layer_enclosure_width_opt . ';'

    ';'  shift, and go to state 1709


State 1573

  227 layer_minstep_option: K_LENGTHSUM int_number .

    $default  reduce using rule 227 (layer_minstep_option)


State 1574

  228 layer_minstep_option: K_MAXEDGES int_number .

    $default  reduce using rule 228 (layer_minstep_option)


State 1575

  175 layer_option: K_PROTRUSIONWIDTH int_number K_LENGTH int_number K_WIDTH int_number . ';'

    ';'  shift, and go to state 1710


State 1576

  102 layer_option: K_SPACINGTABLE K_ORTHOGONAL K_WITHIN int_number K_SPACING int_number . $@7 layer_spacingtable_opts ';'

    $default  reduce using rule 101 ($@7)

    $@7  go to state 1711


State 1577

  202 sp_options: K_INFLUENCE K_WIDTH int_number K_WITHIN . int_number K_SPACING int_number $@36 layer_sp_influence_widths

    NUMBER  shift, and go to state 99

    int_number  go to state 1712


State 1578

  197 sp_options: K_PARALLELRUNLENGTH int_number $@30 int_number_list . $@31 K_WIDTH int_number $@32 int_number_list $@33 layer_sp_parallel_widths
  255 int_number_list: int_number_list . int_number

    NUMBER  shift, and go to state 99

    $default  reduce using rule 194 ($@31)

    int_number  go to state 1699
    $@31        go to state 1713


State 1579

  297 layer_sp_TwoWidthsPRL: K_PRL . int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1714


State 1580

  200 sp_options: K_TWOWIDTHS K_WIDTH int_number layer_sp_TwoWidthsPRL . int_number $@34 int_number_list $@35 layer_sp_TwoWidths

    NUMBER  shift, and go to state 99

    int_number  go to state 1715


State 1581

  216 layer_preferenclosure_width_opt: K_WIDTH . int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1716


State 1582

  181 layer_option: K_PREFERENCLOSURE layer_enclosure_type_opt int_number int_number $@28 layer_preferenclosure_width_opt . ';'

    ';'  shift, and go to state 1717


State 1583

   86 layer_option: K_ARRAYSPACING $@4 layer_arraySpacing_long layer_arraySpacing_width K_CUTSPACING int_number . $@5 layer_arraySpacing_arraycuts ';'

    $default  reduce using rule 85 ($@5)

    $@5  go to state 1718


State 1584

  153 layer_option: K_ANTENNAAREADIFFREDUCEPWL '(' pt pt $@21 layer_diffusion_ratios . ')' ';' $@22
  236 layer_diffusion_ratios: layer_diffusion_ratios . layer_diffusion_ratio

    NUMBER  shift, and go to state 99
    '('     shift, and go to state 514
    ')'     shift, and go to state 1719

    int_number             go to state 515
    layer_diffusion_ratio  go to state 1720
    pt                     go to state 1721


State 1585

  313 via_viarule: K_VIARULE $@49 T_STRING ';' K_CUTSIZE int_number . int_number ';' K_LAYERS $@50 T_STRING T_STRING T_STRING ';' K_CUTSPACING int_number int_number ';' K_ENCLOSURE int_number int_number int_number int_number ';' $@51 via_viarule_options

    NUMBER  shift, and go to state 99

    int_number  go to state 1722


State 1586

  366 via_geometry: K_POLYGON maskColor $@56 firstPt . nextPt nextPt otherPts ';'

    NUMBER  shift, and go to state 99
    '('     shift, and go to state 514

    int_number  go to state 515
    pt          go to state 1374
    nextPt      go to state 1723


State 1587

  364 via_geometry: K_RECT maskColor pt pt . ';'

    ';'  shift, and go to state 1724


State 1588

  402 viarule_layer_option: K_SPACING int_number K_BY int_number ';' .

    $default  reduce using rule 402 (viarule_layer_option)


State 1589

  400 viarule_layer_option: K_WIDTH int_number K_TO int_number ';' .

    $default  reduce using rule 400 (viarule_layer_option)


State 1590

  414 spacing: samenet_keyword T_STRING T_STRING int_number K_STACK ';' .

    $default  reduce using rule 414 (spacing)


State 1591

  495 site_rowpattern: T_STRING orientation $@74 .

    $default  reduce using rule 495 (site_rowpattern)


State 1592

  748 density_layer: K_LAYER $@101 T_STRING ';' $@102 . density_layer_rect density_layer_rects

    K_RECT  shift, and go to state 1725

    density_layer_rect  go to state 1726


State 1593

  725 sitePattern: T_STRING int_number int_number orientation K_DO . int_number K_BY int_number K_STEP int_number int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1727


State 1594

  677 electrical_direction: K_DIRECTION K_OUTPUT K_TRISTATE ';' .

    $default  reduce using rule 677 (electrical_direction)


State 1595

  629 macro_pin_option: K_INPUTNOISEMARGIN $@87 int_number int_number . ';'

    ';'  shift, and go to state 1728


State 1596

  639 macro_pin_option: K_IV_TABLES T_STRING T_STRING ';' .

    $default  reduce using rule 639 (macro_pin_option)


State 1597

  608 macro_pin_option: K_LEQ $@83 T_STRING ';' .

    $default  reduce using rule 608 (macro_pin_option)


State 1598

  623 macro_pin_option: K_MUSTJOIN $@84 T_STRING ';' .

    $default  reduce using rule 623 (macro_pin_option)


State 1599

  625 macro_pin_option: K_OUTPUTNOISEMARGIN $@85 int_number int_number . ';'

    ';'  shift, and go to state 1729


State 1600

  674 pin_name_value_pair: T_STRING T_STRING .

    $default  reduce using rule 674 (pin_name_value_pair)


State 1601

  673 pin_name_value_pair: T_STRING QSTRING .

    $default  reduce using rule 673 (pin_name_value_pair)


State 1602

  672 pin_name_value_pair: T_STRING NUMBER .

    $default  reduce using rule 672 (pin_name_value_pair)


State 1603

  642 macro_pin_option: K_PROPERTY $@88 pin_prop_list ';' .

    $default  reduce using rule 642 (macro_pin_option)


State 1604

  671 pin_prop_list: pin_prop_list pin_name_value_pair .

    $default  reduce using rule 671 (pin_prop_list)


State 1605

  627 macro_pin_option: K_OUTPUTRESISTANCE $@86 int_number int_number . ';'

    ';'  shift, and go to state 1730


State 1606

  970 opt_layer_name: K_LAYER $@138 . T_STRING

    T_STRING  shift, and go to state 1731


State 1607

  645 macro_pin_option: K_ANTENNASIZE int_number opt_layer_name ';' .

    $default  reduce using rule 645 (macro_pin_option)


State 1608

  647 macro_pin_option: K_ANTENNAMETALLENGTH int_number opt_layer_name ';' .

    $default  reduce using rule 647 (macro_pin_option)


State 1609

  646 macro_pin_option: K_ANTENNAMETALAREA NUMBER opt_layer_name ';' .

    $default  reduce using rule 646 (macro_pin_option)


State 1610

  650 macro_pin_option: K_ANTENNAPARTIALMETALAREA NUMBER opt_layer_name ';' .

    $default  reduce using rule 650 (macro_pin_option)


State 1611

  651 macro_pin_option: K_ANTENNAPARTIALMETALSIDEAREA NUMBER opt_layer_name ';' .

    $default  reduce using rule 651 (macro_pin_option)


State 1612

  654 macro_pin_option: K_ANTENNAGATEAREA NUMBER opt_layer_name ';' .

    $default  reduce using rule 654 (macro_pin_option)


State 1613

  653 macro_pin_option: K_ANTENNADIFFAREA NUMBER opt_layer_name ';' .

    $default  reduce using rule 653 (macro_pin_option)


State 1614

  972 req_layer_name: K_LAYER $@139 . T_STRING

    T_STRING  shift, and go to state 1732


State 1615

  655 macro_pin_option: K_ANTENNAMAXAREACAR NUMBER req_layer_name ';' .

    $default  reduce using rule 655 (macro_pin_option)


State 1616

  656 macro_pin_option: K_ANTENNAMAXSIDEAREACAR NUMBER req_layer_name ';' .

    $default  reduce using rule 656 (macro_pin_option)


State 1617

  652 macro_pin_option: K_ANTENNAPARTIALCUTAREA NUMBER opt_layer_name ';' .

    $default  reduce using rule 652 (macro_pin_option)


State 1618

  657 macro_pin_option: K_ANTENNAMAXCUTCAR NUMBER req_layer_name ';' .

    $default  reduce using rule 657 (macro_pin_option)


State 1619

  659 macro_pin_option: K_ANTENNAMODEL $@89 pin_layer_oxide ';' .

    $default  reduce using rule 659 (macro_pin_option)


State 1620

  665 macro_pin_option: K_GROUNDSENSITIVITY $@92 T_STRING ';' .

    $default  reduce using rule 665 (macro_pin_option)


State 1621

  661 macro_pin_option: K_NETEXPR $@90 QSTRING ';' .

    $default  reduce using rule 661 (macro_pin_option)


State 1622

  663 macro_pin_option: K_SUPPLYSENSITIVITY $@91 T_STRING ';' .

    $default  reduce using rule 663 (macro_pin_option)


State 1623

  605 macro_pin_option: start_foreign K_STRUCTURE pt ';' .

    $default  reduce using rule 605 (macro_pin_option)


State 1624

  606 macro_pin_option: start_foreign K_STRUCTURE pt orientation . ';'

    ';'  shift, and go to state 1733


State 1625

  603 macro_pin_option: start_foreign pt orientation ';' .

    $default  reduce using rule 603 (macro_pin_option)


State 1626

  682 macro_port_class_option: K_CLASS class_type ';' .

    $default  reduce using rule 682 (macro_port_class_option)


State 1627

  643 macro_pin_option: start_macro_port macro_port_class_option geometries K_END .

    $default  reduce using rule 643 (macro_pin_option)


State 1628

  712 layer_exceptpgnet: K_EXCEPTPGNET .

    $default  reduce using rule 712 (layer_exceptpgnet)


State 1629

  700 geometry: K_LAYER $@93 T_STRING $@94 layer_exceptpgnet . layer_spacing ';'

    K_DESIGNRULEWIDTH  shift, and go to state 1734
    K_SPACING          shift, and go to state 1735

    $default  reduce using rule 713 (layer_spacing)

    layer_spacing  go to state 1736


State 1630

  703 geometry: K_PATH maskColor K_ITERATE firstPt otherPts . stepPattern ';'
  719 otherPts: otherPts . nextPt

    K_DO    shift, and go to state 1737
    NUMBER  shift, and go to state 99
    '('     shift, and go to state 514

    int_number   go to state 515
    pt           go to state 1374
    nextPt       go to state 1375
    stepPattern  go to state 1738


State 1631

  702 geometry: K_PATH maskColor firstPt otherPts ';' .

    $default  reduce using rule 702 (geometry)


State 1632

  707 geometry: K_POLYGON maskColor K_ITERATE firstPt nextPt . nextPt otherPts stepPattern ';'

    NUMBER  shift, and go to state 99
    '('     shift, and go to state 514

    int_number  go to state 515
    pt          go to state 1374
    nextPt      go to state 1739


State 1633

  706 geometry: K_POLYGON maskColor firstPt nextPt nextPt . otherPts ';'

    $default  reduce using rule 718 (otherPts)

    otherPts  go to state 1740


State 1634

  705 geometry: K_RECT maskColor K_ITERATE pt pt . stepPattern ';'

    K_DO  shift, and go to state 1737

    stepPattern  go to state 1741


State 1635

  704 geometry: K_RECT maskColor pt pt ';' .

    $default  reduce using rule 704 (geometry)


State 1636

  723 via_placement: K_VIA K_ITERATE maskColor pt $@96 . T_STRING stepPattern ';'

    T_STRING  shift, and go to state 1742


State 1637

  721 via_placement: K_VIA maskColor pt $@95 T_STRING . ';'

    ';'  shift, and go to state 1743


State 1638

  771 timing_option: K_FALLCS int_number int_number ';' .

    $default  reduce using rule 771 (timing_option)


State 1639

  775 timing_option: K_FALLT0 int_number int_number ';' .

    $default  reduce using rule 775 (timing_option)


State 1640

  773 timing_option: K_FALLSATT1 int_number int_number ';' .

    $default  reduce using rule 773 (timing_option)


State 1641

  769 timing_option: K_FALLRS int_number int_number ';' .

    $default  reduce using rule 769 (timing_option)


State 1642

  813 list_of_from_strings: list_of_from_strings T_STRING .

    $default  reduce using rule 813 (list_of_from_strings)


State 1643

  760 timing_option: K_FROMPIN $@104 list_of_from_strings ';' .

    $default  reduce using rule 760 (timing_option)


State 1644

  770 timing_option: K_RISECS int_number int_number ';' .

    $default  reduce using rule 770 (timing_option)


State 1645

  768 timing_option: K_RISERS int_number int_number ';' .

    $default  reduce using rule 768 (timing_option)


State 1646

  772 timing_option: K_RISESATT1 int_number int_number ';' .

    $default  reduce using rule 772 (timing_option)


State 1647

  774 timing_option: K_RISET0 int_number int_number ';' .

    $default  reduce using rule 774 (timing_option)


State 1648

  815 list_of_to_strings: list_of_to_strings T_STRING .

    $default  reduce using rule 815 (list_of_to_strings)


State 1649

  762 timing_option: K_TOPIN $@105 list_of_to_strings ';' .

    $default  reduce using rule 762 (timing_option)


State 1650

  777 timing_option: K_STABLE K_SETUP int_number K_HOLD . int_number risefall ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1744


State 1651

  801 table_entry: '(' int_number int_number . int_number ')'

    NUMBER  shift, and go to state 99

    int_number  go to state 1745


State 1652

  779 timing_option: one_pin_trigger K_TABLEDIMENSION int_number int_number . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1746


State 1653

  778 timing_option: two_pin_trigger from_pin_trigger to_pin_trigger K_TABLEDIMENSION . int_number int_number int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1747


State 1654

  764 timing_option: risefall K_INTRINSIC int_number int_number . $@106 slew_spec K_VARIABLE int_number int_number ';'

    $default  reduce using rule 763 ($@106)

    $@106  go to state 1748


State 1655

  765 timing_option: risefall delay_or_transition K_UNATENESS unateness . K_TABLEDIMENSION int_number int_number int_number ';'

    K_TABLEDIMENSION  shift, and go to state 1749


State 1656

  729 trackPattern: K_X int_number K_DO int_number . K_STEP int_number $@97 K_LAYER $@98 trackLayers
  733             | K_X int_number K_DO int_number . K_STEP int_number

    K_STEP  shift, and go to state 1750


State 1657

  732 trackPattern: K_Y int_number K_DO int_number . K_STEP int_number $@99 K_LAYER $@100 trackLayers
  734             | K_Y int_number K_DO int_number . K_STEP int_number

    K_STEP  shift, and go to state 1751


State 1658

  738 gcellPattern: K_X int_number K_DO int_number . K_STEP int_number

    K_STEP  shift, and go to state 1752


State 1659

  739 gcellPattern: K_Y int_number K_DO int_number . K_STEP int_number

    K_STEP  shift, and go to state 1753


State 1660

  846 one_cap: K_MINPINS int_number K_WIRECAP . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1754


State 1661

  841 floorplan_element: K_CANPLACE $@115 sitePattern ';' .

    $default  reduce using rule 841 (floorplan_element)


State 1662

  843 floorplan_element: K_CANNOTOCCUPY $@116 sitePattern ';' .

    $default  reduce using rule 843 (floorplan_element)


State 1663

  461 nd_layer: K_LAYER $@67 T_STRING . $@68 K_WIDTH int_number ';' $@69 nd_layer_stmts K_END $@70 T_STRING

    $default  reduce using rule 458 ($@68)

    $@68  go to state 1755


State 1664

  452 nd_prop_list: nd_prop .

    $default  reduce using rule 452 (nd_prop_list)


State 1665

  451 nd_prop: K_PROPERTY $@66 nd_prop_list . ';'
  453 nd_prop_list: nd_prop_list . nd_prop

    T_STRING    shift, and go to state 1269
    K_PROPERTY  shift, and go to state 1270
    ';'         shift, and go to state 1756

    nd_prop  go to state 1757


State 1666

  449 mincuts: K_MINCUTS T_STRING int_number . ';'

    ';'  shift, and go to state 1758


State 1667

  447 usevia: K_USEVIA T_STRING ';' .

    $default  reduce using rule 447 (usevia)


State 1668

  448 useviarule: K_USEVIARULE T_STRING ';' .

    $default  reduce using rule 448 (useviarule)


State 1669

  435 end_nd_rule: K_END T_STRING .

    $default  reduce using rule 435 (end_nd_rule)


State 1670

  862 else: K_ELSE .

    $default  reduce using rule 862 (else)


State 1671

  863 else: '\n' . K_ELSE

    K_ELSE  shift, and go to state 1759


State 1672

  870 expression: K_IF b_expr then expression else . expression

    NUMBER  shift, and go to state 99
    K_IF    shift, and go to state 610
    '-'     shift, and go to state 611
    '('     shift, and go to state 612

    int_number  go to state 613
    expression  go to state 1760


State 1673

  889 s_expr: K_IF b_expr then s_expr else . s_expr

    QSTRING  shift, and go to state 615
    K_IF     shift, and go to state 616
    '('      shift, and go to state 617

    s_expr  go to state 1761


State 1674

  884 b_expr: K_IF b_expr then b_expr else . b_expr

    QSTRING  shift, and go to state 615
    NUMBER   shift, and go to state 99
    K_IF     shift, and go to state 619
    K_FALSE  shift, and go to state 620
    K_TRUE   shift, and go to state 621
    K_NOT    shift, and go to state 622
    '-'      shift, and go to state 611
    '('      shift, and go to state 623

    int_number  go to state 613
    expression  go to state 624
    b_expr      go to state 1762
    s_expr      go to state 626


State 1675

  938 opt_def_range: K_RANGE int_number . int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1763


State 1676

  942 opt_def_dvalue: int_number .

    $default  reduce using rule 942 (opt_def_dvalue)


State 1677

  920 prop_define: K_INTEGER opt_def_range opt_def_dvalue .

    $default  reduce using rule 920 (prop_define)


State 1678

  940 opt_def_value: NUMBER .

    $default  reduce using rule 940 (opt_def_value)


State 1679

  921 prop_define: K_REAL opt_def_range opt_def_value .

    $default  reduce using rule 921 (prop_define)


State 1680

  1008 corr_victim: K_VICTIMLENGTH . int_number ';' $@145 K_CORRECTIONFACTOR corr_list ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1764


State 1681

  1002 output_list: K_OUTPUTRESISTANCE $@144 numo_list ';' corr_victim_list .
  1006 corr_victim_list: corr_victim_list . corr_victim

    K_VICTIMLENGTH  shift, and go to state 1680

    $default  reduce using rule 1002 (output_list)

    corr_victim  go to state 1765


State 1682

  1005 corr_victim_list: corr_victim .

    $default  reduce using rule 1005 (corr_victim_list)


State 1683

  991 victim: K_VICTIMLENGTH . int_number ';' $@142 K_VICTIMNOISE vnoiselist ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1766


State 1684

  985 output_resistance_entry: K_OUTPUTRESISTANCE $@141 num_list ';' victim_list .
  989 victim_list: victim_list . victim

    K_VICTIMLENGTH  shift, and go to state 1683

    $default  reduce using rule 985 (output_resistance_entry)

    victim  go to state 1767


State 1685

  988 victim_list: victim .

    $default  reduce using rule 988 (victim_list)


State 1686

  268 cap_point: '(' int_number int_number . ')'

    ')'  shift, and go to state 1768


State 1687

  107 layer_option: K_CAPACITANCE K_CPERSQDIST K_PWL '(' cap_points ')' ';' .

    $default  reduce using rule 107 (layer_option)


State 1688

  271 res_point: '(' int_number int_number . ')'

    ')'  shift, and go to state 1769


State 1689

  105 layer_option: K_RESISTANCE K_RPERSQ K_PWL '(' res_points ')' ';' .

    $default  reduce using rule 105 (layer_option)


State 1690

  952 layer_spacing_cut_routing: K_LAYER $@133 T_STRING . $@134 spacing_cut_layer_opt

    $default  reduce using rule 951 ($@134)

    $@134  go to state 1770


State 1691

  957 layer_spacing_cut_routing: K_RANGE int_number int_number . $@136 opt_range_second

    $default  reduce using rule 956 ($@136)

    $@136  go to state 1771


State 1692

  959 layer_spacing_cut_routing: K_LENGTHTHRESHOLD int_number K_RANGE . int_number int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1772


State 1693

  954 layer_spacing_cut_routing: K_ADJACENTCUTS int_number K_WITHIN . int_number $@135 opt_adjacentcuts_exceptsame

    NUMBER  shift, and go to state 99

    int_number  go to state 1773


State 1694

  961 layer_spacing_cut_routing: K_ENDOFLINE int_number K_WITHIN . int_number $@137 opt_endofline

    NUMBER  shift, and go to state 99

    int_number  go to state 1774


State 1695

  963 layer_spacing_cut_routing: K_ENDOFNOTCHWIDTH int_number K_NOTCHSPACING . int_number K_NOTCHLENGTH int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1775


State 1696

  265 current_density_pwl: '(' int_number int_number ')' .

    $default  reduce using rule 265 (current_density_pwl)


State 1697

  246 layer_frequency: K_FREQUENCY NUMBER $@39 number_list . ';' $@40 ac_layer_table_opt K_TABLEENTRIES NUMBER $@41 number_list ';'
  257 number_list: number_list . NUMBER

    NUMBER  shift, and go to state 1700
    ';'     shift, and go to state 1776


State 1698

  129 layer_option: K_DCCURRENTDENSITY K_AVERAGE K_WIDTH int_number $@12 int_number_list ';' . $@13 dc_layer_table

    $default  reduce using rule 128 ($@13)

    $@13  go to state 1777


State 1699

  255 int_number_list: int_number_list int_number .

    $default  reduce using rule 255 (int_number_list)


State 1700

  257 number_list: number_list NUMBER .

    $default  reduce using rule 257 (number_list)


State 1701

  126 layer_option: K_DCCURRENTDENSITY K_AVERAGE K_CUTAREA NUMBER $@10 number_list ';' . $@11 dc_layer_table

    $default  reduce using rule 125 ($@11)

    $@11  go to state 1778


State 1702

  234 layer_antenna_pwl: K_PWL '(' pt pt $@38 . layer_diffusion_ratios ')'

    $default  reduce using rule 235 (layer_diffusion_ratios)

    layer_diffusion_ratios  go to state 1779


State 1703

  218 layer_minimumcut_within: K_WITHIN int_number .

    $default  reduce using rule 218 (layer_minimumcut_within)


State 1704

  220 layer_minimumcut_from: K_FROMABOVE .

    $default  reduce using rule 220 (layer_minimumcut_from)


State 1705

  221 layer_minimumcut_from: K_FROMBELOW .

    $default  reduce using rule 221 (layer_minimumcut_from)


State 1706

  172 layer_option: K_MINIMUMCUT int_number K_WIDTH int_number $@24 layer_minimumcut_within layer_minimumcut_from . layer_minimumcut_length ';'

    K_LENGTH  shift, and go to state 1780

    $default  reduce using rule 222 (layer_minimumcut_length)

    layer_minimumcut_length  go to state 1781


State 1707

  211 layer_enclosure_width_opt: K_WIDTH int_number . $@37 layer_enclosure_width_except_opt

    $default  reduce using rule 210 ($@37)

    $@37  go to state 1782


State 1708

  212 layer_enclosure_width_opt: K_LENGTH int_number .

    $default  reduce using rule 212 (layer_enclosure_width_opt)


State 1709

  179 layer_option: K_ENCLOSURE layer_enclosure_type_opt int_number int_number $@27 layer_enclosure_width_opt ';' .

    $default  reduce using rule 179 (layer_option)


State 1710

  175 layer_option: K_PROTRUSIONWIDTH int_number K_LENGTH int_number K_WIDTH int_number ';' .

    $default  reduce using rule 175 (layer_option)


State 1711

  102 layer_option: K_SPACINGTABLE K_ORTHOGONAL K_WITHIN int_number K_SPACING int_number $@7 . layer_spacingtable_opts ';'

    K_WITHIN  shift, and go to state 1783

    $default  reduce using rule 203 (layer_spacingtable_opts)

    layer_spacingtable_opts  go to state 1784
    layer_spacingtable_opt   go to state 1785


State 1712

  202 sp_options: K_INFLUENCE K_WIDTH int_number K_WITHIN int_number . K_SPACING int_number $@36 layer_sp_influence_widths

    K_SPACING  shift, and go to state 1786


State 1713

  197 sp_options: K_PARALLELRUNLENGTH int_number $@30 int_number_list $@31 . K_WIDTH int_number $@32 int_number_list $@33 layer_sp_parallel_widths

    K_WIDTH  shift, and go to state 1787


State 1714

  297 layer_sp_TwoWidthsPRL: K_PRL int_number .

    $default  reduce using rule 297 (layer_sp_TwoWidthsPRL)


State 1715

  200 sp_options: K_TWOWIDTHS K_WIDTH int_number layer_sp_TwoWidthsPRL int_number . $@34 int_number_list $@35 layer_sp_TwoWidths

    $default  reduce using rule 198 ($@34)

    $@34  go to state 1788


State 1716

  216 layer_preferenclosure_width_opt: K_WIDTH int_number .

    $default  reduce using rule 216 (layer_preferenclosure_width_opt)


State 1717

  181 layer_option: K_PREFERENCLOSURE layer_enclosure_type_opt int_number int_number $@28 layer_preferenclosure_width_opt ';' .

    $default  reduce using rule 181 (layer_option)


State 1718

   86 layer_option: K_ARRAYSPACING $@4 layer_arraySpacing_long layer_arraySpacing_width K_CUTSPACING int_number $@5 . layer_arraySpacing_arraycuts ';'

    K_ARRAYCUTS  shift, and go to state 1789

    $default  reduce using rule 190 (layer_arraySpacing_arraycuts)

    layer_arraySpacing_arraycuts  go to state 1790
    layer_arraySpacing_arraycut   go to state 1791


State 1719

  153 layer_option: K_ANTENNAAREADIFFREDUCEPWL '(' pt pt $@21 layer_diffusion_ratios ')' . ';' $@22

    ';'  shift, and go to state 1792


State 1720

  236 layer_diffusion_ratios: layer_diffusion_ratios layer_diffusion_ratio .

    $default  reduce using rule 236 (layer_diffusion_ratios)


State 1721

  237 layer_diffusion_ratio: pt .

    $default  reduce using rule 237 (layer_diffusion_ratio)


State 1722

  313 via_viarule: K_VIARULE $@49 T_STRING ';' K_CUTSIZE int_number int_number . ';' K_LAYERS $@50 T_STRING T_STRING T_STRING ';' K_CUTSPACING int_number int_number ';' K_ENCLOSURE int_number int_number int_number int_number ';' $@51 via_viarule_options

    ';'  shift, and go to state 1793


State 1723

  366 via_geometry: K_POLYGON maskColor $@56 firstPt nextPt . nextPt otherPts ';'

    NUMBER  shift, and go to state 99
    '('     shift, and go to state 514

    int_number  go to state 515
    pt          go to state 1374
    nextPt      go to state 1794


State 1724

  364 via_geometry: K_RECT maskColor pt pt ';' .

    $default  reduce using rule 364 (via_geometry)


State 1725

  751 density_layer_rect: K_RECT . pt pt int_number ';'

    NUMBER  shift, and go to state 99
    '('     shift, and go to state 514

    int_number  go to state 515
    pt          go to state 1795


State 1726

  748 density_layer: K_LAYER $@101 T_STRING ';' $@102 density_layer_rect . density_layer_rects

    $default  reduce using rule 749 (density_layer_rects)

    density_layer_rects  go to state 1796


State 1727

  725 sitePattern: T_STRING int_number int_number orientation K_DO int_number . K_BY int_number K_STEP int_number int_number

    K_BY  shift, and go to state 1797


State 1728

  629 macro_pin_option: K_INPUTNOISEMARGIN $@87 int_number int_number ';' .

    $default  reduce using rule 629 (macro_pin_option)


State 1729

  625 macro_pin_option: K_OUTPUTNOISEMARGIN $@85 int_number int_number ';' .

    $default  reduce using rule 625 (macro_pin_option)


State 1730

  627 macro_pin_option: K_OUTPUTRESISTANCE $@86 int_number int_number ';' .

    $default  reduce using rule 627 (macro_pin_option)


State 1731

  970 opt_layer_name: K_LAYER $@138 T_STRING .

    $default  reduce using rule 970 (opt_layer_name)


State 1732

  972 req_layer_name: K_LAYER $@139 T_STRING .

    $default  reduce using rule 972 (req_layer_name)


State 1733

  606 macro_pin_option: start_foreign K_STRUCTURE pt orientation ';' .

    $default  reduce using rule 606 (macro_pin_option)


State 1734

  715 layer_spacing: K_DESIGNRULEWIDTH . int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1798


State 1735

  714 layer_spacing: K_SPACING . int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1799


State 1736

  700 geometry: K_LAYER $@93 T_STRING $@94 layer_exceptpgnet layer_spacing . ';'

    ';'  shift, and go to state 1800


State 1737

  724 stepPattern: K_DO . int_number K_BY int_number K_STEP int_number int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1801


State 1738

  703 geometry: K_PATH maskColor K_ITERATE firstPt otherPts stepPattern . ';'

    ';'  shift, and go to state 1802


State 1739

  707 geometry: K_POLYGON maskColor K_ITERATE firstPt nextPt nextPt . otherPts stepPattern ';'

    $default  reduce using rule 718 (otherPts)

    otherPts  go to state 1803


State 1740

  706 geometry: K_POLYGON maskColor firstPt nextPt nextPt otherPts . ';'
  719 otherPts: otherPts . nextPt

    NUMBER  shift, and go to state 99
    ';'     shift, and go to state 1804
    '('     shift, and go to state 514

    int_number  go to state 515
    pt          go to state 1374
    nextPt      go to state 1375


State 1741

  705 geometry: K_RECT maskColor K_ITERATE pt pt stepPattern . ';'

    ';'  shift, and go to state 1805


State 1742

  723 via_placement: K_VIA K_ITERATE maskColor pt $@96 T_STRING . stepPattern ';'

    K_DO  shift, and go to state 1737

    stepPattern  go to state 1806


State 1743

  721 via_placement: K_VIA maskColor pt $@95 T_STRING ';' .

    $default  reduce using rule 721 (via_placement)


State 1744

  777 timing_option: K_STABLE K_SETUP int_number K_HOLD int_number . risefall ';'

    K_FALL  shift, and go to state 917
    K_RISE  shift, and go to state 923

    risefall  go to state 1807


State 1745

  801 table_entry: '(' int_number int_number int_number . ')'

    ')'  shift, and go to state 1808


State 1746

  779 timing_option: one_pin_trigger K_TABLEDIMENSION int_number int_number int_number . ';'

    ';'  shift, and go to state 1809


State 1747

  778 timing_option: two_pin_trigger from_pin_trigger to_pin_trigger K_TABLEDIMENSION int_number . int_number int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1810


State 1748

  764 timing_option: risefall K_INTRINSIC int_number int_number $@106 . slew_spec K_VARIABLE int_number int_number ';'

    NUMBER  shift, and go to state 99

    $default  reduce using rule 804 (slew_spec)

    int_number  go to state 1811
    slew_spec   go to state 1812


State 1749

  765 timing_option: risefall delay_or_transition K_UNATENESS unateness K_TABLEDIMENSION . int_number int_number int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1813


State 1750

  729 trackPattern: K_X int_number K_DO int_number K_STEP . int_number $@97 K_LAYER $@98 trackLayers
  733             | K_X int_number K_DO int_number K_STEP . int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1814


State 1751

  732 trackPattern: K_Y int_number K_DO int_number K_STEP . int_number $@99 K_LAYER $@100 trackLayers
  734             | K_Y int_number K_DO int_number K_STEP . int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1815


State 1752

  738 gcellPattern: K_X int_number K_DO int_number K_STEP . int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1816


State 1753

  739 gcellPattern: K_Y int_number K_DO int_number K_STEP . int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1817


State 1754

  846 one_cap: K_MINPINS int_number K_WIRECAP int_number . ';'

    ';'  shift, and go to state 1818


State 1755

  461 nd_layer: K_LAYER $@67 T_STRING $@68 . K_WIDTH int_number ';' $@69 nd_layer_stmts K_END $@70 T_STRING

    K_WIDTH  shift, and go to state 1819


State 1756

  451 nd_prop: K_PROPERTY $@66 nd_prop_list ';' .

    $default  reduce using rule 451 (nd_prop)


State 1757

  453 nd_prop_list: nd_prop_list nd_prop .

    $default  reduce using rule 453 (nd_prop_list)


State 1758

  449 mincuts: K_MINCUTS T_STRING int_number ';' .

    $default  reduce using rule 449 (mincuts)


State 1759

  863 else: '\n' K_ELSE .

    $default  reduce using rule 863 (else)


State 1760

  864 expression: expression . '+' expression
  865           | expression . '-' expression
  866           | expression . '*' expression
  867           | expression . '/' expression
  870           | K_IF b_expr then expression else expression .

    '-'  shift, and go to state 970
    '+'  shift, and go to state 971
    '*'  shift, and go to state 972
    '/'  shift, and go to state 973

    $default  reduce using rule 870 (expression)


State 1761

  887 s_expr: s_expr . '+' s_expr
  889       | K_IF b_expr then s_expr else s_expr .

    '+'  shift, and go to state 979

    $default  reduce using rule 889 (s_expr)


State 1762

  878 b_expr: b_expr . K_EQ b_expr
  879       | b_expr . K_NE b_expr
  880       | b_expr . K_AND b_expr
  881       | b_expr . K_OR b_expr
  884       | K_IF b_expr then b_expr else b_expr .

    K_EQ   shift, and go to state 998
    K_NE   shift, and go to state 999
    K_OR   shift, and go to state 1000
    K_AND  shift, and go to state 1001

    $default  reduce using rule 884 (b_expr)


State 1763

  938 opt_def_range: K_RANGE int_number int_number .

    $default  reduce using rule 938 (opt_def_range)


State 1764

  1008 corr_victim: K_VICTIMLENGTH int_number . ';' $@145 K_CORRECTIONFACTOR corr_list ';'

    ';'  shift, and go to state 1820


State 1765

  1006 corr_victim_list: corr_victim_list corr_victim .

    $default  reduce using rule 1006 (corr_victim_list)


State 1766

  991 victim: K_VICTIMLENGTH int_number . ';' $@142 K_VICTIMNOISE vnoiselist ';'

    ';'  shift, and go to state 1821


State 1767

  989 victim_list: victim_list victim .

    $default  reduce using rule 989 (victim_list)


State 1768

  268 cap_point: '(' int_number int_number ')' .

    $default  reduce using rule 268 (cap_point)


State 1769

  271 res_point: '(' int_number int_number ')' .

    $default  reduce using rule 271 (res_point)


State 1770

  952 layer_spacing_cut_routing: K_LAYER $@133 T_STRING $@134 . spacing_cut_layer_opt

    K_STACK  shift, and go to state 1822

    $default  reduce using rule 964 (spacing_cut_layer_opt)

    spacing_cut_layer_opt  go to state 1823


State 1771

  957 layer_spacing_cut_routing: K_RANGE int_number int_number $@136 . opt_range_second

    K_INFLUENCE           shift, and go to state 1824
    K_RANGE               shift, and go to state 1825
    K_USELENGTHTHRESHOLD  shift, and go to state 1826

    $default  reduce using rule 925 (opt_range_second)

    opt_range_second  go to state 1827


State 1772

  959 layer_spacing_cut_routing: K_LENGTHTHRESHOLD int_number K_RANGE int_number . int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1828


State 1773

  954 layer_spacing_cut_routing: K_ADJACENTCUTS int_number K_WITHIN int_number . $@135 opt_adjacentcuts_exceptsame

    $default  reduce using rule 953 ($@135)

    $@135  go to state 1829


State 1774

  961 layer_spacing_cut_routing: K_ENDOFLINE int_number K_WITHIN int_number . $@137 opt_endofline

    $default  reduce using rule 960 ($@137)

    $@137  go to state 1830


State 1775

  963 layer_spacing_cut_routing: K_ENDOFNOTCHWIDTH int_number K_NOTCHSPACING int_number . K_NOTCHLENGTH int_number

    K_NOTCHLENGTH  shift, and go to state 1831


State 1776

  246 layer_frequency: K_FREQUENCY NUMBER $@39 number_list ';' . $@40 ac_layer_table_opt K_TABLEENTRIES NUMBER $@41 number_list ';'

    $default  reduce using rule 244 ($@40)

    $@40  go to state 1832


State 1777

  129 layer_option: K_DCCURRENTDENSITY K_AVERAGE K_WIDTH int_number $@12 int_number_list ';' $@13 . dc_layer_table

    K_TABLEENTRIES  shift, and go to state 1833

    dc_layer_table  go to state 1834


State 1778

  126 layer_option: K_DCCURRENTDENSITY K_AVERAGE K_CUTAREA NUMBER $@10 number_list ';' $@11 . dc_layer_table

    K_TABLEENTRIES  shift, and go to state 1833

    dc_layer_table  go to state 1835


State 1779

  234 layer_antenna_pwl: K_PWL '(' pt pt $@38 layer_diffusion_ratios . ')'
  236 layer_diffusion_ratios: layer_diffusion_ratios . layer_diffusion_ratio

    NUMBER  shift, and go to state 99
    '('     shift, and go to state 514
    ')'     shift, and go to state 1836

    int_number             go to state 515
    layer_diffusion_ratio  go to state 1720
    pt                     go to state 1721


State 1780

  223 layer_minimumcut_length: K_LENGTH . int_number K_WITHIN int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1837


State 1781

  172 layer_option: K_MINIMUMCUT int_number K_WIDTH int_number $@24 layer_minimumcut_within layer_minimumcut_from layer_minimumcut_length . ';'

    ';'  shift, and go to state 1838


State 1782

  211 layer_enclosure_width_opt: K_WIDTH int_number $@37 . layer_enclosure_width_except_opt

    K_EXCEPTEXTRACUT  shift, and go to state 1839

    $default  reduce using rule 213 (layer_enclosure_width_except_opt)

    layer_enclosure_width_except_opt  go to state 1840


State 1783

  205 layer_spacingtable_opt: K_WITHIN . int_number K_SPACING int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1841


State 1784

  102 layer_option: K_SPACINGTABLE K_ORTHOGONAL K_WITHIN int_number K_SPACING int_number $@7 layer_spacingtable_opts . ';'

    ';'  shift, and go to state 1842


State 1785

  204 layer_spacingtable_opts: layer_spacingtable_opt . layer_spacingtable_opts

    K_WITHIN  shift, and go to state 1783

    $default  reduce using rule 203 (layer_spacingtable_opts)

    layer_spacingtable_opts  go to state 1843
    layer_spacingtable_opt   go to state 1785


State 1786

  202 sp_options: K_INFLUENCE K_WIDTH int_number K_WITHIN int_number K_SPACING . int_number $@36 layer_sp_influence_widths

    NUMBER  shift, and go to state 99

    int_number  go to state 1844


State 1787

  197 sp_options: K_PARALLELRUNLENGTH int_number $@30 int_number_list $@31 K_WIDTH . int_number $@32 int_number_list $@33 layer_sp_parallel_widths

    NUMBER  shift, and go to state 99

    int_number  go to state 1845


State 1788

  200 sp_options: K_TWOWIDTHS K_WIDTH int_number layer_sp_TwoWidthsPRL int_number $@34 . int_number_list $@35 layer_sp_TwoWidths

    $default  reduce using rule 254 (int_number_list)

    int_number_list  go to state 1846


State 1789

  192 layer_arraySpacing_arraycut: K_ARRAYCUTS . int_number K_SPACING int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1847


State 1790

   86 layer_option: K_ARRAYSPACING $@4 layer_arraySpacing_long layer_arraySpacing_width K_CUTSPACING int_number $@5 layer_arraySpacing_arraycuts . ';'

    ';'  shift, and go to state 1848


State 1791

  191 layer_arraySpacing_arraycuts: layer_arraySpacing_arraycut . layer_arraySpacing_arraycuts

    K_ARRAYCUTS  shift, and go to state 1789

    $default  reduce using rule 190 (layer_arraySpacing_arraycuts)

    layer_arraySpacing_arraycuts  go to state 1849
    layer_arraySpacing_arraycut   go to state 1791


State 1792

  153 layer_option: K_ANTENNAAREADIFFREDUCEPWL '(' pt pt $@21 layer_diffusion_ratios ')' ';' . $@22

    $default  reduce using rule 152 ($@22)

    $@22  go to state 1850


State 1793

  313 via_viarule: K_VIARULE $@49 T_STRING ';' K_CUTSIZE int_number int_number ';' . K_LAYERS $@50 T_STRING T_STRING T_STRING ';' K_CUTSPACING int_number int_number ';' K_ENCLOSURE int_number int_number int_number int_number ';' $@51 via_viarule_options

    K_LAYERS  shift, and go to state 1851


State 1794

  366 via_geometry: K_POLYGON maskColor $@56 firstPt nextPt nextPt . otherPts ';'

    $default  reduce using rule 718 (otherPts)

    otherPts  go to state 1852


State 1795

  751 density_layer_rect: K_RECT pt . pt int_number ';'

    NUMBER  shift, and go to state 99
    '('     shift, and go to state 514

    int_number  go to state 515
    pt          go to state 1853


State 1796

  748 density_layer: K_LAYER $@101 T_STRING ';' $@102 density_layer_rect density_layer_rects .
  750 density_layer_rects: density_layer_rects . density_layer_rect

    K_RECT  shift, and go to state 1725

    $default  reduce using rule 748 (density_layer)

    density_layer_rect  go to state 1854


State 1797

  725 sitePattern: T_STRING int_number int_number orientation K_DO int_number K_BY . int_number K_STEP int_number int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1855


State 1798

  715 layer_spacing: K_DESIGNRULEWIDTH int_number .

    $default  reduce using rule 715 (layer_spacing)


State 1799

  714 layer_spacing: K_SPACING int_number .

    $default  reduce using rule 714 (layer_spacing)


State 1800

  700 geometry: K_LAYER $@93 T_STRING $@94 layer_exceptpgnet layer_spacing ';' .

    $default  reduce using rule 700 (geometry)


State 1801

  724 stepPattern: K_DO int_number . K_BY int_number K_STEP int_number int_number

    K_BY  shift, and go to state 1856


State 1802

  703 geometry: K_PATH maskColor K_ITERATE firstPt otherPts stepPattern ';' .

    $default  reduce using rule 703 (geometry)


State 1803

  707 geometry: K_POLYGON maskColor K_ITERATE firstPt nextPt nextPt otherPts . stepPattern ';'
  719 otherPts: otherPts . nextPt

    K_DO    shift, and go to state 1737
    NUMBER  shift, and go to state 99
    '('     shift, and go to state 514

    int_number   go to state 515
    pt           go to state 1374
    nextPt       go to state 1375
    stepPattern  go to state 1857


State 1804

  706 geometry: K_POLYGON maskColor firstPt nextPt nextPt otherPts ';' .

    $default  reduce using rule 706 (geometry)


State 1805

  705 geometry: K_RECT maskColor K_ITERATE pt pt stepPattern ';' .

    $default  reduce using rule 705 (geometry)


State 1806

  723 via_placement: K_VIA K_ITERATE maskColor pt $@96 T_STRING stepPattern . ';'

    ';'  shift, and go to state 1858


State 1807

  777 timing_option: K_STABLE K_SETUP int_number K_HOLD int_number risefall . ';'

    ';'  shift, and go to state 1859


State 1808

  801 table_entry: '(' int_number int_number int_number ')' .

    $default  reduce using rule 801 (table_entry)


State 1809

  779 timing_option: one_pin_trigger K_TABLEDIMENSION int_number int_number int_number ';' .

    $default  reduce using rule 779 (timing_option)


State 1810

  778 timing_option: two_pin_trigger from_pin_trigger to_pin_trigger K_TABLEDIMENSION int_number int_number . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1860


State 1811

  805 slew_spec: int_number . int_number int_number int_number
  806          | int_number . int_number int_number int_number int_number int_number int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1861


State 1812

  764 timing_option: risefall K_INTRINSIC int_number int_number $@106 slew_spec . K_VARIABLE int_number int_number ';'

    K_VARIABLE  shift, and go to state 1862


State 1813

  765 timing_option: risefall delay_or_transition K_UNATENESS unateness K_TABLEDIMENSION int_number . int_number int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1863


State 1814

  729 trackPattern: K_X int_number K_DO int_number K_STEP int_number . $@97 K_LAYER $@98 trackLayers
  733             | K_X int_number K_DO int_number K_STEP int_number .

    ';'       reduce using rule 733 (trackPattern)
    $default  reduce using rule 727 ($@97)

    $@97  go to state 1864


State 1815

  732 trackPattern: K_Y int_number K_DO int_number K_STEP int_number . $@99 K_LAYER $@100 trackLayers
  734             | K_Y int_number K_DO int_number K_STEP int_number .

    ';'       reduce using rule 734 (trackPattern)
    $default  reduce using rule 730 ($@99)

    $@99  go to state 1865


State 1816

  738 gcellPattern: K_X int_number K_DO int_number K_STEP int_number .

    $default  reduce using rule 738 (gcellPattern)


State 1817

  739 gcellPattern: K_Y int_number K_DO int_number K_STEP int_number .

    $default  reduce using rule 739 (gcellPattern)


State 1818

  846 one_cap: K_MINPINS int_number K_WIRECAP int_number ';' .

    $default  reduce using rule 846 (one_cap)


State 1819

  461 nd_layer: K_LAYER $@67 T_STRING $@68 K_WIDTH . int_number ';' $@69 nd_layer_stmts K_END $@70 T_STRING

    NUMBER  shift, and go to state 99

    int_number  go to state 1866


State 1820

  1008 corr_victim: K_VICTIMLENGTH int_number ';' . $@145 K_CORRECTIONFACTOR corr_list ';'

    $default  reduce using rule 1007 ($@145)

    $@145  go to state 1867


State 1821

  991 victim: K_VICTIMLENGTH int_number ';' . $@142 K_VICTIMNOISE vnoiselist ';'

    $default  reduce using rule 990 ($@142)

    $@142  go to state 1868


State 1822

  965 spacing_cut_layer_opt: K_STACK .

    $default  reduce using rule 965 (spacing_cut_layer_opt)


State 1823

  952 layer_spacing_cut_routing: K_LAYER $@133 T_STRING $@134 spacing_cut_layer_opt .

    $default  reduce using rule 952 (layer_spacing_cut_routing)


State 1824

  927 opt_range_second: K_INFLUENCE . int_number
  928                 | K_INFLUENCE . int_number K_RANGE int_number int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1869


State 1825

  929 opt_range_second: K_RANGE . int_number int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1870


State 1826

  926 opt_range_second: K_USELENGTHTHRESHOLD .

    $default  reduce using rule 926 (opt_range_second)


State 1827

  957 layer_spacing_cut_routing: K_RANGE int_number int_number $@136 opt_range_second .

    $default  reduce using rule 957 (layer_spacing_cut_routing)


State 1828

  959 layer_spacing_cut_routing: K_LENGTHTHRESHOLD int_number K_RANGE int_number int_number .

    $default  reduce using rule 959 (layer_spacing_cut_routing)


State 1829

  954 layer_spacing_cut_routing: K_ADJACENTCUTS int_number K_WITHIN int_number $@135 . opt_adjacentcuts_exceptsame

    K_EXCEPTSAMEPGNET  shift, and go to state 1871

    $default  reduce using rule 966 (opt_adjacentcuts_exceptsame)

    opt_adjacentcuts_exceptsame  go to state 1872


State 1830

  961 layer_spacing_cut_routing: K_ENDOFLINE int_number K_WITHIN int_number $@137 . opt_endofline

    K_PARALLELEDGE  shift, and go to state 1873

    $default  reduce using rule 930 (opt_endofline)

    opt_endofline  go to state 1874


State 1831

  963 layer_spacing_cut_routing: K_ENDOFNOTCHWIDTH int_number K_NOTCHSPACING int_number K_NOTCHLENGTH . int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1875


State 1832

  246 layer_frequency: K_FREQUENCY NUMBER $@39 number_list ';' $@40 . ac_layer_table_opt K_TABLEENTRIES NUMBER $@41 number_list ';'

    K_WIDTH    shift, and go to state 1876
    K_CUTAREA  shift, and go to state 1877

    $default  reduce using rule 247 (ac_layer_table_opt)

    ac_layer_table_opt  go to state 1878


State 1833

  253 dc_layer_table: K_TABLEENTRIES . int_number $@44 int_number_list ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1879


State 1834

  129 layer_option: K_DCCURRENTDENSITY K_AVERAGE K_WIDTH int_number $@12 int_number_list ';' $@13 dc_layer_table .

    $default  reduce using rule 129 (layer_option)


State 1835

  126 layer_option: K_DCCURRENTDENSITY K_AVERAGE K_CUTAREA NUMBER $@10 number_list ';' $@11 dc_layer_table .

    $default  reduce using rule 126 (layer_option)


State 1836

  234 layer_antenna_pwl: K_PWL '(' pt pt $@38 layer_diffusion_ratios ')' .

    $default  reduce using rule 234 (layer_antenna_pwl)


State 1837

  223 layer_minimumcut_length: K_LENGTH int_number . K_WITHIN int_number

    K_WITHIN  shift, and go to state 1880


State 1838

  172 layer_option: K_MINIMUMCUT int_number K_WIDTH int_number $@24 layer_minimumcut_within layer_minimumcut_from layer_minimumcut_length ';' .

    $default  reduce using rule 172 (layer_option)


State 1839

  214 layer_enclosure_width_except_opt: K_EXCEPTEXTRACUT . int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1881


State 1840

  211 layer_enclosure_width_opt: K_WIDTH int_number $@37 layer_enclosure_width_except_opt .

    $default  reduce using rule 211 (layer_enclosure_width_opt)


State 1841

  205 layer_spacingtable_opt: K_WITHIN int_number . K_SPACING int_number

    K_SPACING  shift, and go to state 1882


State 1842

  102 layer_option: K_SPACINGTABLE K_ORTHOGONAL K_WITHIN int_number K_SPACING int_number $@7 layer_spacingtable_opts ';' .

    $default  reduce using rule 102 (layer_option)


State 1843

  204 layer_spacingtable_opts: layer_spacingtable_opt layer_spacingtable_opts .

    $default  reduce using rule 204 (layer_spacingtable_opts)


State 1844

  202 sp_options: K_INFLUENCE K_WIDTH int_number K_WITHIN int_number K_SPACING int_number . $@36 layer_sp_influence_widths

    $default  reduce using rule 201 ($@36)

    $@36  go to state 1883


State 1845

  197 sp_options: K_PARALLELRUNLENGTH int_number $@30 int_number_list $@31 K_WIDTH int_number . $@32 int_number_list $@33 layer_sp_parallel_widths

    $default  reduce using rule 195 ($@32)

    $@32  go to state 1884


State 1846

  200 sp_options: K_TWOWIDTHS K_WIDTH int_number layer_sp_TwoWidthsPRL int_number $@34 int_number_list . $@35 layer_sp_TwoWidths
  255 int_number_list: int_number_list . int_number

    NUMBER  shift, and go to state 99

    $default  reduce using rule 199 ($@35)

    int_number  go to state 1699
    $@35        go to state 1885


State 1847

  192 layer_arraySpacing_arraycut: K_ARRAYCUTS int_number . K_SPACING int_number

    K_SPACING  shift, and go to state 1886


State 1848

   86 layer_option: K_ARRAYSPACING $@4 layer_arraySpacing_long layer_arraySpacing_width K_CUTSPACING int_number $@5 layer_arraySpacing_arraycuts ';' .

    $default  reduce using rule 86 (layer_option)


State 1849

  191 layer_arraySpacing_arraycuts: layer_arraySpacing_arraycut layer_arraySpacing_arraycuts .

    $default  reduce using rule 191 (layer_arraySpacing_arraycuts)


State 1850

  153 layer_option: K_ANTENNAAREADIFFREDUCEPWL '(' pt pt $@21 layer_diffusion_ratios ')' ';' $@22 .

    $default  reduce using rule 153 (layer_option)


State 1851

  313 via_viarule: K_VIARULE $@49 T_STRING ';' K_CUTSIZE int_number int_number ';' K_LAYERS . $@50 T_STRING T_STRING T_STRING ';' K_CUTSPACING int_number int_number ';' K_ENCLOSURE int_number int_number int_number int_number ';' $@51 via_viarule_options

    $default  reduce using rule 311 ($@50)

    $@50  go to state 1887


State 1852

  366 via_geometry: K_POLYGON maskColor $@56 firstPt nextPt nextPt otherPts . ';'
  719 otherPts: otherPts . nextPt

    NUMBER  shift, and go to state 99
    ';'     shift, and go to state 1888
    '('     shift, and go to state 514

    int_number  go to state 515
    pt          go to state 1374
    nextPt      go to state 1375


State 1853

  751 density_layer_rect: K_RECT pt pt . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1889


State 1854

  750 density_layer_rects: density_layer_rects density_layer_rect .

    $default  reduce using rule 750 (density_layer_rects)


State 1855

  725 sitePattern: T_STRING int_number int_number orientation K_DO int_number K_BY int_number . K_STEP int_number int_number

    K_STEP  shift, and go to state 1890


State 1856

  724 stepPattern: K_DO int_number K_BY . int_number K_STEP int_number int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1891


State 1857

  707 geometry: K_POLYGON maskColor K_ITERATE firstPt nextPt nextPt otherPts stepPattern . ';'

    ';'  shift, and go to state 1892


State 1858

  723 via_placement: K_VIA K_ITERATE maskColor pt $@96 T_STRING stepPattern ';' .

    $default  reduce using rule 723 (via_placement)


State 1859

  777 timing_option: K_STABLE K_SETUP int_number K_HOLD int_number risefall ';' .

    $default  reduce using rule 777 (timing_option)


State 1860

  778 timing_option: two_pin_trigger from_pin_trigger to_pin_trigger K_TABLEDIMENSION int_number int_number int_number . ';'

    ';'  shift, and go to state 1893


State 1861

  805 slew_spec: int_number int_number . int_number int_number
  806          | int_number int_number . int_number int_number int_number int_number int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1894


State 1862

  764 timing_option: risefall K_INTRINSIC int_number int_number $@106 slew_spec K_VARIABLE . int_number int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1895


State 1863

  765 timing_option: risefall delay_or_transition K_UNATENESS unateness K_TABLEDIMENSION int_number int_number . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1896


State 1864

  729 trackPattern: K_X int_number K_DO int_number K_STEP int_number $@97 . K_LAYER $@98 trackLayers

    K_LAYER  shift, and go to state 1897


State 1865

  732 trackPattern: K_Y int_number K_DO int_number K_STEP int_number $@99 . K_LAYER $@100 trackLayers

    K_LAYER  shift, and go to state 1898


State 1866

  461 nd_layer: K_LAYER $@67 T_STRING $@68 K_WIDTH int_number . ';' $@69 nd_layer_stmts K_END $@70 T_STRING

    ';'  shift, and go to state 1899


State 1867

  1008 corr_victim: K_VICTIMLENGTH int_number ';' $@145 . K_CORRECTIONFACTOR corr_list ';'

    K_CORRECTIONFACTOR  shift, and go to state 1900


State 1868

  991 victim: K_VICTIMLENGTH int_number ';' $@142 . K_VICTIMNOISE vnoiselist ';'

    K_VICTIMNOISE  shift, and go to state 1901


State 1869

  927 opt_range_second: K_INFLUENCE int_number .
  928                 | K_INFLUENCE int_number . K_RANGE int_number int_number

    K_RANGE  shift, and go to state 1902

    $default  reduce using rule 927 (opt_range_second)


State 1870

  929 opt_range_second: K_RANGE int_number . int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1903


State 1871

  967 opt_adjacentcuts_exceptsame: K_EXCEPTSAMEPGNET .

    $default  reduce using rule 967 (opt_adjacentcuts_exceptsame)


State 1872

  954 layer_spacing_cut_routing: K_ADJACENTCUTS int_number K_WITHIN int_number $@135 opt_adjacentcuts_exceptsame .

    $default  reduce using rule 954 (layer_spacing_cut_routing)


State 1873

  932 opt_endofline: K_PARALLELEDGE . int_number K_WITHIN int_number $@131 opt_endofline_twoedges

    NUMBER  shift, and go to state 99

    int_number  go to state 1904


State 1874

  961 layer_spacing_cut_routing: K_ENDOFLINE int_number K_WITHIN int_number $@137 opt_endofline .

    $default  reduce using rule 961 (layer_spacing_cut_routing)


State 1875

  963 layer_spacing_cut_routing: K_ENDOFNOTCHWIDTH int_number K_NOTCHSPACING int_number K_NOTCHLENGTH int_number .

    $default  reduce using rule 963 (layer_spacing_cut_routing)


State 1876

  251 ac_layer_table_opt: K_WIDTH . int_number $@43 int_number_list ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1905


State 1877

  249 ac_layer_table_opt: K_CUTAREA . NUMBER $@42 number_list ';'

    NUMBER  shift, and go to state 1906


State 1878

  246 layer_frequency: K_FREQUENCY NUMBER $@39 number_list ';' $@40 ac_layer_table_opt . K_TABLEENTRIES NUMBER $@41 number_list ';'

    K_TABLEENTRIES  shift, and go to state 1907


State 1879

  253 dc_layer_table: K_TABLEENTRIES int_number . $@44 int_number_list ';'

    $default  reduce using rule 252 ($@44)

    $@44  go to state 1908


State 1880

  223 layer_minimumcut_length: K_LENGTH int_number K_WITHIN . int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1909


State 1881

  214 layer_enclosure_width_except_opt: K_EXCEPTEXTRACUT int_number .

    $default  reduce using rule 214 (layer_enclosure_width_except_opt)


State 1882

  205 layer_spacingtable_opt: K_WITHIN int_number K_SPACING . int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1910


State 1883

  202 sp_options: K_INFLUENCE K_WIDTH int_number K_WITHIN int_number K_SPACING int_number $@36 . layer_sp_influence_widths

    $default  reduce using rule 298 (layer_sp_influence_widths)

    layer_sp_influence_widths  go to state 1911


State 1884

  197 sp_options: K_PARALLELRUNLENGTH int_number $@30 int_number_list $@31 K_WIDTH int_number $@32 . int_number_list $@33 layer_sp_parallel_widths

    $default  reduce using rule 254 (int_number_list)

    int_number_list  go to state 1912


State 1885

  200 sp_options: K_TWOWIDTHS K_WIDTH int_number layer_sp_TwoWidthsPRL int_number $@34 int_number_list $@35 . layer_sp_TwoWidths

    K_WIDTH  shift, and go to state 1913

    $default  reduce using rule 292 (layer_sp_TwoWidths)

    layer_sp_TwoWidths  go to state 1914
    layer_sp_TwoWidth   go to state 1915


State 1886

  192 layer_arraySpacing_arraycut: K_ARRAYCUTS int_number K_SPACING . int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1916


State 1887

  313 via_viarule: K_VIARULE $@49 T_STRING ';' K_CUTSIZE int_number int_number ';' K_LAYERS $@50 . T_STRING T_STRING T_STRING ';' K_CUTSPACING int_number int_number ';' K_ENCLOSURE int_number int_number int_number int_number ';' $@51 via_viarule_options

    T_STRING  shift, and go to state 1917


State 1888

  366 via_geometry: K_POLYGON maskColor $@56 firstPt nextPt nextPt otherPts ';' .

    $default  reduce using rule 366 (via_geometry)


State 1889

  751 density_layer_rect: K_RECT pt pt int_number . ';'

    ';'  shift, and go to state 1918


State 1890

  725 sitePattern: T_STRING int_number int_number orientation K_DO int_number K_BY int_number K_STEP . int_number int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1919


State 1891

  724 stepPattern: K_DO int_number K_BY int_number . K_STEP int_number int_number

    K_STEP  shift, and go to state 1920


State 1892

  707 geometry: K_POLYGON maskColor K_ITERATE firstPt nextPt nextPt otherPts stepPattern ';' .

    $default  reduce using rule 707 (geometry)


State 1893

  778 timing_option: two_pin_trigger from_pin_trigger to_pin_trigger K_TABLEDIMENSION int_number int_number int_number ';' .

    $default  reduce using rule 778 (timing_option)


State 1894

  805 slew_spec: int_number int_number int_number . int_number
  806          | int_number int_number int_number . int_number int_number int_number int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1921


State 1895

  764 timing_option: risefall K_INTRINSIC int_number int_number $@106 slew_spec K_VARIABLE int_number . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1922


State 1896

  765 timing_option: risefall delay_or_transition K_UNATENESS unateness K_TABLEDIMENSION int_number int_number int_number . ';'

    ';'  shift, and go to state 1923


State 1897

  729 trackPattern: K_X int_number K_DO int_number K_STEP int_number $@97 K_LAYER . $@98 trackLayers

    $default  reduce using rule 728 ($@98)

    $@98  go to state 1924


State 1898

  732 trackPattern: K_Y int_number K_DO int_number K_STEP int_number $@99 K_LAYER . $@100 trackLayers

    $default  reduce using rule 731 ($@100)

    $@100  go to state 1925


State 1899

  461 nd_layer: K_LAYER $@67 T_STRING $@68 K_WIDTH int_number ';' . $@69 nd_layer_stmts K_END $@70 T_STRING

    $default  reduce using rule 459 ($@69)

    $@69  go to state 1926


State 1900

  1008 corr_victim: K_VICTIMLENGTH int_number ';' $@145 K_CORRECTIONFACTOR . corr_list ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1927
    corr_list   go to state 1928


State 1901

  991 victim: K_VICTIMLENGTH int_number ';' $@142 K_VICTIMNOISE . vnoiselist ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1929
    vnoiselist  go to state 1930


State 1902

  928 opt_range_second: K_INFLUENCE int_number K_RANGE . int_number int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1931


State 1903

  929 opt_range_second: K_RANGE int_number int_number .

    $default  reduce using rule 929 (opt_range_second)


State 1904

  932 opt_endofline: K_PARALLELEDGE int_number . K_WITHIN int_number $@131 opt_endofline_twoedges

    K_WITHIN  shift, and go to state 1932


State 1905

  251 ac_layer_table_opt: K_WIDTH int_number . $@43 int_number_list ';'

    $default  reduce using rule 250 ($@43)

    $@43  go to state 1933


State 1906

  249 ac_layer_table_opt: K_CUTAREA NUMBER . $@42 number_list ';'

    $default  reduce using rule 248 ($@42)

    $@42  go to state 1934


State 1907

  246 layer_frequency: K_FREQUENCY NUMBER $@39 number_list ';' $@40 ac_layer_table_opt K_TABLEENTRIES . NUMBER $@41 number_list ';'

    NUMBER  shift, and go to state 1935


State 1908

  253 dc_layer_table: K_TABLEENTRIES int_number $@44 . int_number_list ';'

    $default  reduce using rule 254 (int_number_list)

    int_number_list  go to state 1936


State 1909

  223 layer_minimumcut_length: K_LENGTH int_number K_WITHIN int_number .

    $default  reduce using rule 223 (layer_minimumcut_length)


State 1910

  205 layer_spacingtable_opt: K_WITHIN int_number K_SPACING int_number .

    $default  reduce using rule 205 (layer_spacingtable_opt)


State 1911

  202 sp_options: K_INFLUENCE K_WIDTH int_number K_WITHIN int_number K_SPACING int_number $@36 layer_sp_influence_widths .
  299 layer_sp_influence_widths: layer_sp_influence_widths . layer_sp_influence_width

    K_WIDTH  shift, and go to state 1937

    $default  reduce using rule 202 (sp_options)

    layer_sp_influence_width  go to state 1938


State 1912

  197 sp_options: K_PARALLELRUNLENGTH int_number $@30 int_number_list $@31 K_WIDTH int_number $@32 int_number_list . $@33 layer_sp_parallel_widths
  255 int_number_list: int_number_list . int_number

    NUMBER  shift, and go to state 99

    $default  reduce using rule 196 ($@33)

    int_number  go to state 1699
    $@33        go to state 1939


State 1913

  295 layer_sp_TwoWidth: K_WIDTH . int_number layer_sp_TwoWidthsPRL int_number $@46 int_number_list

    NUMBER  shift, and go to state 99

    int_number  go to state 1940


State 1914

  200 sp_options: K_TWOWIDTHS K_WIDTH int_number layer_sp_TwoWidthsPRL int_number $@34 int_number_list $@35 layer_sp_TwoWidths .

    $default  reduce using rule 200 (sp_options)


State 1915

  293 layer_sp_TwoWidths: layer_sp_TwoWidth . layer_sp_TwoWidths

    K_WIDTH  shift, and go to state 1913

    $default  reduce using rule 292 (layer_sp_TwoWidths)

    layer_sp_TwoWidths  go to state 1941
    layer_sp_TwoWidth   go to state 1915


State 1916

  192 layer_arraySpacing_arraycut: K_ARRAYCUTS int_number K_SPACING int_number .

    $default  reduce using rule 192 (layer_arraySpacing_arraycut)


State 1917

  313 via_viarule: K_VIARULE $@49 T_STRING ';' K_CUTSIZE int_number int_number ';' K_LAYERS $@50 T_STRING . T_STRING T_STRING ';' K_CUTSPACING int_number int_number ';' K_ENCLOSURE int_number int_number int_number int_number ';' $@51 via_viarule_options

    T_STRING  shift, and go to state 1942


State 1918

  751 density_layer_rect: K_RECT pt pt int_number ';' .

    $default  reduce using rule 751 (density_layer_rect)


State 1919

  725 sitePattern: T_STRING int_number int_number orientation K_DO int_number K_BY int_number K_STEP int_number . int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1943


State 1920

  724 stepPattern: K_DO int_number K_BY int_number K_STEP . int_number int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1944


State 1921

  805 slew_spec: int_number int_number int_number int_number .
  806          | int_number int_number int_number int_number . int_number int_number int_number

    NUMBER  shift, and go to state 99

    $default  reduce using rule 805 (slew_spec)

    int_number  go to state 1945


State 1922

  764 timing_option: risefall K_INTRINSIC int_number int_number $@106 slew_spec K_VARIABLE int_number int_number . ';'

    ';'  shift, and go to state 1946


State 1923

  765 timing_option: risefall delay_or_transition K_UNATENESS unateness K_TABLEDIMENSION int_number int_number int_number ';' .

    $default  reduce using rule 765 (timing_option)


State 1924

  729 trackPattern: K_X int_number K_DO int_number K_STEP int_number $@97 K_LAYER $@98 . trackLayers

    $default  reduce using rule 735 (trackLayers)

    trackLayers  go to state 1947


State 1925

  732 trackPattern: K_Y int_number K_DO int_number K_STEP int_number $@99 K_LAYER $@100 . trackLayers

    $default  reduce using rule 735 (trackLayers)

    trackLayers  go to state 1948


State 1926

  461 nd_layer: K_LAYER $@67 T_STRING $@68 K_WIDTH int_number ';' $@69 . nd_layer_stmts K_END $@70 T_STRING

    $default  reduce using rule 462 (nd_layer_stmts)

    nd_layer_stmts  go to state 1949


State 1927

  1009 corr_list: int_number .

    $default  reduce using rule 1009 (corr_list)


State 1928

  1008 corr_victim: K_VICTIMLENGTH int_number ';' $@145 K_CORRECTIONFACTOR corr_list . ';'
  1010 corr_list: corr_list . int_number

    NUMBER  shift, and go to state 99
    ';'     shift, and go to state 1950

    int_number  go to state 1951


State 1929

  992 vnoiselist: int_number .

    $default  reduce using rule 992 (vnoiselist)


State 1930

  991 victim: K_VICTIMLENGTH int_number ';' $@142 K_VICTIMNOISE vnoiselist . ';'
  993 vnoiselist: vnoiselist . int_number

    NUMBER  shift, and go to state 99
    ';'     shift, and go to state 1952

    int_number  go to state 1953


State 1931

  928 opt_range_second: K_INFLUENCE int_number K_RANGE int_number . int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1954


State 1932

  932 opt_endofline: K_PARALLELEDGE int_number K_WITHIN . int_number $@131 opt_endofline_twoedges

    NUMBER  shift, and go to state 99

    int_number  go to state 1955


State 1933

  251 ac_layer_table_opt: K_WIDTH int_number $@43 . int_number_list ';'

    $default  reduce using rule 254 (int_number_list)

    int_number_list  go to state 1956


State 1934

  249 ac_layer_table_opt: K_CUTAREA NUMBER $@42 . number_list ';'

    $default  reduce using rule 256 (number_list)

    number_list  go to state 1957


State 1935

  246 layer_frequency: K_FREQUENCY NUMBER $@39 number_list ';' $@40 ac_layer_table_opt K_TABLEENTRIES NUMBER . $@41 number_list ';'

    $default  reduce using rule 245 ($@41)

    $@41  go to state 1958


State 1936

  253 dc_layer_table: K_TABLEENTRIES int_number $@44 int_number_list . ';'
  255 int_number_list: int_number_list . int_number

    NUMBER  shift, and go to state 99
    ';'     shift, and go to state 1959

    int_number  go to state 1699


State 1937

  300 layer_sp_influence_width: K_WIDTH . int_number K_WITHIN int_number K_SPACING int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1960


State 1938

  299 layer_sp_influence_widths: layer_sp_influence_widths layer_sp_influence_width .

    $default  reduce using rule 299 (layer_sp_influence_widths)


State 1939

  197 sp_options: K_PARALLELRUNLENGTH int_number $@30 int_number_list $@31 K_WIDTH int_number $@32 int_number_list $@33 . layer_sp_parallel_widths

    $default  reduce using rule 288 (layer_sp_parallel_widths)

    layer_sp_parallel_widths  go to state 1961


State 1940

  295 layer_sp_TwoWidth: K_WIDTH int_number . layer_sp_TwoWidthsPRL int_number $@46 int_number_list

    K_PRL  shift, and go to state 1579

    $default  reduce using rule 296 (layer_sp_TwoWidthsPRL)

    layer_sp_TwoWidthsPRL  go to state 1962


State 1941

  293 layer_sp_TwoWidths: layer_sp_TwoWidth layer_sp_TwoWidths .

    $default  reduce using rule 293 (layer_sp_TwoWidths)


State 1942

  313 via_viarule: K_VIARULE $@49 T_STRING ';' K_CUTSIZE int_number int_number ';' K_LAYERS $@50 T_STRING T_STRING . T_STRING ';' K_CUTSPACING int_number int_number ';' K_ENCLOSURE int_number int_number int_number int_number ';' $@51 via_viarule_options

    T_STRING  shift, and go to state 1963


State 1943

  725 sitePattern: T_STRING int_number int_number orientation K_DO int_number K_BY int_number K_STEP int_number int_number .

    $default  reduce using rule 725 (sitePattern)


State 1944

  724 stepPattern: K_DO int_number K_BY int_number K_STEP int_number . int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1964


State 1945

  806 slew_spec: int_number int_number int_number int_number int_number . int_number int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1965


State 1946

  764 timing_option: risefall K_INTRINSIC int_number int_number $@106 slew_spec K_VARIABLE int_number int_number ';' .

    $default  reduce using rule 764 (timing_option)


State 1947

  729 trackPattern: K_X int_number K_DO int_number K_STEP int_number $@97 K_LAYER $@98 trackLayers .
  736 trackLayers: trackLayers . layer_name

    T_STRING  shift, and go to state 1966

    $default  reduce using rule 729 (trackPattern)

    layer_name  go to state 1967


State 1948

  732 trackPattern: K_Y int_number K_DO int_number K_STEP int_number $@99 K_LAYER $@100 trackLayers .
  736 trackLayers: trackLayers . layer_name

    T_STRING  shift, and go to state 1966

    $default  reduce using rule 732 (trackPattern)

    layer_name  go to state 1967


State 1949

  461 nd_layer: K_LAYER $@67 T_STRING $@68 K_WIDTH int_number ';' $@69 nd_layer_stmts . K_END $@70 T_STRING
  463 nd_layer_stmts: nd_layer_stmts . nd_layer_stmt

    K_CAPACITANCE      shift, and go to state 1968
    K_EDGECAPACITANCE  shift, and go to state 1969
    K_END              shift, and go to state 1970
    K_RESISTANCE       shift, and go to state 1971
    K_SPACING          shift, and go to state 1972
    K_WIREEXTENSION    shift, and go to state 1973
    K_DIAGWIDTH        shift, and go to state 1974

    nd_layer_stmt  go to state 1975


State 1950

  1008 corr_victim: K_VICTIMLENGTH int_number ';' $@145 K_CORRECTIONFACTOR corr_list ';' .

    $default  reduce using rule 1008 (corr_victim)


State 1951

  1010 corr_list: corr_list int_number .

    $default  reduce using rule 1010 (corr_list)


State 1952

  991 victim: K_VICTIMLENGTH int_number ';' $@142 K_VICTIMNOISE vnoiselist ';' .

    $default  reduce using rule 991 (victim)


State 1953

  993 vnoiselist: vnoiselist int_number .

    $default  reduce using rule 993 (vnoiselist)


State 1954

  928 opt_range_second: K_INFLUENCE int_number K_RANGE int_number int_number .

    $default  reduce using rule 928 (opt_range_second)


State 1955

  932 opt_endofline: K_PARALLELEDGE int_number K_WITHIN int_number . $@131 opt_endofline_twoedges

    $default  reduce using rule 931 ($@131)

    $@131  go to state 1976


State 1956

  251 ac_layer_table_opt: K_WIDTH int_number $@43 int_number_list . ';'
  255 int_number_list: int_number_list . int_number

    NUMBER  shift, and go to state 99
    ';'     shift, and go to state 1977

    int_number  go to state 1699


State 1957

  249 ac_layer_table_opt: K_CUTAREA NUMBER $@42 number_list . ';'
  257 number_list: number_list . NUMBER

    NUMBER  shift, and go to state 1700
    ';'     shift, and go to state 1978


State 1958

  246 layer_frequency: K_FREQUENCY NUMBER $@39 number_list ';' $@40 ac_layer_table_opt K_TABLEENTRIES NUMBER $@41 . number_list ';'

    $default  reduce using rule 256 (number_list)

    number_list  go to state 1979


State 1959

  253 dc_layer_table: K_TABLEENTRIES int_number $@44 int_number_list ';' .

    $default  reduce using rule 253 (dc_layer_table)


State 1960

  300 layer_sp_influence_width: K_WIDTH int_number . K_WITHIN int_number K_SPACING int_number

    K_WITHIN  shift, and go to state 1980


State 1961

  197 sp_options: K_PARALLELRUNLENGTH int_number $@30 int_number_list $@31 K_WIDTH int_number $@32 int_number_list $@33 layer_sp_parallel_widths .
  289 layer_sp_parallel_widths: layer_sp_parallel_widths . layer_sp_parallel_width

    K_WIDTH  shift, and go to state 1981

    $default  reduce using rule 197 (sp_options)

    layer_sp_parallel_width  go to state 1982


State 1962

  295 layer_sp_TwoWidth: K_WIDTH int_number layer_sp_TwoWidthsPRL . int_number $@46 int_number_list

    NUMBER  shift, and go to state 99

    int_number  go to state 1983


State 1963

  313 via_viarule: K_VIARULE $@49 T_STRING ';' K_CUTSIZE int_number int_number ';' K_LAYERS $@50 T_STRING T_STRING T_STRING . ';' K_CUTSPACING int_number int_number ';' K_ENCLOSURE int_number int_number int_number int_number ';' $@51 via_viarule_options

    ';'  shift, and go to state 1984


State 1964

  724 stepPattern: K_DO int_number K_BY int_number K_STEP int_number int_number .

    $default  reduce using rule 724 (stepPattern)


State 1965

  806 slew_spec: int_number int_number int_number int_number int_number int_number . int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1985


State 1966

  737 layer_name: T_STRING .

    $default  reduce using rule 737 (layer_name)


State 1967

  736 trackLayers: trackLayers layer_name .

    $default  reduce using rule 736 (trackLayers)


State 1968

  467 nd_layer_stmt: K_CAPACITANCE . K_CPERSQDIST int_number ';'

    K_CPERSQDIST  shift, and go to state 1986


State 1969

  468 nd_layer_stmt: K_EDGECAPACITANCE . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1987


State 1970

  461 nd_layer: K_LAYER $@67 T_STRING $@68 K_WIDTH int_number ';' $@69 nd_layer_stmts K_END . $@70 T_STRING

    $default  reduce using rule 460 ($@70)

    $@70  go to state 1988


State 1971

  466 nd_layer_stmt: K_RESISTANCE . K_RPERSQ int_number ';'

    K_RPERSQ  shift, and go to state 1989


State 1972

  464 nd_layer_stmt: K_SPACING . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1990


State 1973

  465 nd_layer_stmt: K_WIREEXTENSION . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1991


State 1974

  469 nd_layer_stmt: K_DIAGWIDTH . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 1992


State 1975

  463 nd_layer_stmts: nd_layer_stmts nd_layer_stmt .

    $default  reduce using rule 463 (nd_layer_stmts)


State 1976

  932 opt_endofline: K_PARALLELEDGE int_number K_WITHIN int_number $@131 . opt_endofline_twoedges

    K_TWOEDGES  shift, and go to state 1993

    $default  reduce using rule 933 (opt_endofline_twoedges)

    opt_endofline_twoedges  go to state 1994


State 1977

  251 ac_layer_table_opt: K_WIDTH int_number $@43 int_number_list ';' .

    $default  reduce using rule 251 (ac_layer_table_opt)


State 1978

  249 ac_layer_table_opt: K_CUTAREA NUMBER $@42 number_list ';' .

    $default  reduce using rule 249 (ac_layer_table_opt)


State 1979

  246 layer_frequency: K_FREQUENCY NUMBER $@39 number_list ';' $@40 ac_layer_table_opt K_TABLEENTRIES NUMBER $@41 number_list . ';'
  257 number_list: number_list . NUMBER

    NUMBER  shift, and go to state 1700
    ';'     shift, and go to state 1995


State 1980

  300 layer_sp_influence_width: K_WIDTH int_number K_WITHIN . int_number K_SPACING int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 1996


State 1981

  291 layer_sp_parallel_width: K_WIDTH . int_number $@45 int_number_list

    NUMBER  shift, and go to state 99

    int_number  go to state 1997


State 1982

  289 layer_sp_parallel_widths: layer_sp_parallel_widths layer_sp_parallel_width .

    $default  reduce using rule 289 (layer_sp_parallel_widths)


State 1983

  295 layer_sp_TwoWidth: K_WIDTH int_number layer_sp_TwoWidthsPRL int_number . $@46 int_number_list

    $default  reduce using rule 294 ($@46)

    $@46  go to state 1998


State 1984

  313 via_viarule: K_VIARULE $@49 T_STRING ';' K_CUTSIZE int_number int_number ';' K_LAYERS $@50 T_STRING T_STRING T_STRING ';' . K_CUTSPACING int_number int_number ';' K_ENCLOSURE int_number int_number int_number int_number ';' $@51 via_viarule_options

    K_CUTSPACING  shift, and go to state 1999


State 1985

  806 slew_spec: int_number int_number int_number int_number int_number int_number int_number .

    $default  reduce using rule 806 (slew_spec)


State 1986

  467 nd_layer_stmt: K_CAPACITANCE K_CPERSQDIST . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 2000


State 1987

  468 nd_layer_stmt: K_EDGECAPACITANCE int_number . ';'

    ';'  shift, and go to state 2001


State 1988

  461 nd_layer: K_LAYER $@67 T_STRING $@68 K_WIDTH int_number ';' $@69 nd_layer_stmts K_END $@70 . T_STRING

    T_STRING  shift, and go to state 2002


State 1989

  466 nd_layer_stmt: K_RESISTANCE K_RPERSQ . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 2003


State 1990

  464 nd_layer_stmt: K_SPACING int_number . ';'

    ';'  shift, and go to state 2004


State 1991

  465 nd_layer_stmt: K_WIREEXTENSION int_number . ';'

    ';'  shift, and go to state 2005


State 1992

  469 nd_layer_stmt: K_DIAGWIDTH int_number . ';'

    ';'  shift, and go to state 2006


State 1993

  934 opt_endofline_twoedges: K_TWOEDGES .

    $default  reduce using rule 934 (opt_endofline_twoedges)


State 1994

  932 opt_endofline: K_PARALLELEDGE int_number K_WITHIN int_number $@131 opt_endofline_twoedges .

    $default  reduce using rule 932 (opt_endofline)


State 1995

  246 layer_frequency: K_FREQUENCY NUMBER $@39 number_list ';' $@40 ac_layer_table_opt K_TABLEENTRIES NUMBER $@41 number_list ';' .

    $default  reduce using rule 246 (layer_frequency)


State 1996

  300 layer_sp_influence_width: K_WIDTH int_number K_WITHIN int_number . K_SPACING int_number

    K_SPACING  shift, and go to state 2007


State 1997

  291 layer_sp_parallel_width: K_WIDTH int_number . $@45 int_number_list

    $default  reduce using rule 290 ($@45)

    $@45  go to state 2008


State 1998

  295 layer_sp_TwoWidth: K_WIDTH int_number layer_sp_TwoWidthsPRL int_number $@46 . int_number_list

    $default  reduce using rule 254 (int_number_list)

    int_number_list  go to state 2009


State 1999

  313 via_viarule: K_VIARULE $@49 T_STRING ';' K_CUTSIZE int_number int_number ';' K_LAYERS $@50 T_STRING T_STRING T_STRING ';' K_CUTSPACING . int_number int_number ';' K_ENCLOSURE int_number int_number int_number int_number ';' $@51 via_viarule_options

    NUMBER  shift, and go to state 99

    int_number  go to state 2010


State 2000

  467 nd_layer_stmt: K_CAPACITANCE K_CPERSQDIST int_number . ';'

    ';'  shift, and go to state 2011


State 2001

  468 nd_layer_stmt: K_EDGECAPACITANCE int_number ';' .

    $default  reduce using rule 468 (nd_layer_stmt)


State 2002

  461 nd_layer: K_LAYER $@67 T_STRING $@68 K_WIDTH int_number ';' $@69 nd_layer_stmts K_END $@70 T_STRING .

    $default  reduce using rule 461 (nd_layer)


State 2003

  466 nd_layer_stmt: K_RESISTANCE K_RPERSQ int_number . ';'

    ';'  shift, and go to state 2012


State 2004

  464 nd_layer_stmt: K_SPACING int_number ';' .

    $default  reduce using rule 464 (nd_layer_stmt)


State 2005

  465 nd_layer_stmt: K_WIREEXTENSION int_number ';' .

    $default  reduce using rule 465 (nd_layer_stmt)


State 2006

  469 nd_layer_stmt: K_DIAGWIDTH int_number ';' .

    $default  reduce using rule 469 (nd_layer_stmt)


State 2007

  300 layer_sp_influence_width: K_WIDTH int_number K_WITHIN int_number K_SPACING . int_number

    NUMBER  shift, and go to state 99

    int_number  go to state 2013


State 2008

  291 layer_sp_parallel_width: K_WIDTH int_number $@45 . int_number_list

    $default  reduce using rule 254 (int_number_list)

    int_number_list  go to state 2014


State 2009

  255 int_number_list: int_number_list . int_number
  295 layer_sp_TwoWidth: K_WIDTH int_number layer_sp_TwoWidthsPRL int_number $@46 int_number_list .

    NUMBER  shift, and go to state 99

    $default  reduce using rule 295 (layer_sp_TwoWidth)

    int_number  go to state 1699


State 2010

  313 via_viarule: K_VIARULE $@49 T_STRING ';' K_CUTSIZE int_number int_number ';' K_LAYERS $@50 T_STRING T_STRING T_STRING ';' K_CUTSPACING int_number . int_number ';' K_ENCLOSURE int_number int_number int_number int_number ';' $@51 via_viarule_options

    NUMBER  shift, and go to state 99

    int_number  go to state 2015


State 2011

  467 nd_layer_stmt: K_CAPACITANCE K_CPERSQDIST int_number ';' .

    $default  reduce using rule 467 (nd_layer_stmt)


State 2012

  466 nd_layer_stmt: K_RESISTANCE K_RPERSQ int_number ';' .

    $default  reduce using rule 466 (nd_layer_stmt)


State 2013

  300 layer_sp_influence_width: K_WIDTH int_number K_WITHIN int_number K_SPACING int_number .

    $default  reduce using rule 300 (layer_sp_influence_width)


State 2014

  255 int_number_list: int_number_list . int_number
  291 layer_sp_parallel_width: K_WIDTH int_number $@45 int_number_list .

    NUMBER  shift, and go to state 99

    $default  reduce using rule 291 (layer_sp_parallel_width)

    int_number  go to state 1699


State 2015

  313 via_viarule: K_VIARULE $@49 T_STRING ';' K_CUTSIZE int_number int_number ';' K_LAYERS $@50 T_STRING T_STRING T_STRING ';' K_CUTSPACING int_number int_number . ';' K_ENCLOSURE int_number int_number int_number int_number ';' $@51 via_viarule_options

    ';'  shift, and go to state 2016


State 2016

  313 via_viarule: K_VIARULE $@49 T_STRING ';' K_CUTSIZE int_number int_number ';' K_LAYERS $@50 T_STRING T_STRING T_STRING ';' K_CUTSPACING int_number int_number ';' . K_ENCLOSURE int_number int_number int_number int_number ';' $@51 via_viarule_options

    K_ENCLOSURE  shift, and go to state 2017


State 2017

  313 via_viarule: K_VIARULE $@49 T_STRING ';' K_CUTSIZE int_number int_number ';' K_LAYERS $@50 T_STRING T_STRING T_STRING ';' K_CUTSPACING int_number int_number ';' K_ENCLOSURE . int_number int_number int_number int_number ';' $@51 via_viarule_options

    NUMBER  shift, and go to state 99

    int_number  go to state 2018


State 2018

  313 via_viarule: K_VIARULE $@49 T_STRING ';' K_CUTSIZE int_number int_number ';' K_LAYERS $@50 T_STRING T_STRING T_STRING ';' K_CUTSPACING int_number int_number ';' K_ENCLOSURE int_number . int_number int_number int_number ';' $@51 via_viarule_options

    NUMBER  shift, and go to state 99

    int_number  go to state 2019


State 2019

  313 via_viarule: K_VIARULE $@49 T_STRING ';' K_CUTSIZE int_number int_number ';' K_LAYERS $@50 T_STRING T_STRING T_STRING ';' K_CUTSPACING int_number int_number ';' K_ENCLOSURE int_number int_number . int_number int_number ';' $@51 via_viarule_options

    NUMBER  shift, and go to state 99

    int_number  go to state 2020


State 2020

  313 via_viarule: K_VIARULE $@49 T_STRING ';' K_CUTSIZE int_number int_number ';' K_LAYERS $@50 T_STRING T_STRING T_STRING ';' K_CUTSPACING int_number int_number ';' K_ENCLOSURE int_number int_number int_number . int_number ';' $@51 via_viarule_options

    NUMBER  shift, and go to state 99

    int_number  go to state 2021


State 2021

  313 via_viarule: K_VIARULE $@49 T_STRING ';' K_CUTSIZE int_number int_number ';' K_LAYERS $@50 T_STRING T_STRING T_STRING ';' K_CUTSPACING int_number int_number ';' K_ENCLOSURE int_number int_number int_number int_number . ';' $@51 via_viarule_options

    ';'  shift, and go to state 2022


State 2022

  313 via_viarule: K_VIARULE $@49 T_STRING ';' K_CUTSIZE int_number int_number ';' K_LAYERS $@50 T_STRING T_STRING T_STRING ';' K_CUTSPACING int_number int_number ';' K_ENCLOSURE int_number int_number int_number int_number ';' . $@51 via_viarule_options

    $default  reduce using rule 312 ($@51)

    $@51  go to state 2023


State 2023

  313 via_viarule: K_VIARULE $@49 T_STRING ';' K_CUTSIZE int_number int_number ';' K_LAYERS $@50 T_STRING T_STRING T_STRING ';' K_CUTSPACING int_number int_number ';' K_ENCLOSURE int_number int_number int_number int_number ';' $@51 . via_viarule_options

    $default  reduce using rule 314 (via_viarule_options)

    via_viarule_options  go to state 2024


State 2024

  313 via_viarule: K_VIARULE $@49 T_STRING ';' K_CUTSIZE int_number int_number ';' K_LAYERS $@50 T_STRING T_STRING T_STRING ';' K_CUTSPACING int_number int_number ';' K_ENCLOSURE int_number int_number int_number int_number ';' $@51 via_viarule_options .
  315 via_viarule_options: via_viarule_options . via_viarule_option

    K_OFFSET   shift, and go to state 2025
    K_ORIGIN   shift, and go to state 2026
    K_PATTERN  shift, and go to state 2027
    K_ROWCOL   shift, and go to state 2028

    $default  reduce using rule 313 (via_viarule)

    via_viarule_option  go to state 2029


State 2025

  318 via_viarule_option: K_OFFSET . int_number int_number int_number int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 2030


State 2026

  317 via_viarule_option: K_ORIGIN . int_number int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 2031


State 2027

  320 via_viarule_option: K_PATTERN . $@52 T_STRING ';'

    $default  reduce using rule 319 ($@52)

    $@52  go to state 2032


State 2028

  316 via_viarule_option: K_ROWCOL . int_number int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 2033


State 2029

  315 via_viarule_options: via_viarule_options via_viarule_option .

    $default  reduce using rule 315 (via_viarule_options)


State 2030

  318 via_viarule_option: K_OFFSET int_number . int_number int_number int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 2034


State 2031

  317 via_viarule_option: K_ORIGIN int_number . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 2035


State 2032

  320 via_viarule_option: K_PATTERN $@52 . T_STRING ';'

    T_STRING  shift, and go to state 2036


State 2033

  316 via_viarule_option: K_ROWCOL int_number . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 2037


State 2034

  318 via_viarule_option: K_OFFSET int_number int_number . int_number int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 2038


State 2035

  317 via_viarule_option: K_ORIGIN int_number int_number . ';'

    ';'  shift, and go to state 2039


State 2036

  320 via_viarule_option: K_PATTERN $@52 T_STRING . ';'

    ';'  shift, and go to state 2040


State 2037

  316 via_viarule_option: K_ROWCOL int_number int_number . ';'

    ';'  shift, and go to state 2041


State 2038

  318 via_viarule_option: K_OFFSET int_number int_number int_number . int_number ';'

    NUMBER  shift, and go to state 99

    int_number  go to state 2042


State 2039

  317 via_viarule_option: K_ORIGIN int_number int_number ';' .

    $default  reduce using rule 317 (via_viarule_option)


State 2040

  320 via_viarule_option: K_PATTERN $@52 T_STRING ';' .

    $default  reduce using rule 320 (via_viarule_option)


State 2041

  316 via_viarule_option: K_ROWCOL int_number int_number ';' .

    $default  reduce using rule 316 (via_viarule_option)


State 2042

  318 via_viarule_option: K_OFFSET int_number int_number int_number int_number . ';'

    ';'  shift, and go to state 2043


State 2043

  318 via_viarule_option: K_OFFSET int_number int_number int_number int_number ';' .

    $default  reduce using rule 318 (via_viarule_option)
