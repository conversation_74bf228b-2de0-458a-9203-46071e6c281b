\begin{thebibliography}{1}

\bibitem{BorchersB:CSDCls}
B.~Borchers.
\newblock {CSDP}, a {C} library for semidefinite programming.
\newblock {\em Optimization Methods \& Software}, 11-2(1-4):613 -- 623, 1999.

\bibitem{SDPA}
K.~<PERSON>sa<PERSON>, M.~<PERSON>, K.~<PERSON>, and <PERSON><PERSON>~<PERSON>.
\newblock {SDPA} (semidefinite programming algorithm) users manual - version
  6.00.
\newblock Technical Report B--308, Tokyo Institute of Technology, 1995.

\bibitem{HelmbergC:Anims}
C.~<PERSON>, F.~<PERSON>, R<PERSON>~<PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON>.
\newblock An interior-point method for semidefinite programming.
\newblock {\em {SIAM} Journal on Optimization}, 6(2):342 -- 361, May 1996.

\bibitem{MittelmannHD:AnibS}
H.~<PERSON><PERSON>.
\newblock An independent benchmarking of {SDP} and {SOCP} solvers.
\newblock {\em Mathematical Programming}, 95(2):407 -- 430, February 2003.

\bibitem{SturmJF:UsiS1M}
J<PERSON>~<PERSON><PERSON>.
\newblock Using {S}e{D}u{M}i 1.02, a {MATLAB} toolbox for optimization over
  symmetric cones.
\newblock {\em Optimization Methods \& Software}, 11-2(1-4):625 -- 653, 1999.

\end{thebibliography}
