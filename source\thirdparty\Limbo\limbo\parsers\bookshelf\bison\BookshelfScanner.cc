#line 2 "BookshelfScanner.cc"

#line 4 "BookshelfScanner.cc"

#define  YY_INT_ALIGNED short int

/* A lexical scanner generated by flex */

/* %not-for-header */
/* %if-c-only */
/* %if-not-reentrant */
/* %endif */
/* %endif */
/* %ok-for-header */

#define FLEX_SCANNER
#define YY_FLEX_MAJOR_VERSION 2
#define YY_FLEX_MINOR_VERSION 6
#define YY_FLEX_SUBMINOR_VERSION 4
#if YY_FLEX_SUBMINOR_VERSION > 0
#define FLEX_BETA
#endif

/* %if-c++-only */
    /* The c++ scanner is a mess. The FlexLexer.h header file relies on the
     * following macro. This is required in order to pass the c++-multiple-scanners
     * test in the regression suite. We get reports that it breaks inheritance.
     * We will address this in a future release of flex, or omit the C++ scanner
     * altogether.
     */
    #define yyFlexLexer BookshelfParserFlexLexer
/* %endif */

/* %if-c-only */
/* %endif */

#ifdef yyalloc
#define BookshelfParseralloc_ALREADY_DEFINED
#else
#define yyalloc BookshelfParseralloc
#endif

#ifdef yyrealloc
#define BookshelfParserrealloc_ALREADY_DEFINED
#else
#define yyrealloc BookshelfParserrealloc
#endif

#ifdef yyfree
#define BookshelfParserfree_ALREADY_DEFINED
#else
#define yyfree BookshelfParserfree
#endif

/* %if-c-only */
/* %endif */

/* First, we deal with  platform-specific or compiler-specific issues. */

/* begin standard C headers. */
/* %if-c-only */
/* %endif */

/* %if-tables-serialization */
/* %endif */
/* end standard C headers. */

/* %if-c-or-c++ */
/* flex integer type definitions */

#ifndef FLEXINT_H
#define FLEXINT_H

/* C99 systems have <inttypes.h>. Non-C99 systems may or may not. */

#if defined (__STDC_VERSION__) && __STDC_VERSION__ >= 199901L

/* C99 says to define __STDC_LIMIT_MACROS before including stdint.h,
 * if you want the limit (max/min) macros for int types. 
 */
#ifndef __STDC_LIMIT_MACROS
#define __STDC_LIMIT_MACROS 1
#endif

#include <inttypes.h>
typedef int8_t flex_int8_t;
typedef uint8_t flex_uint8_t;
typedef int16_t flex_int16_t;
typedef uint16_t flex_uint16_t;
typedef int32_t flex_int32_t;
typedef uint32_t flex_uint32_t;
#else
typedef signed char flex_int8_t;
typedef short int flex_int16_t;
typedef int flex_int32_t;
typedef unsigned char flex_uint8_t; 
typedef unsigned short int flex_uint16_t;
typedef unsigned int flex_uint32_t;

/* Limits of integral types. */
#ifndef INT8_MIN
#define INT8_MIN               (-128)
#endif
#ifndef INT16_MIN
#define INT16_MIN              (-32767-1)
#endif
#ifndef INT32_MIN
#define INT32_MIN              (-2147483647-1)
#endif
#ifndef INT8_MAX
#define INT8_MAX               (127)
#endif
#ifndef INT16_MAX
#define INT16_MAX              (32767)
#endif
#ifndef INT32_MAX
#define INT32_MAX              (2147483647)
#endif
#ifndef UINT8_MAX
#define UINT8_MAX              (255U)
#endif
#ifndef UINT16_MAX
#define UINT16_MAX             (65535U)
#endif
#ifndef UINT32_MAX
#define UINT32_MAX             (4294967295U)
#endif

#ifndef SIZE_MAX
#define SIZE_MAX               (~(size_t)0)
#endif

#endif /* ! C99 */

#endif /* ! FLEXINT_H */

/* %endif */

/* begin standard C++ headers. */
/* %if-c++-only */
#include <iostream>
#include <errno.h>
#include <cstdlib>
#include <cstdio>
#include <cstring>
/* end standard C++ headers. */
/* %endif */

/* TODO: this is always defined, so inline it */
#define yyconst const

#if defined(__GNUC__) && __GNUC__ >= 3
#define yynoreturn __attribute__((__noreturn__))
#else
#define yynoreturn
#endif

/* %not-for-header */
/* Returned upon end-of-file. */
#define YY_NULL 0
/* %ok-for-header */

/* %not-for-header */
/* Promotes a possibly negative, possibly signed char to an
 *   integer in range [0..255] for use as an array index.
 */
#define YY_SC_TO_UI(c) ((YY_CHAR) (c))
/* %ok-for-header */

/* %if-reentrant */
/* %endif */

/* %if-not-reentrant */

/* %endif */

/* Enter a start condition.  This macro really ought to take a parameter,
 * but we do it the disgusting crufty way forced on us by the ()-less
 * definition of BEGIN.
 */
#define BEGIN (yy_start) = 1 + 2 *
/* Translate the current start state into a value that can be later handed
 * to BEGIN to return to the state.  The YYSTATE alias is for lex
 * compatibility.
 */
#define YY_START (((yy_start) - 1) / 2)
#define YYSTATE YY_START
/* Action number for EOF rule of a given start state. */
#define YY_STATE_EOF(state) (YY_END_OF_BUFFER + state + 1)
/* Special action meaning "start processing a new file". */
#define YY_NEW_FILE yyrestart( yyin  )
#define YY_END_OF_BUFFER_CHAR 0

/* Size of default input buffer. */
#ifndef YY_BUF_SIZE
#ifdef __ia64__
/* On IA-64, the buffer size is 16k, not 8k.
 * Moreover, YY_BUF_SIZE is 2*YY_READ_BUF_SIZE in the general case.
 * Ditto for the __ia64__ case accordingly.
 */
#define YY_BUF_SIZE 32768
#else
#define YY_BUF_SIZE 16384
#endif /* __ia64__ */
#endif

/* The state buf must be large enough to hold one state per character in the main buffer.
 */
#define YY_STATE_BUF_SIZE   ((YY_BUF_SIZE + 2) * sizeof(yy_state_type))

#ifndef YY_TYPEDEF_YY_BUFFER_STATE
#define YY_TYPEDEF_YY_BUFFER_STATE
typedef struct yy_buffer_state *YY_BUFFER_STATE;
#endif

#ifndef YY_TYPEDEF_YY_SIZE_T
#define YY_TYPEDEF_YY_SIZE_T
typedef size_t yy_size_t;
#endif

/* %if-not-reentrant */
extern int yyleng;
/* %endif */

/* %if-c-only */
/* %if-not-reentrant */
/* %endif */
/* %endif */

#define EOB_ACT_CONTINUE_SCAN 0
#define EOB_ACT_END_OF_FILE 1
#define EOB_ACT_LAST_MATCH 2
    
    #define YY_LESS_LINENO(n)
    #define YY_LINENO_REWIND_TO(ptr)
    
/* Return all but the first "n" matched characters back to the input stream. */
#define yyless(n) \
	do \
		{ \
		/* Undo effects of setting up yytext. */ \
        int yyless_macro_arg = (n); \
        YY_LESS_LINENO(yyless_macro_arg);\
		*yy_cp = (yy_hold_char); \
		YY_RESTORE_YY_MORE_OFFSET \
		(yy_c_buf_p) = yy_cp = yy_bp + yyless_macro_arg - YY_MORE_ADJ; \
		YY_DO_BEFORE_ACTION; /* set up yytext again */ \
		} \
	while ( 0 )
#define unput(c) yyunput( c, (yytext_ptr)  )

#ifndef YY_STRUCT_YY_BUFFER_STATE
#define YY_STRUCT_YY_BUFFER_STATE
struct yy_buffer_state
	{
/* %if-c-only */
/* %endif */

/* %if-c++-only */
	std::streambuf* yy_input_file;
/* %endif */

	char *yy_ch_buf;		/* input buffer */
	char *yy_buf_pos;		/* current position in input buffer */

	/* Size of input buffer in bytes, not including room for EOB
	 * characters.
	 */
	int yy_buf_size;

	/* Number of characters read into yy_ch_buf, not including EOB
	 * characters.
	 */
	int yy_n_chars;

	/* Whether we "own" the buffer - i.e., we know we created it,
	 * and can realloc() it to grow it, and should free() it to
	 * delete it.
	 */
	int yy_is_our_buffer;

	/* Whether this is an "interactive" input source; if so, and
	 * if we're using stdio for input, then we want to use getc()
	 * instead of fread(), to make sure we stop fetching input after
	 * each newline.
	 */
	int yy_is_interactive;

	/* Whether we're considered to be at the beginning of a line.
	 * If so, '^' rules will be active on the next match, otherwise
	 * not.
	 */
	int yy_at_bol;

    int yy_bs_lineno; /**< The line count. */
    int yy_bs_column; /**< The column count. */

	/* Whether to try to fill the input buffer when we reach the
	 * end of it.
	 */
	int yy_fill_buffer;

	int yy_buffer_status;

#define YY_BUFFER_NEW 0
#define YY_BUFFER_NORMAL 1
	/* When an EOF's been seen but there's still some text to process
	 * then we mark the buffer as YY_EOF_PENDING, to indicate that we
	 * shouldn't try reading from the input source any more.  We might
	 * still have a bunch of tokens to match, though, because of
	 * possible backing-up.
	 *
	 * When we actually see the EOF, we change the status to "new"
	 * (via yyrestart()), so that the user can continue scanning by
	 * just pointing yyin at a new input file.
	 */
#define YY_BUFFER_EOF_PENDING 2

	};
#endif /* !YY_STRUCT_YY_BUFFER_STATE */

/* %if-c-only Standard (non-C++) definition */
/* %not-for-header */
/* %if-not-reentrant */
/* %endif */
/* %ok-for-header */

/* %endif */

/* We provide macros for accessing buffer states in case in the
 * future we want to put the buffer states in a more general
 * "scanner state".
 *
 * Returns the top of the stack, or NULL.
 */
#define YY_CURRENT_BUFFER ( (yy_buffer_stack) \
                          ? (yy_buffer_stack)[(yy_buffer_stack_top)] \
                          : NULL)
/* Same as previous macro, but useful when we know that the buffer stack is not
 * NULL or when we need an lvalue. For internal use only.
 */
#define YY_CURRENT_BUFFER_LVALUE (yy_buffer_stack)[(yy_buffer_stack_top)]

/* %if-c-only Standard (non-C++) definition */
/* %if-not-reentrant */
/* %not-for-header */
/* %ok-for-header */

/* %endif */
/* %endif */

void *yyalloc ( yy_size_t  );
void *yyrealloc ( void *, yy_size_t  );
void yyfree ( void *  );

#define yy_new_buffer yy_create_buffer
#define yy_set_interactive(is_interactive) \
	{ \
	if ( ! YY_CURRENT_BUFFER ){ \
        yyensure_buffer_stack (); \
		YY_CURRENT_BUFFER_LVALUE =    \
            yy_create_buffer( yyin, YY_BUF_SIZE ); \
	} \
	YY_CURRENT_BUFFER_LVALUE->yy_is_interactive = is_interactive; \
	}
#define yy_set_bol(at_bol) \
	{ \
	if ( ! YY_CURRENT_BUFFER ){\
        yyensure_buffer_stack (); \
		YY_CURRENT_BUFFER_LVALUE =    \
            yy_create_buffer( yyin, YY_BUF_SIZE ); \
	} \
	YY_CURRENT_BUFFER_LVALUE->yy_at_bol = at_bol; \
	}
#define YY_AT_BOL() (YY_CURRENT_BUFFER_LVALUE->yy_at_bol)

/* %% [1.0] yytext/yyin/yyout/yy_state_type/yylineno etc. def's & init go here */
/* Begin user sect3 */

#define FLEX_DEBUG
typedef flex_uint8_t YY_CHAR;

#define yytext_ptr yytext

#include <FlexLexer.h>

/* %% [1.5] DFA */

/* %if-c-only Standard (non-C++) definition */
/* %endif */

/* Done after the current pattern has been matched and before the
 * corresponding action - sets up yytext.
 */
#define YY_DO_BEFORE_ACTION \
	(yytext_ptr) = yy_bp; \
/* %% [2.0] code to fiddle yytext and yyleng for yymore() goes here \ */\
	yyleng = (int) (yy_cp - yy_bp); \
	(yy_hold_char) = *yy_cp; \
	*yy_cp = '\0'; \
/* %% [3.0] code to copy yytext_ptr to yytext[] goes here, if %array \ */\
	(yy_c_buf_p) = yy_cp;
/* %% [4.0] data tables for the DFA and the user's section 1 definitions go here */
#define YY_NUM_RULES 52
#define YY_END_OF_BUFFER 53
/* This struct is not used in this scanner,
   but its presence is necessary. */
struct yy_trans_info
	{
	flex_int32_t yy_verify;
	flex_int32_t yy_nxt;
	};
static const flex_int16_t yy_accept[230] =
    {   0,
       47,   47,   53,   51,   49,   50,   51,   48,   47,   51,
       51,   47,   47,   42,   45,   33,   45,   45,   37,   45,
       45,   32,   34,   31,   45,   35,   45,   45,   45,   36,
       49,    0,   46,   48,   47,    0,   42,   42,   47,   47,
       47,    0,   43,   42,   45,   45,   45,   45,   41,   45,
       38,   39,   40,   45,   45,   45,   45,   45,   24,   45,
       45,   45,   45,   45,   45,   45,   45,   44,   43,   47,
       47,   47,   43,   26,   45,   45,   17,   45,   45,   45,
       45,   45,   45,   45,   21,   45,   45,   45,   45,   45,
       45,   25,   43,   47,   47,   47,   45,   45,   45,   45,

       45,   45,   23,   45,   45,   45,   45,   45,   45,   45,
       45,   45,   45,   19,   45,   45,   47,   47,   47,   45,
       45,   27,   45,   45,   45,   22,   45,   45,   45,   45,
       45,   45,   45,   45,   45,   45,   45,   45,   45,   45,
       27,   47,   47,   45,   45,   45,   10,   45,   45,   45,
       45,   45,   45,   45,   45,   29,   45,   45,   45,   45,
       45,   45,   45,   45,   47,   29,   47,   45,    6,   45,
       45,   45,    1,   45,    2,    5,   45,   45,   45,   45,
       45,   45,   45,   45,   45,   45,   47,   47,   45,   28,
       45,   45,    3,   16,   45,   45,   45,   45,   45,   45,

       18,   30,    8,   28,   30,   45,   45,   20,   45,   45,
       45,   45,   11,   45,    9,    7,   45,   13,   45,   45,
       45,   45,   12,   45,   45,    4,   14,   15,    0
    } ;

static const YY_CHAR yy_ec[256] =
    {   0,
        1,    1,    1,    1,    1,    1,    1,    1,    2,    3,
        1,    1,    2,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    2,    1,    4,    5,    6,    1,    1,    7,    8,
        8,    1,    9,    6,   10,   11,   12,   13,   13,   13,
       13,   13,   13,   13,   13,   13,   13,    1,    1,    1,
        1,    1,    1,    1,   14,   15,   16,   17,   18,   19,
       20,   21,   22,   23,   23,   24,   25,   26,   27,   28,
       23,   29,   30,   31,   32,   33,   34,   35,   36,   37,
        6,    1,    6,    1,   38,    1,   39,   40,   41,   42,

       43,   44,   45,   46,   47,   23,   23,   48,   49,   50,
       51,   52,   23,   53,   54,   55,   56,   57,   58,   59,
       60,   61,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,

        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1
    } ;

static const YY_CHAR yy_meta[62] =
    {   0,
        1,    1,    2,    1,    1,    3,    1,    3,    1,    3,
        3,    3,    3,    4,    4,    4,    4,    4,    4,    4,
        4,    4,    4,    4,    4,    4,    4,    4,    4,    4,
        4,    4,    4,    4,    4,    4,    4,    3,    4,    4,
        4,    4,    4,    4,    4,    4,    4,    4,    4,    4,
        4,    4,    4,    4,    4,    4,    4,    4,    4,    4,
        4
    } ;

static const flex_int16_t yy_base[235] =
    {   0,
        0,    0,  290,  917,  281,  917,  242,    0,    0,    0,
      215,  204,   43,   56,   56,   58,   64,   65,   66,  109,
       71,   72,   93,   73,   78,  125,   85,  134,  111,  122,
      209,  205,  917,    0,    0,  158,   83,   93,   60,   76,
       82,    0,  135,  151,  101,  143,  153,  171,  161,  162,
      177,  178,  179,  186,  187,  184,  193,  195,  210,  217,
      211,  219,  218,  224,  242,  244,  245,  132,  127,  141,
      154,  186,  125,  243,  250,  266,  252,  268,  269,  279,
      288,  280,  335,  294,  255,  307,  300,  308,  320,  328,
      329,  305,  105,  194,  227,  221,  337,  319,  350,  361,

      343,  363,  362,  383,  385,  386,  388,  397,  402,  409,
      423,  414,  426,  417,  446,  439,  265,  303,  363,  447,
      455,  434,  456,  463,  458,  468,  471,  476,  483,  488,
      489,  496,  511,  505,  504,  519,  527,  539,  547,  559,
       72,  369,  383,  542,  540,  561,  530,  564,  570,  571,
      585,  572,  583,  597,  569,  596,  598,  619,  604,  622,
      609,  630,  638,  635,  235,    0,  400,  640,  643,  649,
      651,  654,  665,  668,  675,  676,  677,  678,  683,  696,
      691,  697,  703,  705,  702,  712,  477,  488,  715,  722,
      727,  730,  735,  737,  742,  743,  749,  764,  766,  755,

      750,  767,  772,    0,    0,  773,  782,  787,  789,  792,
      801,  802,  803,  809,  810,  812,  824,  823,  826,  829,
      831,  832,  837,  844,  847,  852,  853,  855,  917,  904,
      908,  910,   61,  912
    } ;

static const flex_int16_t yy_def[235] =
    {   0,
      229,    1,  229,  229,  229,  229,  230,  231,  232,  233,
      229,  232,  232,  232,  234,  234,  234,  234,  234,  234,
      234,  234,  234,  234,  234,  234,  234,  234,  234,  234,
      229,  230,  229,  231,  232,  229,  229,  232,  232,  232,
      232,  233,  232,  232,  234,  234,  234,  234,  234,  234,
      234,  234,  234,  234,  234,  234,  234,  234,  234,  234,
      234,  234,  234,  234,  234,  234,  234,  229,  229,  232,
      232,  232,  232,  234,  234,  234,  234,  234,  234,  234,
      234,  234,  234,  234,  234,  234,  234,  234,  234,  234,
      234,  234,  229,  232,  232,  232,  234,  234,  234,  234,

      234,  234,  234,  234,  234,  234,  234,  234,  234,  234,
      234,  234,  234,  234,  234,  234,  232,  232,  232,  234,
      234,  234,  234,  234,  234,  234,  234,  234,  234,  234,
      234,  234,  234,  234,  234,  234,  234,  234,  234,  234,
      232,  232,  232,  234,  234,  234,  234,  234,  234,  234,
      234,  234,  234,  234,  234,  234,  234,  234,  234,  234,
      234,  234,  234,  234,  232,  232,  232,  234,  234,  234,
      234,  234,  234,  234,  234,  234,  234,  234,  234,  234,
      234,  234,  234,  234,  234,  234,  232,  232,  234,  234,
      234,  234,  234,  234,  234,  234,  234,  234,  234,  234,

      234,  234,  234,  232,  232,  234,  234,  234,  234,  234,
      234,  234,  234,  234,  234,  234,  234,  234,  234,  234,
      234,  234,  234,  234,  234,  234,  234,  234,    0,  229,
      229,  229,  229,  229
    } ;

static const flex_int16_t yy_nxt[979] =
    {   0,
        4,    5,    6,    7,    8,    9,   10,    9,   11,   12,
        9,   13,   14,   15,   16,   17,   18,   19,   20,   18,
       21,   22,   18,   18,   18,   23,   24,   25,   18,   26,
       27,   28,   29,   30,   18,   18,   18,    9,   15,   16,
       17,   18,   19,   20,   18,   21,   22,   18,   18,   23,
       24,   25,   18,   26,   27,   28,   29,   30,   18,   18,
       18,   39,   42,   35,   36,   35,   43,   35,   44,   35,
       40,   35,   35,   35,   41,   35,   35,   35,   35,   35,
       35,   70,   35,   35,   35,   35,   39,   46,   54,   35,
       47,   48,   35,   69,   40,   37,   35,   55,   41,   71,

       35,   59,   63,   43,   35,   38,   70,   72,   35,  165,
       56,   46,   35,   54,   47,   48,   35,   93,   35,   57,
       35,   55,   35,   71,   58,   59,   49,   63,   66,   35,
       50,   72,   35,   35,   51,   56,   35,   73,   52,   93,
       60,   35,   53,   57,   68,   35,   61,   73,   58,   64,
       35,   49,   67,   66,   35,   50,   62,   42,   51,   65,
       35,   43,   52,   44,   35,   60,   53,   95,   35,   35,
       68,   61,   35,   35,   64,   94,   67,   74,   35,   75,
       62,   76,   35,   65,   35,   35,   35,   77,   35,   35,
       35,   35,   95,   35,   35,   35,   78,   35,   35,   94,

       35,   74,   35,   75,   35,   76,   35,   79,   33,   82,
       31,  117,   77,   96,   81,   80,   38,   35,   35,   83,
       78,   35,   35,   84,   35,   35,   35,   37,   35,   35,
       35,   35,   79,   87,   82,   35,  117,   96,   81,   80,
       85,   86,  118,   83,  119,   33,   88,   89,   84,   35,
       35,   35,   35,   35,   35,   35,   35,   35,   87,   35,
      187,   35,   35,   35,   85,   86,   35,  118,  119,   90,
       88,   89,   91,   35,   92,   35,   35,   35,   97,   35,
       35,  141,   31,   98,  187,   99,   35,   35,  100,  229,
       35,   35,  229,   90,  229,   35,   91,  104,   92,   35,

      101,   35,   97,  229,  102,   35,  141,   35,   98,  110,
       99,   35,   35,  100,   35,   35,   35,  103,   35,   35,
      142,  229,  104,  229,  111,  101,   35,   35,  112,  102,
       35,   35,  113,  114,  110,   35,   35,  229,  229,   35,
       35,  103,   35,  229,   35,  142,   35,  121,   35,  111,
       35,  115,  112,  120,   35,  229,  113,   35,  114,  116,
      105,   35,  106,  107,  108,  109,  122,  229,   35,   35,
       35,  121,   35,   35,   35,  115,  143,  229,  120,  124,
      125,  123,  229,  116,  105,  166,  106,  107,  108,  109,
       35,  122,   35,   35,   35,   35,   35,   35,  167,   35,

      229,  143,  127,  124,   35,  125,  123,  129,   35,   35,
      166,  128,  126,   35,  130,  229,   35,  188,  131,  132,
       35,   35,  229,  167,   35,   35,  133,  127,   35,  229,
       35,  229,  129,   35,   35,  128,  126,   35,  130,  229,
      137,   35,  188,  131,  132,   35,   35,  138,  229,  134,
       35,  133,  135,   35,   35,  229,  136,   35,   35,  139,
      140,  229,   35,   35,  137,   35,   35,   35,  144,   35,
       35,  146,  138,  134,   35,   35,  135,  149,   35,   35,
      136,  145,   35,   35,  139,  140,  147,   35,  229,  148,
       35,  229,  151,  144,   35,   35,   35,  229,  204,   35,

       35,  150,  149,   35,  205,  145,  229,   35,  152,  229,
      147,   35,   35,  148,  229,   35,   35,  151,   35,  154,
      229,  153,   35,  204,  155,  150,   35,  156,  229,  205,
       35,  158,  152,  157,   35,  229,  229,   35,   35,  159,
      160,   35,  229,  154,  229,  153,   35,   35,  155,   35,
       35,   35,  156,   35,   35,  158,  229,  157,   35,  229,
      161,  229,  163,  159,  162,  160,   35,  168,   35,  229,
       35,   35,   35,  169,  164,   35,   35,   35,   35,   35,
       35,   35,   35,   35,  161,  229,  170,  163,  162,  171,
       35,  168,   35,  178,   35,  229,   35,  169,  172,  164,

      173,  175,  174,   35,   35,   35,  229,   35,   35,   35,
      170,   35,  176,  171,  177,   35,   35,  178,  229,  179,
       35,  229,  172,  229,  173,  175,   35,  174,  181,   35,
       35,  229,  180,   35,  229,  183,  176,   35,  182,  177,
      229,   35,   35,  184,  179,   35,   35,   35,  186,   35,
       35,   35,  181,  189,   35,  185,   35,  180,   35,  183,
       35,   35,   35,  182,  229,   35,  229,  229,  184,  229,
      190,  192,   35,  186,  229,   35,   35,  229,  189,   35,
      185,  191,   35,   35,   35,   35,   35,   35,   35,   35,
       35,  229,  229,  229,   35,  190,  192,  193,   35,  195,

      196,  229,   35,   35,   35,  191,  194,   35,   35,   35,
       35,  197,   35,   35,   35,  198,   35,  229,  202,   35,
      229,  193,   35,   35,  195,  196,   35,  199,  201,   35,
      194,  200,  229,   35,   35,  203,  197,   35,   35,  198,
      207,   35,   35,  202,   35,  206,   35,  208,   35,   35,
       35,  199,  201,   35,   35,  200,   35,   35,  229,  203,
       35,   35,   35,  229,  229,  207,   35,  209,  210,  206,
      211,   35,  208,   35,   35,   35,  214,   35,   35,   35,
       35,  212,  229,   35,   35,  229,  213,  229,  229,   35,
      215,  209,  210,   35,   35,  211,   35,  229,   35,   35,

       35,  214,  217,   35,  229,  216,  212,  229,   35,   35,
       35,  213,   35,   35,   35,  215,   35,   35,  229,   35,
       35,   35,  218,   35,  229,  229,  219,  217,  221,  216,
       35,   35,  220,   35,   35,   35,   35,   35,   35,   35,
       35,  229,   35,   35,   35,  223,  218,  222,   35,  229,
      219,   35,  225,  221,   35,   35,  220,  224,   35,   35,
       35,  226,   35,   35,   35,  229,   35,  229,  229,  229,
      223,  222,  228,  229,  229,  229,  229,  225,  229,  227,
      229,  224,  229,  229,  229,  226,  229,  229,  229,  229,
      229,  229,  229,  229,  229,  229,  228,  229,  229,  229,

      229,  229,  229,  227,   32,   32,   32,   32,   34,  229,
       34,   34,   35,   35,   45,   45,    3,  229,  229,  229,
      229,  229,  229,  229,  229,  229,  229,  229,  229,  229,
      229,  229,  229,  229,  229,  229,  229,  229,  229,  229,
      229,  229,  229,  229,  229,  229,  229,  229,  229,  229,
      229,  229,  229,  229,  229,  229,  229,  229,  229,  229,
      229,  229,  229,  229,  229,  229,  229,  229,  229,  229,
      229,  229,  229,  229,  229,  229,  229,  229
    } ;

static const flex_int16_t yy_chk[979] =
    {   0,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,   13,   14,   15,  233,   16,   14,   15,   14,   16,
       13,   17,   18,   19,   13,   17,   18,   19,   21,   22,
       24,   39,   21,   22,   24,   25,   13,   15,   21,   25,
       17,   19,   27,   37,   13,   37,   27,   21,   13,   40,

       23,   25,   27,   38,   23,   38,   39,   41,   45,  141,
       23,   15,   45,   21,   17,   19,   20,   93,   29,   23,
       20,   21,   29,   40,   23,   25,   20,   27,   29,   30,
       20,   41,   26,   30,   20,   23,   26,   73,   20,   69,
       26,   28,   20,   23,   68,   28,   26,   43,   23,   28,
       46,   20,   30,   29,   46,   20,   26,   44,   20,   28,
       47,   44,   20,   44,   47,   26,   20,   71,   49,   50,
       36,   26,   49,   50,   28,   70,   30,   46,   48,   47,
       26,   47,   48,   28,   51,   52,   53,   48,   51,   52,
       53,   56,   71,   54,   55,   56,   50,   54,   55,   70,

       57,   46,   58,   47,   57,   47,   58,   54,   32,   57,
       31,   94,   48,   72,   56,   55,   12,   59,   61,   58,
       50,   59,   61,   59,   60,   63,   62,   11,   60,   63,
       62,   64,   54,   62,   57,   64,   94,   72,   56,   55,
       60,   61,   95,   58,   96,    7,   63,   64,   59,   65,
       74,   66,   67,   65,   74,   66,   67,   75,   62,   77,
      165,   75,   85,   77,   60,   61,   85,   95,   96,   65,
       63,   64,   66,   76,   67,   78,   79,   76,   75,   78,
       79,  117,    5,   76,  165,   78,   80,   82,   79,    3,
       80,   82,    0,   65,    0,   81,   66,   82,   67,   81,

       80,   84,   75,    0,   81,   84,  117,   87,   76,   84,
       78,   87,   92,   79,   86,   88,   92,   81,   86,   88,
      118,    0,   82,    0,   86,   80,   98,   89,   87,   81,
       98,   89,   88,   89,   84,   90,   91,    0,    0,   90,
       91,   81,   83,    0,   97,  118,   83,   98,   97,   86,
      101,   90,   87,   97,  101,    0,   88,   99,   89,   91,
       83,   99,   83,   83,   83,   83,   99,    0,  100,  103,
      102,   98,  100,  103,  102,   90,  119,    0,   97,  101,
      102,  100,    0,   91,   83,  142,   83,   83,   83,   83,
      104,   99,  105,  106,  104,  107,  105,  106,  143,  107,

        0,  119,  105,  101,  108,  102,  100,  106,  108,  109,
      142,  105,  104,  109,  107,    0,  110,  167,  108,  109,
      110,  112,    0,  143,  114,  112,  110,  105,  114,    0,
      111,    0,  106,  113,  111,  105,  104,  113,  107,    0,
      112,  122,  167,  108,  109,  122,  116,  113,    0,  111,
      116,  110,  111,  115,  120,    0,  111,  115,  120,  115,
      116,    0,  121,  123,  112,  125,  121,  123,  120,  125,
      124,  122,  113,  111,  124,  126,  111,  125,  127,  126,
      111,  121,  127,  128,  115,  116,  123,  128,    0,  124,
      129,    0,  128,  120,  129,  130,  131,    0,  187,  130,

      131,  127,  125,  132,  188,  121,    0,  132,  129,    0,
      123,  135,  134,  124,    0,  135,  134,  128,  133,  131,
        0,  130,  133,  187,  132,  127,  136,  133,    0,  188,
      136,  135,  129,  134,  137,    0,    0,  147,  137,  135,
      136,  147,    0,  131,    0,  130,  138,  145,  132,  144,
      138,  145,  133,  144,  139,  135,    0,  134,  139,    0,
      137,    0,  139,  135,  138,  136,  140,  144,  146,    0,
      140,  148,  146,  145,  140,  148,  155,  149,  150,  152,
      155,  149,  150,  152,  137,    0,  146,  139,  138,  148,
      153,  144,  151,  155,  153,    0,  151,  145,  149,  140,

      150,  152,  151,  156,  154,  157,    0,  156,  154,  157,
      146,  159,  153,  148,  154,  159,  161,  155,    0,  157,
      161,    0,  149,    0,  150,  152,  158,  151,  159,  160,
      158,    0,  158,  160,    0,  161,  153,  162,  160,  154,
        0,  162,  164,  162,  157,  163,  164,  168,  164,  163,
      169,  168,  159,  168,  169,  163,  170,  158,  171,  161,
      170,  172,  171,  160,    0,  172,    0,    0,  162,    0,
      170,  172,  173,  164,    0,  174,  173,    0,  168,  174,
      163,  171,  175,  176,  177,  178,  175,  176,  177,  178,
      179,    0,    0,    0,  179,  170,  172,  174,  181,  178,

      179,    0,  181,  180,  182,  171,  177,  180,  182,  185,
      183,  180,  184,  185,  183,  181,  184,    0,  185,  186,
        0,  174,  189,  186,  178,  179,  189,  182,  184,  190,
      177,  183,    0,  190,  191,  186,  180,  192,  191,  181,
      191,  192,  193,  185,  194,  189,  193,  192,  194,  195,
      196,  182,  184,  195,  196,  183,  197,  201,    0,  186,
      197,  201,  200,    0,    0,  191,  200,  195,  196,  189,
      197,  198,  192,  199,  202,  198,  200,  199,  202,  203,
      206,  198,    0,  203,  206,    0,  199,    0,    0,  207,
      206,  195,  196,  207,  208,  197,  209,    0,  208,  210,

      209,  200,  209,  210,    0,  207,  198,    0,  211,  212,
      213,  199,  211,  212,  213,  206,  214,  215,    0,  216,
      214,  215,  210,  216,    0,    0,  211,  209,  214,  207,
      218,  217,  212,  219,  218,  217,  220,  219,  221,  222,
      220,    0,  221,  222,  223,  219,  210,  217,  223,    0,
      211,  224,  221,  214,  225,  224,  212,  220,  225,  226,
      227,  222,  228,  226,  227,    0,  228,    0,    0,    0,
      219,  217,  225,    0,    0,    0,    0,  221,    0,  224,
        0,  220,    0,    0,    0,  222,    0,    0,    0,    0,
        0,    0,    0,    0,    0,    0,  225,    0,    0,    0,

        0,    0,    0,  224,  230,  230,  230,  230,  231,    0,
      231,  231,  232,  232,  234,  234,  229,  229,  229,  229,
      229,  229,  229,  229,  229,  229,  229,  229,  229,  229,
      229,  229,  229,  229,  229,  229,  229,  229,  229,  229,
      229,  229,  229,  229,  229,  229,  229,  229,  229,  229,
      229,  229,  229,  229,  229,  229,  229,  229,  229,  229,
      229,  229,  229,  229,  229,  229,  229,  229,  229,  229,
      229,  229,  229,  229,  229,  229,  229,  229
    } ;

static const flex_int16_t yy_rule_linenum[52] =
    {   0,
       62,   63,   64,   65,   66,   67,   68,   69,   70,   71,
       72,   73,   74,   75,   76,   77,   78,   79,   80,   81,
       82,   83,   84,   85,   86,   87,   88,   89,   90,   91,
       92,   93,   94,   95,   96,   97,   98,   99,  100,  101,
      102,  105,  110,  115,  120,  125,  130,  136,  141,  146,
      152
    } ;

/* The intent behind this definition is that it'll catch
 * any uses of REJECT which flex missed.
 */
#define REJECT reject_used_but_not_detected
#define yymore() yymore_used_but_not_detected
#define YY_MORE_ADJ 0
#define YY_RESTORE_YY_MORE_OFFSET
#line 1 "BookshelfScanner.ll"
/* $Id: scanner.ll 44 2008-10-23 09:03:19Z tb $ -*- mode: c++ -*- */
/** \file scanner.ll Define the example Flex lexical scanner */
#line 5 "BookshelfScanner.ll"

#include <string>

#include "BookshelfScanner.h"

/* import the parser's token type into a local typedef */
typedef BookshelfParser::Parser::token token;
typedef BookshelfParser::Parser::token_type token_type;

/* By default yylex returns int, we use token_type. Unfortunately yyterminate
 * by default returns 0, which is not of token_type. */
#define yyterminate() return token::END

/* This disables inclusion of unistd.h, which is not available under Visual C++
 * on Win32. The C++ scanner uses STL streams instead. */
#define YY_NO_UNISTD_H

#line 806 "BookshelfScanner.cc"
/*** Flex Declarations and Options ***/
/* enable c++ scanner class generation */
/* change the name of the scanner class. results in "ExampleFlexLexer" */
/* the manual says "somewhat more optimized" */
/* enable scanner to generate debug output. disable this for release
 * versions. */
/* no support for include files is planned */
/* enables the use of start condition stacks */
/* The following paragraph suffices to track locations accurately. Each time
 * yylex is invoked, the begin position is moved onto the end position. */
#line 48 "BookshelfScanner.ll"
#define YY_USER_ACTION  yylloc->columns(yyleng);
#line 819 "BookshelfScanner.cc"
#line 820 "BookshelfScanner.cc"

#define INITIAL 0

#ifndef YY_NO_UNISTD_H
/* Special case for "unistd.h", since it is non-ANSI. We include it way
 * down here because we want the user's section 1 to have been scanned first.
 * The user has a chance to override it with an option.
 */
/* %if-c-only */
/* %endif */
/* %if-c++-only */
#include <unistd.h>
/* %endif */
#endif

#ifndef YY_EXTRA_TYPE
#define YY_EXTRA_TYPE void *
#endif

/* %if-c-only Reentrant structure and macros (non-C++). */
/* %if-reentrant */
/* %if-c-only */
/* %endif */
/* %if-reentrant */
/* %endif */
/* %endif End reentrant structures and macros. */
/* %if-bison-bridge */
/* %endif */
/* %not-for-header */
/* %ok-for-header */

/* %endif */

#ifndef yytext_ptr
static void yy_flex_strncpy ( char *, const char *, int );
#endif

#ifdef YY_NEED_STRLEN
static int yy_flex_strlen ( const char * );
#endif

#ifndef YY_NO_INPUT
/* %if-c-only Standard (non-C++) definition */
/* %not-for-header */
/* %ok-for-header */

/* %endif */
#endif

/* %if-c-only */
/* %endif */

/* Amount of stuff to slurp up with each read. */
#ifndef YY_READ_BUF_SIZE
#ifdef __ia64__
/* On IA-64, the buffer size is 16k, not 8k */
#define YY_READ_BUF_SIZE 16384
#else
#define YY_READ_BUF_SIZE 8192
#endif /* __ia64__ */
#endif

/* Copy whatever the last rule matched to the standard output. */
#ifndef ECHO
/* %if-c-only Standard (non-C++) definition */
/* %endif */
/* %if-c++-only C++ definition */
#define ECHO LexerOutput( yytext, yyleng )
/* %endif */
#endif

/* Gets input and stuffs it into "buf".  number of characters read, or YY_NULL,
 * is returned in "result".
 */
#ifndef YY_INPUT
#define YY_INPUT(buf,result,max_size) \
/* %% [5.0] fread()/read() definition of YY_INPUT goes here unless we're doing C++ \ */\
\
/* %if-c++-only C++ definition \ */\
	if ( (int)(result = LexerInput( (char *) buf, max_size )) < 0 ) \
		YY_FATAL_ERROR( "input in flex scanner failed" );
/* %endif */

#endif

/* No semi-colon after return; correct usage is to write "yyterminate();" -
 * we don't want an extra ';' after the "return" because that will cause
 * some compilers to complain about unreachable statements.
 */
#ifndef yyterminate
#define yyterminate() return YY_NULL
#endif

/* Number of entries by which start-condition stack grows. */
#ifndef YY_START_STACK_INCR
#define YY_START_STACK_INCR 25
#endif

/* Report a fatal error. */
#ifndef YY_FATAL_ERROR
/* %if-c-only */
/* %endif */
/* %if-c++-only */
#define YY_FATAL_ERROR(msg) LexerError( msg )
/* %endif */
#endif

/* %if-tables-serialization structures and prototypes */
/* %not-for-header */
/* %ok-for-header */

/* %not-for-header */
/* %tables-yydmap generated elements */
/* %endif */
/* end tables serialization structures and prototypes */

/* %ok-for-header */

/* Default declaration of generated scanner - a define so the user can
 * easily add parameters.
 */
#ifndef YY_DECL
#define YY_DECL_IS_OURS 1
/* %if-c-only Standard (non-C++) definition */
/* %endif */
/* %if-c++-only C++ definition */
#define YY_DECL int yyFlexLexer::yylex()
/* %endif */
#endif /* !YY_DECL */

/* Code executed at the beginning of each rule, after yytext and yyleng
 * have been set up.
 */
#ifndef YY_USER_ACTION
#define YY_USER_ACTION
#endif

/* Code executed at the end of each rule. */
#ifndef YY_BREAK
#define YY_BREAK /*LINTED*/break;
#endif

/* %% [6.0] YY_RULE_SETUP definition goes here */
#define YY_RULE_SETUP \
	YY_USER_ACTION

/* %not-for-header */
/** The main scanner function which does all the work.
 */
YY_DECL
{
	yy_state_type yy_current_state;
	char *yy_cp, *yy_bp;
	int yy_act;
    
	if ( !(yy_init) )
		{
		(yy_init) = 1;

#ifdef YY_USER_INIT
		YY_USER_INIT;
#endif

		if ( ! (yy_start) )
			(yy_start) = 1;	/* first start state */

		if ( ! yyin )
/* %if-c-only */
/* %endif */
/* %if-c++-only */
			yyin.rdbuf(std::cin.rdbuf());
/* %endif */

		if ( ! yyout )
/* %if-c-only */
/* %endif */
/* %if-c++-only */
			yyout.rdbuf(std::cout.rdbuf());
/* %endif */

		if ( ! YY_CURRENT_BUFFER ) {
			yyensure_buffer_stack ();
			YY_CURRENT_BUFFER_LVALUE =
				yy_create_buffer( yyin, YY_BUF_SIZE );
		}

		yy_load_buffer_state(  );
		}

	{
/* %% [7.0] user's declarations go here */
#line 51 "BookshelfScanner.ll"


#line 54 "BookshelfScanner.ll"
 /* code to place at the beginning of yylex() */

    // reset location
    yylloc->step();


 /*** BEGIN EXAMPLE - Change the example lexer rules below ***/

#line 1024 "BookshelfScanner.cc"

	while ( /*CONSTCOND*/1 )		/* loops until end-of-file is reached */
		{
/* %% [8.0] yymore()-related code goes here */
		yy_cp = (yy_c_buf_p);

		/* Support of yytext. */
		*yy_cp = (yy_hold_char);

		/* yy_bp points to the position in yy_ch_buf of the start of
		 * the current run.
		 */
		yy_bp = yy_cp;

/* %% [9.0] code to set up and find next match goes here */
		yy_current_state = (yy_start);
yy_match:
		do
			{
			YY_CHAR yy_c = yy_ec[YY_SC_TO_UI(*yy_cp)] ;
			if ( yy_accept[yy_current_state] )
				{
				(yy_last_accepting_state) = yy_current_state;
				(yy_last_accepting_cpos) = yy_cp;
				}
			while ( yy_chk[yy_base[yy_current_state] + yy_c] != yy_current_state )
				{
				yy_current_state = (int) yy_def[yy_current_state];
				if ( yy_current_state >= 230 )
					yy_c = yy_meta[yy_c];
				}
			yy_current_state = yy_nxt[yy_base[yy_current_state] + yy_c];
			++yy_cp;
			}
		while ( yy_current_state != 229 );
		yy_cp = (yy_last_accepting_cpos);
		yy_current_state = (yy_last_accepting_state);

yy_find_action:
/* %% [10.0] code to find the action number goes here */
		yy_act = yy_accept[yy_current_state];

		YY_DO_BEFORE_ACTION;

/* %% [11.0] code for yylineno update goes here */

do_action:	/* This label is used only to access EOF actions. */

/* %% [12.0] debug code goes here */
		if ( yy_flex_debug )
			{
			if ( yy_act == 0 )
				std::cerr << "--scanner backing up\n";
			else if ( yy_act < 52 )
				std::cerr << "--accepting rule at line " << yy_rule_linenum[yy_act] <<
				         "(\"" << yytext << "\")\n";
			else if ( yy_act == 52 )
				std::cerr << "--accepting default rule (\"" << yytext << "\")\n";
			else if ( yy_act == 53 )
				std::cerr << "--(end of buffer or a NUL)\n";
			else
				std::cerr << "--EOF (start condition " << YY_START << ")\n";
			}

		switch ( yy_act )
	{ /* beginning of action switch */
/* %% [13.0] actions go here */
			case 0: /* must back up */
			/* undo the effects of YY_DO_BEFORE_ACTION */
			*yy_cp = (yy_hold_char);
			yy_cp = (yy_last_accepting_cpos);
			yy_current_state = (yy_last_accepting_state);
			goto yy_find_action;

case 1:
YY_RULE_SETUP
#line 62 "BookshelfScanner.ll"
{return token::KWD_NUMNETS;}
	YY_BREAK
case 2:
YY_RULE_SETUP
#line 63 "BookshelfScanner.ll"
{return token::KWD_NUMPINS;}
	YY_BREAK
case 3:
YY_RULE_SETUP
#line 64 "BookshelfScanner.ll"
{return token::KWD_NUMNODES;}
	YY_BREAK
case 4:
YY_RULE_SETUP
#line 65 "BookshelfScanner.ll"
{return token::KWD_NUMTERMINALS;}
	YY_BREAK
case 5:
YY_RULE_SETUP
#line 66 "BookshelfScanner.ll"
{return token::KWD_NUMROWS;}
	YY_BREAK
case 6:
YY_RULE_SETUP
#line 67 "BookshelfScanner.ll"
{return token::KWD_COREROW;}
	YY_BREAK
case 7:
YY_RULE_SETUP
#line 68 "BookshelfScanner.ll"
{return token::KWD_HORIZONTAL;}
	YY_BREAK
case 8:
YY_RULE_SETUP
#line 69 "BookshelfScanner.ll"
{return token::KWD_VERTICAL;}
	YY_BREAK
case 9:
YY_RULE_SETUP
#line 70 "BookshelfScanner.ll"
{return token::KWD_COORDINATE;}
	YY_BREAK
case 10:
YY_RULE_SETUP
#line 71 "BookshelfScanner.ll"
{return token::KWD_HEIGHT;}
	YY_BREAK
case 11:
YY_RULE_SETUP
#line 72 "BookshelfScanner.ll"
{return token::KWD_SITEWIDTH;}
	YY_BREAK
case 12:
YY_RULE_SETUP
#line 73 "BookshelfScanner.ll"
{return token::KWD_SITESPACING;}
	YY_BREAK
case 13:
YY_RULE_SETUP
#line 74 "BookshelfScanner.ll"
{return token::KWD_SITEORIENT;}
	YY_BREAK
case 14:
YY_RULE_SETUP
#line 75 "BookshelfScanner.ll"
{return token::KWD_SITESYMMETRY;}
	YY_BREAK
case 15:
YY_RULE_SETUP
#line 76 "BookshelfScanner.ll"
{return token::KWD_SUBROWORIGIN;}
	YY_BREAK
case 16:
YY_RULE_SETUP
#line 77 "BookshelfScanner.ll"
{return token::KWD_NUMSITES;}
	YY_BREAK
case 17:
YY_RULE_SETUP
#line 78 "BookshelfScanner.ll"
{return token::KWD_END;}
	YY_BREAK
case 18:
YY_RULE_SETUP
#line 79 "BookshelfScanner.ll"
{return token::KWD_TERMINAL;}
	YY_BREAK
case 19:
YY_RULE_SETUP
#line 80 "BookshelfScanner.ll"
{return token::KWD_UCLA;}
	YY_BREAK
case 20:
YY_RULE_SETUP
#line 81 "BookshelfScanner.ll"
{return token::KWD_NETDEGREE;}
	YY_BREAK
case 21:
YY_RULE_SETUP
#line 82 "BookshelfScanner.ll"
{return token::KWD_SCL;}
	YY_BREAK
case 22:
YY_RULE_SETUP
#line 83 "BookshelfScanner.ll"
{return token::KWD_NODES;}
	YY_BREAK
case 23:
YY_RULE_SETUP
#line 84 "BookshelfScanner.ll"
{return token::KWD_NETS;}
	YY_BREAK
case 24:
YY_RULE_SETUP
#line 85 "BookshelfScanner.ll"
{return token::KWD_PL;}
	YY_BREAK
case 25:
YY_RULE_SETUP
#line 86 "BookshelfScanner.ll"
{return token::KWD_WTS;}
	YY_BREAK
case 26:
YY_RULE_SETUP
#line 87 "BookshelfScanner.ll"
{return token::KWD_AUX;}
	YY_BREAK
case 27:
YY_RULE_SETUP
#line 88 "BookshelfScanner.ll"
{return token::KWD_FIXED;}
	YY_BREAK
case 28:
YY_RULE_SETUP
#line 89 "BookshelfScanner.ll"
{return token::KWD_FIXED_NI;}
	YY_BREAK
case 29:
YY_RULE_SETUP
#line 90 "BookshelfScanner.ll"
{return token::KWD_PLACED;}
	YY_BREAK
case 30:
YY_RULE_SETUP
#line 91 "BookshelfScanner.ll"
{return token::KWD_UNPLACED;}
	YY_BREAK
case 31:
YY_RULE_SETUP
#line 92 "BookshelfScanner.ll"
{return token::KWD_O;}
	YY_BREAK
case 32:
YY_RULE_SETUP
#line 93 "BookshelfScanner.ll"
{return token::KWD_I;}
	YY_BREAK
case 33:
YY_RULE_SETUP
#line 94 "BookshelfScanner.ll"
{return token::KWD_B;}
	YY_BREAK
case 34:
YY_RULE_SETUP
#line 95 "BookshelfScanner.ll"
{return token::KWD_N;}
	YY_BREAK
case 35:
YY_RULE_SETUP
#line 96 "BookshelfScanner.ll"
{return token::KWD_S;}
	YY_BREAK
case 36:
YY_RULE_SETUP
#line 97 "BookshelfScanner.ll"
{return token::KWD_W;}
	YY_BREAK
case 37:
YY_RULE_SETUP
#line 98 "BookshelfScanner.ll"
{return token::KWD_E;}
	YY_BREAK
case 38:
YY_RULE_SETUP
#line 99 "BookshelfScanner.ll"
{return token::KWD_FN;}
	YY_BREAK
case 39:
YY_RULE_SETUP
#line 100 "BookshelfScanner.ll"
{return token::KWD_FS;}
	YY_BREAK
case 40:
YY_RULE_SETUP
#line 101 "BookshelfScanner.ll"
{return token::KWD_FW;}
	YY_BREAK
case 41:
YY_RULE_SETUP
#line 102 "BookshelfScanner.ll"
{return token::KWD_FE;}
	YY_BREAK
case 42:
YY_RULE_SETUP
#line 105 "BookshelfScanner.ll"
{
    yylval->integerVal = atoi(yytext);
    return token::INTEGER;
}
	YY_BREAK
case 43:
YY_RULE_SETUP
#line 110 "BookshelfScanner.ll"
{
    yylval->doubleVal = atof(yytext);
    return token::DOUBLE;
}
	YY_BREAK
case 44:
YY_RULE_SETUP
#line 115 "BookshelfScanner.ll"
{
    yylval->stringVal = new std::string(yytext, yyleng);
    return token::BINARY;
}
	YY_BREAK
case 45:
YY_RULE_SETUP
#line 120 "BookshelfScanner.ll"
{
    yylval->stringVal = new std::string(yytext, yyleng);
    return token::STRING;
}
	YY_BREAK
case 46:
/* rule 46 can match eol */
YY_RULE_SETUP
#line 125 "BookshelfScanner.ll"
{
    yylval->quoteVal = new std::string(yytext+1, yyleng-2);
    return token::QUOTE;
}
	YY_BREAK
case 47:
YY_RULE_SETUP
#line 130 "BookshelfScanner.ll"
{
    yylval->stringVal = new std::string(yytext, yyleng);
    return token::PATH;
}
	YY_BREAK
/* gobble up comments */
case 48:
YY_RULE_SETUP
#line 136 "BookshelfScanner.ll"
{
    yylloc->step();
}
	YY_BREAK
/* gobble up white-spaces */
case 49:
YY_RULE_SETUP
#line 141 "BookshelfScanner.ll"
{
    yylloc->step();
}
	YY_BREAK
/* gobble up end-of-lines */
case 50:
/* rule 50 can match eol */
YY_RULE_SETUP
#line 146 "BookshelfScanner.ll"
{
    yylloc->lines(yyleng); yylloc->step();
     return token::EOL; 
}
	YY_BREAK
/* pass all other characters up to bison */
case 51:
YY_RULE_SETUP
#line 152 "BookshelfScanner.ll"
{
    return static_cast<token_type>(*yytext);
}
	YY_BREAK
/*** END EXAMPLE - Change the example lexer rules above ***/
case 52:
YY_RULE_SETUP
#line 158 "BookshelfScanner.ll"
ECHO;
	YY_BREAK
#line 1393 "BookshelfScanner.cc"
case YY_STATE_EOF(INITIAL):
	yyterminate();

	case YY_END_OF_BUFFER:
		{
		/* Amount of text matched not including the EOB char. */
		int yy_amount_of_matched_text = (int) (yy_cp - (yytext_ptr)) - 1;

		/* Undo the effects of YY_DO_BEFORE_ACTION. */
		*yy_cp = (yy_hold_char);
		YY_RESTORE_YY_MORE_OFFSET

		if ( YY_CURRENT_BUFFER_LVALUE->yy_buffer_status == YY_BUFFER_NEW )
			{
			/* We're scanning a new file or input source.  It's
			 * possible that this happened because the user
			 * just pointed yyin at a new source and called
			 * yylex().  If so, then we have to assure
			 * consistency between YY_CURRENT_BUFFER and our
			 * globals.  Here is the right place to do so, because
			 * this is the first action (other than possibly a
			 * back-up) that will match for the new input source.
			 */
			(yy_n_chars) = YY_CURRENT_BUFFER_LVALUE->yy_n_chars;
/* %if-c-only */
/* %endif */
/* %if-c++-only */
			YY_CURRENT_BUFFER_LVALUE->yy_input_file = yyin.rdbuf();
/* %endif */
			YY_CURRENT_BUFFER_LVALUE->yy_buffer_status = YY_BUFFER_NORMAL;
			}

		/* Note that here we test for yy_c_buf_p "<=" to the position
		 * of the first EOB in the buffer, since yy_c_buf_p will
		 * already have been incremented past the NUL character
		 * (since all states make transitions on EOB to the
		 * end-of-buffer state).  Contrast this with the test
		 * in input().
		 */
		if ( (yy_c_buf_p) <= &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars)] )
			{ /* This was really a NUL. */
			yy_state_type yy_next_state;

			(yy_c_buf_p) = (yytext_ptr) + yy_amount_of_matched_text;

			yy_current_state = yy_get_previous_state(  );

			/* Okay, we're now positioned to make the NUL
			 * transition.  We couldn't have
			 * yy_get_previous_state() go ahead and do it
			 * for us because it doesn't know how to deal
			 * with the possibility of jamming (and we don't
			 * want to build jamming into it because then it
			 * will run more slowly).
			 */

			yy_next_state = yy_try_NUL_trans( yy_current_state );

			yy_bp = (yytext_ptr) + YY_MORE_ADJ;

			if ( yy_next_state )
				{
				/* Consume the NUL. */
				yy_cp = ++(yy_c_buf_p);
				yy_current_state = yy_next_state;
				goto yy_match;
				}

			else
				{
/* %% [14.0] code to do back-up for compressed tables and set up yy_cp goes here */
				yy_cp = (yy_last_accepting_cpos);
				yy_current_state = (yy_last_accepting_state);
				goto yy_find_action;
				}
			}

		else switch ( yy_get_next_buffer(  ) )
			{
			case EOB_ACT_END_OF_FILE:
				{
				(yy_did_buffer_switch_on_eof) = 0;

				if ( yywrap(  ) )
					{
					/* Note: because we've taken care in
					 * yy_get_next_buffer() to have set up
					 * yytext, we can now set up
					 * yy_c_buf_p so that if some total
					 * hoser (like flex itself) wants to
					 * call the scanner after we return the
					 * YY_NULL, it'll still work - another
					 * YY_NULL will get returned.
					 */
					(yy_c_buf_p) = (yytext_ptr) + YY_MORE_ADJ;

					yy_act = YY_STATE_EOF(YY_START);
					goto do_action;
					}

				else
					{
					if ( ! (yy_did_buffer_switch_on_eof) )
						YY_NEW_FILE;
					}
				break;
				}

			case EOB_ACT_CONTINUE_SCAN:
				(yy_c_buf_p) =
					(yytext_ptr) + yy_amount_of_matched_text;

				yy_current_state = yy_get_previous_state(  );

				yy_cp = (yy_c_buf_p);
				yy_bp = (yytext_ptr) + YY_MORE_ADJ;
				goto yy_match;

			case EOB_ACT_LAST_MATCH:
				(yy_c_buf_p) =
				&YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars)];

				yy_current_state = yy_get_previous_state(  );

				yy_cp = (yy_c_buf_p);
				yy_bp = (yytext_ptr) + YY_MORE_ADJ;
				goto yy_find_action;
			}
		break;
		}

	default:
		YY_FATAL_ERROR(
			"fatal flex scanner internal error--no action found" );
	} /* end of action switch */
		} /* end of scanning one token */
	} /* end of user's declarations */
} /* end of yylex */
/* %ok-for-header */

/* %if-c++-only */
/* %not-for-header */
/* The contents of this function are C++ specific, so the () macro is not used.
 * This constructor simply maintains backward compatibility.
 * DEPRECATED
 */
yyFlexLexer::yyFlexLexer( std::istream* arg_yyin, std::ostream* arg_yyout ):
	yyin(arg_yyin ? arg_yyin->rdbuf() : std::cin.rdbuf()),
	yyout(arg_yyout ? arg_yyout->rdbuf() : std::cout.rdbuf())
{
	ctor_common();
}

/* The contents of this function are C++ specific, so the () macro is not used.
 */
yyFlexLexer::yyFlexLexer( std::istream& arg_yyin, std::ostream& arg_yyout ):
	yyin(arg_yyin.rdbuf()),
	yyout(arg_yyout.rdbuf())
{
	ctor_common();
}

/* The contents of this function are C++ specific, so the () macro is not used.
 */
void yyFlexLexer::ctor_common()
{
	yy_c_buf_p = 0;
	yy_init = 0;
	yy_start = 0;
	yy_flex_debug = 0;
	yylineno = 1;	// this will only get updated if %option yylineno

	yy_did_buffer_switch_on_eof = 0;

	yy_looking_for_trail_begin = 0;
	yy_more_flag = 0;
	yy_more_len = 0;
	yy_more_offset = yy_prev_more_offset = 0;

	yy_start_stack_ptr = yy_start_stack_depth = 0;
	yy_start_stack = NULL;

	yy_buffer_stack = NULL;
	yy_buffer_stack_top = 0;
	yy_buffer_stack_max = 0;

	yy_state_buf = 0;

}

/* The contents of this function are C++ specific, so the () macro is not used.
 */
yyFlexLexer::~yyFlexLexer()
{
	delete [] yy_state_buf;
	yyfree( yy_start_stack  );
	yy_delete_buffer( YY_CURRENT_BUFFER );
	yyfree( yy_buffer_stack  );
}

/* The contents of this function are C++ specific, so the () macro is not used.
 */
void yyFlexLexer::switch_streams( std::istream& new_in, std::ostream& new_out )
{
	// was if( new_in )
	yy_delete_buffer( YY_CURRENT_BUFFER );
	yy_switch_to_buffer( yy_create_buffer( new_in, YY_BUF_SIZE  ) );

	// was if( new_out )
	yyout.rdbuf(new_out.rdbuf());
}

/* The contents of this function are C++ specific, so the () macro is not used.
 */
void yyFlexLexer::switch_streams( std::istream* new_in, std::ostream* new_out )
{
	if( ! new_in ) {
		new_in = &yyin;
	}

	if ( ! new_out ) {
		new_out = &yyout;
	}

	switch_streams(*new_in, *new_out);
}

#ifdef YY_INTERACTIVE
int yyFlexLexer::LexerInput( char* buf, int /* max_size */ )
#else
int yyFlexLexer::LexerInput( char* buf, int max_size )
#endif
{
	if ( yyin.eof() || yyin.fail() )
		return 0;

#ifdef YY_INTERACTIVE
	yyin.get( buf[0] );

	if ( yyin.eof() )
		return 0;

	if ( yyin.bad() )
		return -1;

	return 1;

#else
	(void) yyin.read( buf, max_size );

	if ( yyin.bad() )
		return -1;
	else
		return yyin.gcount();
#endif
}

void yyFlexLexer::LexerOutput( const char* buf, int size )
{
	(void) yyout.write( buf, size );
}
/* %ok-for-header */

/* %endif */

/* yy_get_next_buffer - try to read in a new buffer
 *
 * Returns a code representing an action:
 *	EOB_ACT_LAST_MATCH -
 *	EOB_ACT_CONTINUE_SCAN - continue scanning from current position
 *	EOB_ACT_END_OF_FILE - end of file
 */
/* %if-c-only */
/* %endif */
/* %if-c++-only */
int yyFlexLexer::yy_get_next_buffer()
/* %endif */
{
    	char *dest = YY_CURRENT_BUFFER_LVALUE->yy_ch_buf;
	char *source = (yytext_ptr);
	int number_to_move, i;
	int ret_val;

	if ( (yy_c_buf_p) > &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars) + 1] )
		YY_FATAL_ERROR(
		"fatal flex scanner internal error--end of buffer missed" );

	if ( YY_CURRENT_BUFFER_LVALUE->yy_fill_buffer == 0 )
		{ /* Don't try to fill the buffer, so this is an EOF. */
		if ( (yy_c_buf_p) - (yytext_ptr) - YY_MORE_ADJ == 1 )
			{
			/* We matched a single character, the EOB, so
			 * treat this as a final EOF.
			 */
			return EOB_ACT_END_OF_FILE;
			}

		else
			{
			/* We matched some text prior to the EOB, first
			 * process it.
			 */
			return EOB_ACT_LAST_MATCH;
			}
		}

	/* Try to read more data. */

	/* First move last chars to start of buffer. */
	number_to_move = (int) ((yy_c_buf_p) - (yytext_ptr) - 1);

	for ( i = 0; i < number_to_move; ++i )
		*(dest++) = *(source++);

	if ( YY_CURRENT_BUFFER_LVALUE->yy_buffer_status == YY_BUFFER_EOF_PENDING )
		/* don't do the read, it's not guaranteed to return an EOF,
		 * just force an EOF
		 */
		YY_CURRENT_BUFFER_LVALUE->yy_n_chars = (yy_n_chars) = 0;

	else
		{
			int num_to_read =
			YY_CURRENT_BUFFER_LVALUE->yy_buf_size - number_to_move - 1;

		while ( num_to_read <= 0 )
			{ /* Not enough room in the buffer - grow it. */

			/* just a shorter name for the current buffer */
			YY_BUFFER_STATE b = YY_CURRENT_BUFFER_LVALUE;

			int yy_c_buf_p_offset =
				(int) ((yy_c_buf_p) - b->yy_ch_buf);

			if ( b->yy_is_our_buffer )
				{
				int new_size = b->yy_buf_size * 2;

				if ( new_size <= 0 )
					b->yy_buf_size += b->yy_buf_size / 8;
				else
					b->yy_buf_size *= 2;

				b->yy_ch_buf = (char *)
					/* Include room in for 2 EOB chars. */
					yyrealloc( (void *) b->yy_ch_buf,
							 (yy_size_t) (b->yy_buf_size + 2)  );
				}
			else
				/* Can't grow it, we don't own it. */
				b->yy_ch_buf = NULL;

			if ( ! b->yy_ch_buf )
				YY_FATAL_ERROR(
				"fatal error - scanner input buffer overflow" );

			(yy_c_buf_p) = &b->yy_ch_buf[yy_c_buf_p_offset];

			num_to_read = YY_CURRENT_BUFFER_LVALUE->yy_buf_size -
						number_to_move - 1;

			}

		if ( num_to_read > YY_READ_BUF_SIZE )
			num_to_read = YY_READ_BUF_SIZE;

		/* Read in more data. */
		YY_INPUT( (&YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[number_to_move]),
			(yy_n_chars), num_to_read );

		YY_CURRENT_BUFFER_LVALUE->yy_n_chars = (yy_n_chars);
		}

	if ( (yy_n_chars) == 0 )
		{
		if ( number_to_move == YY_MORE_ADJ )
			{
			ret_val = EOB_ACT_END_OF_FILE;
			yyrestart( yyin  );
			}

		else
			{
			ret_val = EOB_ACT_LAST_MATCH;
			YY_CURRENT_BUFFER_LVALUE->yy_buffer_status =
				YY_BUFFER_EOF_PENDING;
			}
		}

	else
		ret_val = EOB_ACT_CONTINUE_SCAN;

	if (((yy_n_chars) + number_to_move) > YY_CURRENT_BUFFER_LVALUE->yy_buf_size) {
		/* Extend the array by 50%, plus the number we really need. */
		int new_size = (yy_n_chars) + number_to_move + ((yy_n_chars) >> 1);
		YY_CURRENT_BUFFER_LVALUE->yy_ch_buf = (char *) yyrealloc(
			(void *) YY_CURRENT_BUFFER_LVALUE->yy_ch_buf, (yy_size_t) new_size  );
		if ( ! YY_CURRENT_BUFFER_LVALUE->yy_ch_buf )
			YY_FATAL_ERROR( "out of dynamic memory in yy_get_next_buffer()" );
		/* "- 2" to take care of EOB's */
		YY_CURRENT_BUFFER_LVALUE->yy_buf_size = (int) (new_size - 2);
	}

	(yy_n_chars) += number_to_move;
	YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars)] = YY_END_OF_BUFFER_CHAR;
	YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars) + 1] = YY_END_OF_BUFFER_CHAR;

	(yytext_ptr) = &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[0];

	return ret_val;
}

/* yy_get_previous_state - get the state just before the EOB char was reached */

/* %if-c-only */
/* %not-for-header */
/* %endif */
/* %if-c++-only */
    yy_state_type yyFlexLexer::yy_get_previous_state()
/* %endif */
{
	yy_state_type yy_current_state;
	char *yy_cp;
    
/* %% [15.0] code to get the start state into yy_current_state goes here */
	yy_current_state = (yy_start);

	for ( yy_cp = (yytext_ptr) + YY_MORE_ADJ; yy_cp < (yy_c_buf_p); ++yy_cp )
		{
/* %% [16.0] code to find the next state goes here */
		YY_CHAR yy_c = (*yy_cp ? yy_ec[YY_SC_TO_UI(*yy_cp)] : 1);
		if ( yy_accept[yy_current_state] )
			{
			(yy_last_accepting_state) = yy_current_state;
			(yy_last_accepting_cpos) = yy_cp;
			}
		while ( yy_chk[yy_base[yy_current_state] + yy_c] != yy_current_state )
			{
			yy_current_state = (int) yy_def[yy_current_state];
			if ( yy_current_state >= 230 )
				yy_c = yy_meta[yy_c];
			}
		yy_current_state = yy_nxt[yy_base[yy_current_state] + yy_c];
		}

	return yy_current_state;
}

/* yy_try_NUL_trans - try to make a transition on the NUL character
 *
 * synopsis
 *	next_state = yy_try_NUL_trans( current_state );
 */
/* %if-c-only */
/* %endif */
/* %if-c++-only */
    yy_state_type yyFlexLexer::yy_try_NUL_trans( yy_state_type yy_current_state )
/* %endif */
{
	int yy_is_jam;
    /* %% [17.0] code to find the next state, and perhaps do backing up, goes here */
	char *yy_cp = (yy_c_buf_p);

	YY_CHAR yy_c = 1;
	if ( yy_accept[yy_current_state] )
		{
		(yy_last_accepting_state) = yy_current_state;
		(yy_last_accepting_cpos) = yy_cp;
		}
	while ( yy_chk[yy_base[yy_current_state] + yy_c] != yy_current_state )
		{
		yy_current_state = (int) yy_def[yy_current_state];
		if ( yy_current_state >= 230 )
			yy_c = yy_meta[yy_c];
		}
	yy_current_state = yy_nxt[yy_base[yy_current_state] + yy_c];
	yy_is_jam = (yy_current_state == 229);

		return yy_is_jam ? 0 : yy_current_state;
}

#ifndef YY_NO_UNPUT
/* %if-c-only */
/* %endif */
/* %if-c++-only */
    void yyFlexLexer::yyunput( int c, char* yy_bp)
/* %endif */
{
	char *yy_cp;
    
    yy_cp = (yy_c_buf_p);

	/* undo effects of setting up yytext */
	*yy_cp = (yy_hold_char);

	if ( yy_cp < YY_CURRENT_BUFFER_LVALUE->yy_ch_buf + 2 )
		{ /* need to shift things up to make room */
		/* +2 for EOB chars. */
		int number_to_move = (yy_n_chars) + 2;
		char *dest = &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[
					YY_CURRENT_BUFFER_LVALUE->yy_buf_size + 2];
		char *source =
				&YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[number_to_move];

		while ( source > YY_CURRENT_BUFFER_LVALUE->yy_ch_buf )
			*--dest = *--source;

		yy_cp += (int) (dest - source);
		yy_bp += (int) (dest - source);
		YY_CURRENT_BUFFER_LVALUE->yy_n_chars =
			(yy_n_chars) = (int) YY_CURRENT_BUFFER_LVALUE->yy_buf_size;

		if ( yy_cp < YY_CURRENT_BUFFER_LVALUE->yy_ch_buf + 2 )
			YY_FATAL_ERROR( "flex scanner push-back overflow" );
		}

	*--yy_cp = (char) c;

/* %% [18.0] update yylineno here */

	(yytext_ptr) = yy_bp;
	(yy_hold_char) = *yy_cp;
	(yy_c_buf_p) = yy_cp;
}
/* %if-c-only */
/* %endif */
#endif

/* %if-c-only */
/* %endif */
/* %if-c++-only */
    int yyFlexLexer::yyinput()
/* %endif */
{
	int c;
    
	*(yy_c_buf_p) = (yy_hold_char);

	if ( *(yy_c_buf_p) == YY_END_OF_BUFFER_CHAR )
		{
		/* yy_c_buf_p now points to the character we want to return.
		 * If this occurs *before* the EOB characters, then it's a
		 * valid NUL; if not, then we've hit the end of the buffer.
		 */
		if ( (yy_c_buf_p) < &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars)] )
			/* This was really a NUL. */
			*(yy_c_buf_p) = '\0';

		else
			{ /* need more input */
			int offset = (int) ((yy_c_buf_p) - (yytext_ptr));
			++(yy_c_buf_p);

			switch ( yy_get_next_buffer(  ) )
				{
				case EOB_ACT_LAST_MATCH:
					/* This happens because yy_g_n_b()
					 * sees that we've accumulated a
					 * token and flags that we need to
					 * try matching the token before
					 * proceeding.  But for input(),
					 * there's no matching to consider.
					 * So convert the EOB_ACT_LAST_MATCH
					 * to EOB_ACT_END_OF_FILE.
					 */

					/* Reset buffer status. */
					yyrestart( yyin );

					/*FALLTHROUGH*/

				case EOB_ACT_END_OF_FILE:
					{
					if ( yywrap(  ) )
						return 0;

					if ( ! (yy_did_buffer_switch_on_eof) )
						YY_NEW_FILE;
#ifdef __cplusplus
					return yyinput();
#else
					return input();
#endif
					}

				case EOB_ACT_CONTINUE_SCAN:
					(yy_c_buf_p) = (yytext_ptr) + offset;
					break;
				}
			}
		}

	c = *(unsigned char *) (yy_c_buf_p);	/* cast for 8-bit char's */
	*(yy_c_buf_p) = '\0';	/* preserve yytext */
	(yy_hold_char) = *++(yy_c_buf_p);

/* %% [19.0] update BOL and yylineno */

	return c;
}
/* %if-c-only */
/* %endif */

/** Immediately switch to a different input stream.
 * @param input_file A readable stream.
 * 
 * @note This function does not reset the start condition to @c INITIAL .
 */
/* %if-c-only */
/* %endif */
/* %if-c++-only */
    void yyFlexLexer::yyrestart( std::istream& input_file )
/* %endif */
{
    
	if ( ! YY_CURRENT_BUFFER ){
        yyensure_buffer_stack ();
		YY_CURRENT_BUFFER_LVALUE =
            yy_create_buffer( yyin, YY_BUF_SIZE );
	}

	yy_init_buffer( YY_CURRENT_BUFFER, input_file );
	yy_load_buffer_state(  );
}

/* %if-c++-only */
/** Delegate to the new version that takes an istream reference.
 * @param input_file A readable stream.
 * 
 * @note This function does not reset the start condition to @c INITIAL .
 */
void yyFlexLexer::yyrestart( std::istream* input_file )
{
	if( ! input_file ) {
		input_file = &yyin;
	}
	yyrestart( *input_file );
}
/* %endif */

/** Switch to a different input buffer.
 * @param new_buffer The new input buffer.
 * 
 */
/* %if-c-only */
/* %endif */
/* %if-c++-only */
    void yyFlexLexer::yy_switch_to_buffer( YY_BUFFER_STATE new_buffer )
/* %endif */
{
    
	/* TODO. We should be able to replace this entire function body
	 * with
	 *		yypop_buffer_state();
	 *		yypush_buffer_state(new_buffer);
     */
	yyensure_buffer_stack ();
	if ( YY_CURRENT_BUFFER == new_buffer )
		return;

	if ( YY_CURRENT_BUFFER )
		{
		/* Flush out information for old buffer. */
		*(yy_c_buf_p) = (yy_hold_char);
		YY_CURRENT_BUFFER_LVALUE->yy_buf_pos = (yy_c_buf_p);
		YY_CURRENT_BUFFER_LVALUE->yy_n_chars = (yy_n_chars);
		}

	YY_CURRENT_BUFFER_LVALUE = new_buffer;
	yy_load_buffer_state(  );

	/* We don't actually know whether we did this switch during
	 * EOF (yywrap()) processing, but the only time this flag
	 * is looked at is after yywrap() is called, so it's safe
	 * to go ahead and always set it.
	 */
	(yy_did_buffer_switch_on_eof) = 1;
}

/* %if-c-only */
/* %endif */
/* %if-c++-only */
    void yyFlexLexer::yy_load_buffer_state()
/* %endif */
{
    	(yy_n_chars) = YY_CURRENT_BUFFER_LVALUE->yy_n_chars;
	(yytext_ptr) = (yy_c_buf_p) = YY_CURRENT_BUFFER_LVALUE->yy_buf_pos;
/* %if-c-only */
/* %endif */
/* %if-c++-only */
	yyin.rdbuf(YY_CURRENT_BUFFER_LVALUE->yy_input_file);
/* %endif */
	(yy_hold_char) = *(yy_c_buf_p);
}

/** Allocate and initialize an input buffer state.
 * @param file A readable stream.
 * @param size The character buffer size in bytes. When in doubt, use @c YY_BUF_SIZE.
 * 
 * @return the allocated buffer state.
 */
/* %if-c-only */
/* %endif */
/* %if-c++-only */
    YY_BUFFER_STATE yyFlexLexer::yy_create_buffer( std::istream& file, int size )
/* %endif */
{
	YY_BUFFER_STATE b;
    
	b = (YY_BUFFER_STATE) yyalloc( sizeof( struct yy_buffer_state )  );
	if ( ! b )
		YY_FATAL_ERROR( "out of dynamic memory in yy_create_buffer()" );

	b->yy_buf_size = size;

	/* yy_ch_buf has to be 2 characters longer than the size given because
	 * we need to put in 2 end-of-buffer characters.
	 */
	b->yy_ch_buf = (char *) yyalloc( (yy_size_t) (b->yy_buf_size + 2)  );
	if ( ! b->yy_ch_buf )
		YY_FATAL_ERROR( "out of dynamic memory in yy_create_buffer()" );

	b->yy_is_our_buffer = 1;

	yy_init_buffer( b, file );

	return b;
}

/* %if-c++-only */
/** Delegate creation of buffers to the new version that takes an istream reference.
 * @param file A readable stream.
 * @param size The character buffer size in bytes. When in doubt, use @c YY_BUF_SIZE.
 * 
 * @return the allocated buffer state.
 */
	YY_BUFFER_STATE yyFlexLexer::yy_create_buffer( std::istream* file, int size )
{
	return yy_create_buffer( *file, size );
}
/* %endif */

/** Destroy the buffer.
 * @param b a buffer created with yy_create_buffer()
 * 
 */
/* %if-c-only */
/* %endif */
/* %if-c++-only */
    void yyFlexLexer::yy_delete_buffer( YY_BUFFER_STATE b )
/* %endif */
{
    
	if ( ! b )
		return;

	if ( b == YY_CURRENT_BUFFER ) /* Not sure if we should pop here. */
		YY_CURRENT_BUFFER_LVALUE = (YY_BUFFER_STATE) 0;

	if ( b->yy_is_our_buffer )
		yyfree( (void *) b->yy_ch_buf  );

	yyfree( (void *) b  );
}

/* Initializes or reinitializes a buffer.
 * This function is sometimes called more than once on the same buffer,
 * such as during a yyrestart() or at EOF.
 */
/* %if-c-only */
/* %endif */
/* %if-c++-only */
    void yyFlexLexer::yy_init_buffer( YY_BUFFER_STATE b, std::istream& file )
/* %endif */

{
	int oerrno = errno;
    
	yy_flush_buffer( b );

/* %if-c-only */
/* %endif */
/* %if-c++-only */
	b->yy_input_file = file.rdbuf();
/* %endif */
	b->yy_fill_buffer = 1;

    /* If b is the current buffer, then yy_init_buffer was _probably_
     * called from yyrestart() or through yy_get_next_buffer.
     * In that case, we don't want to reset the lineno or column.
     */
    if (b != YY_CURRENT_BUFFER){
        b->yy_bs_lineno = 1;
        b->yy_bs_column = 0;
    }

/* %if-c-only */
/* %endif */
/* %if-c++-only */
	b->yy_is_interactive = 0;
/* %endif */
	errno = oerrno;
}

/** Discard all buffered characters. On the next scan, YY_INPUT will be called.
 * @param b the buffer state to be flushed, usually @c YY_CURRENT_BUFFER.
 * 
 */
/* %if-c-only */
/* %endif */
/* %if-c++-only */
    void yyFlexLexer::yy_flush_buffer( YY_BUFFER_STATE b )
/* %endif */
{
    	if ( ! b )
		return;

	b->yy_n_chars = 0;

	/* We always need two end-of-buffer characters.  The first causes
	 * a transition to the end-of-buffer state.  The second causes
	 * a jam in that state.
	 */
	b->yy_ch_buf[0] = YY_END_OF_BUFFER_CHAR;
	b->yy_ch_buf[1] = YY_END_OF_BUFFER_CHAR;

	b->yy_buf_pos = &b->yy_ch_buf[0];

	b->yy_at_bol = 1;
	b->yy_buffer_status = YY_BUFFER_NEW;

	if ( b == YY_CURRENT_BUFFER )
		yy_load_buffer_state(  );
}

/* %if-c-or-c++ */
/** Pushes the new state onto the stack. The new state becomes
 *  the current state. This function will allocate the stack
 *  if necessary.
 *  @param new_buffer The new state.
 *  
 */
/* %if-c-only */
/* %endif */
/* %if-c++-only */
void yyFlexLexer::yypush_buffer_state (YY_BUFFER_STATE new_buffer)
/* %endif */
{
    	if (new_buffer == NULL)
		return;

	yyensure_buffer_stack();

	/* This block is copied from yy_switch_to_buffer. */
	if ( YY_CURRENT_BUFFER )
		{
		/* Flush out information for old buffer. */
		*(yy_c_buf_p) = (yy_hold_char);
		YY_CURRENT_BUFFER_LVALUE->yy_buf_pos = (yy_c_buf_p);
		YY_CURRENT_BUFFER_LVALUE->yy_n_chars = (yy_n_chars);
		}

	/* Only push if top exists. Otherwise, replace top. */
	if (YY_CURRENT_BUFFER)
		(yy_buffer_stack_top)++;
	YY_CURRENT_BUFFER_LVALUE = new_buffer;

	/* copied from yy_switch_to_buffer. */
	yy_load_buffer_state(  );
	(yy_did_buffer_switch_on_eof) = 1;
}
/* %endif */

/* %if-c-or-c++ */
/** Removes and deletes the top of the stack, if present.
 *  The next element becomes the new top.
 *  
 */
/* %if-c-only */
/* %endif */
/* %if-c++-only */
void yyFlexLexer::yypop_buffer_state (void)
/* %endif */
{
    	if (!YY_CURRENT_BUFFER)
		return;

	yy_delete_buffer(YY_CURRENT_BUFFER );
	YY_CURRENT_BUFFER_LVALUE = NULL;
	if ((yy_buffer_stack_top) > 0)
		--(yy_buffer_stack_top);

	if (YY_CURRENT_BUFFER) {
		yy_load_buffer_state(  );
		(yy_did_buffer_switch_on_eof) = 1;
	}
}
/* %endif */

/* %if-c-or-c++ */
/* Allocates the stack if it does not exist.
 *  Guarantees space for at least one push.
 */
/* %if-c-only */
/* %endif */
/* %if-c++-only */
void yyFlexLexer::yyensure_buffer_stack(void)
/* %endif */
{
	yy_size_t num_to_alloc;
    
	if (!(yy_buffer_stack)) {

		/* First allocation is just for 2 elements, since we don't know if this
		 * scanner will even need a stack. We use 2 instead of 1 to avoid an
		 * immediate realloc on the next call.
         */
      num_to_alloc = 1; /* After all that talk, this was set to 1 anyways... */
		(yy_buffer_stack) = (struct yy_buffer_state**)yyalloc
								(num_to_alloc * sizeof(struct yy_buffer_state*)
								);
		if ( ! (yy_buffer_stack) )
			YY_FATAL_ERROR( "out of dynamic memory in yyensure_buffer_stack()" );

		memset((yy_buffer_stack), 0, num_to_alloc * sizeof(struct yy_buffer_state*));

		(yy_buffer_stack_max) = num_to_alloc;
		(yy_buffer_stack_top) = 0;
		return;
	}

	if ((yy_buffer_stack_top) >= ((yy_buffer_stack_max)) - 1){

		/* Increase the buffer to prepare for a possible push. */
		yy_size_t grow_size = 8 /* arbitrary grow size */;

		num_to_alloc = (yy_buffer_stack_max) + grow_size;
		(yy_buffer_stack) = (struct yy_buffer_state**)yyrealloc
								((yy_buffer_stack),
								num_to_alloc * sizeof(struct yy_buffer_state*)
								);
		if ( ! (yy_buffer_stack) )
			YY_FATAL_ERROR( "out of dynamic memory in yyensure_buffer_stack()" );

		/* zero only the new slots.*/
		memset((yy_buffer_stack) + (yy_buffer_stack_max), 0, grow_size * sizeof(struct yy_buffer_state*));
		(yy_buffer_stack_max) = num_to_alloc;
	}
}
/* %endif */

/* %if-c-only */
/* %endif */

/* %if-c-only */
/* %endif */

/* %if-c-only */
/* %endif */

/* %if-c-only */
/* %endif */
/* %if-c++-only */
    void yyFlexLexer::yy_push_state( int _new_state )
/* %endif */
{
    	if ( (yy_start_stack_ptr) >= (yy_start_stack_depth) )
		{
		yy_size_t new_size;

		(yy_start_stack_depth) += YY_START_STACK_INCR;
		new_size = (yy_size_t) (yy_start_stack_depth) * sizeof( int );

		if ( ! (yy_start_stack) )
			(yy_start_stack) = (int *) yyalloc( new_size  );

		else
			(yy_start_stack) = (int *) yyrealloc(
					(void *) (yy_start_stack), new_size  );

		if ( ! (yy_start_stack) )
			YY_FATAL_ERROR( "out of memory expanding start-condition stack" );
		}

	(yy_start_stack)[(yy_start_stack_ptr)++] = YY_START;

	BEGIN(_new_state);
}

/* %if-c-only */
/* %endif */
/* %if-c++-only */
    void yyFlexLexer::yy_pop_state()
/* %endif */
{
    	if ( --(yy_start_stack_ptr) < 0 )
		YY_FATAL_ERROR( "start-condition stack underflow" );

	BEGIN((yy_start_stack)[(yy_start_stack_ptr)]);
}

/* %if-c-only */
/* %endif */
/* %if-c++-only */
    int yyFlexLexer::yy_top_state()
/* %endif */
{
    	return (yy_start_stack)[(yy_start_stack_ptr) - 1];
}

#ifndef YY_EXIT_FAILURE
#define YY_EXIT_FAILURE 2
#endif

/* %if-c-only */
/* %endif */
/* %if-c++-only */
void yyFlexLexer::LexerError( const char* msg )
{
    	std::cerr << msg << std::endl;
	exit( YY_EXIT_FAILURE );
}
/* %endif */

/* Redefine yyless() so it works in section 3 code. */

#undef yyless
#define yyless(n) \
	do \
		{ \
		/* Undo effects of setting up yytext. */ \
        int yyless_macro_arg = (n); \
        YY_LESS_LINENO(yyless_macro_arg);\
		yytext[yyleng] = (yy_hold_char); \
		(yy_c_buf_p) = yytext + yyless_macro_arg; \
		(yy_hold_char) = *(yy_c_buf_p); \
		*(yy_c_buf_p) = '\0'; \
		yyleng = yyless_macro_arg; \
		} \
	while ( 0 )

/* Accessor  methods (get/set functions) to struct members. */

/* %if-c-only */
/* %if-reentrant */
/* %endif */
/* %if-reentrant */
/* %endif */
/* %endif */

/* %if-reentrant */
/* %if-bison-bridge */
/* %endif */
/* %endif if-c-only */

/* %if-c-only */
/* %endif */

/* %if-c-only SNIP! this currently causes conflicts with the c++ scanner */
/* %if-reentrant */
/* %endif */
/* %endif */

/*
 * Internal utility routines.
 */

#ifndef yytext_ptr
static void yy_flex_strncpy (char* s1, const char * s2, int n )
{
		
	int i;
	for ( i = 0; i < n; ++i )
		s1[i] = s2[i];
}
#endif

#ifdef YY_NEED_STRLEN
static int yy_flex_strlen (const char * s )
{
	int n;
	for ( n = 0; s[n]; ++n )
		;

	return n;
}
#endif

void *yyalloc (yy_size_t  size )
{
			return malloc(size);
}

void *yyrealloc  (void * ptr, yy_size_t  size )
{
		
	/* The cast to (char *) in the following accommodates both
	 * implementations that use char* generic pointers, and those
	 * that use void* generic pointers.  It works with the latter
	 * because both ANSI C and C++ allow castless assignment from
	 * any pointer type to void*, and deal with argument conversions
	 * as though doing an assignment.
	 */
	return realloc(ptr, size);
}

void yyfree (void * ptr )
{
			free( (char *) ptr );	/* see yyrealloc() for (char *) cast */
}

/* %if-tables-serialization definitions */
/* %define-yytables   The name for this specific scanner's tables. */
#define YYTABLES_NAME "yytables"
/* %endif */

/* %ok-for-header */

#line 158 "BookshelfScanner.ll"


namespace BookshelfParser {

Scanner::Scanner(std::istream* in,
		 std::ostream* out)
    : BookshelfParserFlexLexer(in, out)
{
}

Scanner::~Scanner()
{
}

void Scanner::set_debug(bool b)
{
    yy_flex_debug = b;
}

}

/* This implementation of ExampleFlexLexer::yylex() is required to fill the
 * vtable of the class ExampleFlexLexer. We define the scanner's main yylex
 * function via YY_DECL to reside in the Scanner class instead. */

#ifdef yylex
#undef yylex
#endif

int BookshelfParserFlexLexer::yylex()
{
    std::cerr << "in BookshelfParserFlexLexer::yylex() !" << std::endl;
    return 0;
}

/* When the scanner receives an end-of-file indication from YY_INPUT, it then
 * checks the yywrap() function. If yywrap() returns false (zero), then it is
 * assumed that the function has gone ahead and set up `yyin' to point to
 * another input file, and scanning continues. If it returns true (non-zero),
 * then the scanner terminates, returning 0 to its caller. */

int BookshelfParserFlexLexer::yywrap()
{
    return 1;
}

