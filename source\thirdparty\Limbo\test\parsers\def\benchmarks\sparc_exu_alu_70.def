VERSION 5.7 ;
DIVIDERCHAR "/" ;
BUSBITCHARS "[]" ;
DESIGN sparc_exu_alu ;
UNITS DISTANCE MICRONS 2000 ;

PROPERTYDEFINITIONS
    COMPONENTPIN designRuleWidth REAL ;
    DESIGN FE_CORE_BOX_LL_X REAL 20.140 ;
    DESIGN FE_CORE_BOX_UR_X REAL 82.840 ;
    DESIGN FE_CORE_BOX_LL_Y REAL 20.020 ;
    <PERSON>SIG<PERSON> FE_CORE_BOX_UR_Y REAL 80.220 ;
END PROPERTYDEFINITIONS

DIEAREA ( 0 0 ) ( 205680 200440 ) ;

ROW CORE_ROW_0 FreePDK45_38x28_10R_NP_162NW_34O 40280 40040 N DO 330 BY 1 STEP 380 0
 ;
ROW CORE_ROW_1 FreePDK45_38x28_10R_NP_162NW_34O 40280 42840 N DO 330 BY 1 STEP 380 0
 ;
ROW CORE_ROW_2 FreePDK45_38x28_10R_NP_162NW_34O 40280 45640 N DO 330 BY 1 STEP 380 0
 ;
ROW CORE_ROW_3 FreePDK45_38x28_10R_NP_162NW_34O 40280 48440 N DO 330 BY 1 STEP 380 0
 ;
ROW CORE_ROW_4 FreePDK45_38x28_10R_NP_162NW_34O 40280 51240 N DO 330 BY 1 STEP 380 0
 ;
ROW CORE_ROW_5 FreePDK45_38x28_10R_NP_162NW_34O 40280 54040 N DO 330 BY 1 STEP 380 0
 ;
ROW CORE_ROW_6 FreePDK45_38x28_10R_NP_162NW_34O 40280 56840 N DO 330 BY 1 STEP 380 0
 ;
ROW CORE_ROW_7 FreePDK45_38x28_10R_NP_162NW_34O 40280 59640 N DO 330 BY 1 STEP 380 0
 ;
ROW CORE_ROW_8 FreePDK45_38x28_10R_NP_162NW_34O 40280 62440 N DO 330 BY 1 STEP 380 0
 ;
ROW CORE_ROW_9 FreePDK45_38x28_10R_NP_162NW_34O 40280 65240 N DO 330 BY 1 STEP 380 0
 ;
ROW CORE_ROW_10 FreePDK45_38x28_10R_NP_162NW_34O 40280 68040 N DO 330 BY 1 STEP 380 0
 ;
ROW CORE_ROW_11 FreePDK45_38x28_10R_NP_162NW_34O 40280 70840 N DO 330 BY 1 STEP 380 0
 ;
ROW CORE_ROW_12 FreePDK45_38x28_10R_NP_162NW_34O 40280 73640 N DO 330 BY 1 STEP 380 0
 ;
ROW CORE_ROW_13 FreePDK45_38x28_10R_NP_162NW_34O 40280 76440 N DO 330 BY 1 STEP 380 0
 ;
ROW CORE_ROW_14 FreePDK45_38x28_10R_NP_162NW_34O 40280 79240 N DO 330 BY 1 STEP 380 0
 ;
ROW CORE_ROW_15 FreePDK45_38x28_10R_NP_162NW_34O 40280 82040 N DO 330 BY 1 STEP 380 0
 ;
ROW CORE_ROW_16 FreePDK45_38x28_10R_NP_162NW_34O 40280 84840 N DO 330 BY 1 STEP 380 0
 ;
ROW CORE_ROW_17 FreePDK45_38x28_10R_NP_162NW_34O 40280 87640 N DO 330 BY 1 STEP 380 0
 ;
ROW CORE_ROW_18 FreePDK45_38x28_10R_NP_162NW_34O 40280 90440 N DO 330 BY 1 STEP 380 0
 ;
ROW CORE_ROW_19 FreePDK45_38x28_10R_NP_162NW_34O 40280 93240 N DO 330 BY 1 STEP 380 0
 ;
ROW CORE_ROW_20 FreePDK45_38x28_10R_NP_162NW_34O 40280 96040 N DO 330 BY 1 STEP 380 0
 ;
ROW CORE_ROW_21 FreePDK45_38x28_10R_NP_162NW_34O 40280 98840 N DO 330 BY 1 STEP 380 0
 ;
ROW CORE_ROW_22 FreePDK45_38x28_10R_NP_162NW_34O 40280 101640 N DO 330 BY 1 STEP 380 0
 ;
ROW CORE_ROW_23 FreePDK45_38x28_10R_NP_162NW_34O 40280 104440 N DO 330 BY 1 STEP 380 0
 ;
ROW CORE_ROW_24 FreePDK45_38x28_10R_NP_162NW_34O 40280 107240 N DO 330 BY 1 STEP 380 0
 ;
ROW CORE_ROW_25 FreePDK45_38x28_10R_NP_162NW_34O 40280 110040 N DO 330 BY 1 STEP 380 0
 ;
ROW CORE_ROW_26 FreePDK45_38x28_10R_NP_162NW_34O 40280 112840 N DO 330 BY 1 STEP 380 0
 ;
ROW CORE_ROW_27 FreePDK45_38x28_10R_NP_162NW_34O 40280 115640 N DO 330 BY 1 STEP 380 0
 ;
ROW CORE_ROW_28 FreePDK45_38x28_10R_NP_162NW_34O 40280 118440 N DO 330 BY 1 STEP 380 0
 ;
ROW CORE_ROW_29 FreePDK45_38x28_10R_NP_162NW_34O 40280 121240 N DO 330 BY 1 STEP 380 0
 ;
ROW CORE_ROW_30 FreePDK45_38x28_10R_NP_162NW_34O 40280 124040 N DO 330 BY 1 STEP 380 0
 ;
ROW CORE_ROW_31 FreePDK45_38x28_10R_NP_162NW_34O 40280 126840 N DO 330 BY 1 STEP 380 0
 ;
ROW CORE_ROW_32 FreePDK45_38x28_10R_NP_162NW_34O 40280 129640 N DO 330 BY 1 STEP 380 0
 ;
ROW CORE_ROW_33 FreePDK45_38x28_10R_NP_162NW_34O 40280 132440 N DO 330 BY 1 STEP 380 0
 ;
ROW CORE_ROW_34 FreePDK45_38x28_10R_NP_162NW_34O 40280 135240 N DO 330 BY 1 STEP 380 0
 ;
ROW CORE_ROW_35 FreePDK45_38x28_10R_NP_162NW_34O 40280 138040 N DO 330 BY 1 STEP 380 0
 ;
ROW CORE_ROW_36 FreePDK45_38x28_10R_NP_162NW_34O 40280 140840 N DO 330 BY 1 STEP 380 0
 ;
ROW CORE_ROW_37 FreePDK45_38x28_10R_NP_162NW_34O 40280 143640 N DO 330 BY 1 STEP 380 0
 ;
ROW CORE_ROW_38 FreePDK45_38x28_10R_NP_162NW_34O 40280 146440 N DO 330 BY 1 STEP 380 0
 ;
ROW CORE_ROW_39 FreePDK45_38x28_10R_NP_162NW_34O 40280 149240 N DO 330 BY 1 STEP 380 0
 ;
ROW CORE_ROW_40 FreePDK45_38x28_10R_NP_162NW_34O 40280 152040 N DO 330 BY 1 STEP 380 0
 ;
ROW CORE_ROW_41 FreePDK45_38x28_10R_NP_162NW_34O 40280 154840 N DO 330 BY 1 STEP 380 0
 ;
ROW CORE_ROW_42 FreePDK45_38x28_10R_NP_162NW_34O 40280 157640 N DO 330 BY 1 STEP 380 0
 ;

TRACKS Y 1780 DO 63 STEP 3200 LAYER metal10 ;
TRACKS X 3510 DO 61 STEP 3360 LAYER metal10 ;
TRACKS X 1830 DO 122 STEP 1680 LAYER metal9 ;
TRACKS Y 1780 DO 63 STEP 3200 LAYER metal9 ;
TRACKS Y 1540 DO 119 STEP 1680 LAYER metal8 ;
TRACKS X 1830 DO 122 STEP 1680 LAYER metal8 ;
TRACKS X 710 DO 367 STEP 560 LAYER metal7 ;
TRACKS Y 1540 DO 119 STEP 1680 LAYER metal7 ;
TRACKS Y 420 DO 358 STEP 560 LAYER metal6 ;
TRACKS X 710 DO 367 STEP 560 LAYER metal6 ;
TRACKS X 710 DO 367 STEP 560 LAYER metal5 ;
TRACKS Y 420 DO 358 STEP 560 LAYER metal5 ;
TRACKS Y 140 DO 716 STEP 280 LAYER metal4 ;
TRACKS X 710 DO 367 STEP 560 LAYER metal4 ;
TRACKS X 190 DO 541 STEP 380 LAYER metal3 ;
TRACKS Y 140 DO 716 STEP 280 LAYER metal3 ;
TRACKS Y 140 DO 716 STEP 280 LAYER metal2 ;
TRACKS X 190 DO 541 STEP 380 LAYER metal2 ;
TRACKS X 190 DO 541 STEP 380 LAYER metal1 ;
TRACKS Y 140 DO 716 STEP 280 LAYER metal1 ;

GCELLGRID X 205390 DO 2 STEP 290 ;
GCELLGRID X 190 DO 55 STEP 3800 ;
GCELLGRID X 0 DO 2 STEP 190 ;
GCELLGRID Y 198940 DO 2 STEP 1500 ;
GCELLGRID Y 140 DO 72 STEP 2800 ;
GCELLGRID Y 0 DO 2 STEP 140 ;

VIAS 1 ;
- Via1Array-0_1
 + VIARULE Via1Array-0
 + CUTSIZE 140 140
 + LAYERS metal1 via1 metal2
 + CUTSPACING 160 160
 + ENCLOSURE 130 130 130 130
 + ROWCOL 3 3
 ;
END VIAS

COMPONENTS 4955 ;
- addsub_sub_dff_q_reg_0_ DFF_X1 + PLACED ( 112860 115640 ) FN
 ;
- add_1_root_addsub_adder_add_55_2_U5 NAND2_X1 + PLACED ( 68780 59640 ) FN
 ;
- add_1_root_addsub_adder_add_55_2_U4 OAI211_X1 + PLACED ( 80560 79240 ) N
 ;
- add_1_root_addsub_adder_add_55_2_U3 XNOR2_X1 + PLACED ( 50540 82040 ) N
 ;
- add_1_root_addsub_adder_add_55_2_U2 OR2_X1 + PLACED ( 95380 48440 ) FN
 ;
- FILLER_2844 FILLCELL_X16 + SOURCE DIST + FIXED ( 158080 157640 ) N
 ;
- FILLER_2845 FILLCELL_X4 + SOURCE DIST + FIXED ( 164160 157640 ) N
 ;
END COMPONENTS

PINS 526 ;
- so + NET so + DIRECTION OUTPUT + USE SIGNAL
  + LAYER metal4 ( -140 0 ) ( 140 280 )
  + PLACED ( 102630 200440 ) S ;
- alu_byp_rd_data_e[63] + NET alu_byp_rd_data_e[63] + DIRECTION OUTPUT + USE SIGNAL
  + LAYER metal3 ( -70 0 ) ( 70 140 )
  + PLACED ( 205680 93940 ) W ;
- alu_byp_rd_data_e[62] + NET alu_byp_rd_data_e[62] + DIRECTION OUTPUT + USE SIGNAL
  + LAYER metal3 ( -70 0 ) ( 70 140 )
  + PLACED ( 205680 91140 ) W ;
END PINS

SPECIALNETS 2 ;
- 1'b0
  + ROUTED metal1 1000 + SHAPE RING ( 20440 20820 ) ( 185380 * )
    NEW metal2 1000 + SHAPE RING ( 20940 20320 ) ( * 180140 )
    NEW metal2 1000 + SHAPE RING ( 184880 20320 ) ( * 180140 )
    NEW metal1 1000 + SHAPE RING ( 20440 179640 ) ( 185380 * )
    NEW metal2 0 + SHAPE RING ( 20940 20820 ) Via1Array-0_1
    NEW metal2 0 + SHAPE RING ( 184880 20820 ) Via1Array-0_1
    NEW metal2 0 + SHAPE RING ( 20940 179640 ) Via1Array-0_1
    NEW metal2 0 + SHAPE RING ( 184880 179640 ) Via1Array-0_1
  + USE GROUND
 ;
- 1'b1
  + ROUTED metal1 1000 + SHAPE RING ( 18840 19220 ) ( 186980 * )
    NEW metal2 1000 + SHAPE RING ( 19340 18720 ) ( * 181740 )
    NEW metal2 1000 + SHAPE RING ( 186480 18720 ) ( * 181740 )
    NEW metal1 1000 + SHAPE RING ( 18840 181240 ) ( 186980 * )
    NEW metal2 0 + SHAPE RING ( 19340 19220 ) Via1Array-0_1
    NEW metal2 0 + SHAPE RING ( 186480 19220 ) Via1Array-0_1
    NEW metal2 0 + SHAPE RING ( 19340 181240 ) Via1Array-0_1
    NEW metal2 0 + SHAPE RING ( 186480 181240 ) Via1Array-0_1
  + USE POWER
 ;
END SPECIALNETS

NETS 2447 ;
- alu_byp_rd_data_e[63]
  ( PIN alu_byp_rd_data_e[63] ) ( U2295 ZN )
 ;
- alu_byp_rd_data_e[62]
  ( PIN alu_byp_rd_data_e[62] ) ( U2292 ZN )
 ;
END NETS

END DESIGN
