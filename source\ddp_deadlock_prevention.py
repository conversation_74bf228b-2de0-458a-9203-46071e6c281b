#!/usr/bin/env python3
##
# @file   ddp_deadlock_prevention.py
# <AUTHOR> Assistant
# @date   2024
# @brief  DDP deadlock detection and prevention utilities
#

import torch
import torch.distributed as dist
import time
import threading
import signal
import logging
from typing import Optional, Callable, Any
from contextlib import contextmanager

logger = logging.getLogger(__name__)

class DDPDeadlockDetector:
    """
    @brief DDP deadlock detection and prevention utility
    """
    
    def __init__(self, timeout_seconds: int = 60):
        """
        @brief Initialize deadlock detector
        @param timeout_seconds timeout for DDP operations
        """
        self.timeout_seconds = timeout_seconds
        self.active_operations = set()
        self.lock = threading.Lock()
        
    def register_operation(self, op_name: str):
        """Register an active DDP operation"""
        with self.lock:
            self.active_operations.add(op_name)
            logger.debug(f"Registered DDP operation: {op_name}")
    
    def unregister_operation(self, op_name: str):
        """Unregister a completed DDP operation"""
        with self.lock:
            self.active_operations.discard(op_name)
            logger.debug(f"Unregistered DDP operation: {op_name}")
    
    def get_active_operations(self):
        """Get list of currently active operations"""
        with self.lock:
            return list(self.active_operations)
    
    @contextmanager
    def monitor_operation(self, op_name: str):
        """Context manager to monitor a DDP operation"""
        self.register_operation(op_name)
        try:
            yield
        finally:
            self.unregister_operation(op_name)

# Global deadlock detector instance
_deadlock_detector = DDPDeadlockDetector()

def safe_all_reduce(tensor: torch.Tensor, op=dist.ReduceOp.SUM, timeout: int = 30) -> bool:
    """
    @brief Safe all-reduce with deadlock prevention
    @param tensor tensor to reduce
    @param op reduce operation
    @param timeout timeout in seconds
    @return True if successful, False if failed/timeout
    """
    if not dist.is_initialized() or dist.get_world_size() <= 1:
        return True

    rank = dist.get_rank()
    op_name = f"all_reduce_rank_{rank}"

    try:
        with _deadlock_detector.monitor_operation(op_name):
            # Use threading.Timer instead of signal for thread safety
            timeout_occurred = threading.Event()

            def timeout_handler():
                timeout_occurred.set()

            timer = threading.Timer(timeout, timeout_handler)
            timer.start()

            try:
                if not timeout_occurred.is_set():
                    dist.all_reduce(tensor, op=op)
                    return True
                else:
                    raise TimeoutError(f"All-reduce timed out after {timeout}s")
            finally:
                timer.cancel()

    except TimeoutError:
        logger.error(f"Rank {rank}: All-reduce operation timed out")
        active_ops = _deadlock_detector.get_active_operations()
        logger.error(f"Rank {rank}: Active operations during timeout: {active_ops}")
        return False
    except Exception as e:
        logger.error(f"Rank {rank}: All-reduce failed: {e}")
        return False

def safe_barrier(timeout: int = 60) -> bool:
    """
    @brief Safe barrier with deadlock prevention
    @param timeout timeout in seconds
    @return True if successful, False if failed/timeout
    """
    if not dist.is_initialized() or dist.get_world_size() <= 1:
        return True

    rank = dist.get_rank()
    op_name = f"barrier_rank_{rank}"

    try:
        with _deadlock_detector.monitor_operation(op_name):
            timeout_occurred = threading.Event()

            def timeout_handler():
                timeout_occurred.set()

            timer = threading.Timer(timeout, timeout_handler)
            timer.start()

            try:
                if not timeout_occurred.is_set():
                    dist.barrier()
                    return True
                else:
                    raise TimeoutError(f"Barrier timed out after {timeout}s")
            finally:
                timer.cancel()

    except TimeoutError:
        logger.error(f"Rank {rank}: Barrier operation timed out")
        active_ops = _deadlock_detector.get_active_operations()
        logger.error(f"Rank {rank}: Active operations during timeout: {active_ops}")
        return False
    except Exception as e:
        logger.error(f"Rank {rank}: Barrier failed: {e}")
        return False

def safe_broadcast(tensor: torch.Tensor, src: int = 0, timeout: int = 30) -> bool:
    """
    @brief Safe broadcast with deadlock prevention
    @param tensor tensor to broadcast
    @param src source rank
    @param timeout timeout in seconds
    @return True if successful, False if failed/timeout
    """
    if not dist.is_initialized() or dist.get_world_size() <= 1:
        return True

    rank = dist.get_rank()
    op_name = f"broadcast_rank_{rank}_src_{src}"

    try:
        with _deadlock_detector.monitor_operation(op_name):
            timeout_occurred = threading.Event()

            def timeout_handler():
                timeout_occurred.set()

            timer = threading.Timer(timeout, timeout_handler)
            timer.start()

            try:
                if not timeout_occurred.is_set():
                    dist.broadcast(tensor, src=src)
                    return True
                else:
                    raise TimeoutError(f"Broadcast timed out after {timeout}s")
            finally:
                timer.cancel()

    except TimeoutError:
        logger.error(f"Rank {rank}: Broadcast operation timed out")
        active_ops = _deadlock_detector.get_active_operations()
        logger.error(f"Rank {rank}: Active operations during timeout: {active_ops}")
        return False
    except Exception as e:
        logger.error(f"Rank {rank}: Broadcast failed: {e}")
        return False

def check_ddp_health() -> dict:
    """
    @brief Check DDP health status
    @return dictionary with health information
    """
    health_info = {
        'ddp_initialized': dist.is_initialized(),
        'world_size': dist.get_world_size() if dist.is_initialized() else 0,
        'rank': dist.get_rank() if dist.is_initialized() else -1,
        'active_operations': _deadlock_detector.get_active_operations(),
        'cuda_available': torch.cuda.is_available(),
        'cuda_device_count': torch.cuda.device_count() if torch.cuda.is_available() else 0
    }
    
    if torch.cuda.is_available() and dist.is_initialized():
        try:
            current_device = torch.cuda.current_device()
            health_info['current_cuda_device'] = current_device
            health_info['cuda_memory_allocated'] = torch.cuda.memory_allocated(current_device)
            health_info['cuda_memory_reserved'] = torch.cuda.memory_reserved(current_device)
        except Exception as e:
            health_info['cuda_error'] = str(e)
    
    return health_info

def emergency_ddp_reset():
    """
    @brief Emergency DDP reset in case of deadlock
    """
    rank = dist.get_rank() if dist.is_initialized() else -1
    logger.warning(f"Rank {rank}: Performing emergency DDP reset")
    
    try:
        # Clear active operations
        _deadlock_detector.active_operations.clear()
        
        # Force CUDA synchronization if available
        if torch.cuda.is_available():
            torch.cuda.synchronize()
        
        # Try to destroy and reinitialize process group
        if dist.is_initialized():
            logger.warning(f"Rank {rank}: Destroying process group")
            dist.destroy_process_group()
        
        logger.warning(f"Rank {rank}: Emergency reset completed")
        
    except Exception as e:
        logger.error(f"Rank {rank}: Emergency reset failed: {e}")

def monitor_ddp_operations(interval: int = 10):
    """
    @brief Monitor DDP operations for potential deadlocks
    @param interval monitoring interval in seconds
    """
    def monitor_thread():
        while True:
            try:
                time.sleep(interval)
                active_ops = _deadlock_detector.get_active_operations()
                if active_ops:
                    rank = dist.get_rank() if dist.is_initialized() else -1
                    logger.info(f"Rank {rank}: Active DDP operations: {active_ops}")
                    
                    # Check for long-running operations
                    if len(active_ops) > 0:
                        logger.warning(f"Rank {rank}: Long-running DDP operations detected")
                        
            except Exception as e:
                logger.error(f"DDP monitor thread error: {e}")
    
    # Start monitoring thread
    monitor = threading.Thread(target=monitor_thread, daemon=True)
    monitor.start()
    logger.info("DDP operation monitor started")

# Context manager for safe DDP operations
@contextmanager
def safe_ddp_context(operation_name: str = "ddp_operation"):
    """
    @brief Context manager for safe DDP operations
    @param operation_name name of the operation for monitoring
    """
    rank = dist.get_rank() if dist.is_initialized() else -1
    logger.debug(f"Rank {rank}: Starting DDP operation: {operation_name}")
    
    with _deadlock_detector.monitor_operation(operation_name):
        try:
            yield
        except Exception as e:
            logger.error(f"Rank {rank}: DDP operation {operation_name} failed: {e}")
            raise
        finally:
            logger.debug(f"Rank {rank}: Completed DDP operation: {operation_name}")

def get_deadlock_detector():
    """Get the global deadlock detector instance"""
    return _deadlock_detector
