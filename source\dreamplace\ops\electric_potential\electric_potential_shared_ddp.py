##
# @file   electric_potential_shared_ddp.py
# <AUTHOR> Assistant
# @date   2024
# @brief  Shared parameter DDP-aware electric potential computation
#

import os
import sys
import math
import numpy as np
import time
import torch
import torch.distributed as dist
from torch import nn
from torch.autograd import Function
from torch.nn import functional as F

# Import original electric potential modules
import dreamplace.ops.dct.dct as dct
import dreamplace.ops.dct.discrete_spectral_transform as discrete_spectral_transform
import dreamplace.ops.electric_potential.electric_potential_cpp as electric_potential_cpp

try:
    import dreamplace.ops.electric_potential.electric_potential_hip as electric_potential_hip
except:
    pass

from dreamplace.ddp_shared_param_utils import all_reduce_tensor_sum
import pdb

class ElectricPotentialSharedDDPFunction(Function):
    """
    @brief Shared parameter DDP-aware electric potential computation
    Node positions are shared, but each node's bin contribution is handled by only one GPU
    """

    @staticmethod
    def forward(
            ctx,
            pos,
            node_size_x, node_size_y,
            bin_center_x, bin_center_y,
            initial_density_map,
            target_density,
            xl, yl, xh, yh,
            bin_size_x, bin_size_y,
            num_movable_nodes,
            num_filler_nodes,
            padding,
            padding_mask,
            num_bins_x,
            num_bins_y,
            num_movable_impacted_bins_x,
            num_movable_impacted_bins_y,
            num_filler_impacted_bins_x,
            num_filler_impacted_bins_y,
            local_node_mask,  # New parameter: which nodes this GPU handles
            perm_M=None,
            perm_N=None,
            expk_M=None,
            expk_N=None,
            inv_wu2_plus_wv2_2X=None,
            wu_by_wu2_plus_wv2_2X=None,
            wv_by_wu2_plus_wv2_2X=None,
            fast_mode=True,
            num_threads=8,
            ddp_rank=0,
            ddp_world_size=1
    ):
        """
        @brief Forward pass for shared parameter DDP electric potential
        @param local_node_mask mask indicating which nodes this GPU processes
        @param ddp_rank current GPU rank
        @param ddp_world_size total number of GPUs
        """
        
        # Store DDP information
        ctx.ddp_rank = ddp_rank
        ctx.ddp_world_size = ddp_world_size
        ctx.local_node_mask = local_node_mask
        
        # Create local position tensor with only assigned nodes
        local_pos = pos.clone()
        num_nodes = pos.shape[0] // 2
        
        # Zero out positions for nodes not assigned to this GPU
        node_mask_expanded = local_node_mask.bool()
        local_pos[:num_nodes][~node_mask_expanded] = 0.0
        local_pos[num_nodes:][~node_mask_expanded] = 0.0
        
        # Compute local density map (only for assigned nodes)
        if local_pos.is_cuda:
            local_density_output = electric_potential_hip.density_map(
                local_pos.view(local_pos.numel()),
                node_size_x, node_size_y,
                bin_center_x, bin_center_y,
                initial_density_map,
                target_density,
                xl, yl, xh, yh,
                bin_size_x, bin_size_y,
                num_movable_nodes,
                num_filler_nodes,
                padding,
                padding_mask,
                num_bins_x,
                num_bins_y,
                num_movable_impacted_bins_x,
                num_movable_impacted_bins_y,
                num_filler_impacted_bins_x,
                num_filler_impacted_bins_y
            )
        else:
            local_density_output = electric_potential_cpp.density_map(
                local_pos.view(local_pos.numel()),
                node_size_x, node_size_y,
                bin_center_x, bin_center_y,
                initial_density_map,
                target_density,
                xl, yl, xh, yh,
                bin_size_x, bin_size_y,
                num_movable_nodes,
                num_filler_nodes,
                padding,
                padding_mask,
                num_bins_x,
                num_bins_y,
                num_movable_impacted_bins_x,
                num_movable_impacted_bins_y,
                num_filler_impacted_bins_x,
                num_filler_impacted_bins_y,
                num_threads
            )

        # Store context for backward pass
        ctx.node_size_x = node_size_x
        ctx.node_size_y = node_size_y
        ctx.bin_center_x = bin_center_x
        ctx.bin_center_y = bin_center_y
        ctx.target_density = target_density
        ctx.xl = xl
        ctx.yl = yl
        ctx.xh = xh
        ctx.yh = yh
        ctx.bin_size_x = bin_size_x
        ctx.bin_size_y = bin_size_y
        ctx.num_movable_nodes = num_movable_nodes
        ctx.num_filler_nodes = num_filler_nodes
        ctx.padding = padding
        ctx.num_bins_x = num_bins_x
        ctx.num_bins_y = num_bins_y
        ctx.num_movable_impacted_bins_x = num_movable_impacted_bins_x
        ctx.num_movable_impacted_bins_y = num_movable_impacted_bins_y
        ctx.num_filler_impacted_bins_x = num_filler_impacted_bins_x
        ctx.num_filler_impacted_bins_y = num_filler_impacted_bins_y
        ctx.pos = pos  # Store original shared positions
        ctx.num_threads = num_threads
        
        # Get local density map
        local_density_map = local_density_output.view([ctx.num_bins_x, ctx.num_bins_y])
        
        # All-reduce to get global density map
        global_density_map = all_reduce_tensor_sum(local_density_map.clone())

        # DCT computation (same as original, but using global density map)
        M = num_bins_x
        N = num_bins_y
        if expk_M is None:
            perm_M = discrete_spectral_transform.get_perm(M, dtype=torch.int64, device=global_density_map.device)
            perm_N = discrete_spectral_transform.get_perm(N, dtype=torch.int64, device=global_density_map.device)
            expk_M = discrete_spectral_transform.get_expk(M, dtype=global_density_map.dtype, device=global_density_map.device)
            expk_N = discrete_spectral_transform.get_expk(N, dtype=global_density_map.dtype, device=global_density_map.device)
            
        if inv_wu2_plus_wv2_2X is None:
            wu = torch.arange(M, dtype=global_density_map.dtype, device=global_density_map.device).mul(2 * np.pi / M).view([M, 1])
            wv = torch.arange(N, dtype=global_density_map.dtype, device=global_density_map.device).mul(2 * np.pi / N).view([1, N])
            wu2_plus_wv2 = wu.pow(2) + wv.pow(2)
            wu2_plus_wv2[0, 0] = 1.0
            inv_wu2_plus_wv2_2X = 2.0 / wu2_plus_wv2
            inv_wu2_plus_wv2_2X[0, 0] = 0.0
            wu_by_wu2_plus_wv2_2X = wu.mul(inv_wu2_plus_wv2_2X)
            wv_by_wu2_plus_wv2_2X = wv.mul(inv_wu2_plus_wv2_2X)

        # Compute field maps using global density map
        global_density_map.mul_(1.0 / (ctx.bin_size_x * ctx.bin_size_y))
        auv = dct.dct2(global_density_map, expk0=expk_M, expk1=expk_N)
        auv[0, :].mul_(0.5)
        auv[:, 0].mul_(0.5)

        auv_by_wu2_plus_wv2_wu = auv.mul(wu_by_wu2_plus_wv2_2X)
        auv_by_wu2_plus_wv2_wv = auv.mul(wv_by_wu2_plus_wv2_2X)
        ctx.field_map_x = dct.idsct2(auv_by_wu2_plus_wv2_wu, expk_M, expk_N)
        ctx.field_map_y = dct.idcst2(auv_by_wu2_plus_wv2_wv, expk_M, expk_N)

        # Energy computation
        if fast_mode:
            energy = torch.zeros(1, dtype=pos.dtype, device=pos.device)
        else:
            auv_by_wu2_plus_wv2 = auv.mul(inv_wu2_plus_wv2_2X).mul_(2)
            potential_map = dct.idcct2(auv_by_wu2_plus_wv2, expk_M, expk_N)
            energy = potential_map.mul_(global_density_map).sum()

        if pos.is_cuda:
            torch.cuda.synchronize()
            
        return energy

    @staticmethod
    def backward(ctx, grad_pos):
        """
        @brief Backward pass with shared parameter DDP gradient handling
        """
        # Compute electric force using shared positions but only for assigned nodes
        if grad_pos.is_cuda:
            full_electric_force = -electric_potential_hip.electric_force(
                grad_pos,
                ctx.num_bins_x, ctx.num_bins_y,
                ctx.num_movable_impacted_bins_x, ctx.num_movable_impacted_bins_y,
                ctx.num_filler_impacted_bins_x, ctx.num_filler_impacted_bins_y,
                ctx.field_map_x.view([-1]), ctx.field_map_y.view([-1]),
                ctx.pos,
                ctx.node_size_x, ctx.node_size_y,
                ctx.bin_center_x, ctx.bin_center_y,
                ctx.xl, ctx.yl, ctx.xh, ctx.yh,
                ctx.bin_size_x, ctx.bin_size_y,
                ctx.num_movable_nodes,
                ctx.num_filler_nodes
            )
        else:
            full_electric_force = -electric_potential_cpp.electric_force(
                grad_pos,
                ctx.num_bins_x, ctx.num_bins_y,
                ctx.num_movable_impacted_bins_x, ctx.num_movable_impacted_bins_y,
                ctx.num_filler_impacted_bins_x, ctx.num_filler_impacted_bins_y,
                ctx.field_map_x.view([-1]), ctx.field_map_y.view([-1]),
                ctx.pos,
                ctx.node_size_x, ctx.node_size_y,
                ctx.bin_center_x, ctx.bin_center_y,
                ctx.xl, ctx.yl, ctx.xh, ctx.yh,
                ctx.bin_size_x, ctx.bin_size_y,
                ctx.num_movable_nodes,
                ctx.num_filler_nodes,
                ctx.num_threads
            )

        # Zero out electric force for nodes not assigned to this GPU
        num_nodes = full_electric_force.shape[0] // 2
        node_mask_expanded = ctx.local_node_mask.bool()
        
        local_electric_force = full_electric_force.clone()
        local_electric_force[:num_nodes][~node_mask_expanded] = 0.0
        local_electric_force[num_nodes:][~node_mask_expanded] = 0.0

        # All-reduce to sum electric forces from all GPUs
        all_reduce_tensor_sum(local_electric_force)

        if grad_pos.is_cuda:
            torch.cuda.synchronize()
            
        return local_electric_force, \
            None, None, None, None, \
            None, None, None, None, \
            None, None, None, None, \
            None, None, None, None, \
            None, None, None, None, \
            None, None, None, None, \
            None, None, None, None, \
            None, None, None, None, None

class ElectricPotentialSharedDDP(nn.Module):
    """
    @brief Shared parameter DDP-aware Electric Potential computation
    Node positions are shared, but each node's bin contribution is handled by only one GPU
    """

    def __init__(self,
                 node_size_x, node_size_y,
                 bin_center_x, bin_center_y,
                 target_density,
                 xl, yl, xh, yh,
                 bin_size_x, bin_size_y,
                 num_movable_nodes,
                 num_terminals,
                 num_filler_nodes,
                 local_node_mask,  # New parameter: which nodes this GPU handles
                 padding,
                 fast_mode=False,
                 num_threads=8,
                 ddp_rank=0,
                 ddp_world_size=1
                 ):
        """
        @brief Initialize shared parameter DDP Electric Potential
        @param local_node_mask mask indicating which nodes this GPU processes
        @param ddp_rank current GPU rank
        @param ddp_world_size total number of GPUs
        """
        super(ElectricPotentialSharedDDP, self).__init__()

        # Store DDP parameters
        self.ddp_rank = ddp_rank
        self.ddp_world_size = ddp_world_size
        self.local_node_mask = local_node_mask

        # Store all other parameters (same as original)
        self.node_size_x = node_size_x
        self.node_size_y = node_size_y
        self.bin_center_x = bin_center_x
        self.bin_center_y = bin_center_y
        self.target_density = target_density
        self.xl = xl
        self.yl = yl
        self.xh = xh
        self.yh = yh
        self.bin_size_x = bin_size_x
        self.bin_size_y = bin_size_y
        self.num_movable_nodes = num_movable_nodes
        self.num_terminals = num_terminals
        self.num_filler_nodes = num_filler_nodes
        self.padding = padding

        # Compute bin parameters
        self.num_bins_x = int(math.ceil((xh - xl) / bin_size_x))
        self.num_bins_y = int(math.ceil((yh - yl) / bin_size_y))
        sqrt2 = 1.414213562

        # Impact bin calculations (same as original)
        self.num_movable_impacted_bins_x = int(
            ((node_size_x[:num_movable_nodes].max() + 2 * sqrt2 * self.bin_size_x) / self.bin_size_x).ceil().clamp(
                max=self.num_bins_x))
        self.num_movable_impacted_bins_y = int(
            ((node_size_y[:num_movable_nodes].max() + 2 * sqrt2 * self.bin_size_y) / self.bin_size_y).ceil().clamp(
                max=self.num_bins_y))

        if num_filler_nodes:
            self.num_filler_impacted_bins_x = int(
                ((node_size_x[-num_filler_nodes:].max() + 2 * sqrt2 * self.bin_size_x) / self.bin_size_x).ceil().clamp(
                    max=self.num_bins_x))
            self.num_filler_impacted_bins_y = int(
                ((node_size_y[-num_filler_nodes:].max() + 2 * sqrt2 * self.bin_size_y) / self.bin_size_y).ceil().clamp(
                    max=self.num_bins_y))
        else:
            self.num_filler_impacted_bins_x = 0
            self.num_filler_impacted_bins_y = 0

        # Padding mask
        if self.padding > 0:
            self.padding_mask = torch.ones(self.num_bins_x, self.num_bins_y, dtype=torch.uint8,
                                           device=node_size_x.device)
            self.padding_mask[self.padding:self.num_bins_x - self.padding,
            self.padding:self.num_bins_y - self.padding].fill_(0)
        else:
            self.padding_mask = torch.zeros(self.num_bins_x, self.num_bins_y, dtype=torch.uint8,
                                            device=node_size_x.device)

        # Initialize other parameters
        self.initial_density_map = None
        self.perm_M = None
        self.perm_N = None
        self.expk_M = None
        self.expk_N = None
        self.inv_wu2_plus_wv2_2X = None
        self.wu_by_wu2_plus_wv2_2X = None
        self.wv_by_wu2_plus_wv2_2X = None
        self.fast_mode = fast_mode
        self.num_threads = num_threads

    def forward(self, pos):
        """
        @brief Forward pass for shared parameter DDP electric potential
        @param pos shared position tensor
        """
        # Initialize density map if needed (same as original)
        if self.initial_density_map is None:
            self._initialize_density_map(pos)
            self._initialize_dct_parameters(pos)

        return ElectricPotentialSharedDDPFunction.apply(
            pos,
            self.node_size_x, self.node_size_y,
            self.bin_center_x, self.bin_center_y,
            self.initial_density_map,
            self.target_density,
            self.xl, self.yl, self.xh, self.yh,
            self.bin_size_x, self.bin_size_y,
            self.num_movable_nodes, self.num_filler_nodes,
            self.padding,
            self.padding_mask,
            self.num_bins_x,
            self.num_bins_y,
            self.num_movable_impacted_bins_x,
            self.num_movable_impacted_bins_y,
            self.num_filler_impacted_bins_x,
            self.num_filler_impacted_bins_y,
            self.local_node_mask,  # Pass local node mask
            self.perm_M, self.perm_N,
            self.expk_M, self.expk_N,
            self.inv_wu2_plus_wv2_2X,
            self.wu_by_wu2_plus_wv2_2X, self.wv_by_wu2_plus_wv2_2X,
            self.fast_mode,
            self.num_threads,
            self.ddp_rank,
            self.ddp_world_size
        )

    def _initialize_density_map(self, pos):
        """Initialize density map for fixed cells (same as original but DDP-aware)"""
        if self.num_terminals == 0:
            num_fixed_impacted_bins_x = 0
            num_fixed_impacted_bins_y = 0
        else:
            num_fixed_impacted_bins_x = int(((self.node_size_x[
                                              self.num_movable_nodes:self.num_movable_nodes + self.num_terminals].max() + self.bin_size_x) / self.bin_size_x).ceil().clamp(
                max=self.num_bins_x))
            num_fixed_impacted_bins_y = int(((self.node_size_y[
                                              self.num_movable_nodes:self.num_movable_nodes + self.num_terminals].max() + self.bin_size_y) / self.bin_size_y).ceil().clamp(
                max=self.num_bins_y))

        # For shared parameter DDP, use full positions for fixed density map
        if pos.is_cuda:
            self.initial_density_map = electric_potential_hip.fixed_density_map(
                pos.view(pos.numel()),
                self.node_size_x, self.node_size_y,
                self.bin_center_x, self.bin_center_y,
                self.xl, self.yl, self.xh, self.yh,
                self.bin_size_x, self.bin_size_y,
                self.num_movable_nodes,
                self.num_terminals,
                self.num_bins_x,
                self.num_bins_y,
                num_fixed_impacted_bins_x,
                num_fixed_impacted_bins_y
            )
        else:
            self.initial_density_map = electric_potential_cpp.fixed_density_map(
                pos.view(pos.numel()),
                self.node_size_x, self.node_size_y,
                self.bin_center_x, self.bin_center_y,
                self.xl, self.yl, self.xh, self.yh,
                self.bin_size_x, self.bin_size_y,
                self.num_movable_nodes,
                self.num_terminals,
                self.num_bins_x,
                self.num_bins_y,
                num_fixed_impacted_bins_x,
                num_fixed_impacted_bins_y,
                self.num_threads
            )

        # Scale density of fixed macros
        self.initial_density_map.mul_(self.target_density)

    def _initialize_dct_parameters(self, pos):
        """Initialize DCT parameters (same as original)"""
        M = self.num_bins_x
        N = self.num_bins_y
        self.perm_M = discrete_spectral_transform.get_perm(M, dtype=torch.int64, device=pos.device)
        self.perm_N = discrete_spectral_transform.get_perm(N, dtype=torch.int64, device=pos.device)
        self.expk_M = discrete_spectral_transform.get_expk(M, dtype=pos.dtype, device=pos.device)
        self.expk_N = discrete_spectral_transform.get_expk(N, dtype=pos.dtype, device=pos.device)

        # wu and wv
        wu = torch.arange(M, dtype=pos.dtype, device=pos.device).mul(2 * np.pi / M).view([M, 1])
        # scale wv because the aspect ratio of a bin may not be 1
        wv = torch.arange(N, dtype=pos.dtype, device=pos.device).mul(2 * np.pi / N).view([1, N]).mul_(
            self.bin_size_x / self.bin_size_y)
        wu2_plus_wv2 = wu.pow(2) + wv.pow(2)
        wu2_plus_wv2[0, 0] = 1.0  # avoid zero-division, it will be zeroed out
        self.inv_wu2_plus_wv2_2X = 2.0 / wu2_plus_wv2
        self.inv_wu2_plus_wv2_2X[0, 0] = 0.0
        self.wu_by_wu2_plus_wv2_2X = wu.mul(self.inv_wu2_plus_wv2_2X)
        self.wv_by_wu2_plus_wv2_2X = wv.mul(self.inv_wu2_plus_wv2_2X)
