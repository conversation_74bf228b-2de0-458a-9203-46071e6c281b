VERSION 5.8 ;
NAMESCASESENSITIVE ON ;
FIXEDMASK ;
NOWIREEX<PERSON>NSIONATPIN ON ;
BUSBITCHARS "<>" ;
DIVIDERCHAR ":" ;
USEMINSPACING OBS OFF ;
USEMINSPACING PIN ON ;
CLEARANCEMEASURE EUCLIDEAN ;
CLEARANCEMEASURE MAXXY ;

&defines &VDD/GND_site = "VDDGND" ;
&defines &VDD/GND_site = "VDDGND1" ;

UNITS
   TIME NANOSECONDS 100 ;
   CAPACITANCE PICOFARADS 10 ;
   RESISTANCE OHMS 10000 ;
   POWER MILLIWATTS 10000 ;
   CURRENT MILLIAMPS 10000 ;
   VOLTAGE VOLTS 1000 ;
   DATABASE MICRONS 20000 ;
   FREQUENCY MEGAHERTZ 10 ;
END UNITS

MANUFACTURINGGRID 3.5 ;

PROPERTYDEFINITIONS
   LIBRARY NAME STRING "Cadence96" ;
   LIBRARY intNum  INTEGER 20 ;
   LIBRARY realNum REAL 21.22 ;
   LIBRARY LEF57_MAXFLOATINGAREAGATE STRING "MAXFLOATINGAREA GATEISGROUND;" ;
   LAYER lsp STRING ;
   LAYER lip INTEGER ;
   LAYER lrp REAL ;
   LAYER LEF57_SPACING STRING ;
   LAYER LEF57_SPACINGADJACENTCUTS STRING ;
   LAYER LEF57_MAXFLOATINGAREA STRING ;
   LAYER LEF57_ARRAYSPACING STRING ;
   LAYER LEF57_SPACINGSAMENET STRING ;
   LAYER LEF57_MINSTEP STRING ;
   LAYER LEF57_ANTENNAGATEPLUSDIFF STRING ;
   LAYER LEF57_ANTENNACUMROUTINGPLUSCUT STRING ;
   LAYER LEF57_ANTENNAAREAMINUSDIFF STRING ;
   LAYER LEF57_ANTENNAAREADIFFREDUCEPWL STRING ;
   LAYER LEF57_ENCLOSURE STRING ;
   VIA stringProperty STRING ;
   VIA realProperty REAL ;
   VIA COUNT INTEGER RANGE 1 100 ;
   VIARULE vrsp STRING ;
   VIARULE vrip INTEGER ;
   VIARULE vrrp REAL ;
   NONDEFAULTRULE ndrsp STRING ;
   NONDEFAULTRULE ndrip INTEGER ;
   NONDEFAULTRULE ndrrp REAL ;
   MACRO stringProp STRING ;
   MACRO integerProp INTEGER ;
   MACRO WEIGHT REAL RANGE 1.0 100.0 ;
   PIN TYPE STRING ;
   PIN intProp INTEGER ;
   PIN realProp REAL ;
END PROPERTYDEFINITIONS

LAYER POLYS
   TYPE MASTERSLICE ;
   PROPERTY lsp "top" lip 1 lrp 2.3 ;
END POLYS

LAYER POLYS01
   TYPE MASTERSLICE ;
END POLYS01

LAYER CUT01
   TYPE CUT ;
   SPACING 0.35 ADJACENTCUTS 3 WITHIN 0.25 ;
   DIAGPITCH 6.5 ;
   OFFSET 0.5 .6 ;
   PITCH 1.2 1.3 ;
   PROPERTY lip 5 ;
   PROPERTY LEF57_SPACING "SPACING 1.5 PARALLELOVERLAP ;" ;
   PROPERTY LEF57_ARRAYSPACING "ARRAYSPACING  WIDTH 2.0 CUTSPACING 0.2 ARRAYCUTS 3 SPACING 1.0 ;" ;
   PROPERTY LEF57_ENCLOSURE "ENCLOSURE ABOVE .01 .05 ;" ;
   PROPERTY LEF57_ENCLOSURE "ENCLOSURE ABOVE .02 .05 WIDTH 3.1 EXCEPTEXTRACUT 1.5 NOSHAREDEDGE ;" ;
   PROPERTY LEF57_ENCLOSURE "ENCLOSURE BELOW .03 .05 WIDTH 3.1 EXCEPTEXTRACUT 1.5 ;" ;
   PROPERTY LEF57_ENCLOSURE "ENCLOSURE .05 .05 ;" ;
   PROPERTY LEF57_ENCLOSURE "ENCLOSURE BELOW .08 .05 WIDTH 3.1 EXCEPTEXTRACUT 1.5 NOSHAREDEDGE ;" ;
END CUT01

LAYER RX
   TYPE ROUTING ;
   MASK 2 ;
   PITCH 1.8 ;
   OFFSET 0.9 ;
   WIDTH 1 ;
   AREA 34.1 ;
   MINIMUMCUT 2 WIDTH 2.5 ;
   SPACING 0.6 ;
   SPACING 0.18 LENGTHTHRESHOLD 0.9 ;
   SPACING 0.4 RANGE 0.1 0.12 ;
   SPACING 0.32 RANGE 1.01 2000.0 USELENGTHTHRESHOLD ;
   SPACING 0.1 RANGE 0.1 0.1 INFLUENCE 2.01 RANGE 2.1 10000.0 ;
   SPACING 0.44 RANGE 1.0 1.0 INFLUENCE 1.01 ;
   SPACING 0.33 RANGE 1.01 20.0 INFLUENCE 1.01 ;
   SPACING 0.7 RANGE 0.3 0.15 USELENGTHTHRESHOLD ;
   SPACING 0.5 ;
   SPACING 0.6 RANGE 4.5 6.12 RANGE 3.0 3.1 ;
   SPACING 4.3 RANGE 0.1 0.1 INFLUENCE 3.81 RANGE 0.1 0.2 ;
   SPACING 0.53 LENGTHTHRESHOLD 0.45 RANGE 0 0.1 ;
   PROPERTY LEF57_SPACING "SPACING 2.2 ENDOFLINE 2.3 WITHIN 1.6 ;" ;
   PROPERTY LEF57_ARRAYSPACING "ARRAYSPACING LONGARRAY CUTSPACING 0.2 ARRAYCUTS 3 SPACING 1.0 ARRAYCUTS 4 SPACING 1.5 ARRAYCUTS 5 SPACING 2.0 ;" ;
   DIRECTION HORIZONTAL ;
   WIREEXTENSION 0.75 ;
   RESISTANCE RPERSQ 0.103 ;
   CAPACITANCE CPERSQDIST 0.000156 ;
   HEIGHT 9 ;
   THICKNESS 1 ;
   SHRINKAGE 0.1 ;
   SLOTWIREWIDTH 5 ;
   SLOTWIRELENGTH 4 ;
   SLOTWIDTH 6 ;
   SLOTLENGTH 5 ;
   MAXADJACENTSLOTSPACING 45 ;
   MAXCOAXIALSLOTSPACING 55 ;
   SPLITWIREWIDTH 5 ;
   MINIMUMDENSITY 4 ;
   MAXIMUMDENSITY 10 ;
#   DENSITYCHECKWINDOW 4 5 ;
   DENSITYCHECKSTEP 2 ;
   FILLACTIVESPACING 4 ;
   CAPMULTIPLIER 1 ;
   EDGECAPACITANCE 0.00005 ;
   ANTENNAMODEL OXIDE1 ;
   ANTENNAAREAFACTOR 1 ;
   ANTENNAMODEL OXIDE2 ;
   ANTENNAAREARATIO 4.6 ;
   ANTENNAAREARATIO 7.6 ;
   ANTENNADIFFAREARATIO 4.7 ;
   ANTENNADIFFAREARATIO PWL ( ( 5.4 5.4 ) ( 6.5 6.5 ) ( 7.5 7.5 ) ) ;
   ANTENNACUMAREARATIO 6.7 ;             # 5.7
   ANTENNAAREAFACTOR 10 ;                # 5.7
   ANTENNACUMROUTINGPLUSCUT ;            # 5.7
   ANTENNAAREAMINUSDIFF 100.0 ;          # 5.7
   ANTENNAGATEPLUSDIFF 2.0 ;             # 5.7
   ANTENNACUMDIFFAREARATIO 1000 ;        # 5.7
   ANTENNAAREADIFFREDUCEPWL ( ( 0.0 1.0 ) ( 0.09999 1.0 ) ( 0.1 0.2 ) ( 1.0 0.1 ) ( 1000.0 0.1 ) ) ;
   ANTENNACUMDIFFAREARATIO 1000 ;        # 5.7
   PROPERTY LEF57_ANTENNACUMROUTINGPLUSCUT "ANTENNACUMROUTINGPLUSCUT ;" ;
   PROPERTY LEF57_ANTENNAAREAMINUSDIFF "ANTENNAAREAMINUSDIFF 100.0 ;" ;
   PROPERTY LEF57_ANTENNAGATEPLUSDIFF "ANTENNAGATEPLUSDIFF 2.0 ;" ;
   PROPERTY LEF57_ANTENNAAREADIFFREDUCEPWL "ANTENNAAREADIFFREDUCEPWL ( ( 0.0 1.0 ) ( 0.0999 1.0 ) ( 0.1 0.2 ) ( 1.0 0.1 ) ( 1000.0 0.1 ) ) ;" ;
   ANTENNACUMDIFFAREARATIO 4.5 ;
   ANTENNACUMDIFFAREARATIO PWL ( ( 5.4 5.4 ) ( 6.5 6.5 ) ( 7.5 7.5 ) ) ;
   ANTENNAAREAFACTOR 6.5 ;
   ANTENNAAREAFACTOR 6.5 DIFFUSEONLY ;
   ANTENNASIDEAREARATIO 6.5 ;
   ANTENNADIFFSIDEAREARATIO 6.5 ;
   ANTENNADIFFSIDEAREARATIO PWL ( ( 5.4 5.4 ) ( 6.5 6.5 ) ( 7.5 7.5 ) ) ;
   ANTENNACUMSIDEAREARATIO 4.5 ;
   ANTENNACUMSIDEAREARATIO 7.5 ;
   ANTENNACUMDIFFSIDEAREARATIO 4.6 ; 
   ANTENNACUMDIFFSIDEAREARATIO PWL ( ( 5.4 5.4 ) ( 6.5 6.5 ) ( 7.5 7.5 ) ) ;
   ANTENNASIDEAREAFACTOR 6.5 ;
   ANTENNASIDEAREAFACTOR 7.5 DIFFUSEONLY ;
   ANTENNAMODEL OXIDE3 ;
   ANTENNAMODEL OXIDE4 ;
   PROPERTY lsp "rxlay" lip 3 lrp 1.2 ;
   CURRENTDEN 1E3 ;
#   CURRENTDEN ( 1E3 4E5 ) ;
   ACCURRENTDENSITY PEAK
      FREQUENCY 1E6 100E6 ;
   TABLEENTRIES 0.5E-6 0.4E-6 ;
   ACCURRENTDENSITY AVERAGE 5.5 ;
   ACCURRENTDENSITY RMS
      FREQUENCY 100E6 400E6 800E6 ;
   WIDTH     0.4 0.8 10.0 50.0 100.0 ;
   TABLEENTRIES
      2.0E-6 1.9E-6 1.8E-6 1.7E-6 1.5E-6
      1.4E-6 1.3E-6 1.2E-6 1.1E-6 1.0E-6
      0.9E-6 0.8E-6 0.7E-6 0.6E-6 0.4E-6 ;
   DCCURRENTDENSITY AVERAGE
      WIDTH 20.0 50.0 ;
   TABLEENTRIES 0.6E-6 0.5E-6 ;
END RX

LAYER CUT12
TYPE CUT ;
   DIAGPITCH 1.5 1.7 ;
   DIAGWIDTH 1.6 ;
   DIAGSPACING 0.5 ;
   SPACING 0.7 LAYER RX ;
   SPACING 0.22 ADJACENTCUTS 4 WITHIN 0.25 ;
   SPACING 1.5 PARALLELOVERLAP ;                             # 5.7
   SPACING 1.2 ADJACENTCUTS 2 WITHIN 1.5 EXCEPTSAMEPGNET ;   # 5.7
   # 5.4
   ANTENNAMODEL OXIDE1 ;
   ANTENNAMODEL OXIDE2 ;
   ANTENNAMODEL OXIDE3 ;
   ANTENNAMODEL OXIDE4 ;
   ANTENNAAREAFACTOR 5.4 ;
   ANTENNACUMROUTINGPLUSCUT ;            # 5.7
   ANTENNAAREAMINUSDIFF 100.0 ;          # 5.7
   ANTENNAGATEPLUSDIFF 2.0 ;             # 5.7
   ANTENNADIFFAREARATIO 1000 ;           # 5.7
   ANTENNACUMDIFFAREARATIO 5000 ;        # 5.7
   ANTENNADIFFAREARATIO 6.5 ;
   ANTENNAAREADIFFREDUCEPWL ( ( 0.0 1.0 ) ( 0.09999 1.0 ) ( 0.1 0.2 ) ( 1.0 0.1 ) ( 1000.0 0.1 ) ) ;                     # 5.7
   ANTENNADIFFAREARATIO PWL ( ( 5.4 5.4 ) ( 6.5 6.5 ) ( 7.5 7.5 ) ) ;
   ANTENNACUMDIFFAREARATIO PWL ( ( 5.4 5.4 ) ( 6.5 6.5 ) ( 7.5 7.5 ) ) ;
   ANTENNACUMDIFFAREARATIO 5.6 ;
   ANTENNAAREARATIO 5.6 ;
   ANTENNACUMAREARATIO 6.7 ;
   ACCURRENTDENSITY PEAK
   FREQUENCY 1E6 100E6 ;
   TABLEENTRIES 0.5E-6 0.4E-6 ;
   ACCURRENTDENSITY AVERAGE 5.5 ;
   ACCURRENTDENSITY RMS
   FREQUENCY 100E6 400E6 800E6 ;
   CUTAREA     0.4 0.8 10.0 50.0 100.0 ;
   TABLEENTRIES
      2.0E-6 1.9E-6 1.8E-6 1.7E-6 1.5E-6
      1.4E-6 1.3E-6 1.2E-6 1.1E-6 1.0E-6
      0.9E-6 0.8E-6 0.7E-6 0.6E-6 0.4E-6 ;
   DCCURRENTDENSITY AVERAGE
   CUTAREA 2.0 5.0 ; 
   TABLEENTRIES 0.5E-6 0.4E-6 ;
   DCCURRENTDENSITY AVERAGE 4.9 ;
END CUT12

LAYER PC
   TYPE ROUTING ;
   WIDTH 1 ;
   WIREEXTENSION 0.4 ; #should be ignored
   PITCH 3.8 3.5 ;
   DIAGPITCH 1.4 ;
   SPACING 0.6 ;
   SPACING 1.2 ENDOFLINE 1.3 WITHIN 0.6 ;  # 5.7
   SPACING 1.3 ENDOFLINE 1.4 WITHIN 0.7 PARALLELEDGE 1.1 WITHIN 0.5 TWOEDGES ;
   SPACING 1.4 ENDOFLINE 1.5 WITHIN 0.8 PARALLELEDGE 1.2 WITHIN 0.6 ;  # 5.7
   DIRECTION VERTICAL ;
   RESISTANCE RPERSQ PWL ( ( 1 0.103 ) ( 10 4.7 ) ) ;
   CAPACITANCE CPERSQDIST PWL ( ( 1 0.000156 ) ( 10 0.001 ) ) ;
   ANTENNAAREARATIO 5.4 ;
   ANTENNADIFFAREARATIO 6.5 ;
   ANTENNACUMAREARATIO 7.5 ;
   ANTENNACUMDIFFAREARATIO PWL ( ( 5.0 5.1 ) ( 6.0 6.1 ) ) ;
   ANTENNAAREAFACTOR 4.5 ;
   ANTENNASIDEAREARATIO 6.5 ;
   ANTENNADIFFSIDEAREARATIO PWL ( ( 7.0 7.1 ) ( 7.2 7.3 ) ) ;  
   ANTENNACUMSIDEAREARATIO 7.4 ;
   ANTENNACUMDIFFSIDEAREARATIO PWL ( ( 8.0 8.1 ) ( 8.2 8.3 ) ( 8.4 8.5 )
        ( 8.6 8.7 ) ) ;
   ANTENNASIDEAREAFACTOR 9.0 DIFFUSEONLY ;

   ACCURRENTDENSITY PEAK
      FREQUENCY 1E6 100E6 ;
      WIDTH 5.6 8.5 8.1 4.5 ;
      TABLEENTRIES 0.5E-6 0.4E-6 ;
   DCCURRENTDENSITY AVERAGE
   WIDTH 20.0 50.0 100.0 ;
   TABLEENTRIES 1.0E-6 0.7E-6 0.5E-6 ;
END PC

LAYER CA
   TYPE CUT ;
   DCCURRENTDENSITY AVERAGE
   CUTAREA   2.0 5.0 10.0 ;
   TABLEENTRIES 0.6E-6 0.5E-6 0.4E-6 ; 
END CA

LAYER M1
   TYPE ROUTING ;
   WIDTH 1 ;
   WIREEXTENSION 7 ;
   PITCH 1.8 ;
   DIRECTION HORIZONTAL ;
   RESISTANCE RPERSQ 0.103 ;
   CAPACITANCE CPERSQDIST 0.000156 ;
   ANTENNACUMAREARATIO 300 ;
   ANTENNACUMDIFFAREARATIO 600 ;
   ANTENNAGATEPLUSDIFF 2.0 ;             # 5.7
   ANTENNADIFFAREARATIO 1000 ;           # 5.7
   ANTENNACUMDIFFAREARATIO 5000 ;        # 5.7
   SPACINGTABLE
      PARALLELRUNLENGTH 0.00 0.50 3.00 5.00
         WIDTH 0.00      0.15 0.15 0.15 0.15
         WIDTH 0.25      0.15 0.20 0.20 0.20
         WIDTH 1.50      0.15 0.50 0.50 0.50
         WIDTH 3.00      0.15 0.50 1.00 1.00
         WIDTH 5.00      0.15 0.50 1.00 2.00 ;
   SPACINGTABLE
      INFLUENCE
         WIDTH 1.5 WITHIN 0.5 SPACING 0.5
         WIDTH 3.0 WITHIN 1.0 SPACING 1.0
         WIDTH 5.0 WITHIN 2.0 SPACING 2.0 ;
   ACCURRENTDENSITY AVERAGE 5.5 ;
   DCCURRENTDENSITY AVERAGE 4.9 ;
END M1

LAYER V1
   TYPE CUT ;
   SPACING 0.6 LAYER CA ;
END V1

LAYER M2
   TYPE ROUTING ;
   WIDTH 0.9 ;
   WIREEXTENSION 8 ;
   PITCH 1.8 ;
   SPACING 0.9 ;
   SPACING 0.28 ;
   SPACING 0.24 LENGTHTHRESHOLD 1.0 ;
   SPACING 0.32 RANGE 1.01 9.99 USELENGTHTHRESHOLD ;
   SPACING 0.5 RANGE 10.0 1000.0 ;
   SPACING 0.5 RANGE 10.0 1000.0 INFLUENCE 1.00 ;
   SPACING 0.5 RANGE 10.0 1000.0 INFLUENCE 1.0 RANGE .28 1.0 ;
   SPACING 0.5 RANGE 3.01 4.0 RANGE 4.01 5.0 ;
   SPACING 0.4 RANGE 3.01 4.0 RANGE 5.01 1000.0 ;
   SPACING 1.0 SAMENET PGONLY ;          # 5.7
   SPACING 1.1 SAMENET ;                 # 5.7
   PROPERTY LEF57_SPACING "SPACING 1.2 ENDOFLINE 1.3 WITHIN 0.6 PARALLELEDGE 2.1 WITHIN 1.5 TWOEDGES ;" ;
   PROPERTY LEF57_SPACING "SPACING 1.5 ENDOFLINE 2.3 WITHIN 1.6 PARALLELEDGE 1.1 WITHIN 0.5 ;" ;
   DIRECTION DIAG45 ;
   RESISTANCE RPERSQ 0.0608 ;
   CAPACITANCE CPERSQDIST 0.000184 ;
   PROPERTY LEF57_MAXFLOATINGAREA "MAXFLOATINGAREA 1000 ;" ;
   ANTENNAMODEL OXIDE1 ;
   ANTENNACUMAREARATIO 5000 ;
   ANTENNACUMDIFFAREARATIO 8000 ;
   ANTENNAMODEL OXIDE2 ;
   ANTENNACUMAREARATIO 500 ;
   ANTENNACUMDIFFAREARATIO 800 ;
   ANTENNAMODEL OXIDE3 ;
   ANTENNACUMAREARATIO 300 ;
   ANTENNACUMDIFFAREARATIO 600 ;
END M2

LAYER V2
   TYPE CUT ;
END V2

LAYER M3
   TYPE ROUTING ;
   WIDTH 0.9 ;
   WIREEXTENSION 8 ;
   PITCH 1.8 ;
   SPACING 0.9 ;
   DIRECTION HORIZONTAL ;
   RESISTANCE RPERSQ 0.0608 ;
   CAPACITANCE CPERSQDIST 0.000184 ;
   ANTENNAMODEL OXIDE3 ;
   ANTENNACUMAREARATIO 5000 ;
   ANTENNACUMDIFFAREARATIO 8000 ;
   ANTENNAMODEL OXIDE4 ;
   ANTENNACUMAREARATIO 500 ;
   ANTENNACUMDIFFAREARATIO 800 ;
   ANTENNAMODEL OXIDE1 ;
   ANTENNACUMAREARATIO 300 ;
   ANTENNACUMDIFFAREARATIO 600 ;
   PROPERTY LEF57_MINSTEP "MINSTEP 1.0 MAXEDGES 2 ;" ;
END M3

LAYER M4
   TYPE ROUTING ;
   PITCH 5.4 ;
   WIDTH 5.4 ;
   DIRECTION VERTICAL ;
   DIRECTION HORIZONTAL ;
   # 2 via cuts required for m4 > 0.50 um when connecting from m3
   MINIMUMCUT 2 WIDTH 0.50 ;
   # 2 via cuts required for m4 > 0.70 um when connecting from m5
   MINIMUMCUT 2 WIDTH 0.70 FROMBELOW ;
   MINIMUMCUT 3 WIDTH 0.80 WITHIN 0.3 ;       # 5.7
   MINIMUMCUT 2 WIDTH 1.00 FROMBELOW LENGTH 20.0 WITHIN 5.0 ;
   # 4 via cuts are required for m4 > 1.0 um when connecting from m3 or m5
   MINIMUMCUT 4 WIDTH 1.0 FROMABOVE ;
   # 2 via cuts are required if m4 > 1.1 um wide and m4 > 20.0 um long,
   # and the via cut is < 5.0 um away from the wide wire
   MINIMUMCUT 2 WIDTH 1.1 LENGTH 20.0 WITHIN 5.0 ;
   MINIMUMCUT 2 WIDTH 1.1 FROMABOVE LENGTH 20.0 WITHIN 5.0 ;
   MINENCLOSEDAREA 0.30 ; # donut hole must be >= 0.30 um^2
   MINENCLOSEDAREA 0.40 WIDTH 0.15 ; # hole area >= 0.40 um^2 when w<=0.15
   MINENCLOSEDAREA 0.80 WIDTH 0.50 ; # hole area >= 0.80 um^2 when w<=0.55
   MAXWIDTH 10.0 ;
   MINWIDTH 0.15 ;
   PROTRUSIONWIDTH 0.30 LENGTH 0.60 WIDTH 1.20 ;
   MINSTEP .20 ;
END M4

LAYER M5
   TYPE ROUTING ;
   PITCH 5.4 ;
   WIDTH 4.0 ;
   DIRECTION DIAG135 ;
   MINSTEP 0.05 ;
   MINSTEP 0.04 ;
   MINSTEP 0.05 LENGTHSUM 0.08 ;
   MINSTEP 0.05 LENGTHSUM 0.16 ;
   MINSTEP 0.05 INSIDECORNER ;
   MINSTEP 0.05 INSIDECORNER LENGTHSUM 0.15 ;
   MINSTEP 1.0 MAXEDGES 2 ;                 # 5.7
   MINIMUMCUT 2 WIDTH 0.70 ;
   MINIMUMCUT 4 WIDTH 1.0 FROMABOVE ;
   MINIMUMCUT 2 WIDTH 1.1 LENGTH 20.0 WITHIN 5.0 ;
   MINIMUMCUT 5 WIDTH 0.5  ;
   ANTENNAMODEL OXIDE3 ;
   ANTENNACUMAREARATIO 5000 ;
   ANTENNACUMDIFFAREARATIO 8000 ;
   ANTENNAMODEL OXIDE3 ;
   ANTENNACUMAREARATIO 500 ;
   ANTENNACUMDIFFAREARATIO 800 ;
   ANTENNAMODEL OXIDE3 ;
   ANTENNACUMAREARATIO 300 ;
   ANTENNACUMDIFFAREARATIO 600 ;
END M5

LAYER implant1
   TYPE IMPLANT ;
   WIDTH 0.50 ;
   SPACING 0.50 ;
   PROPERTY lrp 5.4 ;
END implant1
        
LAYER implant2
   TYPE IMPLANT ;
   WIDTH 0.50 ;
   SPACING 0.50 ;
   PROPERTY lsp "bottom" ;
END implant2

LAYER V3
   TYPE CUT ;
END V3

LAYER MT
   TYPE ROUTING ;
   WIDTH 0.9 ;
   PITCH 1.8 ;
   SPACING 0.9 ;
   DIRECTION VERTICAL ;
   RESISTANCE RPERSQ 0.0608 ;
   CAPACITANCE CPERSQDIST 0.000184 ;
   MINSTEP 0.05 STEP ;
   MINSTEP 0.05 STEP LENGTHSUM 0.08 ;
   MINSTEP 0.04 STEP ;
   DIAGMINEDGELENGTH .075 ;
END MT

layer OVERLAP
   TYPE OVERLAP ;
   PROPERTY lip 5 lsp "top" ;
   PROPERTY lrp 5.5 lsp "bottom" ;
END OVERLAP

LAYER via12
   TYPE CUT ;
   WIDTH 0.20 ;            #cuts .20 x .20 squares
   SPACING 0.15 CENTERTOCENTER ;  #via12 center-to-center spacing is 0.15
   ENCLOSURE BELOW .03 .01 ;    #m1; 0.03 on two sides, 0.01 on other sides
   ENCLOSURE ABOVE .05 .01 ;    #m2; 0.05 on two sides, 0.01 on other sides
   ENCLOSURE ABOVE .04 .09 ;    #m3; 0.04 on two sides, 0.09 on other sides
   PREFERENCLOSURE BELOW 0.06 0.01 ;
   PREFERENCLOSURE ABOVE 0.08 0.2 ;
   RESISTANCE 10.0 ;            #10.0 ohms per cut
END via12

LAYER metal1
   TYPE ROUTING ;
   WIDTH 0.9 ;
   PITCH 1.8 ;
   DIRECTION VERTICAL ;
   MINSIZE 0.14 0.30 0.5 0.56 0.01 0.05  ;
END metal1

LAYER via23
   TYPE CUT ;
   WIDTH 0.20 ;            #cuts .20 x .20 squares
   SPACING 0.15 ;          #via23 edge-to-edge spacing is 0.15
   ENCLOSURE .05 .01 ;     #m2, m3:0.05 on two sides, 0.01 on other sides
   ENCLOSURE .02 .02 WIDTH 1.0 ; #m2 needs 0.02 on all sides if m2 width
                                 #>= 1.0
                           #m3 needs 0.02 on all sides if m3 width >= 1.0
   ENCLOSURE .05 .05 WIDTH 2.0 ; #m2 needs 0.05 on all sides if m2 width
                            #>= 2.0
                           #m3 needs 0.02 on all sides if m3 width >= 3.0
   RESISTANCE 10.0 ;       #10.0 ohms per cut
END via23

LAYER via34
   TYPE CUT ;
   WIDTH 0.25 ;            #cuts .25 x .25 squares
   SPACING 0.10 CENTERTOCENTER ;      # 5.7
   ENCLOSURE .05 .01 ;     #m3, m4 must meet the enclosure rule
   ENCLOSURE .05 0.0 LENGTH 0.7 ;     # 5.7
   ENCLOSURE BELOW .07 .07 WIDTH 1.0 ; #m3 needs .07um on all sides if
                                       #the m3 width is >= 1.0um
   ENCLOSURE ABOVE .09 .09 WIDTH 1.0 ; #m4 needs .09um on all sides if
                                       #the m4 width is >= 1.0um 
   ENCLOSURE 0.03 0.03 WIDTH 1.0 EXCEPTEXTRACUT 0.2 ;   # 5.7
   RESISTANCE 8.0 ;                    #8.0 ohms per cut
END via34

LAYER cut23                # 5.7
   TYPE CUT ;
   SPACING 0.20 SAMENET LAYER cut12 STACK ;
   SPACING 0.30 CENTERTOCENTER SAMENET AREA 0.02 ;
   SPACING 0.40 AREA 0.5 ;
   SPACING 0.10 ;
   SPACINGTABLE ORTHOGONAL
      WITHIN 0.15 SPACING 0.11
      WITHIN 0.13 SPACING 0.13
      WITHIN 0.11 SPACING 0.15 ;
   ARRAYSPACING LONGARRAY CUTSPACING 0.2
      ARRAYCUTS 3 SPACING 1.0
      ARRAYCUTS 4 SPACING 1.5
      ARRAYCUTS 5 SPACING 2.0 ;
END cut23

LAYER cut24                # 5.7
   TYPE ROUTING ;
   WIDTH 1 ;
   PITCH 1.8 ;
   DIRECTION HORIZONTAL ;
   SPACING 0.10 ;
   SPACING 0.12 NOTCHLENGTH 0.15 ;
   SPACING 0.14 ENDOFNOTCHWIDTH 0.15 NOTCHSPACING 0.16 NOTCHLENGTH 0.08 ;
   ARRAYSPACING WIDTH 2.0 CUTSPACING 0.2 ARRAYCUTS 3 SPACING 1.0 ;
END cut24

LAYER cut25                # 5.7
   TYPE ROUTING ;
   WIDTH 1 ;
   WIREEXTENSION 7 ;
   PITCH 1.8 ;
   DIRECTION HORIZONTAL ;
   SPACINGTABLE
      TWOWIDTHS
         WIDTH 0.00          0.15 0.20 0.50 1.00
         WIDTH 0.25 PRL 0.0  0.20 0.25 0.50 1.00
         WIDTH 1.50 PRL 1.50 0.50 0.50 0.60 1.00
         WIDTH 3.00 PRL 3.00 1.00 1.00 1.00 1.20 ;
END cut25

MAXVIASTACK 4 RANGE m1 m7 ;

#layer VIRTUAL
#   TYPE OVIRTUAL ;
#END VIRTUAL

VIA IN1X
   TOPOFSTACKONLY 
   FOREIGN IN1X ;
   RESISTANCE 2 ;
   LAYER RX ; RECT -0.7 -0.7 0.7 0.7 ; RECT 0.0 0 2.1 2.3 ; RECT 5.7 0 95.7 2.3 ; RECT 101.9 0 119.6 2.3 ;
   LAYER CUT12 ;
      RECT -0.25 -0.25 0.25 0.25 ;
   LAYER PC ;
      RECT -0.6 -0.6 0.6 0.6 ;
   PROPERTY stringProperty "DEFAULT" realProperty 32.33 COUNT 34 ;
END IN1X

VIA M1_M2 DEFAULT
   RESISTANCE 1.5 ;
   LAYER M1 ;
      RECT MASK 1 -0.6 -0.6 0.6 0.6 ;
   LAYER V1 ;
      RECT MASK 2 -0.45 -0.45 0.45 0.45 ;
   LAYER M2 ;
      RECT MASK 3 -0.45 -0.45 0.45 0.45 ;
      RECT MASK 1 -0.9 -0.45 0.9 0.45 ;
END M1_M2

VIA M2_M3 DEFAULT
   RESISTANCE 1.5 ;
   LAYER M2 ;
      RECT -0.45 -0.9 0.45 0.9 ;
   LAYER V2 ;
      RECT -0.45 -0.45 0.45 0.45 ;
   LAYER M3 ;
      RECT -0.45 -0.45 0.45 0.45 ;
END M2_M3

VIA M2_M3_PWR GENERATED
   RESISTANCE 0.4 ;
   LAYER M2 ;
      RECT -1.35 -1.35 1.35 1.35 ;
   LAYER V2 ;
      RECT -1.35 -1.35 -0.45 1.35 ;
      RECT 0.45 -1.35 1.35 -0.45 ;
      RECT 0.45 0.45 1.35 1.35 ;
   LAYER M3 ;
      RECT -1.35 -1.35 1.35 1.35 ;
END M2_M3_PWR

VIA M3_MT DEFAULT
   RESISTANCE 1.5 ;
   LAYER M3 ;
      RECT MASK 1 -0.9 -0.45 0.9 0.45 ;
   LAYER V3 ;
      RECT MASK 2 -0.45 -0.45 0.45 0.45 ;
   LAYER MT ;
      RECT MASK 3 -0.45 -0.45 0.45 0.45 ;
END M3_MT

VIA myBlockVia0
   VIARULE viaName0 ;
   CUTSIZE 0.1 0.1 ;
   LAYERS metal1 via12 metal2 ;
   CUTSPACING 0.1 0.1 ;
   ENCLOSURE 0.05 0.01 0.01 0.05 ;
   ROWCOL 5 14 ;
   PATTERN 2_FF70_3_R4F ;
END myBlockVia0

VIA VIACENTER12
   LAYER M1 ;
      RECT -4.6 -2.2 4.6 2.2 ;
   LAYER V1 ;
      RECT -3.1 -0.8 -1.9 0.8 ;
      RECT 1.9 -0.8 3.1 0.8 ;
   LAYER M2 ;
      RECT -4.4 -2.0 4.4 2.0 ;
   RESISTANCE 0.24 ; 
END VIACENTER12

VIA M2_TURN 
   LAYER M2 ;
      RECT -0.45 -0.45 0.45 0.45 ;
      RECT -4.4 -2.0 4.4 2.0 ;
END M2_TURN

VIA myVia23
   LAYER metal2 ;
      POLYGON MASK 1 -2.1 -1.0 -0.2 1.0 2.1 1.0 0.2 -1.0 ;
      POLYGON MASK 2 -1.1 -2.0 -0.1 2.0 1.1 2.0 0.1 -2.0 ;
      POLYGON MASK 3 -3.1 -2.0 -0.3 2.0 3.1 2.0 0.3 -2.0 ;
      POLYGON MASK 1 -4.1 -2.0 -0.4 2.0 4.1 2.0 0.4 -2.0 ;
   LAYER cut23 ;
      RECT MASK 2 -0.4 -0.4 0.4 0.4 ;
      POLYGON MASK 3 -2.1 -1.0 -0.2 1.0 2.1 1.0 0.2 -1.0 ;
   LAYER metal3 ;
      POLYGON MASK 1 -0.2 -1.0 -2.1 1.0 0.2 1.0 2.1 -1.0 ;
   LAYER cut33 ;
      RECT MASK 1 -0.4 -0.4 0.4 0.4 ;
      POLYGON MASK 2 -2.1 -1.0 -0.2 1.0 2.1 1.0 0.2 -1.0 ;
      POLYGON MASK 2 -1.1 -2.0 -0.1 2.0 1.1 2.0 0.1 -2.0 ;
      RECT MASK 1 -0.5 -0.5 0.5 0.5 ;
      RECT MASK 2 -0.3 -0.3 0.3 0.3 ;
      POLYGON MASK 3 -3.1 -2.0 -0.3 2.0 3.1 2.0 0.3 -2.0 ;
      POLYGON MASK 1 -4.1 -2.0 -0.4 2.0 4.1 2.0 0.4 -2.0 ;
      RECT MASK 1 -0.2 -0.2 0.2 0.2 ;
      RECT MASK 2 -0.1 -0.1 0.1 0.1 ;
END myVia23

VIA myBlockVia
   VIARULE DEFAULT ;
   CUTSIZE 0.1 0.1 ;
   LAYERS metal1 via12 metal2 ;
   CUTSPACING 0.1 0.1 ;
   ENCLOSURE 0.05 0.01 0.01 0.05 ;
   ORIGIN .1 .2 ;
   OFFSET 5.1 4.1 3.1 2.1 ;
   ROWCOL 1 2 ;
END myBlockVia

VIARULE VIALIST12
   LAYER M1 ;
      DIRECTION VERTICAL ;
#      OVERHANG 4.5 ;
      WIDTH 9.0 TO 9.6 ;
#      METALOVERHANG 0.4 ;
   LAYER M2 ;
      DIRECTION HORIZONTAL ;
      WIDTH 3.0 TO 3.0 ;
#      METALOVERHANG 0.3 ;
   VIA VIACENTER12 ;
   PROPERTY vrsp "new" vrip 1 vrrp 4.5 ;
END VIALIST12

VIARULE VIALIST1
   LAYER M1 ;
      DIRECTION VERTICAL ;
      WIDTH 9.0 TO 9.6 ;
#      OVERHANG 4.5 ;
#      METALOVERHANG 0.5 ;
   LAYER M1 ;
      DIRECTION HORIZONTAL ;
      WIDTH 3.0 TO 3.0 ;
#      OVERHANG 5.5 ;
#      METALOVERHANG 0.6 ;
#   VIA VIACENTER12 ;
END VIALIST1
 

VIARULE VIAGEN12 GENERATE 
   LAYER M1 ;
      DIRECTION VERTICAL ;
      WIDTH 0.1 TO 19 ;
      OVERHANG 1.4 ;
      METALOVERHANG 1.0 ;
   LAYER M2 ;
      DIRECTION HORIZONTAL ;
      OVERHANG 1.5 ;
      METALOVERHANG 1.0 ;
      WIDTH 0.2 TO 1.9 ;
   LAYER M3 ;
      RECT -0.3 -0.3 0.3 0.3 ;
      SPACING 5.6 BY 7.0 ;
      RESISTANCE 0.5 ; 
   PROPERTY vrsp "new" vrip 1 vrrp 5.5 ;
END VIAGEN12

VIARULE VIAGEN1 GENERATE 
   LAYER M1 ;
      DIRECTION HORIZONTAL ;
      OVERHANG 1.4 ;
      METALOVERHANG 1.1 ;
      WIDTH 0.1 TO 1.9 ;
   LAYER M2 ;
      DIRECTION VERTICAL ;
      OVERHANG 1.5 ;
      METALOVERHANG 1.5 ;
      WIDTH 0.2 TO 2.9 ;
#   LAYER M3 ;
#      RECT ( 1 1 ) ( 1 1 ) ;
#      SPACING 0.3 BY 4.5 ;
   PROPERTY vrsp "new" vrip 1 vrrp 5.5 ;
END VIAGEN1

VIARULE via10 GENERATE
   LAYER M1 ;
      DIRECTION HORIZONTAL ;
      OVERHANG 1.1 ;
      WIDTH 0.1 TO 1.9 ;
   LAYER M2 ;
      DIRECTION VERTICAL ;
      OVERHANG 1.2 ;
      WIDTH 0.2 TO 2.9 ;
   LAYER M3 ;
      RECT ( 1 1 ) ( 1 1 ) ;
      SPACING 0.3 BY 4.5 ;
   PROPERTY vrsp "new" vrip 1 vrrp 5.5 ;
END via10

VIARULE via11 GENERATE
   LAYER M1 ;
      DIRECTION HORIZONTAL ;
      WIDTH 0.1 TO 1.9 ;
   LAYER M2 ;
      DIRECTION VERTICAL ;
      WIDTH 0.2 TO 2.9 ;
   LAYER M3 ;
      RECT ( 1 1 ) ( 1 1 ) ;
      SPACING 0.3 BY 4.5 ;
   PROPERTY vrsp "new" vrip 1 vrrp 5.5 ;
END via11

VIARULE via12 GENERATE DEFAULT
   LAYER m1 ;
      ENCLOSURE 0.03 0.01 ;    # 2 sides need >= 0.03, 2 other sides >= 0.01
   LAYER m2 ;
      ENCLOSURE 0.05 0.01 ;    # 2 sides need >= 0.05, 2 other sides >= 0.01
   LAYER cut12 ;
      RECT -0.1 -0.1 0.1 0.1 ; # cut is .20 by .20
      SPACING 0.40 BY 0.40 ;   # center-to-center spacing
      RESISTANCE 20 ;          # ohms per cut
END via12

VIARULE via13 GENERATE
   LAYER m1 ;
      ENCLOSURE 0.05 0.005 ; # 2 sides need >= 0.05, 2 other sides >= 0.005
      WIDTH 1.0 TO 100.0 ;   # for m1 between 1 to 100 um wide
   LAYER m2 ;
      ENCLOSURE 0.05 0.005 ; # 2 sides need >= 0.05, 2 other sides >= 0.005
      WIDTH 1.0 TO 100.0 ;   # for m1 between 1 to 100 um wide
   LAYER cut12 ;
      RECT -0.07 -0.07 0.07 0.07 ; # cut is .14 by .14
      SPACING 0.16 BY 0.16 ;
END via13


VIARULE via14
   LAYER m1 ;
      DIRECTION HORIZONTAL ;
#      ENCLOSURE 0.05 0.005 ;  2 sides need >= 0.05, 2 other sides >= 0.005
      WIDTH 1.0 TO 100.0 ;   # for m1 between 1 to 100 um wide
   LAYER m2 ;
#      ENCLOSURE 0.05 0.005 ;  2 sides need >= 0.05, 2 other sides >= 0.005
      DIRECTION VERTICAL ;
      WIDTH 1.0 TO 100.0 ;   # for m1 between 1 to 100 um wide
   via name1 ;
END via14

VIARULE TURNM3 GENERATE
   LAYER m3 ;
      DIRECTION VERTICAL ;
   LAYER m3 ;
      DIRECTION HORIZONTAL ;
END TURNM3

VIARULE VIAGEN3T GENERATE
   LAYER m3 ;
      DIRECTION HORIZONTAL ;
      OVERHANG 0.2 ;
      METALOVERHANG 0.0 ;
   LAYER v3 ;
      RECT -0.45 -0.45 0.45 0.45 ;
      SPACING 1.80 by 1.80 ;
   LAYER mt ;
      DIRECTION VERTICAL ;
      OVERHANG 0.2 ;
      METALOVERHANG 0.0 ;
END VIAGEN3T

NONDEFAULTRULE RULE1
   LAYER RX
      WIDTH 10.0 ;
      SPACING 2.2 ;
      WIREEXTENSION 6 ;
      RESISTANCE RPERSQ 6.5 ;
      CAPACITANCE CPERSQDIST 6.5 ;
      EDGECAPACITANCE 6.5 ;
   END RX
   LAYER PC
      WIDTH 10.0 ;
      SPACING 2.2 ;
      CAPACITANCE CPERSQDIST 6.5 ;
   END PC

   LAYER M1
      WIDTH 10.0 ;
      SPACING 2.2 ;
      RESISTANCE RPERSQ 6.5 ;
   END M1

   LAYER fw
      WIDTH 4.800 ;
      SPACING 4.800 ;
   END fw
     
   VIA nd1VIARX0
      DEFAULT
      TOPOFSTACKONLY
      FOREIGN IN1X ;
      RESISTANCE 0.2 ;
      PROPERTY realProperty 2.3 ;
      LAYER RX ;
         RECT -3 -3 3 3 ;
      LAYER CUT12 ;
         RECT -1.0 -1.0 1.0 1.0 ;
      LAYER PC ;
         RECT -3 -3 3 3 ;
   END nd1VIARX0

   VIA nd1VIA01
      FOREIGN IN1X 5.6 5.3 E ;
      RESISTANCE 0.2 ;
      LAYER PC ;
         RECT -3 -3 3 3 ;
         RECT -5 -5 5 5 ;
      LAYER CA ;
         RECT -1.0 -1.0 1.0 1.0 ;
      LAYER M1 ;
         RECT -3 -3 3 3 ;
   END nd1VIA01

   VIA nd1VIA12
      RESISTANCE 0.2 ;
      LAYER M1 ;
         RECT -3 -3 3 3 ;
      LAYER V1 ;
         RECT -1.0 -1.0 1.0 1.0 ;
      LAYER M2 ;
         RECT -3 -3 3 3 ;
   END nd1VIA12
        
   SPACING
      SAMENET
      CUT01 RX 0.1 STACK ;
   END SPACING
   PROPERTY ndrsp "single" ndrip 1 ndrrp 6.7 ;
END RULE1

NONDEFAULTRULE wide1_5x
   LAYER metal1
      WIDTH 1.5 ;    # metal1 has 1.5um width
   END metal1
   LAYER metal2
      WIDTH 1.5 ;
   END metal2
   LAYER metal3
      WIDTH 1.5 ;
   END metal3
END wide1_5x

NONDEFAULTRULE wide3x
   LAYER metal1
      WIDTH 3.0 ;   # metal1 has 3.0um width
   END metal1
   LAYER metal2
      WIDTH 3.0 ;
   END metal2
   LAYER metal3
      WIDTH 3.0 ;
   END metal3
   #via12rule and via23rule are used implicitly
   MINCUTS cut12 2 ; # at least two-cut vias for cut12 required
   MINCUTS cut23 2 ;
END wide3x

NONDEFAULTRULE analog_rule
   HARDSPACING ;     # don't let any other signal close to this one
   LAYER metal1
      WIDTH 1.5 ;   # metal1 has 1.5um width
      SPACING 3.0 ; # extra spacing of 3.0um
      DIAGWIDTH 5.5 ;
   END metal1
   LAYER metal2
      WIDTH 1.5 ;
      SPACING 3.0 ;
   END metal2
   LAYER metal3
      WIDTH 1.5 ;
      SPACING 3.0 ;
   END metal3
   #use pre-defined "analog vias"
   #the DEFAULT VIARULEs will NOT be inherited
   USEVIA via12_fixed_analog_via ;
   USEVIA via23_fixed_analog_via ;
   USEVIARULE viarule14_fixed_analog ;
END analog_rule

NONDEFAULTRULE clock1
   LAYER metal1
      WIDTH 1.5 ;    # metal1 has 1.5um width
   END metal1
   LAYER metal2
     WIDTH 1.5 ;
   END metal2
   LAYER metal3
     WIDTH 1.5 ;
   END metal3
END clock1
NONDEFAULTRULE clock2
   LAYER metal1
      WIDTH 1.5 ;    # metal1 has 1.5um width
   END metal1
   LAYER metal2
      WIDTH 1.5 ;
   END metal2
   LAYER metal3
      WIDTH 1.5 ;
   END metal3
END
NONDEFAULTRULE clock
   LAYER metal1
      WIDTH 1.5 ;    # metal1 has 1.5um width
   END metal1
   LAYER metal2
      WIDTH 1.5 ;
   END metal2
   LAYER metal3
      WIDTH 1.5 ;
   END metal3
END clock

UNIVERSALNOISEMARGIN 0.1 20 ;
EDGERATETHRESHOLD1 0.1 ;
EDGERATETHRESHOLD2 0.9 ;
EDGERATESCALEFACTOR 1.0 ;

NOISETABLE 1 ;
   EDGERATE 20 ;
   OUTPUTRESISTANCE 3 ;
   VICTIMLENGTH 25 ;
   VICTIMNOISE 10 ;
#   CORRECTIONFACTOR 3 ;
#   OUTPUTRESISTANCE 5 ;
END NOISETABLE

#CORRECTIONTABLE 1 ;
#   EDGERATE 20 ;
#   OUTPUTRESISTANCE 3 ;
#   VICTIMLENGTH 25 ;
#   CORRECTIONFACTOR 10.5 ;
#   OUTPUTRESISTANCE 5.4 ;
#END CORRECTIONTABLE

SPACING
   SAMENET CUT01 CA 1.5 ;
   SAMENET CA V1 1.5 STACK ;
   SAMENET M1 M1 3.5 STACK ;
   SAMENET V1 V2 1.5 STACK ;
   SAMENET M2 M2 3.5 STACK ;
   SAMENET V2 V3 1.5 STACK ;
END SPACING

MINFEATURE 0.1 0.1 ;

DIELECTRIC 0.000345 ;

IRDROP 
   TABLE DRESHI
      0.0001 -0.7 0.001 -0.8 0.01 -0.9 0.1 -1.0 ;
   TABLE DRESLO
      0.0001 -1.7 0.001 -1.6 0.01 -1.5 0.1 -1.3 ;
   TABLE DNORESHI
      0.0001 -0.6 0.001 -0.7 0.01 -0.9 0.1 -1.1 ;
   TABLE DNORESLO
      0.0001 -1.5 0.001 -1.5 0.01 -1.4 0.1 -1.4 ;
END IRDROP

SITE  COVER
   CLASS PAD ;
   SYMMETRY R90 ;
   SIZE 10.000 BY 10.000 ;
END  COVER

SITE  IO
   CLASS PAD ;
   SIZE 80.000 BY 560.000 ;
END  IO

SITE  CORE
   CLASS CORE ;
   SIZE 0.700 BY 8.400 ;
END  CORE

SITE CORE1
   CLASS CORE ;
   SYMMETRY X ;
   SIZE 67.2 BY 6 ;
END CORE1

SITE MRCORE
#   CLASS VIRTUAL ;
   CLASS CORE ;
   SIZE 3.6 BY 28.8 ;
   SYMMETRY  Y  ;
END MRCORE

SITE IOWIRED
   CLASS PAD ;
   SIZE 57.6 BY 432 ;
END IOWIRED

SITE IMAGE
   CLASS CORE ;
   ROWPATTERN Fsite1 N Lsite1 N Lsite1 FS ;
   SIZE 1 BY 1 ;
END IMAGE

SITE Fsite
   CLASS CORE ;
   SIZE 4.0 BY 7.0 ;   # 4.0 um wide, 7.0 um high
END Fsite

SITE Lsite
   CLASS CORE ;
   SIZE 6.0 BY 7.0 ;   # 6.0 um wide, 7.0 um high
END Lsite

SITE mySite
   CLASS CORE ;
   ROWPATTERN Fsite N Lsite N Lsite FS ; # a pattern of F + L + flipped L
   SIZE 16.0 BY 7.0 ;                    # width = width(F + L + L)
END mySite

ARRAY M7E4XXX
   SITE CORE         -5021.450 -4998.000 N DO 14346 BY 595 STEP 0.700 16.800 ;
   SITE CORE         -5021.450 -4989.600 FS DO 14346 BY 595 STEP 0.700 16.800 ;
   SITE IO           6148.800 5800.000 E DO 1 BY 1 STEP 0.000 0.000 ;
   SITE IO           6148.800 3240.000 E DO 1 BY 1 STEP 0.000 0.000 ;
   SITE COVER        -7315.000 -7315.000 N DO 1 BY 1 STEP 0.000 0.000 ;
   SITE COVER        7305.000 7305.000 N DO 1 BY 1 STEP 0.000 0.000 ;
   CANPLACE COVER    -7315.000 -7315.000 N DO 1 BY 1 STEP 0.000 0.000 ;
   CANPLACE COVER    -7250.000 -7250.000 N DO 5 BY 1 STEP 40.000 0.000 ;
   CANPLACE COVER    -7250.000 -7250.000 N DO 5 BY 1 STEP 40.000 0.000 ;
   CANNOTOCCUPY CORE -5021.450 -4989.600 FS DO 100 BY 595 STEP 0.700 16.800 ;
   CANNOTOCCUPY CORE -5021.450 -4998.000 N DO 100 BY 595 STEP 0.700 16.800 ;
   CANNOTOCCUPY CORE -5021.450 -4998.000 N DO 100 BY 595 STEP 0.700 16.800 ;
   TRACKS X -6148.800 DO 17569 STEP 0.700 LAYER RX ;
   TRACKS Y -6148.800 DO 20497 STEP 0.600 LAYER RX ;
   TRACKS Y -6148.800 DO 20497 STEP 0.600 LAYER RX ;

   FLOORPLAN 100%
      CANPLACE COVER  -7315.000 -7315.000 N DO 1 BY 1 STEP 0.000 0.000 ;
      CANPLACE COVER  -7250.000 -7250.000 N DO 5 BY 1 STEP 40.000 0.000 ;
      CANPLACE CORE   -5021.450 -4998.000 N DO 14346 BY 595 STEP 0.700 16.800 ;
      CANPLACE CORE   -5021.450 -4989.600 FS DO 14346 BY 595 STEP 0.700 16.800 ;
      CANNOTOCCUPY CORE -5021.450 -4989.600 FS DO 100 BY 595 STEP 0.700 16.800 ;
      CANNOTOCCUPY CORE -5021.450 -4998.000 N DO 100 BY 595 STEP 0.700 16.800 ;
   END 100%
   GCELLGRID X  -6157.200 DO 1467 STEP 8.400 ;
   GCELLGRID Y  -6157.200 DO 1467 STEP 8.400 ;
   GCELLGRID Y  -6157.200 DO 1467 STEP 8.400 ;
END M7E4XXX

MACRO CHK3A
CLASS RING ;
   FIXEDMASK ;
   SOURCE USER ;
   FOREIGN CHKS 0 0 FN ;
   ORIGIN 0.9 0.9 ;
   EEQ CHK1 ; 
   LEQ CHK2 ; 
   SIZE 10.8 BY 28.8 ;
#  for testing the lefrWarning.log file
#     SITE CORE ;
   SYMMETRY X Y R90  ;
   SITE CORE ;
   POWER 1.0 ;
   PROPERTY stringProp "first" integerProp 1 WEIGHT 30.31 ;

   PIN GND
      TAPERRULE RULE1 ;
      FOREIGN GROUND ( 0 0 ) E ;
      FOREIGN CHKS ( 5 4 ) N ;
      FOREIGN VCC ( 6 5 ) FE ;
      FOREIGN CHK1 ( 7 6 ) W ;
      LEQ  A ;
      DIRECTION INOUT ;
      USE GROUND ;
      SHAPE ABUTMENT ;
      INPUTNOISEMARGIN  6.1 2.3 ;
      OUTPUTNOISEMARGIN 5.0  4.6 ;
      OUTPUTRESISTANCE 7.4 5.4 ;
      POWER 2.0 ;
      LEAKAGE 1.0 ;
      CAPACITANCE 0.1 ;        
      RESISTANCE 0.2 ;
      PULLDOWNRES 0.5 ;
      TIEOFFR 0.8 ; 
      VHI 5 ;        
      VLO 0 ;        
      RISEVOLTAGETHRESHOLD 2.0 ;
      FALLVOLTAGETHRESHOLD 2.0 ;
      RISETHRESH 22 ;        
      FALLTHRESH 100 ;        
      RISESATCUR 4 ;        
      FALLSATCUR .5 ;        
      CURRENTSOURCE ACTIVE ;            
#      ANTENNASIZE 0.6 LAYER RX ;
#      NAMETALAREA 3 LAYER M1 ;
#      ANTENNAMETALAREA 4 LAYER M2 ;
#      ANTENNAMETALLENGTH 5 LAYER M1 ;
#      ANTENNAMETALLENGTH 6 LAYER M2 ;
      RISESLEWLIMIT 0.01 ;
      FALLSLEWLIMIT 0.02 ;
      MAXDELAY 21 ;                        
      MAXLOAD 0.1 ;
      PROPERTY TYPE "special" intProp 23 realProp 24.25 ;
      IV_TABLES LOWT HIGHT ;

      PORT
         CLASS CORE ;
         LAYER M1 SPACING 0.05 ;
         WIDTH 1.0 ;
         RECT -0.9 3 9.9 6 ;
         VIA 100 300 IN1X ;
      END
      PORT                              # 5.7
         CLASS BUMP ;                   # 5.7
         LAYER M2 SPACING 0.06 ;        # 5.7
      END                               # 5.7
   END GND
   PIN VDD
      DIRECTION INOUT ;
      FOREIGN GROUND  STRUCTURE ( 0 0 ) E ;
      USE POWER ;
      SHAPE ABUTMENT ;
      PORT
      END
      PORT
         CLASS NONE ;
         LAYER M1 ;
            RECT ITERATE -0.9 21 9.9 24 
               DO 1 BY 2 STEP 1 1 ;
            VIA ITERATE 100 300 nd1VIA12 
               DO 1 BY 2 STEP 1 2 ;
      END
#      ANTENNAMETALAREA 3 LAYER M1 ;
#      ANTENNAMETALAREA 4 LAYER M2 ;
#      ANTENNAMETALLENGTH 5 LAYER M1 ;
#      ANTENNAMETALLENGTH 6 LAYER M2 ;
      # Test for combination of both 5.3 & 5.4, which is not allowed
      # ANTENNAPARTIALMETALAREA 4 LAYER M1 ;
      ANTENNAPARTIALCUTAREA 4.8216 LAYER V1 ;
      ANTENNAMODEL OXIDE1 ;
      ANTENNAGATEAREA 297.0130 LAYER M2 ;
      ANTENNAMODEL OXIDE3 ;
      ANTENNAGATEAREA 162.4800 LAYER M2 ;
      ANTENNADIFFAREA 5008.4600 LAYER M2 ;
      ANTENNAPARTIALMETALAREA 10611.2002 LAYER M2 ;
      ANTENNAPARTIALCUTAREA 185.7300 LAYER V2 ;
      ANTENNAMODEL OXIDE1 ;
      ANTENNAGATEAREA 297.2140 LAYER M3 ;
      ANTENNADIFFAREA 5163.8799 LAYER M3 ;
      ANTENNAPARTIALMETALAREA 2450.2600 LAYER M3 ;
      LEAKAGE 1.0 ;
      FALLVOLTAGETHRESHOLD 2.0 ;
      RISEVOLTAGETHRESHOLD 2.0 ;
      CURRENTSOURCE ACTIVE ;
   END VDD
   PIN PA3
      DIRECTION INPUT ;
      # 5.4
      ANTENNAPARTIALMETALAREA 4 LAYER M1 ;
      ANTENNAPARTIALMETALAREA 5 LAYER M2 ;
      ANTENNAPARTIALMETALSIDEAREA 5 LAYER M2 ;
      ANTENNAPARTIALMETALSIDEAREA 6 LAYER M2 ;
      ANTENNAPARTIALMETALSIDEAREA 7 LAYER M2 ;
      ANTENNAGATEAREA 1 LAYER M1 ;
      ANTENNAGATEAREA 2 ;
      ANTENNAGATEAREA 3 LAYER M3 ;
      ANTENNADIFFAREA 1 LAYER M1 ;
      ANTENNAMAXAREACAR 1 LAYER L1 ;
      ANTENNAMAXAREACAR 2 LAYER L2 ;
      ANTENNAMAXAREACAR 3 LAYER L3 ;
      ANTENNAMAXAREACAR 4 LAYER L4 ;
      ANTENNAMAXSIDEAREACAR 1 LAYER L1 ;
      ANTENNAMAXSIDEAREACAR 2 LAYER L2 ;
      ANTENNAPARTIALCUTAREA 1 ;
      ANTENNAPARTIALCUTAREA 2 LAYER M2 ;
      ANTENNAPARTIALCUTAREA 3 ;
      ANTENNAPARTIALCUTAREA 4 LAYER M4 ;
      ANTENNAMAXCUTCAR 1 LAYER L1 ;
      ANTENNAMAXCUTCAR 2 LAYER L2 ;
      ANTENNAMAXCUTCAR 3 LAYER L3 ;
      # Test for combination of both 5.3 & 5.4, which is not allowed
      # ANTENNAMETALLENGTH 5 LAYER M1 ;
      PORT
         LAYER M1 SPACING 0.02 ;
            RECT 1.35 -0.45 2.25 0.45 ;
            RECT -0.45 -0.45 0.45 0.45 ;
      END
      PORT
         LAYER PC DESIGNRULEWIDTH 0.05 ;
            RECT -0.45 12.15 0.45 13.05 ;
      END
      PORT
        LAYER PC ;
            RECT -0.45 24.75 0.45 25.65 ;
      END
      PORT
      END
   END PA3
   PIN PA0
      DIRECTION INPUT ;
      MUSTJOIN PA3 ;
      PORT
         CLASS NONE ;
         LAYER M1 ;
            RECT 8.55 8.55 9.45 9.45 ;
            RECT 6.75 6.75 7.65 7.65 ;
            RECT 6.75 8.55 7.65 9.45 ;
            RECT 6.75 10.35 7.65 11.25 ;
      END
      PORT
         CLASS CORE ;
         LAYER PC ;
            RECT 8.55 24.75 9.45 25.65 ;
      END
      PORT
         LAYER PC ;
            RECT 6.75 1.35 7.65 2.25 ;
      END
      PORT
         LAYER PC ;
            RECT 6.75 24.75 7.65 25.65 ;
      END
      PORT
         LAYER PC ;
            RECT 4.95 1.35 5.85 2.25 ;
      END
   END PA0
   PIN PA1
      DIRECTION INPUT ;
      PORT
         LAYER M1 ;
            RECT 8.55 -0.45 9.45 0.45 ;
            RECT 6.75 -0.45 7.65 0.45 ;
      END
      PORT
         LAYER M1 ;
            RECT 8.55 12.15 9.45 13.05 ;
            RECT 6.75 12.15 7.65 13.05 ;
            RECT 4.95 12.15 5.85 13.05 ;
      END
      PORT
         LAYER PC ;
            RECT 4.95 24.75 5.85 25.65 ;
      END
      PORT
         LAYER PC ;
            RECT 3.15 24.75 4.05 25.65 ;
      END
   END PA1
   PIN PA20
      DIRECTION INPUT ;
      PORT
         LAYER M1 ;
            POLYGON 15 35 15 60 65 60 65 35 15 35 ;
      END
      PORT
         LAYER M1 ;
            PATH 8.55 12.15 9.45 13.05 ;
      END
   END PA20
   PIN PA21
      DIRECTION OUTPUT TRISTATE ;
      PORT
         LAYER M1 ;
            POLYGON ITERATE 20 35 20 60 70 60 70 35 DO 1 BY 2 STEP 5 5 ;
      END
      PORT
         LAYER M1 ;
            PATH ITERATE 5.55 12.15 10.45 13.05 DO 1 BY 2 STEP 2 2 ;
      END
   END PA21
   OBS
      LAYER M1 SPACING 5.6 ;
         RECT 6.6 -0.6 9.6 0.6 ;
         RECT 4.8 12 9.6 13.2 ;
         RECT 3 13.8 7.8 16.8 ;
         RECT 3 -0.6 6 0.6 ;
         RECT 3 8.4 6 11.4 ;
         RECT 3 8.4 4.2 16.8 ;
         RECT -0.6 13.8 4.2 16.8 ;
         RECT -0.6 -0.6 2.4 0.6 ;
         RECT 6.6 6.6 9.6 11.4 ;
         RECT 6.6 6.6 7.8 11.4 ;
   END 
   TIMING
      FROMPIN PA21 ;
      TOPIN PA20 ;
      RISE INTRINSIC .39 .41 1.2 .25 .29 1.8 .67 .87 2.2
         VARIABLE 0.12 0.13 ;
      FALL INTRINSIC .24 .29 1.3 .26 .31 1.7 .6 .8 2.1
         VARIABLE 0.11 0.14 ;
      RISERS 83.178 90.109 ;
      FALLRS 76.246 97.041 ;
      RISECS 0.751 0.751 ;
      FALLCS 0.751 0.751 ;
      RISET0 0.65493 0.65493 ;
      FALLT0 0.38 0.38 ;
      RISESATT1 0 0 ;
      FALLSATT1 0.15 0.15 ;
      UNATENESS INVERT ;
   END TIMING
END CHK3A

MACRO INV
   CLASS CORE ;
   SOURCE BLOCK ;
   FOREIGN INVS ;
   POWER 1.0 ;
   SIZE 67.2 BY 24 ;
   SYMMETRY X Y R90 ;
   SITE CORE1 ;

   PIN Z DIRECTION OUTPUT ;
      USE SIGNAL ;
      RISETHRESH 22 ;
      FALLTHRESH 100 ;
      RISESATCUR 4 ;
      FALLSATCUR .5 ;
      VLO 0 ;
      VHI 5 ;
      CAPACITANCE 0.1 ;
      MAXDELAY 21 ;
      POWER 0.1 ;
      ANTENNAPARTIALCUTAREA 4.8216 LAYER V1 ;
      ANTENNAMODEL OXIDE1 ;
      ANTENNAGATEAREA 297.0130 LAYER M2 ;
      ANTENNAMODEL OXIDE2 ;
      ANTENNAMODEL OXIDE3 ;
      ANTENNAGATEAREA 162.4800 LAYER M2 ;
      ANTENNADIFFAREA 5008.4600 LAYER M2 ;
      ANTENNAPARTIALMETALAREA 10611.2002 LAYER M2 ;
      ANTENNAPARTIALCUTAREA 185.7300 LAYER V2 ;
      ANTENNAMODEL OXIDE1 ;
      ANTENNAGATEAREA 297.2140 LAYER M3 ;
      ANTENNADIFFAREA 5163.8799 LAYER M3 ;
      ANTENNAPARTIALMETALAREA 2450.2600 LAYER M3 ;
      PORT
         LAYER M2 ;
            PATH 30.8 9 42 9 ;
      END
   END Z

   PIN A DIRECTION INPUT ;
      USE ANALOG ;
      RISETHRESH 22 ;
      FALLTHRESH 100 ;
      RISESATCUR 4 ;
      FALLSATCUR .5 ;
      VLO 0 ;
      VHI 5 ;
      CAPACITANCE 0.08 ;
      MAXDELAY 21 ;
      PORT
         LAYER M1 ;
            PATH 25.2 15 ;
      END
   END A

   PIN VDD DIRECTION INOUT ;
      SHAPE ABUTMENT ;
      POWER 0.1 ;
      ANTENNAPARTIALCUTAREA 4.8216 LAYER V1 ;
      ANTENNAMODEL OXIDE1 ;
      ANTENNAGATEAREA 297.0130 LAYER M2 ;
      ANTENNAMODEL OXIDE3 ;
      ANTENNAGATEAREA 162.4800 LAYER M2 ;
      ANTENNADIFFAREA 5008.4600 LAYER M2 ;
      ANTENNAPARTIALMETALAREA 10611.2002 LAYER M2 ;
      ANTENNAPARTIALCUTAREA 185.7300 LAYER V2 ;
      ANTENNAMODEL OXIDE1 ;
      ANTENNAGATEAREA 297.2140 LAYER M3 ;
      ANTENNADIFFAREA 5163.8799 LAYER M3 ;
      ANTENNAPARTIALMETALAREA 2450.2600 LAYER M3 ;
      ANTENNAMODEL OXIDE2 ;
      PORT
         LAYER M1 ;
            WIDTH 5.6 ;
            PATH 50.4 2.8 50.4 21.2 ;
      END
   END VDD

   PIN VSS DIRECTION INOUT ;
      SHAPE ABUTMENT ;
      POWER 0.1 ;
      PORT
         LAYER M1 ;
            WIDTH 5.6 ;
            PATH 16.8 2.8 16.8 21.2 ;
      END
   END VSS

   TIMING
      FROMPIN A ;
      TOPIN Z ;
      RISE INTRINSIC .39 .41 1.2 .25 .29 1.8 .67 .87 2.2
         VARIABLE 0.12 0.13 ;
      FALL INTRINSIC .24 .29 1.3 .26 .31 1.7 .6 .8 2.1
         VARIABLE 0.11 0.14 ;
      RISERS 83.178 90.109 ;
      FALLRS 76.246 97.041 ;
      RISECS 0.751 0.751 ;
      FALLCS 0.751 0.751 ;
      RISET0 0.65493 0.65493 ;
      FALLT0 0.38 0.38 ;
      RISESATT1 0 0 ;
      FALLSATT1 0.15 0.15 ;
      UNATENESS INVERT ;
   END TIMING

   OBS
      LAYER M1 DESIGNRULEWIDTH 4.5 ;
         WIDTH 0.1 ;
         RECT MASK 2 24.1 1.5 43.5 16.5 ;
         RECT MASK 2 ITERATE 24.1 1.5 43.5 16.5
            DO 2 BY 1 STEP 20.0 0 ;
         PATH MASK 3 ITERATE 532.0 534 1999.2 534
            DO 1 BY 2 STEP 0 1446 ;
         VIA ITERATE MASK 123 470.4 475 VIABIGPOWER12
            DO 2 BY 2 STEP 1590.4 1565 ;
         PATH MASK 3 532.0 534 1999.2 534 ;
         PATH MASK 3 532.0 1980 1999.2 1980 ;
         VIA MASK 103 470.4 475 VIABIGPOWER12 ;
         VIA MASK 132 2060.8 475 VIABIGPOWER12 ;
         VIA MASK 112 470.4 2040 VIABIGPOWER12 ;
         VIA MASK 123 2060.8 2040 VIABIGPOWER12 ;
         RECT 44.1 1.5 63.5 16.5 ;
   END

   DENSITY
      LAYER metal1 ;
         RECT 0 0 100 100 45.5 ;  #rec from (0,0) to (100,100), density of 45.5%
         RECT 100 0 200 100 42.2 ; 
      LAYER metal2 ;
         RECT 0 0 250 140 20.5 ; 
         RECT 1 1 250 140 20.5 ; 
         RECT 2 2 250 140 20.5 ; 
      LAYER metal3 ;
         RECT 10 10 40 40 4.5 ;
   END
END INV

MACRO INV_B
   EEQ INV ;
   CLASS CORE SPACER ;
   FOREIGN INVS ( 4 5 ) ;
   FOREIGN INV1 ( 5 6 ) S ;
   FOREIGN INV2 ( 6 7 ) N ;
   FOREIGN INV3 ( 7 8 ) ;
   POWER 1.0 ;
   SIZE 67.2 BY 24 ;
   SYMMETRY X Y R90 ;
   SITE CORE1 ;
   PIN Z DIRECTION OUTPUT ;
      USE CLOCK ;
      RISETHRESH 22 ;
      FALLTHRESH 100 ;
      RISESATCUR 4 ;
      FALLSATCUR .5 ;
      VLO 0 ;
      VHI 5 ;
      CAPACITANCE 0.1 ;
      MAXDELAY 21 ;
      POWER 0.1 ;
      PORT
         LAYER M1 ;
            WIDTH 1 ;
            PATH MASK 2 ( -0.6 -0.6 ) ( 0.6 -0.6 ) ( 0.7 -0.6 ) ;
         LAYER M2 ;
            WIDTH 1 ;
            RECT MASK 1 ( -0.6 -0.6 ) ( 0.6 0.6 ) ;
         LAYER M3 ;
            WIDTH 1 ;
            RECT MASK 2 ITERATE ( -0.6 -0.6 ) ( 0.6 0.6 )
            DO 1 BY 2 STEP 2 1 ;
         LAYER M4 ;
            PATH MASK 1 30.8 9 42 9 ;
	 VIA MASK 103 470.4 475 VIABIGPOWER12 ;
         VIA MASK 130 2060.8 475 VIABIGPOWER12 ;
         VIA MASK 113 470.4 2040 VIABIGPOWER12 ;
         VIA MASK 121 2060.8 2040 VIABIGPOWER12 ;
      END
   END Z

   PIN A DIRECTION FEEDTHRU ;
      USE SIGNAL ;
      RISETHRESH 22 ;
      FALLTHRESH 100 ;
      RISESATCUR 4 ;
      FALLSATCUR .5 ;
      VLO 0 ;
      VHI 5 ;
      CAPACITANCE 0.08 ;
      MAXDELAY 21 ;
      PORT
         LAYER M1 ;
            PATH 25.2 15 ;
      END
   END A

   PIN VDD DIRECTION INOUT ;
      SHAPE ABUTMENT ;
      POWER 0.1 ;
      PORT
         LAYER M1 ;
            WIDTH 5.6 ;
            PATH 50.4 2.8 50.4 21.2 ;
      END
   END VDD

   PIN VSS DIRECTION INOUT ;
      SHAPE ABUTMENT ;
      POWER 0.1 ;
      PORT
         LAYER M1 ;
            WIDTH 5.6 ;
            PATH 16.8 2.8 16.8 21.2 ;
      END
   END VSS

   TIMING
      FROMPIN A ;
      TOPIN Z ;
      RISE INTRINSIC .39 .41 1.2 .25 .29 1.8 .67 .87 2.2
         VARIABLE 0.12 0.13 ;
      FALL INTRINSIC .24 .29 1.3 .26 .31 1.7 .6 .8 2.1
         VARIABLE 0.11 0.14 ;
      RISERS 83.178 90.109 ;
      FALLRS 76.246 97.041 ;
      RISECS 0.751 0.751 ;
      FALLCS 0.751 0.751 ;
      RISET0 0.65493 0.65493 ;
      FALLT0 0.38 0.38 ;
      RISESATT1 0 0 ;
      FALLSATT1 0.15 0.15 ;
      UNATENESS INVERT ;
   END TIMING

   OBS
      LAYER M1 ;
         RECT 24.1 1.5 43.5 16.5 ;
   END
END INV_B

MACRO DFF3
   CLASS CORE ANTENNACELL ;
   FOREIGN DFF3S ;
   POWER 4.0 ;
   SIZE 67.2 BY 210 ;
   SYMMETRY X Y R90 ;
   SITE CORE 34 54 FE DO 30 BY 3 STEP 1 1 ;
   SITE CORE1 21 68 S DO 30 BY 3 STEP 2 2 ;

   PIN Q DIRECTION OUTPUT ;
      USE SIGNAL ;
      RISETHRESH 22 ;
      FALLTHRESH 100 ;
      RISESATCUR 4 ;
      FALLSATCUR .5 ;
      VLO 0 ;
      VHI 5 ;
      CAPACITANCE 0.12 ;
      MAXDELAY 20 ;
      POWER 0.4 ;
      PORT
         LAYER M2 ;
            PATH 19.6 99 47.6 99 ;
      END
   END Q

   PIN QN DIRECTION OUTPUT ;
      USE SIGNAL ;
      RISETHRESH 22 ;
      FALLTHRESH 100 ;
      RISESATCUR 4 ;
      FALLSATCUR .5 ;
      VLO 0 ;
      VHI 5 ;
      CAPACITANCE 0.11 ;
      MAXDELAY 20 ;
      POWER 0.4 ;
      PORT
         LAYER M2 ;
            PATH MASK 1 25.2 123 42 123 ;
	    RECT MASK 2 24.1 1.5 43.5 208.5 ;
      END
   END QN

   PIN D DIRECTION INPUT ;
      USE SIGNAL ;
      RISETHRESH 22 ;
      FALLTHRESH 100 ;
      RISESATCUR 4 ;
      FALLSATCUR .5 ;
      VLO 0 ;
      VHI 5 ;
      CAPACITANCE 0.13 ;
      MAXDELAY 20 ;
      POWER 0.4 ;
      PORT
         LAYER M1 ;
            PATH 30.8 51 ;
      END
   END D

   PIN G DIRECTION INPUT ;
      USE SIGNAL ;
      RISETHRESH 22 ;
      FALLTHRESH 100 ;
      RISESATCUR 4 ;
      FALLSATCUR .5 ;
      VLO 0 ;
      VHI 5 ;
      CAPACITANCE 0.11 ;
      MAXDELAY 20 ;
      POWER 0.4 ;
      PORT
         LAYER M1 ;
            PATH 25.2 3 ;
      END
   END G

   PIN CD DIRECTION INPUT ;
      USE CLOCK ;
      RISETHRESH 22 ;
      FALLTHRESH 100 ;
      RISESATCUR 4 ;
      FALLSATCUR .5 ;
      VLO 0 ;
      VHI 5 ;
      CAPACITANCE 0.1 ;
      MAXDELAY 20 ;
      POWER 0.4 ;
      PORT
         LAYER M1 ;
            PATH 36.4 75 ;
      END
   END CD

   PIN VDD DIRECTION INOUT ;
      SHAPE RING ;
      POWER 0.4 ;
      PORT
         LAYER M1 ;
            WIDTH 5.6 ;
            PATH 50.4 2.8 50.4 207.2 ;
      END
   END VDD

   PIN VSS DIRECTION INOUT ;
      SHAPE FEEDTHRU ;
      POWER 0.4 ;
      PORT
         LAYER M1 ;
            WIDTH 5.6 ;
            PATH 16.8 2.8 16.8 207.2 ;
      END
   END VSS

   TIMING
      FROMPIN D ;
      TOPIN Q ;
      RISE INTRINSIC .51 .6 1.4 .37 .45 1.7 .6 .81 2.1
         VARIABLE 0.06 0.1 ;
      FALL INTRINSIC 1 1.2 1.4 1.77 1.85 1.8 .56 .81 2.4
         VARIABLE 0.08 0.09 ;
      RISERS 41.589 69.315 ;
      FALLRS 55.452 62.383 ;
      RISECS 0.113 0.113 ;
      FallCS 0.113 0.113 ;
      RISET0 0.023929 0.023929 ;
      FALLT0 0.38 0.38 ;
      RISESATT1 0 0 ;
      FALLSATT1 0.15 0.15 ;
      UNATENESS      NONINVERT ;
   END TIMING

   OBS
      LAYER M1 DESIGNRULEWIDTH 0.15 ;
         RECT 24.1 1.5 43.5 208.5 ;
         PATH 8.4 3 8.4 123 ;
         PATH 58.8 3 58.8 123 ;
         PATH 64.4 3 64.4 123 ;
   END

   DENSITY
      LAYER metal4 ;
         RECT 24.1 1.5 43.5 208.5 5.5 ;
   END
END DFF3

MACRO BUF1
   CLASS ENDCAP BOTTOMLEFT ;
   PIN IN
      ANTENNAGATEAREA 1 ;
      ANTENNAGATEAREA 3 ;
      ANTENNADIFFAREA 0 ;
      ANTENNAMODEL OXIDE2 ;
      ANTENNAGATEAREA 2 ;
      ANTENNAGATEAREA 4 ;
      ANTENNADIFFAREA 0 ;
   END IN
   PIN IN2
      ANTENNAGATEAREA 1 ;
   END IN2
   PIN IN3
      SHAPE ABUTMENT ;
   END IN3
   PIN GND
      USE GROUND ;
      PORT
         LAYER metal1 ;
         POLYGON 0 0 0 1.0 1.0 0 2.0 2.0 2.0 0 ;
         RECT 2.0 3.0 4.0 5.0 ;
      END
   END GND
   OBS
      LAYER metal2 EXCEPTPGNET ;             # 5.7
      POLYGON 0 0 0 1.0 1.0 0 2.0 2.0 2.0 0 ;
      RECT 2.0 3.0 4.0 5.0 ;
   END
END BUF1

MACRO DFF4
   CLASS COVER BUMP ;
   FOREIGN DFF3S ;
   POWER 4.0 ;
END DFF4

MACRO DFF5
   CLASS COVER ;
   FOREIGN DFF3S ;
END DFF5

MACRO mydriver
   CLASS PAD AREAIO ;
   FOREIGN DFF3S ;
END mydriver

MACRO myblackbox
   CLASS BLOCK BLACKBOX ;
   FOREIGN DFF3S ;
END myblackbox

MACRO FWHSQCN690V15
   CLASS CORE ;
   FOREIGN FWHSQCN690 0.00 0.00 ;
   SIZE 15.36 BY 4.80 ;
   SYMMETRY Y X ;
   ORIGIN 0.0 0.0 ;
   SITE CORE ;
   PIN R DIRECTION INPUT ;
      CAPACITANCE 0.004872 ;
      USE SIGNAL ;
      PORT
         LAYER a1sig ;
         RECT 11.43 0.80 11.71 1.20 ;
         RECT 9.94 0.80 11.43 1.04 ;
         LAYER a1sig ;
         RECT 9.49 0.80 9.71 0.82 ;
         RECT 9.71 0.80 9.93 1.04 ;
         RECT 9.92 0.80 9.94 1.04 ;
         RECT 9.00 2.06 9.72 2.08 ;
         RECT 9.39 0.80 9.47 1.78 ;
         RECT 9.46 0.80 9.48 1.78 ;
         RECT 9.47 0.80 9.49 1.77 ;
         RECT 9.48 0.81 9.72 2.07 ;
         RECT 9.00 1.77 9.48 2.07 ;
      END
   END R
   PIN SI DIRECTION INPUT ;
      CAPACITANCE 0.002213 ;
      USE SIGNAL ;
      PORT
         LAYER a1sig ;
#         RECT 4.00 2.07 4.20 2.36 ;
#         RECT 4.19 2.07 4.21 2.36 ;
#         RECT 4.21 2.07 4.44 2.25 ;
#         RECT 4.21 2.24 4.44 2.26 ;
#         RECT 4.20 2.25 4.44 3.03 ;
      END
   END SI
   PIN SM DIRECTION INPUT ;
      CAPACITANCE 0.002307 ;
      USE SIGNAL ;
      PORT
         LAYER a1sig ;
         RECT 0.82 3.51 1.08 3.89 ;
         LAYER a1sig ;
         RECT 0.36 3.51 0.59 3.69 ;
         RECT 0.36 3.68 0.59 3.70 ;
         RECT 0.59 3.51 0.81 3.89 ;
         RECT 0.59 3.51 0.81 3.89 ;
         RECT 0.80 3.51 0.82 3.89 ;
         RECT 0.36 3.69 0.60 3.99 ;
         LAYER a1sig ;
      END
   END SM
   PIN T DIRECTION INPUT ;
      CAPACITANCE 0.002260 ;
      USE SIGNAL ;
      PORT
         LAYER a1sig ;
#        RECT 5.14 3.51 5.15 3.99 ;
         LAYER a1sig ;
         RECT 4.85 3.51 4.91 3.70 ;
         RECT 4.91 3.51 5.13 3.99 ;
         RECT 5.12 3.51 5.14 3.99 ;
         RECT 4.20 3.69 4.92 3.99 ;
      END
   END T
   OBS
      LAYER a1sig SPACING 0 ;
      RECT 0.11 0.74 0.81 3.47 ;
      RECT 0.80 0.74 0.82 3.47 ;
      RECT 0.82 0.74 2.54 4.06 ;
      RECT 2.54 0.74 3.70 3.47 ;
      RECT 3.70 0.74 3.98 4.06 ;
      RECT 3.98 0.74 5.14 3.47 ;
      RECT 5.14 0.74 8.78 4.06 ;
      RECT 8.78 0.74 9.94 1.54 ;
      RECT 8.78 1.53 9.94 1.55 ;
      RECT 8.78 2.29 9.94 4.06 ;
      RECT 9.94 0.74 14.06 4.06 ;
      RECT 14.06 0.74 15.24 2.51 ;
      RECT 15.23 0.74 15.25 2.51 ;
      RECT 14.06 3.25 15.25 4.05 ;
      RECT 14.06 4.04 15.25 4.06 ;
   END
END FWHSQCN690V15

MACRO mysoft
   CLASS BLOCK SOFT ;
   FOREIGN DFF3S ;
   OBS
      LAYER a1sig DESIGNRULEWIDTH 0 ;
      RECT 0.11 0.74 0.81 3.47 ;
   END
END mysoft

MACRO mycorewelltap
   CLASS CORE WELLTAP ;
   FOREIGN DFF3S ;
END mycorewelltap

MACRO myTest
   CLASS CORE ;
   SIZE 10.0 BY 14.0 ; #uses two F and one L site, is F+L wide,and double height
   SYMMETRY X ;        #can flip about X-axis
   SITE Fsite 0 0 N ;  #the lower, left Fsite at 0,0
   SITE Fsite 0 7.0 FS ;  #the flipped-south Fsite above the first Fsite at 0,7
   SITE Lsite 4.0 0 N ;   #the Lsite to the right of the first Fsite at 4,0
   SITE Lsite 0 0 S DO 2 BY 1 STEP 4 5 ;
   SITE Fsite 4.0 0 E ;
   SITE Fsite ;
   SITE Lsite 0.3 0 S DO 2 BY 1 STEP 4 5 ;
   SITE Fsite 0 0 N DO 2 BY 1 STEP 4.0 0 ;
END myTest

MACRO myMac
   CLASS CORE ;
   SIZE 10.0 BY 14.0 ;
   SYMMETRY X ;
   PIN In1
      USE SIGNAL ;
      SUPPLYSENSITIVITY vddpin1 ; #if in1 is 1'b1, use net connected to vddpin1
                                 #note, no GROUNDSENSITIVITY is needed because
                                 #only one ground pin exists, so 1'b0 implicitly
                                 #means net from pin gndpin
      CAPACITANCE 0.12 ;
      MAXDELAY 20 ;
   END In1
   PIN vddpin1
      USE SIGNAL ;
      CAPACITANCE 0.11 ;
      MAXDELAY 20 ;
      NETEXPR "power1 VDD1" ;    #if power1 is defined in the netlist, use it to
                                 #find the net connection, else use net VDD1
      POWER 0.4 ;
   END vddpin1
   PIN vddpin2
      USE SIGNAL ;
      MAXDELAY 20 ;
      NETEXPR "power2 VDD2" ;    #if power2 is defined in the netlist, use it to
                                 #find the net connection, else use net VDD2
      POWER 0.4 ;
   END vddpin2
   PIN gndpin
      USE SIGNAL ;
      NETEXPR "gnd1 GND" ;       #if gnd1 is defined in the netlist, use it to
                                 #find the net connection, else use net GND
      MAXDELAY 20 ;
      POWER 0.4 ;
   END gndpin
   PIN In2
      USE SIGNAL ;
      GROUNDSENSITIVITY gndpin ;
      POWER 0.6 ;
   END In2
END myMac

ANTENNAINPUTGATEAREA 45 ;
ANTENNAINOUTDIFFAREA 65 ;
ANTENNAOUTPUTDIFFAREA 55 ;

#INPUTPINANTENNASIZE 1 ;
#OUTPUTPINANTENNASIZE -1 ;
#INOUTPINANTENNASIZE -1 ;

BEGINEXT "SIGNATURE"
   CREATOR "CADENCE"
   DATE "04/14/98"
ENDEXT

#END LIBRARY   This is optional in 5.6
