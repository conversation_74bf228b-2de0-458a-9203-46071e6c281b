Minimize
0 x11 + 15 x12 + 5 x13 + 20 x14 
+ 10 x21 + 10 x22 + 20 x23 
+ 0 x33 + 15 x34 
+ 5 x43 + 10 x44
+ 20 x51 + 15 x52 + 10 x53 + 4 x54
Subject To
x11 + x12 + x13 + x14 = 1
x21 + x22 + x23 = 1
x33 + x34 = 1
x43 + x44 = 1
x51 + x52 + x53 + x54 = 1
4 x11 + 2 x21 + 6 x33 + 5 x43 + 5 x51 <= 10
4 x12 + 2 x22 + 6 x34 + 5 x44 + 5 x52 <= 10
4 x13 + 2 x23 + 6 x33 + 5 x43 + 5 x53 <= 10 
4 x14 + 6 x34 + 5 x44 + 5 x54 <= 10
Bounds
0 <= x11 <= 1
0 <= x12 <= 1
0 <= x13 <= 1
0 <= x14 <= 1
0 <= x21 <= 1
0 <= x22 <= 1
0 <= x23 <= 1
0 <= x33 <= 1
0 <= x34 <= 1
0 <= x43 <= 1
0 <= x44 <= 1
0 <= x51 <= 1
0 <= x52 <= 1
0 <= x53 <= 1
0 <= x54 <= 1
Binary
x11
x12
x13
x14
x21
x22
x23
x33
x34
x43
x44
x51
x52
x53
x54
End
