# $Id$
# plain simple Makefile to build test

#==========================================================================
#                         Directories and names 
# ==========================================================================

LIMBO_ROOT_DIR = $(realpath ../../..)
LIBDIR = $(LIMBO_ROOT_DIR)/lib
LIBMKDIR = if [ ! -d $(@D) ]; then mkdir -p $(@D); fi

#==========================================================================
#                         Compilation Flags
# ==========================================================================

# include environmental configurations 
include $(LIMBO_ROOT_DIR)/Include.mk

# ==========================================================================
#                         Standard Setting
# ==========================================================================

# make single threaded OpenBLAS
all: 
ifeq ($(wildcard OpenBLAS/*.*),)
	@echo "OpenBLAS submodule is EMPTY" 
	@cd OpenBLAS
	@git submodule init 
	@git submodule update 
	@cd ..
else 
	@echo "OpenBLAS submodule is NOT EMPTY" 
endif 
	@make -C OpenBLAS -f Makefile BINARY=64 CC=$(CC) FC=$(FC) USE_OPENMP=0 USE_THREAD=0 NO_CBLAS=1 NO_WARMUP=1 libs netlib
	@$(LIBMKDIR)
	mv OpenBLAS/libopenblas_*.a $(LIBDIR)/libopenblas-st.a # the name may vary from architectures 

.PHONY: install
install:
ifeq ($(wildcard OpenBLAS/*.*),)
	@echo "OpenBLAS submodule is EMPTY" 
else 
	cmp -s $(PREFIX)/lib/libopenblas-st.a $(LIBDIR)/libopenblas-st.a; \
	RETVAL=$$?; \
	if [ $$RETVAL -ne 0 ]; then \
		mkdir -p $(PREFIX)/lib; \
		cp $(LIBDIR)/libopenblas-st.* $(PREFIX)/lib; \
	fi
endif 

.PHONY: clean
clean: 
ifeq ($(wildcard OpenBLAS/*.*),)
	@echo "OpenBLAS submodule is EMPTY" 
else 
	$(MAKE) -C OpenBLAS -f Makefile clean
endif 
