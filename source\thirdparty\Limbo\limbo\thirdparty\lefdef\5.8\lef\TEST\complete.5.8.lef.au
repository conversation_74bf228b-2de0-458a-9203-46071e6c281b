VERSION 5.8 ;
FIXEDMASK ;
BUS<PERSON>TCHARS "<>" ;
DIVIDER : ;
USEMINSPACING OBS OFF ;
CLEARANCEMEASURE EUCLIDEAN ;
CLEARANCEMEASURE MAXXY ;
UNITS
  DATABASE MICRONS 20000 ;
  CA<PERSON><PERSON><PERSON><PERSON><PERSON> PICOFARADS 10 ;
  RESIS<PERSON><PERSON><PERSON> OHMS 10000 ;
  POWER MILLIWATTS 10000 ;
  CURRENT MILLIAMPS 10000 ;
  VOL<PERSON>GE VOLTS 1000 ;
  FREQUENCY MEGAHERTZ 10 ;
END UNITS
MANUFACTURINGGRID 3.5 ;
PROPERTYDEFINITIONS
 library NAME Cadence96
 library intNum INTEGER 20
 library realNum REAL 21.22
 library LEF57_MAXFLOATINGAREAGATE MAXFLOATINGAREA GATEISGROUND;
 layer lsp STRING
 layer lip INTEGER
 layer lrp REAL
 layer LEF57_SPACING STRING
 layer LEF57_SPACINGADJACENTCUTS STRING
 layer LEF57_MAXFLOATINGAREA STRING
 layer LEF57_ARRAYSPACING STRING
 layer LEF57_SPACINGSAMENET STRING
 layer LEF57_MINSTEP STRING
 layer LEF57_ANTENNAGATEPLUSDIFF STRING
 layer LEF57_ANTENNACUMROUTINGPLUSCUT STRING
 layer LEF57_ANTENNAAREAMINUSDIFF STRING
 layer LEF57_ANTENNAAREADIFFREDUCEPWL STRING
 layer LEF57_ENCLOSURE STRING
 via stringProperty STRING
 via realProperty REAL
 via COUNT INTEGER RANGE 1 100
Parsed 50 number of lines!!
 viarule vrsp STRING
 viarule vrip INTEGER
 viarule vrrp REAL
 nondefaultrule ndrsp STRING
 nondefaultrule ndrip INTEGER
 nondefaultrule ndrrp REAL
 macro stringProp STRING
 macro integerProp INTEGER
 macro WEIGHT REAL RANGE 1 100
 pin TYPE STRING
 pin intProp INTEGER
 pin realProp REAL
END PROPERTYDEFINITIONS
LAYER POLYS
  TYPE MASTERSLICE ;
  PROPERTY lsp top STRING lip 1 INTEGER lrp 2.3 REAL ;
END POLYS
LAYER POLYS01
  TYPE MASTERSLICE ;
END POLYS01
LAYER CUT01
  TYPE CUT ;
  PITCH 1.2 1.3 ;
  OFFSET 0.5 0.6 ;
  DIAGPITCH 6.5 ;
  ARRAYSPACING WIDTH 2 CUTSPACING 0.2
	ARRAYCUTS 3 SPACING 1 ;
  SPACING 0.35 ADJACENTCUTS 3 WITHIN 0.25 ;
  SPACING 1.5 PARALLELOVERLAP ;
ENCLOSURE ABOVE 0.01 0.05 ;
ENCLOSURE BELOW 0.03 0.05 WIDTH 3.1 EXCEPTEXTRACUT 1.5 ;
ENCLOSURE 0.05 0.05 ;
  PROPERTY lip 5 INTEGER LEF57_SPACING SPACING 1.5 PARALLELOVERLAP ; STRING LEF57_ARRAYSPACING ARRAYSPACING  WIDTH 2.0 CUTSPACING 0.2 ARRAYCUTS 3 SPACING 1.0 ; STRING LEF57_ENCLOSURE ENCLOSURE ABOVE .01 .05 ; STRING LEF57_ENCLOSURE ENCLOSURE ABOVE .02 .05 WIDTH 3.1 EXCEPTEXTRACUT 1.5 NOSHAREDEDGE ; STRING LEF57_ENCLOSURE ENCLOSURE BELOW .03 .05 WIDTH 3.1 EXCEPTEXTRACUT 1.5 ; STRING LEF57_ENCLOSURE ENCLOSURE .05 .05 ; STRING LEF57_ENCLOSURE ENCLOSURE BELOW .08 .05 WIDTH 3.1 EXCEPTEXTRACUT 1.5 NOSHAREDEDGE ; STRING ;
END CUT01
Parsed 100 number of lines!!
Parsed 150 number of lines!!
LAYER RX
  TYPE ROUTING ;
  MASK 2 ;
  PITCH 1.8 ;
  OFFSET 0.9 ;
  WIDTH 1 ;
  AREA 34.1 ;
  ARRAYSPACING LONGARRAY CUTSPACING 0.2
	ARRAYCUTS 3 SPACING 1
	ARRAYCUTS 4 SPACING 1.5
	ARRAYCUTS 5 SPACING 2 ;
  SPLITWIREWIDTH 5 ;
  MINIMUMDENSITY 4 ;
  MAXIMUMDENSITY 10 ;
  DENSITYCHECKSTEP 2 ;
  FILLACTIVESPACING 4 ;
  MINIMUMCUT 2 WIDTH 2.5 ;
  SPACING 0.6 ;
  SPACING 0.18 LENGTHTHRESHOLD 0.9 ;
  SPACING 0.4 RANGE 0.1 0.12 ;
  SPACING 0.32 RANGE 1.01 2000 USELENGTHTHRESHOLD ;
  SPACING 0.1 RANGE 0.1 0.1 INFLUENCE 2.01 RANGE 2.1 10000 ;
  SPACING 0.44 RANGE 1 1 INFLUENCE 1.01 ;
  SPACING 0.33 RANGE 1.01 20 INFLUENCE 1.01 ;
  SPACING 0.7 RANGE 0.3 0.15 USELENGTHTHRESHOLD ;
  SPACING 0.5 ;
  SPACING 0.6 RANGE 4.5 6.12 RANGE 3 3.1 ;
  SPACING 4.3 RANGE 0.1 0.1 INFLUENCE 3.81 RANGE 0.1 0.2 ;
  SPACING 0.53 LENGTHTHRESHOLD 0.45 RANGE 0 0.1;
  SPACING 2.2 ENDOFLINE 2.3 WITHIN 1.6 ;
  DIRECTION HORIZONTAL ;
  RESISTANCE RPERSQ 0.103 ;
  CAPACITANCE CPERSQDIST 0.000156 ;
  EDGECAPACITANCE 5e-05 ;
  TYPE 9 ;
  THICKNESS 1 ;
  WIREEXTENSION 0.75 ;
  SHRINKAGE 0.1 ;
  CAPMULTIPLIER 1 ;
  ANTENNAMODEL OXIDE1 ;
  ANTENNAAREAFACTOR 1 ;
  ANTENNAMODEL OXIDE2 ;
  ANTENNAAREARATIO 7.6 ;
  ANTENNADIFFAREARATIO 4.7 ;
  ANTENNACUMAREARATIO 6.7 ;
  ANTENNACUMDIFFAREARATIO 4.5
  ANTENNACUMDIFFAREARATIO PWL ( ( 5.4 5.4 ) ( 6.5 6.5 ) ( 7.5 7.5 ) ) ;
  ANTENNAAREAFACTOR 6.5   DIFFUSEONLY ;
  ANTENNASIDEAREARATIO 6.5 ;
  ANTENNADIFFSIDEAREARATIO 6.5
  ANTENNACUMSIDEAREARATIO 7.5 ;
  ANTENNACUMDIFFSIDEAREARATIO 4.6
  ANTENNASIDEAREAFACTOR 7.5   DIFFUSEONLY ;
  ANTENNACUMROUTINGPLUSCUT ;
  ANTENNAGATEPLUSDIFF 2 ;
  ANTENNAAREAMINUSDIFF 100 ;
  ANTENNAAREADIFFREDUCEPWL ( ( 0 1 ) ( 0.09999 1 ) ( 0.1 0.2 ) ( 1 0.1 ) ( 1000 0.1 ) ) ;
  ANTENNAMODEL OXIDE3 ;
  ANTENNAMODEL OXIDE4 ;
  ANTENNACUMROUTINGPLUSCUT ;
  ANTENNAGATEPLUSDIFF 2 ;
  ANTENNAAREAMINUSDIFF 100 ;
  ANTENNAAREADIFFREDUCEPWL ( ( 0 1 ) ( 0.0999 1 ) ( 0.1 0.2 ) ( 1 0.1 ) ( 1000 0.1 ) ) ;
  ACCURRENTDENSITY PEAK
    FREQUENCY 1e+06 1e+08 ;
    TABLEENTRIES
     5e-07 4e-07 ;
  ACCURRENTDENSITY AVERAGE 5.5 ;
  ACCURRENTDENSITY RMS
    FREQUENCY 1e+08 4e+08 8e+08 ;
    WIDTH 0.4 0.8 10 50 100 ;
    TABLEENTRIES
     2e-06 1.9e-06 1.8e-06 1.7e-06 1.5e-06
     1.4e-06 1.3e-06 1.2e-06 1.1e-06 1e-06
     9e-07 8e-07 7e-07 6e-07 4e-07 ;
  DCCURRENTDENSITY AVERAGE
    WIDTH 20 50 ;
    TABLEENTRIES 6e-07 5e-07 ;
  PROPERTY LEF57_SPACING SPACING 2.2 ENDOFLINE 2.3 WITHIN 1.6 ; STRING LEF57_ARRAYSPACING ARRAYSPACING LONGARRAY CUTSPACING 0.2 ARRAYCUTS 3 SPACING 1.0 ARRAYCUTS 4 SPACING 1.5 ARRAYCUTS 5 SPACING 2.0 ; STRING LEF57_ANTENNACUMROUTINGPLUSCUT ANTENNACUMROUTINGPLUSCUT ; STRING LEF57_ANTENNAAREAMINUSDIFF ANTENNAAREAMINUSDIFF 100.0 ; STRING LEF57_ANTENNAGATEPLUSDIFF ANTENNAGATEPLUSDIFF 2.0 ; STRING LEF57_ANTENNAAREADIFFREDUCEPWL ANTENNAAREADIFFREDUCEPWL ( ( 0.0 1.0 ) ( 0.0999 1.0 ) ( 0.1 0.2 ) ( 1.0 0.1 ) ( 1000.0 0.1 ) ) ; STRING lsp rxlay STRING lip 3 INTEGER lrp 1.2 REAL ;
END RX
Parsed 200 number of lines!!
LAYER CUT12
  TYPE CUT ;
  DIAGPITCH 1.5 1.7 ;
  DIAGWIDTH 1.6 ;
  DIAGSPACING 0.5 ;
  SPACING 0.7 LAYER RX ;
  SPACING 0.22 ADJACENTCUTS 4 WITHIN 0.25 ;
  SPACING 1.5 PARALLELOVERLAP ;
  SPACING 1.2 ADJACENTCUTS 2 WITHIN 1.5 EXCEPTSAMEPGNET ;
  ANTENNAMODEL OXIDE1 ;
  ANTENNAMODEL OXIDE2 ;
  ANTENNAMODEL OXIDE3 ;
  ANTENNAMODEL OXIDE4 ;
  ANTENNAAREARATIO 5.6 ;
  ANTENNADIFFAREARATIO 6.5 ;
  ANTENNACUMAREARATIO 6.7 ;
  ANTENNACUMDIFFAREARATIO 5.6
  ANTENNACUMDIFFAREARATIO PWL ( ( 5.4 5.4 ) ( 6.5 6.5 ) ( 7.5 7.5 ) ) ;
  ANTENNAAREAFACTOR 5.4 ;
  ANTENNACUMROUTINGPLUSCUT ;
  ANTENNAGATEPLUSDIFF 2 ;
  ANTENNAAREAMINUSDIFF 100 ;
  ANTENNAAREADIFFREDUCEPWL ( ( 0 1 ) ( 0.09999 1 ) ( 0.1 0.2 ) ( 1 0.1 ) ( 1000 0.1 ) ) ;
  ACCURRENTDENSITY PEAK
    FREQUENCY 1e+06 1e+08 ;
    TABLEENTRIES
     5e-07 4e-07 ;
  ACCURRENTDENSITY AVERAGE 5.5 ;
  ACCURRENTDENSITY RMS
    FREQUENCY 1e+08 4e+08 8e+08 ;
    CUTAREA 0.4 0.8 10 50 100 ;
    TABLEENTRIES
     2e-06 1.9e-06 1.8e-06 1.7e-06 1.5e-06
     1.4e-06 1.3e-06 1.2e-06 1.1e-06 1e-06
     9e-07 8e-07 7e-07 6e-07 4e-07 ;
  DCCURRENTDENSITY AVERAGE
    CUTAREA 2 5 ;
    TABLEENTRIES 5e-07 4e-07 ;
  DCCURRENTDENSITY AVERAGE 4.9 ;
END CUT12
Parsed 250 number of lines!!
LAYER PC
  TYPE ROUTING ;
  PITCH 3.8 3.5 ;
  DIAGPITCH 1.4 ;
  WIDTH 1 ;
  SPACING 0.6 ;
  SPACING 1.2 ENDOFLINE 1.3 WITHIN 0.6 ;
  SPACING 1.3 ENDOFLINE 1.4 WITHIN 0.7 PARALLELEDGE 1.1 WITHIN 0.5 TWOEDGES ;
  SPACING 1.4 ENDOFLINE 1.5 WITHIN 0.8 PARALLELEDGE 1.2 WITHIN 0.6 ;
  DIRECTION VERTICAL ;
  WIREEXTENSION 0.4 ;
  ANTENNAMODEL OXIDE1 ;
  ANTENNAAREARATIO 5.4 ;
  ANTENNADIFFAREARATIO 6.5 ;
  ANTENNACUMAREARATIO 7.5 ;
  ANTENNACUMDIFFAREARATIO PWL ( ( 5 5.1 ) ( 6 6.1 ) ) ;
  ANTENNAAREAFACTOR 4.5 ;
  ANTENNASIDEAREARATIO 6.5 ;
  ANTENNADIFFSIDEAREARATIO PWL ( ( 7 7.1 ) ( 7.2 7.3 ) ) ;
  ANTENNACUMSIDEAREARATIO 7.4 ;
  ANTENNACUMDIFFSIDEAREARATIO PWL ( ( 8 8.1 ) ( 8.2 8.3 ) ( 8.4 8.5 ) ( 8.6 8.7 ) ) ;
  ANTENNASIDEAREAFACTOR 9   DIFFUSEONLY ;
  ACCURRENTDENSITY PEAK
    FREQUENCY 1e+06 1e+08 ;
    WIDTH 5.6 8.5 8.1 4.5 ;
    TABLEENTRIES
     5e-07 4e-07 ;
  DCCURRENTDENSITY AVERAGE
    WIDTH 20 50 100 ;
    TABLEENTRIES 1e-06 7e-07 5e-07 ;
END PC
LAYER CA
  TYPE CUT ;
  DCCURRENTDENSITY AVERAGE
    CUTAREA 2 5 10 ;
    TABLEENTRIES 6e-07 5e-07 4e-07 ;
END CA
LAYER M1
  TYPE ROUTING ;
  PITCH 1.8 ;
  WIDTH 1 ;
  DIRECTION HORIZONTAL ;
  RESISTANCE RPERSQ 0.103 ;
  CAPACITANCE CPERSQDIST 0.000156 ;
  WIREEXTENSION 7 ;
  ANTENNAMODEL OXIDE1 ;
  ANTENNADIFFAREARATIO 1000 ;
  ANTENNACUMAREARATIO 300 ;
  ANTENNACUMDIFFAREARATIO 5000
  ANTENNAGATEPLUSDIFF 2 ;
  ACCURRENTDENSITY AVERAGE 5.5 ;
  DCCURRENTDENSITY AVERAGE 4.9 ;
   SPACINGTABLE
      PARALLELRUNLENGTH 0 0.5 3 5
          WIDTH 0 0.15 0.15 0.15 0.15
          WIDTH 0.25 0.15 0.2 0.2 0.2
          WIDTH 1.5 0.15 0.5 0.5 0.5
          WIDTH 3 0.15 0.5 1 1
          WIDTH 5 0.15 0.5 1 2 ;
   SPACINGTABLE
      INFLUENCE
          WIDTH 1.5 WITHIN 0.5 SPACING 0.5
          WIDTH 3 WITHIN 1 SPACING 1
          WIDTH 5 WITHIN 2 SPACING 2 ;
END M1
Parsed 300 number of lines!!
LAYER V1
  TYPE CUT ;
  SPACING 0.6 LAYER CA ;
END V1
LAYER M2
  TYPE ROUTING ;
  PITCH 1.8 ;
  WIDTH 0.9 ;
  SPACING 0.9 ;
  SPACING 0.28 ;
  SPACING 0.24 LENGTHTHRESHOLD 1 ;
  SPACING 0.32 RANGE 1.01 9.99 USELENGTHTHRESHOLD ;
  SPACING 0.5 RANGE 10 1000 ;
  SPACING 0.5 RANGE 10 1000 INFLUENCE 1 ;
  SPACING 0.5 RANGE 10 1000 INFLUENCE 1 RANGE 0.28 1 ;
  SPACING 0.5 RANGE 3.01 4 RANGE 4.01 5 ;
  SPACING 0.4 RANGE 3.01 4 RANGE 5.01 1000 ;
  SPACING 1 SAMENET PGONLY ;
  SPACING 1.1 SAMENET ;
  SPACING 1.2 ENDOFLINE 1.3 WITHIN 0.6 PARALLELEDGE 2.1 WITHIN 1.5 TWOEDGES ;
  SPACING 1.5 ENDOFLINE 2.3 WITHIN 1.6 PARALLELEDGE 1.1 WITHIN 0.5 ;
  DIRECTION DIAG45 ;
  RESISTANCE RPERSQ 0.0608 ;
  CAPACITANCE CPERSQDIST 0.000184 ;
  WIREEXTENSION 8 ;
  ANTENNAMODEL OXIDE1 ;
  ANTENNACUMAREARATIO 5000 ;
  ANTENNACUMDIFFAREARATIO 8000
  ANTENNAMODEL OXIDE2 ;
  ANTENNACUMAREARATIO 500 ;
  ANTENNACUMDIFFAREARATIO 800
  ANTENNAMODEL OXIDE3 ;
  ANTENNACUMAREARATIO 300 ;
  ANTENNACUMDIFFAREARATIO 600
  PROPERTY LEF57_SPACING SPACING 1.2 ENDOFLINE 1.3 WITHIN 0.6 PARALLELEDGE 2.1 WITHIN 1.5 TWOEDGES ; STRING LEF57_SPACING SPACING 1.5 ENDOFLINE 2.3 WITHIN 1.6 PARALLELEDGE 1.1 WITHIN 0.5 ; STRING LEF57_MAXFLOATINGAREA MAXFLOATINGAREA 1000 ; STRING ;
END M2
LAYER V2
  TYPE CUT ;
END V2
Parsed 350 number of lines!!
LAYER M3
  TYPE ROUTING ;
  PITCH 1.8 ;
  WIDTH 0.9 ;
  MINSTEP 1 MAXEDGES 2 ;
  SPACING 0.9 ;
  DIRECTION HORIZONTAL ;
  RESISTANCE RPERSQ 0.0608 ;
  CAPACITANCE CPERSQDIST 0.000184 ;
  WIREEXTENSION 8 ;
  ANTENNAMODEL OXIDE1 ;
  ANTENNACUMAREARATIO 300 ;
  ANTENNACUMDIFFAREARATIO 600
  ANTENNAMODEL OXIDE2 ;
  ANTENNAMODEL OXIDE3 ;
  ANTENNACUMAREARATIO 5000 ;
  ANTENNACUMDIFFAREARATIO 8000
  ANTENNAMODEL OXIDE4 ;
  ANTENNACUMAREARATIO 500 ;
  ANTENNACUMDIFFAREARATIO 800
  PROPERTY LEF57_MINSTEP MINSTEP 1.0 MAXEDGES 2 ; STRING ;
END M3
LAYER M4
  TYPE ROUTING ;
  PITCH 5.4 ;
  WIDTH 5.4 ;
  MINIMUMCUT 2 WIDTH 0.5 ;
  MINIMUMCUT 2 WIDTH 0.7 FROMBELOW ;
  MINIMUMCUT 3 WIDTH 0.8 WITHIN 0.3 ;
  MINIMUMCUT 2 WIDTH 1 FROMBELOW LENGTH 20 WITHIN 5 ;
  MINIMUMCUT 4 WIDTH 1 FROMABOVE ;
  MINIMUMCUT 2 WIDTH 1.1 LENGTH 20 WITHIN 5 ;
  MINIMUMCUT 2 WIDTH 1.1 FROMABOVE LENGTH 20 WITHIN 5 ;
  MAXWIDTH 10 ;
  MINWIDTH 0.15 ;
  MINENCLOSEDAREA 0.3 ;
  MINENCLOSEDAREA 0.4 MINENCLOSEDAREAWIDTH 0.15 ;
  MINENCLOSEDAREA 0.8 MINENCLOSEDAREAWIDTH 0.5 ;
  MINSTEP 0.2 ;
  PROTRUSIONWIDTH 0.3 LENGTH 0.6 WIDTH 1.2 ;
  DIRECTION HORIZONTAL ;
END M4
Parsed 400 number of lines!!
LAYER M5
  TYPE ROUTING ;
  PITCH 5.4 ;
  WIDTH 4 ;
  MINIMUMCUT 2 WIDTH 0.7 ;
  MINIMUMCUT 4 WIDTH 1 FROMABOVE ;
  MINIMUMCUT 2 WIDTH 1.1 LENGTH 20 WITHIN 5 ;
  MINIMUMCUT 5 WIDTH 0.5 ;
  MINSTEP 0.05 ;
  MINSTEP 0.04 ;
  MINSTEP 0.05 LENGTHSUM 0.08 ;
  MINSTEP 0.05 LENGTHSUM 0.16 ;
  MINSTEP 0.05 INSIDECORNER ;
  MINSTEP 0.05 INSIDECORNER LENGTHSUM 0.15 ;
  MINSTEP 1 MAXEDGES 2 ;
  DIRECTION DIAG135 ;
  ANTENNAMODEL OXIDE1 ;
  ANTENNAMODEL OXIDE2 ;
  ANTENNAMODEL OXIDE3 ;
  ANTENNACUMAREARATIO 300 ;
  ANTENNACUMDIFFAREARATIO 600
END M5
LAYER implant1
  TYPE IMPLANT ;
  WIDTH 0.5 ;
  SPACING 0.5 ;
  PROPERTY lrp 5.4 REAL ;
END implant1
LAYER implant2
  TYPE IMPLANT ;
  WIDTH 0.5 ;
  SPACING 0.5 ;
  PROPERTY lsp bottom STRING ;
END implant2
LAYER V3
  TYPE CUT ;
END V3
LAYER MT
  TYPE ROUTING ;
  PITCH 1.8 ;
  WIDTH 0.9 ;
  MINSTEP 0.05 STEP ;
  MINSTEP 0.05 STEP LENGTHSUM 0.08 ;
  MINSTEP 0.04 STEP ;
  SPACING 0.9 ;
  DIRECTION VERTICAL ;
  RESISTANCE RPERSQ 0.0608 ;
  CAPACITANCE CPERSQDIST 0.000184 ;
  DIAGMINEDGELENGTH 0.075 ;
END MT
Parsed 450 number of lines!!
LAYER OVERLAP
  TYPE OVERLAP ;
  PROPERTY lip 5 INTEGER lsp top STRING lrp 5.5 REAL lsp bottom STRING ;
END OVERLAP
LAYER via12
  TYPE CUT ;
  WIDTH 0.2 ;
  SPACING 0.15 CENTERTOCENTER ;
ENCLOSURE BELOW 0.03 0.01 ;
ENCLOSURE ABOVE 0.05 0.01 ;
ENCLOSURE ABOVE 0.04 0.09 ;
PREFERENCLOSURE BELOW 0.06 0.01 ;
PREFERENCLOSURE ABOVE 0.08 0.2 ;
  RESISTANCE 10 ;
END via12
LAYER metal1
  TYPE ROUTING ;
  PITCH 1.8 ;
  WIDTH 0.9 ;
  DIRECTION VERTICAL ;
  MINSIZE 0.14 0.3 0.5 0.56 0.01 0.05 ;
END metal1
LAYER via23
  TYPE CUT ;
  WIDTH 0.2 ;
  SPACING 0.15 ;
ENCLOSURE 0.05 0.01 ;
ENCLOSURE 0.02 0.02 WIDTH 1 ;
ENCLOSURE 0.05 0.05 WIDTH 2 ;
  RESISTANCE 10 ;
END via23
Parsed 500 number of lines!!
LAYER via34
  TYPE CUT ;
  WIDTH 0.25 ;
  SPACING 0.1 CENTERTOCENTER ;
ENCLOSURE 0.05 0.01 ;
ENCLOSURE 0.05 0 LENGTH 0.7 ;
ENCLOSURE BELOW 0.07 0.07 WIDTH 1 ;
ENCLOSURE ABOVE 0.09 0.09 WIDTH 1 ;
ENCLOSURE 0.03 0.03 WIDTH 1 EXCEPTEXTRACUT 0.2 ;
  RESISTANCE 8 ;
END via34
LAYER cut23
  TYPE CUT ;
  ARRAYSPACING LONGARRAY CUTSPACING 0.2
	ARRAYCUTS 3 SPACING 1
	ARRAYCUTS 4 SPACING 1.5
	ARRAYCUTS 5 SPACING 2 ;
  SPACING 0.2 LAYER cut12 STACK SAMENET ;
  SPACING 0.3 CENTERTOCENTER SAMENET AREA 0.02 ;
  SPACING 0.4 AREA 0.5 ;
  SPACING 0.1 ;
SPACINGTABLE ORTHOGONAL
   WITHIN 0.15 SPACING 0.11
   WITHIN 0.13 SPACING 0.13
   WITHIN 0.11 SPACING 0.15;
END cut23
LAYER cut24
  TYPE ROUTING ;
  PITCH 1.8 ;
  WIDTH 1 ;
  ARRAYSPACING WIDTH 2 CUTSPACING 0.2
	ARRAYCUTS 3 SPACING 1 ;
  SPACING 0.1 ;
  SPACING 0.12 NOTCHLENGTH 0.15;
  SPACING 0.14 ENDOFNOTCHWIDTH 0.15 NOTCHSPACING 0.16, NOTCHLENGTH 0.08;
  DIRECTION HORIZONTAL ;
END cut24
LAYER cut25
  TYPE ROUTING ;
  PITCH 1.8 ;
  WIDTH 1 ;
  DIRECTION HORIZONTAL ;
  WIREEXTENSION 7 ;
   SPACINGTABLE
      TWOWIDTHS
          WIDTH 0 0.15 0.2 0.5 1 
          WIDTH 0.25 PRL 0 0.2 0.25 0.5 1 
          WIDTH 1.5 PRL 1.5 0.5 0.5 0.6 1 
          WIDTH 3 PRL 3 1 1 1 1.2  ;
END cut25
MAXVIASTACK 4 RANGE m1 m7 ;
Parsed 550 number of lines!!
VIA IN1X 
  PROPERTY stringProperty DEFAULT STRING realProperty 32.33 REAL COUNT 34 INTEGER ;
  RESISTANCE 2 ;
  LAYER RX
    RECT ( -0.700000 -0.700000 ) ( 0.700000 0.700000 ) ;
    RECT ( 0.000000 0.000000 ) ( 2.100000 2.300000 ) ;
    RECT ( 5.700000 0.000000 ) ( 95.700000 2.300000 ) ;
    RECT ( 101.900000 0.000000 ) ( 119.600000 2.300000 ) ;
  LAYER CUT12
    RECT ( -0.250000 -0.250000 ) ( 0.250000 0.250000 ) ;
  LAYER PC
    RECT ( -0.600000 -0.600000 ) ( 0.600000 0.600000 ) ;
END IN1X
VIA M1_M2 DEFAULT
  RESISTANCE 1.5 ;
  LAYER M1
    RECT MASK 1 ( -0.600000 -0.600000 ) ( 0.600000 0.600000 ) ;
  LAYER V1
    RECT MASK 2 ( -0.450000 -0.450000 ) ( 0.450000 0.450000 ) ;
  LAYER M2
    RECT MASK 3 ( -0.450000 -0.450000 ) ( 0.450000 0.450000 ) ;
    RECT MASK 1 ( -0.900000 -0.450000 ) ( 0.900000 0.450000 ) ;
END M1_M2
VIA M2_M3 DEFAULT
  RESISTANCE 1.5 ;
  LAYER M2
    RECT ( -0.450000 -0.900000 ) ( 0.450000 0.900000 ) ;
  LAYER V2
    RECT ( -0.450000 -0.450000 ) ( 0.450000 0.450000 ) ;
  LAYER M3
    RECT ( -0.450000 -0.450000 ) ( 0.450000 0.450000 ) ;
END M2_M3
VIA M2_M3_PWR GENERATED
  RESISTANCE 0.4 ;
  LAYER M2
    RECT ( -1.350000 -1.350000 ) ( 1.350000 1.350000 ) ;
  LAYER V2
    RECT ( -1.350000 -1.350000 ) ( -0.450000 1.350000 ) ;
    RECT ( 0.450000 -1.350000 ) ( 1.350000 -0.450000 ) ;
    RECT ( 0.450000 0.450000 ) ( 1.350000 1.350000 ) ;
  LAYER M3
    RECT ( -1.350000 -1.350000 ) ( 1.350000 1.350000 ) ;
END M2_M3_PWR
Parsed 600 number of lines!!
VIA M3_MT DEFAULT
  RESISTANCE 1.5 ;
  LAYER M3
    RECT MASK 1 ( -0.900000 -0.450000 ) ( 0.900000 0.450000 ) ;
  LAYER V3
    RECT MASK 2 ( -0.450000 -0.450000 ) ( 0.450000 0.450000 ) ;
  LAYER MT
    RECT MASK 3 ( -0.450000 -0.450000 ) ( 0.450000 0.450000 ) ;
END M3_MT
VIA myBlockVia0 
  VIARULE viaName0 ;
    CUTSIZE 0.1 0.1 ;
    LAYERS metal1 via12 metal2 ;
    CUTSPACING 0.1 0.1 ;
    ENCLOSURE 0.05 0.01 0.01 0.05 ;
    ROWCOL 5 14 ;
    PATTERN 2_FF70_3_R4F ;
END myBlockVia0
VIA VIACENTER12 
  RESISTANCE 0.24 ;
  LAYER M1
    RECT ( -4.600000 -2.200000 ) ( 4.600000 2.200000 ) ;
  LAYER V1
    RECT ( -3.100000 -0.800000 ) ( -1.900000 0.800000 ) ;
    RECT ( 1.900000 -0.800000 ) ( 3.100000 0.800000 ) ;
  LAYER M2
    RECT ( -4.400000 -2.000000 ) ( 4.400000 2.000000 ) ;
END VIACENTER12
VIA M2_TURN 
  LAYER M2
    RECT ( -0.450000 -0.450000 ) ( 0.450000 0.450000 ) ;
    RECT ( -4.400000 -2.000000 ) ( 4.400000 2.000000 ) ;
END M2_TURN
Parsed 650 number of lines!!
VIA myVia23 
  LAYER metal2
    POLYGON MASK 1 -2.1 -1  -0.2 1  2.1 1  0.2 -1 ;
    POLYGON MASK 2 -1.1 -2  -0.1 2  1.1 2  0.1 -2 ;
    POLYGON MASK 3 -3.1 -2  -0.3 2  3.1 2  0.3 -2 ;
    POLYGON MASK 1 -4.1 -2  -0.4 2  4.1 2  0.4 -2 ;
  LAYER cut23
    RECT MASK 2 ( -0.400000 -0.400000 ) ( 0.400000 0.400000 ) ;
    POLYGON MASK 3 -2.1 -1  -0.2 1  2.1 1  0.2 -1 ;
  LAYER metal3
    POLYGON MASK 1 -0.2 -1  -2.1 1  0.2 1  2.1 -1 ;
  LAYER cut33
    RECT MASK 1 ( -0.400000 -0.400000 ) ( 0.400000 0.400000 ) ;
    RECT MASK 1 ( -0.500000 -0.500000 ) ( 0.500000 0.500000 ) ;
    RECT MASK 2 ( -0.300000 -0.300000 ) ( 0.300000 0.300000 ) ;
    RECT MASK 1 ( -0.200000 -0.200000 ) ( 0.200000 0.200000 ) ;
    RECT MASK 2 ( -0.100000 -0.100000 ) ( 0.100000 0.100000 ) ;
    POLYGON MASK 2 -2.1 -1  -0.2 1  2.1 1  0.2 -1 ;
    POLYGON MASK 2 -1.1 -2  -0.1 2  1.1 2  0.1 -2 ;
    POLYGON MASK 3 -3.1 -2  -0.3 2  3.1 2  0.3 -2 ;
    POLYGON MASK 1 -4.1 -2  -0.4 2  4.1 2  0.4 -2 ;
END myVia23
VIA myBlockVia 
  VIARULE DEFAULT ;
    CUTSIZE 0.1 0.1 ;
    LAYERS metal1 via12 metal2 ;
    CUTSPACING 0.1 0.1 ;
    ENCLOSURE 0.05 0.01 0.01 0.05 ;
    ROWCOL 1 2 ;
    ORIGIN 0.1 0.2 ;
    OFFSET 5.1 4.1 3.1 2.1 ;
END myBlockVia
VIARULE VIALIST12
  LAYER M1 ;
    DIRECTION VERTICAL ;
    WIDTH 9 TO 9.6 ;
  LAYER M2 ;
    DIRECTION HORIZONTAL ;
    WIDTH 3 TO 3 ;
  VIA VIACENTER12 ;
  PROPERTY vrsp new STRING vrip 1 INTEGER vrrp 4.5 REAL ;
END VIALIST12
VIARULE VIALIST1
  LAYER M1 ;
    DIRECTION VERTICAL ;
    WIDTH 9 TO 9.6 ;
  LAYER M1 ;
    DIRECTION HORIZONTAL ;
    WIDTH 3 TO 3 ;
Should have via names in VIARULE.
END VIALIST1
Parsed 700 number of lines!!
VIARULE VIAGEN12 GENERATE
  LAYER M1 ;
    ENCLOSURE 1.4 1.5 ;
    WIDTH 0.1 TO 19 ;
  LAYER M2 ;
    ENCLOSURE 1.4 1.5 ;
    WIDTH 0.2 TO 1.9 ;
  LAYER M3 ;
    RESISTANCE 0.5 ;
    SPACING 5.6 BY 7 ;
    RECT ( -0.300000 -0.300000 ) ( 0.300000 0.300000 ) ;
  PROPERTY vrsp new STRING vrip 1 INTEGER vrrp 5.5 REAL ;
END VIAGEN12
VIARULE via10 GENERATE
  LAYER M1 ;
    ENCLOSURE 1.1 1.2 ;
    WIDTH 0.1 TO 1.9 ;
  LAYER M2 ;
    ENCLOSURE 1.1 1.2 ;
    WIDTH 0.2 TO 2.9 ;
  LAYER M3 ;
    SPACING 0.3 BY 4.5 ;
    RECT ( 1.000000 1.000000 ) ( 1.000000 1.000000 ) ;
  PROPERTY vrsp new STRING vrip 1 INTEGER vrrp 5.5 REAL ;
END via10
Parsed 750 number of lines!!
VIARULE via11 GENERATE
  LAYER M1 ;
    WIDTH 0.1 TO 1.9 ;
  LAYER M2 ;
    WIDTH 0.2 TO 2.9 ;
  LAYER M3 ;
    SPACING 0.3 BY 4.5 ;
    RECT ( 1.000000 1.000000 ) ( 1.000000 1.000000 ) ;
  PROPERTY vrsp new STRING vrip 1 INTEGER vrrp 5.5 REAL ;
END via11
VIARULE via12 GENERATE DEFAULT
  LAYER m1 ;
    ENCLOSURE 0.03 0.01 ;
  LAYER m2 ;
    ENCLOSURE 0.05 0.01 ;
  LAYER cut12 ;
    RESISTANCE 20 ;
    SPACING 0.4 BY 0.4 ;
    RECT ( -0.100000 -0.100000 ) ( 0.100000 0.100000 ) ;
END via12
VIARULE via13 GENERATE
  LAYER m1 ;
    ENCLOSURE 0.05 0.005 ;
    WIDTH 1 TO 100 ;
  LAYER m2 ;
    ENCLOSURE 0.05 0.005 ;
    WIDTH 1 TO 100 ;
  LAYER cut12 ;
    SPACING 0.16 BY 0.16 ;
    RECT ( -0.070000 -0.070000 ) ( 0.070000 0.070000 ) ;
END via13
VIARULE via14
  LAYER m1 ;
    DIRECTION HORIZONTAL ;
    WIDTH 1 TO 100 ;
  LAYER m2 ;
    DIRECTION VERTICAL ;
    WIDTH 1 TO 100 ;
  VIA name1 ;
END via14
Parsed 800 number of lines!!
VIARULE VIAGEN3T GENERATE
  LAYER m3 ;
    ENCLOSURE 0.2 0.2 ;
  LAYER v3 ;
    ENCLOSURE 0.2 0.2 ;
    SPACING 1.8 BY 1.8 ;
    RECT ( -0.450000 -0.450000 ) ( 0.450000 0.450000 ) ;
  LAYER mt ;
END VIAGEN3T
Parsed 850 number of lines!!
NONDEFAULTRULE RULE1
  LAYER RX
    WIDTH 10 ;
    SPACING 2.2 ;
    WIREEXTENSION 6 ;
  END RX
  LAYER PC
    WIDTH 10 ;
    SPACING 2.2 ;
  END PC
  LAYER M1
    WIDTH 10 ;
    SPACING 2.2 ;
  END M1
  LAYER fw
    WIDTH 4.8 ;
    SPACING 4.8 ;
  END fw
VIA nd1VIARX0 DEFAULT
  PROPERTY realProperty 2.3 REAL ;
  RESISTANCE 0.2 ;
  LAYER RX
    RECT ( -3.000000 -3.000000 ) ( 3.000000 3.000000 ) ;
  LAYER CUT12
    RECT ( -1.000000 -1.000000 ) ( 1.000000 1.000000 ) ;
  LAYER PC
    RECT ( -3.000000 -3.000000 ) ( 3.000000 3.000000 ) ;
END nd1VIARX0
VIA nd1VIA01 
  RESISTANCE 0.2 ;
  LAYER PC
    RECT ( -3.000000 -3.000000 ) ( 3.000000 3.000000 ) ;
    RECT ( -5.000000 -5.000000 ) ( 5.000000 5.000000 ) ;
  LAYER CA
    RECT ( -1.000000 -1.000000 ) ( 1.000000 1.000000 ) ;
  LAYER M1
    RECT ( -3.000000 -3.000000 ) ( 3.000000 3.000000 ) ;
END nd1VIA01
VIA nd1VIA12 
  RESISTANCE 0.2 ;
  LAYER M1
    RECT ( -3.000000 -3.000000 ) ( 3.000000 3.000000 ) ;
  LAYER V1
    RECT ( -1.000000 -1.000000 ) ( 1.000000 1.000000 ) ;
  LAYER M2
    RECT ( -3.000000 -3.000000 ) ( 3.000000 3.000000 ) ;
END nd1VIA12
   PROPERTY ndrsp single STRING ndrip 1 INTEGER ndrrp 6.7 REAL ;
END RULE1 ;
NONDEFAULTRULE wide1_5x
  LAYER metal1
    WIDTH 1.5 ;
  END metal1
  LAYER metal2
    WIDTH 1.5 ;
  END metal2
  LAYER metal3
    WIDTH 1.5 ;
  END metal3
END wide1_5x ;
Parsed 900 number of lines!!
NONDEFAULTRULE wide3x
  LAYER metal1
    WIDTH 3 ;
  END metal1
  LAYER metal2
    WIDTH 3 ;
  END metal2
  LAYER metal3
    WIDTH 3 ;
  END metal3
   MINCUTS cut12 2 ;
   MINCUTS cut23 2 ;
END wide3x ;
NONDEFAULTRULE analog_rule
  HARDSPACING ;
  LAYER metal1
    WIDTH 1.5 ;
    SPACING 3 ;
    DIAGWIDTH 5.5 ;
  END metal1
  LAYER metal2
    WIDTH 1.5 ;
    SPACING 3 ;
  END metal2
  LAYER metal3
    WIDTH 1.5 ;
    SPACING 3 ;
  END metal3
    USEVIA via12_fixed_analog_via ;
    USEVIA via23_fixed_analog_via ;
    USEVIARULE viarule14_fixed_analog ;
END analog_rule ;
NONDEFAULTRULE clock1
  LAYER metal1
    WIDTH 1.5 ;
  END metal1
  LAYER metal2
    WIDTH 1.5 ;
  END metal2
  LAYER metal3
    WIDTH 1.5 ;
  END metal3
END clock1 ;
Parsed 950 number of lines!!
NONDEFAULTRULE clock2
  LAYER metal1
    WIDTH 1.5 ;
  END metal1
  LAYER metal2
    WIDTH 1.5 ;
  END metal2
  LAYER metal3
    WIDTH 1.5 ;
  END metal3
END clock2 ;
NONDEFAULTRULE clock
  LAYER metal1
    WIDTH 1.5 ;
  END metal1
  LAYER metal2
    WIDTH 1.5 ;
  END metal2
  LAYER metal3
    WIDTH 1.5 ;
  END metal3
END clock ;
Parsed 1000 number of lines!!
SITE COVER
  CLASS PAD ;
  SYMMETRY R90 ;
  SIZE 10 BY 10 ;
END COVER
SITE IO
  CLASS PAD ;
  SIZE 80 BY 560 ;
END IO
SITE CORE
  CLASS CORE ;
  SIZE 0.7 BY 8.4 ;
END CORE
SITE CORE1
  CLASS CORE ;
  SYMMETRY X ;
  SIZE 67.2 BY 6 ;
END CORE1
SITE MRCORE
  CLASS CORE ;
  SYMMETRY Y ;
  SIZE 3.6 BY 28.8 ;
END MRCORE
SITE IOWIRED
  CLASS PAD ;
  SIZE 57.6 BY 432 ;
END IOWIRED
Parsed 1050 number of lines!!
SITE IMAGE
  CLASS CORE ;
  SIZE 1 BY 1 ;
  ROWPATTERN   Fsite1 N   Lsite1 N   Lsite1 FS ;
END IMAGE
SITE Fsite
  CLASS CORE ;
  SIZE 4 BY 7 ;
END Fsite
SITE Lsite
  CLASS CORE ;
  SIZE 6 BY 7 ;
END Lsite
SITE mySite
  CLASS CORE ;
  SIZE 16 BY 7 ;
  ROWPATTERN   Fsite N   Lsite N   Lsite FS ;
END mySite
ARRAY M7E4XXX
   SITE CORE -5021.45 -4998 N DO 14346 BY 595 STEP 0.7 16.8 ;
   SITE CORE -5021.45 -4989.6 FS DO 14346 BY 595 STEP 0.7 16.8 ;
   SITE IO 6148.8 5800 E DO 1 BY 1 STEP 0 0 ;
   SITE IO 6148.8 3240 E DO 1 BY 1 STEP 0 0 ;
   SITE COVER -7315 -7315 N DO 1 BY 1 STEP 0 0 ;
   SITE COVER 7305 7305 N DO 1 BY 1 STEP 0 0 ;
   CANPLACE COVER -7315 -7315 N DO 1 BY 1 STEP 0 0 ;
   CANPLACE COVER -7250 -7250 N DO 5 BY 1 STEP 40 0 ;
   CANPLACE COVER -7250 -7250 N DO 5 BY 1 STEP 40 0 ;
   CANNOTOCCUPY CORE -5021.45 -4989.6 FS DO 100 BY 595 STEP 0.7 16.8 ;
   CANNOTOCCUPY CORE -5021.45 -4998 N DO 100 BY 595 STEP 0.7 16.8 ;
   CANNOTOCCUPY CORE -5021.45 -4998 N DO 100 BY 595 STEP 0.7 16.8 ;
  TRACKS X, -6148.8 DO 17569 STEP 0.7
  LAYER RX ;
  TRACKS Y, -6148.8 DO 20497 STEP 0.6
  LAYER RX ;
  TRACKS Y, -6148.8 DO 20497 STEP 0.6
  LAYER RX ;
  GCELLGRID X, -6157.2 DO 1467 STEP 8.4
  GCELLGRID Y, -6157.2 DO 1467 STEP 8.4
  GCELLGRID Y, -6157.2 DO 1467 STEP 8.4
   FLOORPLAN 100%
      CANPLACE COVER -7315 -7315 N DO 1 BY 1 STEP 0 0 ;
      CANPLACE COVER -7250 -7250 N DO 5 BY 1 STEP 40 0 ;
      CANPLACE CORE -5021.45 -4998 N DO 14346 BY 595 STEP 0.7 16.8 ;
      CANPLACE CORE -5021.45 -4989.6 FS DO 14346 BY 595 STEP 0.7 16.8 ;
      CANNOTOCCUPY CORE -5021.45 -4989.6 FS DO 100 BY 595 STEP 0.7 16.8 ;
      CANNOTOCCUPY CORE -5021.45 -4998 N DO 100 BY 595 STEP 0.7 16.8 ;
   END 100%

END M7E4XXX

MACRO CHK3A
MACRO CLASS RING
Parsed 1100 number of lines!!
Parsed 1150 number of lines!!
  PIN GND
    DIRECTION INOUT ;
    USE GROUND ;
    SHAPE ABUTMENT ;
    MAXLOAD 0.1 ;
    RISESLEWLIMIT 0.01 ;
    FALLSLEWLIMIT 0.02 ;
    TAPERRULE RULE1 ;
    PROPERTY TYPE special STRING intProp 23 INTEGER realProp 24.25 REAL ;
    PORT
CLASS CORE       LAYER M1 ;
      SPACING 0.05 ;
      WIDTH 1 ;
      RECT ( -0.900000 3.000000 ) ( 9.900000 6.000000 ) ;
      VIA ( 100 300 ) IN1X ;
    END
    PORT
CLASS BUMP       LAYER M2 ;
      SPACING 0.06 ;
    END
  END GND
Parsed 1200 number of lines!!
  PIN VDD
    DIRECTION INOUT ;
    USE POWER ;
    SHAPE ABUTMENT ;
    ANTENNAPARTIALMETALAREA 10611.2 LAYER M2 ;
    ANTENNAPARTIALMETALAREA 2450.26 LAYER M3 ;
    ANTENNAPARTIALCUTAREA 4.8216 LAYER V1 ;
    ANTENNAPARTIALCUTAREA 185.73 LAYER V2 ;
    ANTENNADIFFAREA 5008.46 LAYER M2 ;
    ANTENNADIFFAREA 5163.88 LAYER M3 ;
    ANTENNAMODEL OXIDE1 ;
    ANTENNAGATEAREA 297.214 LAYER M3 ;
    ANTENNAMODEL OXIDE2 ;
    ANTENNAMODEL OXIDE3 ;
    ANTENNAGATEAREA 162.48 LAYER M2 ;
    PORT
    END
    PORT
CLASS NONE       LAYER M1 ;
      RECT ITERATE ( -0.900000 21.000000 ) ( 9.900000 24.000000 )
      DO 1 BY 2 STEP 1 1 ;
      VIA ITERATE ( 100 300 ) nd1VIA12
      DO 1 BY 2 STEP 1 2 ;
    END
  END VDD
  PIN PA3
    DIRECTION INPUT ;
    ANTENNAPARTIALMETALAREA 4 LAYER M1 ;
    ANTENNAPARTIALMETALAREA 5 LAYER M2 ;
    ANTENNAPARTIALMETALSIDEAREA 5 LAYER M2 ;
    ANTENNAPARTIALMETALSIDEAREA 6 LAYER M2 ;
    ANTENNAPARTIALMETALSIDEAREA 7 LAYER M2 ;
    ANTENNAPARTIALCUTAREA 1 ;
    ANTENNAPARTIALCUTAREA 2 LAYER M2 ;
    ANTENNAPARTIALCUTAREA 3 ;
    ANTENNAPARTIALCUTAREA 4 LAYER M4 ;
    ANTENNADIFFAREA 1 LAYER M1 ;
    ANTENNAMODEL OXIDE1 ;
    ANTENNAGATEAREA 1 LAYER M1 ;
    ANTENNAGATEAREA 2 ;
    ANTENNAGATEAREA 3 LAYER M3 ;
    ANTENNAMAXAREACAR 1 LAYER L1 ;
    ANTENNAMAXAREACAR 2 LAYER L2 ;
    ANTENNAMAXAREACAR 3 LAYER L3 ;
    ANTENNAMAXAREACAR 4 LAYER L4 ;
    ANTENNAMAXSIDEAREACAR 1 LAYER L1 ;
    ANTENNAMAXSIDEAREACAR 2 LAYER L2 ;
    ANTENNAMAXCUTCAR 1 LAYER L1 ;
    ANTENNAMAXCUTCAR 2 LAYER L2 ;
    ANTENNAMAXCUTCAR 3 LAYER L3 ;
    PORT
      LAYER M1 ;
      SPACING 0.02 ;
      RECT ( 1.350000 -0.450000 ) ( 2.250000 0.450000 ) ;
      RECT ( -0.450000 -0.450000 ) ( 0.450000 0.450000 ) ;
    END
    PORT
      LAYER PC ;
      DESIGNRULEWIDTH 0.05 ;
      RECT ( -0.450000 12.150000 ) ( 0.450000 13.050000 ) ;
    END
    PORT
      LAYER PC ;
      RECT ( -0.450000 24.750000 ) ( 0.450000 25.650000 ) ;
    END
    PORT
    END
  END PA3
Parsed 1250 number of lines!!
  PIN PA0
    DIRECTION INPUT ;
    MUSTJOIN PA3 ;
    PORT
CLASS NONE       LAYER M1 ;
      RECT ( 8.550000 8.550000 ) ( 9.450000 9.450000 ) ;
      RECT ( 6.750000 6.750000 ) ( 7.650000 7.650000 ) ;
      RECT ( 6.750000 8.550000 ) ( 7.650000 9.450000 ) ;
      RECT ( 6.750000 10.350000 ) ( 7.650000 11.250000 ) ;
    END
    PORT
CLASS CORE       LAYER PC ;
      RECT ( 8.550000 24.750000 ) ( 9.450000 25.650000 ) ;
    END
    PORT
      LAYER PC ;
      RECT ( 6.750000 1.350000 ) ( 7.650000 2.250000 ) ;
    END
    PORT
      LAYER PC ;
      RECT ( 6.750000 24.750000 ) ( 7.650000 25.650000 ) ;
    END
    PORT
      LAYER PC ;
      RECT ( 4.950000 1.350000 ) ( 5.850000 2.250000 ) ;
    END
  END PA0
  PIN PA1
    DIRECTION INPUT ;
    PORT
      LAYER M1 ;
      RECT ( 8.550000 -0.450000 ) ( 9.450000 0.450000 ) ;
      RECT ( 6.750000 -0.450000 ) ( 7.650000 0.450000 ) ;
    END
    PORT
      LAYER M1 ;
      RECT ( 8.550000 12.150000 ) ( 9.450000 13.050000 ) ;
      RECT ( 6.750000 12.150000 ) ( 7.650000 13.050000 ) ;
      RECT ( 4.950000 12.150000 ) ( 5.850000 13.050000 ) ;
    END
    PORT
      LAYER PC ;
      RECT ( 4.950000 24.750000 ) ( 5.850000 25.650000 ) ;
    END
    PORT
      LAYER PC ;
      RECT ( 3.150000 24.750000 ) ( 4.050000 25.650000 ) ;
    END
  END PA1
Parsed 1300 number of lines!!
  PIN PA20
    DIRECTION INPUT ;
    PORT
      LAYER M1 ;
      POLYGON       ( 15 35 )
      ( 15 60 )
      ( 65 60 )
      ( 65 35 )
      ( 15 35 ) ;
    END
    PORT
      LAYER M1 ;
      PATH       ( 8.55 12.15 )
      ( 9.45 13.05 ) ;
    END
  END PA20
  PIN PA21
    DIRECTION OUTPUT TRISTATE ;
    PORT
      LAYER M1 ;
      POLYGON ITERATE      ( 20 35 )
      ( 20 60 )
      ( 70 60 )
      ( 70 35 )
      DO 1 BY 2 STEP 5 5 ;
    END
    PORT
      LAYER M1 ;
      PATH ITERATED       ( 5.55 12.15 )
      ( 10.45 13.05 )
      DO 1 BY 2 STEP 2 2 ;
    END
  END PA21
  OBS
      LAYER M1 ;
      SPACING 5.6 ;
      RECT ( 6.600000 -0.600000 ) ( 9.600000 0.600000 ) ;
      RECT ( 4.800000 12.000000 ) ( 9.600000 13.200000 ) ;
      RECT ( 3.000000 13.800000 ) ( 7.800000 16.800000 ) ;
      RECT ( 3.000000 -0.600000 ) ( 6.000000 0.600000 ) ;
      RECT ( 3.000000 8.400000 ) ( 6.000000 11.400000 ) ;
      RECT ( 3.000000 8.400000 ) ( 4.200000 16.800000 ) ;
      RECT ( -0.600000 13.800000 ) ( 4.200000 16.800000 ) ;
      RECT ( -0.600000 -0.600000 ) ( 2.400000 0.600000 ) ;
      RECT ( 6.600000 6.600000 ) ( 9.600000 11.400000 ) ;
      RECT ( 6.600000 6.600000 ) ( 7.800000 11.400000 ) ;
  END
Parsed 1350 number of lines!!
  CLASS RING ;
  FIXEDMASK ;
  EEQ CHK1 ;
  SYMMETRY X Y R90 ;
  SITE CORE ;
  SIZE 10.8 BY 28.8 ;
  FOREIGN CHKS ( 0 0 ) FN ;
  ORIGIN ( 0.9 0.9 ) ;
  PROPERTY stringProp first STRING integerProp 1 INTEGER WEIGHT 30.31 REAL ;
END CHK3A
MACRO INV
MACRO CLASS CORE
  PIN Z
    DIRECTION OUTPUT ;
    USE SIGNAL ;
    ANTENNAPARTIALMETALAREA 10611.2 LAYER M2 ;
    ANTENNAPARTIALMETALAREA 2450.26 LAYER M3 ;
    ANTENNAPARTIALCUTAREA 4.8216 LAYER V1 ;
    ANTENNAPARTIALCUTAREA 185.73 LAYER V2 ;
    ANTENNADIFFAREA 5008.46 LAYER M2 ;
    ANTENNADIFFAREA 5163.88 LAYER M3 ;
    ANTENNAMODEL OXIDE1 ;
    ANTENNAGATEAREA 297.214 LAYER M3 ;
    ANTENNAMODEL OXIDE2 ;
    ANTENNAMODEL OXIDE3 ;
    ANTENNAGATEAREA 162.48 LAYER M2 ;
    PORT
      LAYER M2 ;
      PATH       ( 30.8 9 )
      ( 42 9 ) ;
    END
  END Z
Parsed 1400 number of lines!!
  PIN A
    DIRECTION INPUT ;
    USE ANALOG ;
    PORT
      LAYER M1 ;
      PATH       ( 25.2 15 ) ;
    END
  END A
  PIN VDD
    DIRECTION INOUT ;
    SHAPE ABUTMENT ;
    ANTENNAPARTIALMETALAREA 10611.2 LAYER M2 ;
    ANTENNAPARTIALMETALAREA 2450.26 LAYER M3 ;
    ANTENNAPARTIALCUTAREA 4.8216 LAYER V1 ;
    ANTENNAPARTIALCUTAREA 185.73 LAYER V2 ;
    ANTENNADIFFAREA 5008.46 LAYER M2 ;
    ANTENNADIFFAREA 5163.88 LAYER M3 ;
    ANTENNAMODEL OXIDE1 ;
    ANTENNAGATEAREA 297.214 LAYER M3 ;
    ANTENNAMODEL OXIDE2 ;
    ANTENNAMODEL OXIDE3 ;
    ANTENNAGATEAREA 162.48 LAYER M2 ;
    PORT
      LAYER M1 ;
      WIDTH 5.6 ;
      PATH       ( 50.4 2.8 )
      ( 50.4 21.2 ) ;
    END
  END VDD
  PIN VSS
    DIRECTION INOUT ;
    SHAPE ABUTMENT ;
    PORT
      LAYER M1 ;
      WIDTH 5.6 ;
      PATH       ( 16.8 2.8 )
      ( 16.8 21.2 ) ;
    END
  END VSS
Parsed 1450 number of lines!!
  OBS
      LAYER M1 ;
      DESIGNRULEWIDTH 4.5 ;
      WIDTH 0.1 ;
      RECT MASK 2 ( 24.100000 1.500000 ) ( 43.500000 16.500000 ) ;
      RECT MASK 2 ITERATE ( 24.100000 1.500000 ) ( 43.500000 16.500000 )
      DO 2 BY 1 STEP 20 0 ;
      PATH MASK 3 ITERATED       ( 532 534 )
      ( 1999.2 534 )
      DO 1 BY 2 STEP 0 1446 ;
      VIA ITERATE MASK 123 ( 470.4 475 ) VIABIGPOWER12
      DO 2 BY 2 STEP 1590.4 1565 ;
      PATH MASK 3       ( 532 534 )
      ( 1999.2 534 ) ;
      PATH MASK 3       ( 532 1980 )
      ( 1999.2 1980 ) ;
      VIA MASK 103 ( 470.4 475 ) VIABIGPOWER12 ;
      VIA MASK 132 ( 2060.8 475 ) VIABIGPOWER12 ;
      VIA MASK 112 ( 470.4 2040 ) VIABIGPOWER12 ;
      VIA MASK 123 ( 2060.8 2040 ) VIABIGPOWER12 ;
      RECT ( 44.100000 1.500000 ) ( 63.500000 16.500000 ) ;
  END
  DENSITY
    LAYER metal1 ;
      RECT 0 0 100 100 45.5 ;
      RECT 100 0 200 100 42.2 ;
    LAYER metal2 ;
      RECT 0 0 250 140 20.5 ;
      RECT 1 1 250 140 20.5 ;
      RECT 2 2 250 140 20.5 ;
    LAYER metal3 ;
      RECT 10 10 40 40 4.5 ;
  END
  CLASS CORE ;
  SYMMETRY X Y R90 ;
  SITE CORE1 ;
  SIZE 67.2 BY 24 ;
  FOREIGN INVS ;
END INV
MACRO INV_B
MACRO CLASS CORE SPACER
Parsed 1500 number of lines!!
  PIN Z
    DIRECTION OUTPUT ;
    USE CLOCK ;
    PORT
      LAYER M1 ;
      WIDTH 1 ;
      PATH MASK 2       ( -0.6 -0.6 )
      ( 0.6 -0.6 )
      ( 0.7 -0.6 ) ;
      LAYER M2 ;
      WIDTH 1 ;
      RECT MASK 1 ( -0.600000 -0.600000 ) ( 0.600000 0.600000 ) ;
      LAYER M3 ;
      WIDTH 1 ;
      RECT MASK 2 ITERATE ( -0.600000 -0.600000 ) ( 0.600000 0.600000 )
      DO 1 BY 2 STEP 2 1 ;
      LAYER M4 ;
      PATH MASK 1       ( 30.8 9 )
      ( 42 9 ) ;
      VIA MASK 103 ( 470.4 475 ) VIABIGPOWER12 ;
      VIA MASK 130 ( 2060.8 475 ) VIABIGPOWER12 ;
      VIA MASK 113 ( 470.4 2040 ) VIABIGPOWER12 ;
      VIA MASK 121 ( 2060.8 2040 ) VIABIGPOWER12 ;
    END
  END Z
  PIN A
    DIRECTION FEEDTHRU ;
    USE SIGNAL ;
    PORT
      LAYER M1 ;
      PATH       ( 25.2 15 ) ;
    END
  END A
Parsed 1550 number of lines!!
  PIN VDD
    DIRECTION INOUT ;
    SHAPE ABUTMENT ;
    PORT
      LAYER M1 ;
      WIDTH 5.6 ;
      PATH       ( 50.4 2.8 )
      ( 50.4 21.2 ) ;
    END
  END VDD
  PIN VSS
    DIRECTION INOUT ;
    SHAPE ABUTMENT ;
    PORT
      LAYER M1 ;
      WIDTH 5.6 ;
      PATH       ( 16.8 2.8 )
      ( 16.8 21.2 ) ;
    END
  END VSS
  OBS
      LAYER M1 ;
      RECT ( 24.100000 1.500000 ) ( 43.500000 16.500000 ) ;
  END
  CLASS CORE SPACER ;
  EEQ INV ;
  SYMMETRY X Y R90 ;
  SITE CORE1 ;
  SIZE 67.2 BY 24 ;
  FOREIGN INVS ( 4 5 ) ;
  FOREIGN INV1 ( 5 6 ) S ;
  FOREIGN INV2 ( 6 7 ) N ;
  FOREIGN INV3 ( 7 8 ) ;
END INV_B
MACRO DFF3
MACRO CLASS CORE ANTENNACELL
Parsed 1600 number of lines!!
  PIN Q
    DIRECTION OUTPUT ;
    USE SIGNAL ;
    PORT
      LAYER M2 ;
      PATH       ( 19.6 99 )
      ( 47.6 99 ) ;
    END
  END Q
  PIN QN
    DIRECTION OUTPUT ;
    USE SIGNAL ;
    PORT
      LAYER M2 ;
      PATH MASK 1       ( 25.2 123 )
      ( 42 123 ) ;
      RECT MASK 2 ( 24.100000 1.500000 ) ( 43.500000 208.500000 ) ;
    END
  END QN
Parsed 1650 number of lines!!
  PIN D
    DIRECTION INPUT ;
    USE SIGNAL ;
    PORT
      LAYER M1 ;
      PATH       ( 30.8 51 ) ;
    END
  END D
  PIN G
    DIRECTION INPUT ;
    USE SIGNAL ;
    PORT
      LAYER M1 ;
      PATH       ( 25.2 3 ) ;
    END
  END G
  PIN CD
    DIRECTION INPUT ;
    USE CLOCK ;
    PORT
      LAYER M1 ;
      PATH       ( 36.4 75 ) ;
    END
  END CD
  PIN VDD
    DIRECTION INOUT ;
    SHAPE RING ;
    PORT
      LAYER M1 ;
      WIDTH 5.6 ;
      PATH       ( 50.4 2.8 )
      ( 50.4 207.2 ) ;
    END
  END VDD
Parsed 1700 number of lines!!
  PIN VSS
    DIRECTION INOUT ;
    SHAPE FEEDTHRU ;
    PORT
      LAYER M1 ;
      WIDTH 5.6 ;
      PATH       ( 16.8 2.8 )
      ( 16.8 207.2 ) ;
    END
  END VSS
  OBS
      LAYER M1 ;
      DESIGNRULEWIDTH 0.15 ;
      RECT ( 24.100000 1.500000 ) ( 43.500000 208.500000 ) ;
      PATH       ( 8.4 3 )
      ( 8.4 123 ) ;
      PATH       ( 58.8 3 )
      ( 58.8 123 ) ;
      PATH       ( 64.4 3 )
      ( 64.4 123 ) ;
  END
  DENSITY
    LAYER metal4 ;
      RECT 24.1 1.5 43.5 208.5 5.5 ;
  END
  CLASS CORE ANTENNACELL ;
  SYMMETRY X Y R90 ;
  SITE CORE 34 54 FE DO 30 BY 3 STEP 1 1 ;
  SITE CORE1 21 68 S DO 30 BY 3 STEP 2 2 ;
  SIZE 67.2 BY 210 ;
  FOREIGN DFF3S ;
END DFF3
MACRO BUF1
MACRO CLASS ENDCAP BOTTOMLEFT
  PIN IN
    ANTENNADIFFAREA 0 ;
    ANTENNADIFFAREA 0 ;
    ANTENNAMODEL OXIDE1 ;
    ANTENNAGATEAREA 1 ;
    ANTENNAGATEAREA 3 ;
    ANTENNAMODEL OXIDE2 ;
    ANTENNAGATEAREA 2 ;
    ANTENNAGATEAREA 4 ;
  END IN
Parsed 1750 number of lines!!
  PIN IN2
    ANTENNAMODEL OXIDE1 ;
    ANTENNAGATEAREA 1 ;
  END IN2
  PIN IN3
    SHAPE ABUTMENT ;
  END IN3
  PIN GND
    USE GROUND ;
    PORT
      LAYER metal1 ;
      POLYGON       ( 0 0 )
      ( 0 1 )
      ( 1 0 )
      ( 2 2 )
      ( 2 0 ) ;
      RECT ( 2.000000 3.000000 ) ( 4.000000 5.000000 ) ;
    END
  END GND
  OBS
      LAYER metal2 ;
      EXCEPTPGNET ;
      POLYGON       ( 0 0 )
      ( 0 1 )
      ( 1 0 )
      ( 2 2 )
      ( 2 0 ) ;
      RECT ( 2.000000 3.000000 ) ( 4.000000 5.000000 ) ;
  END
  CLASS ENDCAP BOTTOMLEFT ;
END BUF1
MACRO DFF4
MACRO CLASS COVER BUMP
  CLASS COVER BUMP ;
  FOREIGN DFF3S ;
END DFF4
MACRO DFF5
MACRO CLASS COVER
  CLASS COVER ;
  FOREIGN DFF3S ;
END DFF5
MACRO mydriver
MACRO CLASS PAD AREAIO
  CLASS PAD AREAIO ;
  FOREIGN DFF3S ;
END mydriver
MACRO myblackbox
MACRO CLASS BLOCK BLACKBOX
  CLASS BLOCK BLACKBOX ;
  FOREIGN DFF3S ;
END myblackbox
MACRO FWHSQCN690V15
MACRO CLASS CORE
Parsed 1800 number of lines!!
  PIN R
    DIRECTION INPUT ;
    USE SIGNAL ;
    PORT
      LAYER a1sig ;
      RECT ( 11.430000 0.800000 ) ( 11.710000 1.200000 ) ;
      RECT ( 9.940000 0.800000 ) ( 11.430000 1.040000 ) ;
      LAYER a1sig ;
      RECT ( 9.490000 0.800000 ) ( 9.710000 0.820000 ) ;
      RECT ( 9.710000 0.800000 ) ( 9.930000 1.040000 ) ;
      RECT ( 9.920000 0.800000 ) ( 9.940000 1.040000 ) ;
      RECT ( 9.000000 2.060000 ) ( 9.720000 2.080000 ) ;
      RECT ( 9.390000 0.800000 ) ( 9.470000 1.780000 ) ;
      RECT ( 9.460000 0.800000 ) ( 9.480000 1.780000 ) ;
      RECT ( 9.470000 0.800000 ) ( 9.490000 1.770000 ) ;
      RECT ( 9.480000 0.810000 ) ( 9.720000 2.070000 ) ;
      RECT ( 9.000000 1.770000 ) ( 9.480000 2.070000 ) ;
    END
  END R
  PIN SI
    DIRECTION INPUT ;
    USE SIGNAL ;
    PORT
      LAYER a1sig ;
    END
  END SI
  PIN SM
    DIRECTION INPUT ;
    USE SIGNAL ;
    PORT
      LAYER a1sig ;
      RECT ( 0.820000 3.510000 ) ( 1.080000 3.890000 ) ;
      LAYER a1sig ;
      RECT ( 0.360000 3.510000 ) ( 0.590000 3.690000 ) ;
      RECT ( 0.360000 3.680000 ) ( 0.590000 3.700000 ) ;
      RECT ( 0.590000 3.510000 ) ( 0.810000 3.890000 ) ;
      RECT ( 0.590000 3.510000 ) ( 0.810000 3.890000 ) ;
      RECT ( 0.800000 3.510000 ) ( 0.820000 3.890000 ) ;
      RECT ( 0.360000 3.690000 ) ( 0.600000 3.990000 ) ;
      LAYER a1sig ;
    END
  END SM
Parsed 1850 number of lines!!
  PIN T
    DIRECTION INPUT ;
    USE SIGNAL ;
    PORT
      LAYER a1sig ;
      LAYER a1sig ;
      RECT ( 4.850000 3.510000 ) ( 4.910000 3.700000 ) ;
      RECT ( 4.910000 3.510000 ) ( 5.130000 3.990000 ) ;
      RECT ( 5.120000 3.510000 ) ( 5.140000 3.990000 ) ;
      RECT ( 4.200000 3.690000 ) ( 4.920000 3.990000 ) ;
    END
  END T
  OBS
      LAYER a1sig ;
      SPACING 0 ;
      RECT ( 0.110000 0.740000 ) ( 0.810000 3.470000 ) ;
      RECT ( 0.800000 0.740000 ) ( 0.820000 3.470000 ) ;
      RECT ( 0.820000 0.740000 ) ( 2.540000 4.060000 ) ;
      RECT ( 2.540000 0.740000 ) ( 3.700000 3.470000 ) ;
      RECT ( 3.700000 0.740000 ) ( 3.980000 4.060000 ) ;
      RECT ( 3.980000 0.740000 ) ( 5.140000 3.470000 ) ;
      RECT ( 5.140000 0.740000 ) ( 8.780000 4.060000 ) ;
      RECT ( 8.780000 0.740000 ) ( 9.940000 1.540000 ) ;
      RECT ( 8.780000 1.530000 ) ( 9.940000 1.550000 ) ;
      RECT ( 8.780000 2.290000 ) ( 9.940000 4.060000 ) ;
      RECT ( 9.940000 0.740000 ) ( 14.060000 4.060000 ) ;
      RECT ( 14.060000 0.740000 ) ( 15.240000 2.510000 ) ;
      RECT ( 15.230000 0.740000 ) ( 15.250000 2.510000 ) ;
      RECT ( 14.060000 3.250000 ) ( 15.250000 4.050000 ) ;
      RECT ( 14.060000 4.040000 ) ( 15.250000 4.060000 ) ;
  END
  CLASS CORE ;
  SYMMETRY X Y ;
  SITE CORE ;
  SIZE 15.36 BY 4.8 ;
  FOREIGN FWHSQCN690 ( 0 0 ) ;
  ORIGIN ( 0 0 ) ;
END FWHSQCN690V15
MACRO mysoft
MACRO CLASS BLOCK SOFT
  OBS
      LAYER a1sig ;
      DESIGNRULEWIDTH 0 ;
      RECT ( 0.110000 0.740000 ) ( 0.810000 3.470000 ) ;
  END
  CLASS BLOCK SOFT ;
  FOREIGN DFF3S ;
END mysoft
MACRO mycorewelltap
MACRO CLASS CORE WELLTAP
  CLASS CORE WELLTAP ;
  FOREIGN DFF3S ;
END mycorewelltap
MACRO myTest
MACRO CLASS CORE
Parsed 1900 number of lines!!
  CLASS CORE ;
  SYMMETRY X ;
  SITE Fsite ;
  SITE Fsite 0 0 N ;
  SITE Fsite 0 7 FS ;
  SITE Lsite 4 0 N ;
  SITE Lsite 0 0 S DO 2 BY 1 STEP 4 5 ;
  SITE Fsite 4 0 E ;
  SITE Lsite 0.3 0 S DO 2 BY 1 STEP 4 5 ;
  SITE Fsite 0 0 N DO 2 BY 1 STEP 4 0 ;
  SIZE 10 BY 14 ;
END myTest
MACRO myMac
MACRO CLASS CORE
  PIN In1
    USE SIGNAL ;
    SUPPLYSENSITIVITY vddpin1 ;
  END In1
  PIN vddpin1
    USE SIGNAL ;
    NETEXPR "power1 VDD1" ;
  END vddpin1
  PIN vddpin2
    USE SIGNAL ;
    NETEXPR "power2 VDD2" ;
  END vddpin2
  PIN gndpin
    USE SIGNAL ;
    NETEXPR "gnd1 GND" ;
  END gndpin
  PIN In2
    USE SIGNAL ;
    GROUNDSENSITIVITY gndpin ;
  END In2
  CLASS CORE ;
  SYMMETRY X ;
  SIZE 10 BY 14 ;
END myMac
ANTENNAINPUTGATEAREA 45 ;
Parsed 1950 number of lines!!
ANTENNAINOUTDIFFAREA 65 ;
ANTENNAOUTPUTDIFFAREA 55 ;
BEGINEXT  "SIGNATURE"
   CREATOR "CADENCE"
   DATE "04/14/98"
ENDEXT ;
