DEF_TABNAME     = def
DEF_BISON_SRCS  = def.y

FAKE_ALL: all

LIBTARGET =	libcdef.a

PUBLIC_HDRS = \
			defiAssertion.h \
			defiBlockage.h \
			defiComponent.h \
			defiDebug.h \
			defiDefs.h \
			defiFPC.h \
			defiFill.h \
			defiGroup.h \
			defiIOTiming.h \
                        defiKRDefs.h \
			defiMisc.h \
			defiNet.h \
			defiNonDefault.h \
			defiPartition.h \
			defiPath.h \
			defiPinCap.h \
			defiPinProp.h \
			defiProp.h \
			defiPropType.h \
			defiRegion.h \
			defiRowTrack.h \
			defiScanchain.h \
			defiSite.h \
			defiSlot.h \
			defiTimingDisable.h \
			defiTypedefs.h \
			defiUser.h \
			defiVia.h \
			defrReader.h \
			defwWriter.h \
			defwWriterCalls.h

LIBSRCS =	\
			xdefiAssertion.cpp \
			xdefiBlockage.cpp \
			xdefiComponent.cpp \
			xdefiDebug.cpp \
			xdefiFPC.cpp \
			xdefiFill.cpp \
			xdefiGroup.cpp \
			xdefiIOTiming.cpp \
			xdefiMisc.cpp \
			xdefiNet.cpp \
			xdefiNonDefault.cpp \
			xdefiPartition.cpp \
			xdefiPath.cpp \
			xdefiPinCap.cpp \
			xdefiPinProp.cpp \
			xdefiProp.cpp \
			xdefiPropType.cpp \
			xdefiRegion.cpp \
			xdefiRowTrack.cpp \
			xdefiScanchain.cpp \
			xdefiSite.cpp \
			xdefiSlot.cpp \
			xdefiTimingDisable.cpp \
			xdefiVia.cpp \
			xdefrReader.cpp \
			xdefwWriter.cpp \
			xdefwWriterCalls.cpp

include ../template.mk
