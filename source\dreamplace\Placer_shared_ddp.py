##
# @file   Placer_shared_ddp.py
# <AUTHOR> Assistant
# @date   2024
# @brief  Shared parameter DDP-aware main file to run the entire placement flow
#

import matplotlib
matplotlib.use('Agg')
import os
import sys
import time
import numpy as np
import torch
import torch.distributed as dist
import torch.multiprocessing as mp
import Params
import PlaceDB
import NonLinearPlace_shared_ddp
from dreamplace.ddp_shared_param_utils import setup_ddp, cleanup_ddp
import pdb

# Import performance optimizations (automatically applied on import)
try:
    import sys
    import os
    sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))
    from performance_optimizer import apply_global_optimizations, performance_monitor
    PERFORMANCE_OPTIMIZATION_AVAILABLE = True
except ImportError:
    PERFORMANCE_OPTIMIZATION_AVAILABLE = False

def place_shared_ddp(rank, world_size, params):
    """
    @brief Shared parameter DDP placement function for single GPU
    @param rank current GPU rank
    @param world_size total number of GPUs
    @param params parameters
    """
    try:
        # Setup DDP
        if world_size > 1:
            setup_ddp(rank, world_size)
        
        # Set random seed for reproducibility
        np.random.seed(params.random_seed + rank)
        torch.manual_seed(params.random_seed + rank)
        
        # Read database (only rank 0 prints timing)
        tt = time.time()
        placedb = PlaceDB.PlaceDB()
        placedb(params)
        if rank == 0:
            print("[I] reading database takes %.2f seconds" % (time.time()-tt))

        # Solve placement with shared parameter DDP
        tt = time.time()
        placer = NonLinearPlace_shared_ddp.NonLinearPlaceSharedDDP(params, placedb, rank, world_size)
        if rank == 0:
            print("[I] Shared parameter DDP non-linear placement initialization takes %.2f seconds" % (time.time()-tt))
        
        metrics = placer(params, placedb)
        if rank == 0:
            print("[I] Shared parameter DDP non-linear placement takes %.2f seconds" % (time.time()-tt))

        # Write placement solution (only rank 0)
        if rank == 0:
            def get_parent_dir(current_path: str, levels: int = 1) -> str:
                for _ in range(levels):
                    current_path = os.path.dirname(current_path)
                return current_path
          
            current_file = os.path.abspath(__file__)
            parent_level = 3
            parent_dir = get_parent_dir(current_file, levels=parent_level)
            
            target_dir = os.path.join(parent_dir, "examples")
            aux_basename = os.path.splitext(os.path.basename(params.aux_file))[0]
            path = os.path.join(target_dir, aux_basename)
            if not os.path.exists(path):
                os.makedirs(path, exist_ok=True)
            gp_out_file = os.path.join(path, os.path.basename(params.aux_file).replace(".aux", ".shared_ddp.gp.pl"))
            placedb.write_pl(params, gp_out_file)

            # Call external detailed placement
            if params.detailed_place_engine and os.path.exists(params.detailed_place_engine):
                print("[I] Use external detailed placement engine %s" % (params.detailed_place_engine))
                dp_out_file = gp_out_file.replace(".shared_ddp.gp.pl", "")
                target_density_cmd = ""
                if params.target_density < 1.0:
                    target_density_cmd = " -util %f" % (params.target_density)
                legalize = "-nolegal" if params.legalize_flag else ""
                detailed_place = "-nodetail" if params.detailed_place_flag else ""
                cmd = "%s -aux %s -loadpl %s %s -out %s -noglobal %s %s" % (
                    params.detailed_place_engine, params.aux_file, gp_out_file, 
                    target_density_cmd, dp_out_file, legalize, detailed_place)
                print("[I] %s" % (cmd))
                tt = time.time()
                os.system(cmd)
                print("[I] detailed placement takes %.2f seconds" % (time.time()-tt))

                if params.plot_flag:
                    # read solution and evaluate
                    placedb.read_pl(dp_out_file+".ntup.pl")
                    placedb.scale_pl(params.scale_factor)
                    iteration = len(metrics)
                    pos = placer.init_pos
                    pos[0:placedb.num_physical_nodes] = placedb.node_x
                    pos[placedb.num_nodes:placedb.num_nodes+placedb.num_physical_nodes] = placedb.node_y
                    hpwl, density_overflow, max_density = placer.validate(placedb, pos, iteration)
                    print("[I] iteration %4d, HPWL %.3E, overflow %.3E, max density %.3E" % (iteration, hpwl, density_overflow, max_density))
                    placer.plot(params, placedb, iteration, pos)
            elif params.detailed_place_engine:
                print("[W] External detailed placement engine %s NOT found" % (params.detailed_place_engine))
                
    except Exception as e:
        print(f"[E] Error in rank {rank}: {e}")
        raise e
    finally:
        # Cleanup DDP
        cleanup_ddp()

def place(params):
    """
    @brief Top API to run the entire shared parameter DDP placement flow
    @param params parameters
    """
    # Determine number of GPUs
    if params.gpu and torch.cuda.is_available():
        world_size = torch.cuda.device_count()
        if world_size > 1:
            print(f"[I] Using shared parameter DDP with {world_size} GPUs")
            # Launch DDP training
            mp.spawn(place_shared_ddp, args=(world_size, params), nprocs=world_size, join=True)
            print("结束")
        else:
            print("[I] Only 1 GPU available, using single GPU training")
            place_shared_ddp(0, 1, params)
    else:
        print("[I] Using CPU training")
        place_shared_ddp(0, 1, params)

if __name__ == "__main__":
    """
    @brief main function to invoke the entire shared parameter DDP placement flow
    """
    params = Params.Params()
    params.printWelcome()
    if len(sys.argv) == 1 or '-h' in sys.argv[1:] or '--help' in sys.argv[1:]:
        params.printHelp()
        exit()
    elif len(sys.argv) != 2:
        print("[E] One input parameters in json format is required")
        params.printHelp()
        exit()

    # load parameters
    params.load(sys.argv[1])
    print("[I] parameters = %s" % (params))

    # run shared parameter DDP placement
    tt = time.time()
    place(params)
    print("[I] Shared parameter DDP placement takes %.3f seconds" % (time.time()-tt))
