# ==========================================================================
#                    Configuration for gcc
# ==========================================================================

# =========== -stdlib and -std flags ===========
# Available combinations of (CXXSTDLIB and CXXSTD) for g++ are 
# -std=c++98 (default)
# -std=c++11 
# (there is no -stdlib option in g++)
# ==============================================
override CXXSTDLIB := # no such option under g++, always set to empty 
CXXSTD = -std=c++98

CXXFLAGS_BASIC = -fmax-errors=1 -fPIC -W -Wall -Wextra -Wreturn-type -m64 $(CXXSTDLIB) $(CXXSTD) -Wno-deprecated -Wno-unused-local-typedefs -Wno-ignored-qualifiers
CXXFLAGS_DEBUG = -g $(CXXFLAGS_BASIC) 
CXXFLAGS_RELEASE = -O3 -fopenmp $(CXXFLAGS_BASIC) 

CFLAGS_BASIC = -fmax-errors=1 -fPIC -W -Wall -Wextra -Wreturn-type -ansi -m64 -Wno-deprecated -Wno-unused-local-typedefs
CFLAGS_DEBUG = -g $(CFLAGS_BASIC) 
CFLAGS_RELEASE = -O3 -fopenmp $(CFLAGS_BASIC) 

ARFLAGS = rvs

