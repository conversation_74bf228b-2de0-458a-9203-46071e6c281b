# ==========================================================================
#                    Configuration under clang
# ==========================================================================

# =========== -stdlib and -std flags ===========
# These flags are very important under darwin. 
# Available combinations of (CXXSTDLIB and CXXSTD) for clang++ are 
# -stdlib=libstdc++ -std=c++98 (default)
# -stdlib=libc++ -std=c++98 
# -stdlib=libc++ -std=c++11
# Available combinations of (CXXSTDLIB and CXXSTD) for g++ are 
# -std=c++98 (default)
# -std=c++11 
# (there is no -stdlib option in g++)
# ==============================================
ifeq ($(CXX), clang++)
	CXXSTDLIB = -stdlib=libstdc++
	CXXSTD = -std=c++98
else 
	override CXXSTDLIB = # no such option under g++ 
	CXXSTD = -std=c++98
endif 

CXXFLAGS_BASIC = -ferror-limit=1 -fPIC -W -Wall -Wextra -Wreturn-type -m64 $(CXXSTDLIB) $(CXXSTD) -Wno-deprecated -Wno-unused-parameter -Wno-unused-local-typedef
CXXFLAGS_DEBUG = -g -DDEBUG $(CXXFLAGS_BASIC) 
CXXFLAGS_RELEASE = -O3 $(CXXFLAGS_BASIC) 

CFLAGS_BASIC = -ferror-limit=1 -fPIC -W -Wall -Wextra -Wreturn-type -m64 -Wno-deprecated -Wno-unused-parameter -Wno-unused-local-typedef
CFLAGS_DEBUG = -g -DDEBUG $(CFLAGS_BASIC) 
CFLAGS_RELEASE = -O3 $(CFLAGS_BASIC) 

ARFLAGS = rvs

