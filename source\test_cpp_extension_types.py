#!/usr/bin/env python3
##
# @file   test_cpp_extension_types.py
# <AUTHOR> Assistant
# @date   2024
# @brief  Test script to verify C++/HIP extension type compatibility
#

import os
import sys
import torch
import numpy as np

# Add dreamplace to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'dreamplace'))

def test_tensor_type_conversion():
    """Test tensor type conversion for C++ extension compatibility"""
    print("Testing tensor type conversion for C++ extensions...")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # Create test tensors with different types
    test_data = {
        'int32_tensor': torch.randint(0, 100, (50,), dtype=torch.int32, device=device),
        'int64_tensor': torch.randint(0, 100, (50,), dtype=torch.int64, device=device),
        'float32_tensor': torch.randn(50, dtype=torch.float32, device=device),
        'float64_tensor': torch.randn(50, dtype=torch.float64, device=device),
    }
    
    print("Original tensor types:")
    for name, tensor in test_data.items():
        print(f"  {name}: {tensor.dtype}")
    
    # Test type conversion functions
    def convert_to_int32_if_needed(tensor):
        return tensor.int() if tensor.dtype != torch.int32 else tensor
    
    def convert_to_long_if_needed(tensor):
        return tensor.long() if tensor.dtype != torch.int64 else tensor
    
    # Test conversions
    print("\nTesting type conversions:")
    
    # Test int64 -> int32 conversion
    int64_tensor = test_data['int64_tensor']
    int32_converted = convert_to_int32_if_needed(int64_tensor)
    print(f"  int64 -> int32: {int64_tensor.dtype} -> {int32_converted.dtype}")
    assert int32_converted.dtype == torch.int32
    
    # Test int32 -> int32 (no conversion)
    int32_tensor = test_data['int32_tensor']
    int32_unchanged = convert_to_int32_if_needed(int32_tensor)
    print(f"  int32 -> int32: {int32_tensor.dtype} -> {int32_unchanged.dtype}")
    assert int32_unchanged.dtype == torch.int32
    assert torch.equal(int32_tensor, int32_unchanged)  # Should be the same tensor
    
    # Test int32 -> int64 conversion
    int64_converted = convert_to_long_if_needed(int32_tensor)
    print(f"  int32 -> int64: {int32_tensor.dtype} -> {int64_converted.dtype}")
    assert int64_converted.dtype == torch.int64
    
    print("✅ All type conversions work correctly")
    return True

def test_weighted_average_wirelength_types():
    """Test type handling in WeightedAverageWirelengthSharedDDP"""
    print("\nTesting WeightedAverageWirelengthSharedDDP type handling...")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # Create test data with long types (as they would come from data_collections)
    num_nodes = 100
    num_nets = 50
    num_pins = 200
    
    pin2node_map = torch.randint(0, num_nodes, (num_pins,), dtype=torch.int64, device=device)
    pin2net_map = torch.randint(0, num_nets, (num_pins,), dtype=torch.int64, device=device)
    flat_netpin = torch.arange(num_pins, dtype=torch.int64, device=device)
    netpin_start = torch.arange(0, num_pins + 1, dtype=torch.int64, device=device)
    
    print(f"Original types:")
    print(f"  pin2node_map: {pin2node_map.dtype}")
    print(f"  pin2net_map: {pin2net_map.dtype}")
    print(f"  flat_netpin: {flat_netpin.dtype}")
    print(f"  netpin_start: {netpin_start.dtype}")
    
    # Test type conversion functions used in the code
    def convert_for_cpp_extension(tensor):
        return tensor.int() if tensor.dtype != torch.int32 else tensor
    
    # Test conversions
    pin2net_map_int32 = convert_for_cpp_extension(pin2net_map)
    flat_netpin_int32 = convert_for_cpp_extension(flat_netpin)
    netpin_start_int32 = convert_for_cpp_extension(netpin_start)
    
    print(f"\nConverted types for C++ extensions:")
    print(f"  pin2net_map_int32: {pin2net_map_int32.dtype}")
    print(f"  flat_netpin_int32: {flat_netpin_int32.dtype}")
    print(f"  netpin_start_int32: {netpin_start_int32.dtype}")
    
    # Verify all are int32
    assert pin2net_map_int32.dtype == torch.int32
    assert flat_netpin_int32.dtype == torch.int32
    assert netpin_start_int32.dtype == torch.int32
    
    # Verify values are preserved
    assert torch.equal(pin2net_map.long(), pin2net_map_int32.long())
    assert torch.equal(flat_netpin.long(), flat_netpin_int32.long())
    assert torch.equal(netpin_start.long(), netpin_start_int32.long())
    
    print("✅ Type conversions preserve values correctly")
    return True

def test_pin_pos_op_indexing():
    """Test PinPosOp indexing with long tensors"""
    print("\nTesting PinPosOp indexing with long tensors...")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # Create test data
    num_nodes = 100
    num_pins = 150
    
    # Create pin2node_map as long tensor (for indexing)
    pin2node_map = torch.randint(0, num_nodes, (num_pins,), dtype=torch.int64, device=device)
    pin_offset_x = torch.randn(num_pins, device=device)
    pin_offset_y = torch.randn(num_pins, device=device)
    
    # Create node positions
    pos = torch.randn(2 * num_nodes, device=device)
    
    print(f"pin2node_map dtype: {pin2node_map.dtype}")
    print(f"pos shape: {pos.shape}")
    
    try:
        # Test indexing (this should work with long tensors)
        node_x = pos[:num_nodes]
        node_y = pos[num_nodes:]
        
        pin_x = node_x[pin2node_map] + pin_offset_x
        pin_y = node_y[pin2node_map] + pin_offset_y
        
        print(f"✅ Indexing successful: pin_x shape {pin_x.shape}, pin_y shape {pin_y.shape}")
        return True
        
    except Exception as e:
        print(f"❌ Indexing failed: {e}")
        return False

def test_dual_type_strategy():
    """Test the dual type strategy: long for indexing, int32 for C++ extensions"""
    print("\nTesting dual type strategy...")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # Simulate data_collections setup
    num_nodes = 100
    num_pins = 150
    
    # Original numpy data (typically int32)
    pin2node_map_np = np.random.randint(0, num_nodes, num_pins, dtype=np.int32)
    
    # Convert to torch tensor with long type for indexing
    pin2node_map_long = torch.from_numpy(pin2node_map_np).long().to(device)
    
    # Create node positions
    pos = torch.randn(2 * num_nodes, device=device)
    
    print(f"Original numpy dtype: {pin2node_map_np.dtype}")
    print(f"Torch long tensor dtype: {pin2node_map_long.dtype}")
    
    # Test 1: Use long tensor for indexing
    try:
        node_x = pos[:num_nodes]
        indexed_nodes = node_x[pin2node_map_long]
        print(f"✅ Indexing with long tensor successful: shape {indexed_nodes.shape}")
    except Exception as e:
        print(f"❌ Indexing with long tensor failed: {e}")
        return False
    
    # Test 2: Convert to int32 for C++ extension simulation
    pin2node_map_int32 = pin2node_map_long.int()
    print(f"Converted to int32 dtype: {pin2node_map_int32.dtype}")
    
    # Verify values are preserved
    assert torch.equal(pin2node_map_long.cpu(), pin2node_map_int32.long().cpu())
    print("✅ Values preserved during type conversion")
    
    # Test 3: Conditional conversion (as used in the actual code)
    def convert_to_int32_if_needed(tensor):
        return tensor.int() if tensor.dtype != torch.int32 else tensor
    
    # Test with long tensor
    converted_from_long = convert_to_int32_if_needed(pin2node_map_long)
    assert converted_from_long.dtype == torch.int32
    
    # Test with int32 tensor (should not convert)
    converted_from_int32 = convert_to_int32_if_needed(pin2node_map_int32)
    assert converted_from_int32.dtype == torch.int32
    assert torch.equal(pin2node_map_int32, converted_from_int32)  # Should be same tensor
    
    print("✅ Conditional conversion works correctly")
    return True

def main():
    """Run all C++ extension type tests"""
    print("=" * 60)
    print("Testing C++/HIP Extension Type Compatibility")
    print("=" * 60)
    
    success = True
    
    # Test 1: Basic type conversion
    try:
        success &= test_tensor_type_conversion()
    except Exception as e:
        print(f"❌ Tensor type conversion test failed: {e}")
        success = False
    
    # Test 2: WeightedAverageWirelength type handling
    try:
        success &= test_weighted_average_wirelength_types()
    except Exception as e:
        print(f"❌ WeightedAverageWirelength type test failed: {e}")
        success = False
    
    # Test 3: PinPosOp indexing
    try:
        success &= test_pin_pos_op_indexing()
    except Exception as e:
        print(f"❌ PinPosOp indexing test failed: {e}")
        success = False
    
    # Test 4: Dual type strategy
    try:
        success &= test_dual_type_strategy()
    except Exception as e:
        print(f"❌ Dual type strategy test failed: {e}")
        success = False
    
    print("\n" + "=" * 60)
    if success:
        print("✅ All C++/HIP extension type tests passed!")
        print("The 'expected scalar type Int but found Long' error should now be fixed.")
    else:
        print("❌ Some C++/HIP extension type tests failed!")
    print("=" * 60)
    
    return success

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
