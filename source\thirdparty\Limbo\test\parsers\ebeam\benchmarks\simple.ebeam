## @file  simple.ebeam 
#  @brief ebeam configurations 

UNITS
	DATABASE MICRONS 2000 ; 
END UNITS

EBEAMBOUNDARY
	OFFSET 0 ; 
	WIDTH 0.1 ; 
	STEP 5 ; 
	LAYERID 15 16 ; 
END EBEAMBOUNDARY

MACRO XOR2_X1
	CONFLICTSITE C200
		LAYERID 200 ; 
		SITE 0 1 2 3 4 5 6 7 8  ; 
	END C200
	CONFLICTSITE C16
		LAYERID 16 ; 
		SITE 0 1 7 8  ; 
	END C16
	CONFLICTSITE C15
		LAYERID 15 ; 
		SITE 1 2  ; 
	END C15
	CONFLICTSITE C6
		LAYERID 6 ; 
		SITE 0 1 2 3 4 5 6 7 8 9  ; 
	END C6
	CONFLICTSITE C8
		LAYERID 8 ; 
		SITE 0 2 4 6 8  ; 
	END C8
	CONFLICTSITE C7
		LAYERID 7 ; 
		SITE 1 3 5 7  ; 
	END C7
	CONFLICTSITE C5
		LAYERID 5 ; 
		SITE 0 1 2 3 4 5 6 7 8 9  ; 
	END C5
	CONFLICTSITE C0
		LAYERID 0 ; 
		SITE 0 1 2 3 4 5 6 7 8 9 10  ; 
	END C0
END XOR2_X1

END LIBRARY
