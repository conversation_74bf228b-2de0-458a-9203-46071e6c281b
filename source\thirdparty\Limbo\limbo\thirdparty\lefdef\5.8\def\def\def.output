Terminals unused in grammar

   SITE_PATTERN
   K_SITE
   K_START_NET
   K_IN
   K_OUT
   K_DEFINE
   K_DEFINES
   K_DEFINEB
   K_IF
   K_THEN
   K_ELSE
   K_FALSE
   K_TRUE
   K_EQ
   K_NE
   K_LE
   K_LT
   K_GE
   K_GT
   K_OR
   K_AND
   K_NOT
   K_ENDEXT


Grammar

    0 $accept: def_file $end

    1 def_file: version_stmt case_sens_stmt rules end_design

    2 version_stmt: %empty

    3 $@1: %empty

    4 version_stmt: K_VERSION $@1 T_STRING ';'

    5 case_sens_stmt: %empty
    6               | K_NAMESCASESENSITIVE K_ON ';'
    7               | K_NAMESCASESENSITIVE K_OFF ';'

    8 rules: %empty
    9      | rules rule
   10      | error

   11 rule: design_section
   12     | assertions_section
   13     | blockage_section
   14     | comps_section
   15     | constraint_section
   16     | extension_section
   17     | fill_section
   18     | comps_maskShift_section
   19     | floorplan_contraints_section
   20     | groups_section
   21     | iotiming_section
   22     | nets_section
   23     | nondefaultrule_section
   24     | partitions_section
   25     | pin_props_section
   26     | regions_section
   27     | scanchains_section
   28     | slot_section
   29     | snets_section
   30     | styles_section
   31     | timingdisables_section
   32     | via_section

   33 design_section: array_name
   34               | bus_bit_chars
   35               | canplace
   36               | cannotoccupy
   37               | design_name
   38               | die_area
   39               | divider_char
   40               | floorplan_name
   41               | gcellgrid
   42               | history
   43               | pin_cap_rule
   44               | pin_rule
   45               | prop_def_section
   46               | row_rule
   47               | tech_name
   48               | tracks_rule
   49               | units

   50 $@2: %empty

   51 design_name: K_DESIGN $@2 T_STRING ';'

   52 end_design: K_END K_DESIGN

   53 $@3: %empty

   54 tech_name: K_TECH $@3 T_STRING ';'

   55 $@4: %empty

   56 array_name: K_ARRAY $@4 T_STRING ';'

   57 $@5: %empty

   58 floorplan_name: K_FLOORPLAN $@5 T_STRING ';'

   59 history: K_HISTORY

   60 $@6: %empty

   61 prop_def_section: K_PROPERTYDEFINITIONS $@6 property_defs K_END K_PROPERTYDEFINITIONS

   62 property_defs: %empty
   63              | property_defs property_def

   64 $@7: %empty

   65 property_def: K_DESIGN $@7 T_STRING property_type_and_val ';'

   66 $@8: %empty

   67 property_def: K_NET $@8 T_STRING property_type_and_val ';'

   68 $@9: %empty

   69 property_def: K_SNET $@9 T_STRING property_type_and_val ';'

   70 $@10: %empty

   71 property_def: K_REGION $@10 T_STRING property_type_and_val ';'

   72 $@11: %empty

   73 property_def: K_GROUP $@11 T_STRING property_type_and_val ';'

   74 $@12: %empty

   75 property_def: K_COMPONENT $@12 T_STRING property_type_and_val ';'

   76 $@13: %empty

   77 property_def: K_ROW $@13 T_STRING property_type_and_val ';'

   78 $@14: %empty

   79 property_def: K_COMPONENTPIN $@14 T_STRING property_type_and_val ';'

   80 $@15: %empty

   81 property_def: K_NONDEFAULTRULE $@15 T_STRING property_type_and_val ';'
   82             | error ';'

   83 $@16: %empty

   84 property_type_and_val: K_INTEGER $@16 opt_range opt_num_val

   85 $@17: %empty

   86 property_type_and_val: K_REAL $@17 opt_range opt_num_val
   87                      | K_STRING
   88                      | K_STRING QSTRING
   89                      | K_NAMEMAPSTRING T_STRING

   90 opt_num_val: %empty
   91            | NUMBER

   92 units: K_UNITS K_DISTANCE K_MICRONS NUMBER ';'

   93 divider_char: K_DIVIDERCHAR QSTRING ';'

   94 bus_bit_chars: K_BUSBITCHARS QSTRING ';'

   95 $@18: %empty

   96 canplace: K_CANPLACE $@18 T_STRING NUMBER NUMBER orient K_DO NUMBER K_BY NUMBER K_STEP NUMBER NUMBER ';'

   97 $@19: %empty

   98 cannotoccupy: K_CANNOTOCCUPY $@19 T_STRING NUMBER NUMBER orient K_DO NUMBER K_BY NUMBER K_STEP NUMBER NUMBER ';'

   99 orient: K_N
  100       | K_W
  101       | K_S
  102       | K_E
  103       | K_FN
  104       | K_FW
  105       | K_FS
  106       | K_FE

  107 $@20: %empty

  108 die_area: K_DIEAREA $@20 firstPt nextPt otherPts ';'

  109 pin_cap_rule: start_def_cap pin_caps end_def_cap

  110 start_def_cap: K_DEFAULTCAP NUMBER

  111 pin_caps: %empty
  112         | pin_caps pin_cap

  113 pin_cap: K_MINPINS NUMBER K_WIRECAP NUMBER ';'

  114 end_def_cap: K_END K_DEFAULTCAP

  115 pin_rule: start_pins pins end_pins

  116 start_pins: K_PINS NUMBER ';'

  117 pins: %empty
  118     | pins pin

  119 $@21: %empty

  120 $@22: %empty

  121 $@23: %empty

  122 pin: '-' $@21 T_STRING '+' K_NET $@22 T_STRING $@23 pin_options ';'

  123 pin_options: %empty
  124            | pin_options pin_option

  125 pin_option: '+' K_SPECIAL
  126           | extension_stmt
  127           | '+' K_DIRECTION T_STRING
  128           | '+' K_NETEXPR QSTRING

  129 $@24: %empty

  130 pin_option: '+' K_SUPPLYSENSITIVITY $@24 T_STRING

  131 $@25: %empty

  132 pin_option: '+' K_GROUNDSENSITIVITY $@25 T_STRING
  133           | '+' K_USE use_type
  134           | '+' K_PORT

  135 $@26: %empty

  136 $@27: %empty

  137 pin_option: '+' K_LAYER $@26 T_STRING $@27 pin_layer_mask_opt pin_layer_spacing_opt pt pt

  138 $@28: %empty

  139 $@29: %empty

  140 pin_option: '+' K_POLYGON $@28 T_STRING $@29 pin_poly_mask_opt pin_poly_spacing_opt firstPt nextPt nextPt otherPts

  141 $@30: %empty

  142 pin_option: '+' K_VIA $@30 T_STRING pin_via_mask_opt '(' NUMBER NUMBER ')'
  143           | placement_status pt orient
  144           | '+' K_ANTENNAPINPARTIALMETALAREA NUMBER pin_layer_opt
  145           | '+' K_ANTENNAPINPARTIALMETALSIDEAREA NUMBER pin_layer_opt
  146           | '+' K_ANTENNAPINGATEAREA NUMBER pin_layer_opt
  147           | '+' K_ANTENNAPINDIFFAREA NUMBER pin_layer_opt

  148 $@31: %empty

  149 pin_option: '+' K_ANTENNAPINMAXAREACAR NUMBER K_LAYER $@31 T_STRING

  150 $@32: %empty

  151 pin_option: '+' K_ANTENNAPINMAXSIDEAREACAR NUMBER K_LAYER $@32 T_STRING
  152           | '+' K_ANTENNAPINPARTIALCUTAREA NUMBER pin_layer_opt

  153 $@33: %empty

  154 pin_option: '+' K_ANTENNAPINMAXCUTCAR NUMBER K_LAYER $@33 T_STRING
  155           | '+' K_ANTENNAMODEL pin_oxide

  156 pin_layer_mask_opt: %empty
  157                   | K_MASK NUMBER

  158 pin_via_mask_opt: %empty
  159                 | K_MASK NUMBER

  160 pin_poly_mask_opt: %empty
  161                  | K_MASK NUMBER

  162 pin_layer_spacing_opt: %empty
  163                      | K_SPACING NUMBER
  164                      | K_DESIGNRULEWIDTH NUMBER

  165 pin_poly_spacing_opt: %empty
  166                     | K_SPACING NUMBER
  167                     | K_DESIGNRULEWIDTH NUMBER

  168 pin_oxide: K_OXIDE1
  169          | K_OXIDE2
  170          | K_OXIDE3
  171          | K_OXIDE4

  172 use_type: K_SIGNAL
  173         | K_POWER
  174         | K_GROUND
  175         | K_CLOCK
  176         | K_TIEOFF
  177         | K_ANALOG
  178         | K_SCAN
  179         | K_RESET

  180 pin_layer_opt: %empty

  181 $@34: %empty

  182 pin_layer_opt: K_LAYER $@34 T_STRING

  183 end_pins: K_END K_PINS

  184 $@35: %empty

  185 $@36: %empty

  186 row_rule: K_ROW $@35 T_STRING T_STRING NUMBER NUMBER orient $@36 row_do_option row_options ';'

  187 row_do_option: %empty
  188              | K_DO NUMBER K_BY NUMBER row_step_option

  189 row_step_option: %empty
  190                | K_STEP NUMBER NUMBER

  191 row_options: %empty
  192            | row_options row_option

  193 $@37: %empty

  194 row_option: '+' K_PROPERTY $@37 row_prop_list

  195 row_prop_list: %empty
  196              | row_prop_list row_prop

  197 row_prop: T_STRING NUMBER
  198         | T_STRING QSTRING
  199         | T_STRING T_STRING

  200 $@38: %empty

  201 tracks_rule: track_start NUMBER $@38 K_DO NUMBER K_STEP NUMBER track_opts ';'

  202 track_start: K_TRACKS track_type

  203 track_type: K_X
  204           | K_Y

  205 track_opts: track_mask_statement track_layer_statement

  206 track_mask_statement: %empty
  207                     | K_MASK NUMBER same_mask

  208 same_mask: %empty
  209          | K_SAMEMASK

  210 track_layer_statement: %empty

  211 $@39: %empty

  212 track_layer_statement: K_LAYER $@39 track_layer track_layers

  213 track_layers: %empty
  214             | track_layer track_layers

  215 track_layer: T_STRING

  216 gcellgrid: K_GCELLGRID track_type NUMBER K_DO NUMBER K_STEP NUMBER ';'

  217 extension_section: K_BEGINEXT

  218 extension_stmt: '+' K_BEGINEXT

  219 via_section: via via_declarations via_end

  220 via: K_VIAS NUMBER ';'

  221 via_declarations: %empty
  222                 | via_declarations via_declaration

  223 $@40: %empty

  224 $@41: %empty

  225 via_declaration: '-' $@40 T_STRING $@41 layer_stmts ';'

  226 layer_stmts: %empty
  227            | layer_stmts layer_stmt

  228 $@42: %empty

  229 layer_stmt: '+' K_RECT $@42 T_STRING mask pt pt

  230 $@43: %empty

  231 $@44: %empty

  232 layer_stmt: '+' K_POLYGON $@43 T_STRING mask $@44 firstPt nextPt nextPt otherPts

  233 $@45: %empty

  234 layer_stmt: '+' K_PATTERNNAME $@45 T_STRING

  235 $@46: %empty

  236 $@47: %empty

  237 layer_stmt: '+' K_VIARULE $@46 T_STRING '+' K_CUTSIZE NUMBER NUMBER '+' K_LAYERS $@47 T_STRING T_STRING T_STRING '+' K_CUTSPACING NUMBER NUMBER '+' K_ENCLOSURE NUMBER NUMBER NUMBER NUMBER
  238           | layer_viarule_opts
  239           | extension_stmt

  240 layer_viarule_opts: '+' K_ROWCOL NUMBER NUMBER
  241                   | '+' K_ORIGIN NUMBER NUMBER
  242                   | '+' K_OFFSET NUMBER NUMBER NUMBER NUMBER

  243 $@48: %empty

  244 layer_viarule_opts: '+' K_PATTERN $@48 T_STRING

  245 firstPt: pt

  246 nextPt: pt

  247 otherPts: %empty
  248         | otherPts nextPt

  249 pt: '(' NUMBER NUMBER ')'
  250   | '(' '*' NUMBER ')'
  251   | '(' NUMBER '*' ')'
  252   | '(' '*' '*' ')'

  253 mask: %empty
  254     | '+' K_MASK NUMBER

  255 via_end: K_END K_VIAS

  256 regions_section: regions_start regions_stmts K_END K_REGIONS

  257 regions_start: K_REGIONS NUMBER ';'

  258 regions_stmts: %empty
  259              | regions_stmts regions_stmt

  260 $@49: %empty

  261 $@50: %empty

  262 regions_stmt: '-' $@49 T_STRING $@50 rect_list region_options ';'

  263 rect_list: pt pt
  264          | rect_list pt pt

  265 region_options: %empty
  266               | region_options region_option

  267 $@51: %empty

  268 region_option: '+' K_PROPERTY $@51 region_prop_list
  269              | '+' K_TYPE region_type

  270 region_prop_list: %empty
  271                 | region_prop_list region_prop

  272 region_prop: T_STRING NUMBER
  273            | T_STRING QSTRING
  274            | T_STRING T_STRING

  275 region_type: K_FENCE
  276            | K_GUIDE

  277 comps_maskShift_section: K_COMPSMASKSHIFT layer_statement ';'

  278 comps_section: start_comps comps_rule end_comps

  279 start_comps: K_COMPS NUMBER ';'

  280 layer_statement: %empty
  281                | layer_statement maskLayer

  282 maskLayer: T_STRING

  283 comps_rule: %empty
  284           | comps_rule comp

  285 comp: comp_start comp_options ';'

  286 comp_start: comp_id_and_name comp_net_list

  287 $@52: %empty

  288 comp_id_and_name: '-' $@52 T_STRING T_STRING

  289 comp_net_list: %empty
  290              | comp_net_list '*'
  291              | comp_net_list T_STRING

  292 comp_options: %empty
  293             | comp_options comp_option

  294 comp_option: comp_generate
  295            | comp_source
  296            | comp_type
  297            | weight
  298            | maskShift
  299            | comp_foreign
  300            | comp_region
  301            | comp_eeq
  302            | comp_halo
  303            | comp_routehalo
  304            | comp_property
  305            | comp_extension_stmt

  306 comp_extension_stmt: extension_stmt

  307 $@53: %empty

  308 comp_eeq: '+' K_EEQMASTER $@53 T_STRING

  309 $@54: %empty

  310 comp_generate: '+' K_COMP_GEN $@54 T_STRING opt_pattern

  311 opt_pattern: %empty
  312            | T_STRING

  313 comp_source: '+' K_SOURCE source_type

  314 source_type: K_NETLIST
  315            | K_DIST
  316            | K_USER
  317            | K_TIMING

  318 comp_region: comp_region_start comp_pnt_list
  319            | comp_region_start T_STRING

  320 comp_pnt_list: pt pt
  321              | comp_pnt_list pt pt

  322 $@55: %empty

  323 comp_halo: '+' K_HALO $@55 halo_soft NUMBER NUMBER NUMBER NUMBER

  324 halo_soft: %empty
  325          | K_SOFT

  326 $@56: %empty

  327 comp_routehalo: '+' K_ROUTEHALO NUMBER $@56 T_STRING T_STRING

  328 $@57: %empty

  329 comp_property: '+' K_PROPERTY $@57 comp_prop_list

  330 comp_prop_list: comp_prop
  331               | comp_prop_list comp_prop

  332 comp_prop: T_STRING NUMBER
  333          | T_STRING QSTRING
  334          | T_STRING T_STRING

  335 comp_region_start: '+' K_REGION

  336 $@58: %empty

  337 comp_foreign: '+' K_FOREIGN $@58 T_STRING opt_paren orient

  338 opt_paren: pt
  339          | NUMBER NUMBER

  340 comp_type: placement_status pt orient
  341          | '+' K_UNPLACED
  342          | '+' K_UNPLACED pt orient

  343 $@59: %empty

  344 maskShift: '+' K_MASKSHIFT $@59 T_STRING

  345 placement_status: '+' K_FIXED
  346                 | '+' K_COVER
  347                 | '+' K_PLACED

  348 weight: '+' K_WEIGHT NUMBER

  349 end_comps: K_END K_COMPS

  350 nets_section: start_nets net_rules end_nets

  351 start_nets: K_NETS NUMBER ';'

  352 net_rules: %empty
  353          | net_rules one_net

  354 one_net: net_and_connections net_options ';'

  355 net_and_connections: net_start

  356 $@60: %empty

  357 net_start: '-' $@60 net_name

  358 $@61: %empty

  359 net_name: T_STRING $@61 net_connections

  360 $@62: %empty

  361 net_name: K_MUSTJOIN '(' T_STRING $@62 T_STRING ')'

  362 net_connections: %empty
  363                | net_connections net_connection

  364 $@63: %empty

  365 net_connection: '(' T_STRING $@63 T_STRING conn_opt ')'

  366 $@64: %empty

  367 net_connection: '(' '*' $@64 T_STRING conn_opt ')'

  368 $@65: %empty

  369 net_connection: '(' K_PIN $@65 T_STRING conn_opt ')'

  370 conn_opt: %empty
  371         | extension_stmt
  372         | '+' K_SYNTHESIZED

  373 net_options: %empty
  374            | net_options net_option

  375 $@66: %empty

  376 net_option: '+' net_type $@66 paths
  377           | '+' K_SOURCE netsource_type
  378           | '+' K_FIXEDBUMP

  379 $@67: %empty

  380 net_option: '+' K_FREQUENCY $@67 NUMBER

  381 $@68: %empty

  382 net_option: '+' K_ORIGINAL $@68 T_STRING
  383           | '+' K_PATTERN pattern_type
  384           | '+' K_WEIGHT NUMBER
  385           | '+' K_XTALK NUMBER
  386           | '+' K_ESTCAP NUMBER
  387           | '+' K_USE use_type
  388           | '+' K_STYLE NUMBER

  389 $@69: %empty

  390 net_option: '+' K_NONDEFAULTRULE $@69 T_STRING
  391           | vpin_stmt

  392 $@70: %empty

  393 net_option: '+' K_SHIELDNET $@70 T_STRING

  394 $@71: %empty

  395 $@72: %empty

  396 net_option: '+' K_NOSHIELD $@71 $@72 paths

  397 $@73: %empty

  398 $@74: %empty

  399 $@75: %empty

  400 net_option: '+' K_SUBNET $@73 T_STRING $@74 comp_names $@75 subnet_options

  401 $@76: %empty

  402 net_option: '+' K_PROPERTY $@76 net_prop_list
  403           | extension_stmt

  404 net_prop_list: net_prop
  405              | net_prop_list net_prop

  406 net_prop: T_STRING NUMBER
  407         | T_STRING QSTRING
  408         | T_STRING T_STRING

  409 netsource_type: K_NETLIST
  410               | K_DIST
  411               | K_USER
  412               | K_TIMING
  413               | K_TEST

  414 $@77: %empty

  415 vpin_stmt: vpin_begin vpin_layer_opt pt pt $@77 vpin_options

  416 $@78: %empty

  417 vpin_begin: '+' K_VPIN $@78 T_STRING

  418 vpin_layer_opt: %empty

  419 $@79: %empty

  420 vpin_layer_opt: K_LAYER $@79 T_STRING

  421 vpin_options: %empty
  422             | vpin_status pt orient

  423 vpin_status: K_PLACED
  424            | K_FIXED
  425            | K_COVER

  426 net_type: K_FIXED
  427         | K_COVER
  428         | K_ROUTED

  429 paths: path
  430      | paths new_path

  431 $@80: %empty

  432 new_path: K_NEW $@80 path

  433 $@81: %empty

  434 $@82: %empty

  435 path: T_STRING $@81 opt_taper_style_s path_pt $@82 path_item_list

  436 virtual_statement: K_VIRTUAL virtual_pt

  437 rect_statement: K_RECT rect_pts

  438 path_item_list: %empty
  439               | path_item_list path_item

  440 path_item: T_STRING
  441          | K_MASK NUMBER T_STRING
  442          | T_STRING orient
  443          | K_MASK NUMBER T_STRING orient
  444          | K_MASK NUMBER T_STRING K_DO NUMBER K_BY NUMBER K_STEP NUMBER NUMBER
  445          | T_STRING K_DO NUMBER K_BY NUMBER K_STEP NUMBER NUMBER
  446          | T_STRING orient K_DO NUMBER K_BY NUMBER K_STEP NUMBER NUMBER
  447          | K_MASK NUMBER T_STRING orient K_DO NUMBER K_BY NUMBER K_STEP NUMBER NUMBER
  448          | virtual_statement
  449          | rect_statement

  450 $@83: %empty

  451 path_item: K_MASK NUMBER K_RECT $@83 '(' NUMBER NUMBER NUMBER NUMBER ')'

  452 $@84: %empty

  453 path_item: K_MASK NUMBER $@84 path_pt
  454          | path_pt

  455 path_pt: '(' NUMBER NUMBER ')'
  456        | '(' '*' NUMBER ')'
  457        | '(' NUMBER '*' ')'
  458        | '(' '*' '*' ')'
  459        | '(' NUMBER NUMBER NUMBER ')'
  460        | '(' '*' NUMBER NUMBER ')'
  461        | '(' NUMBER '*' NUMBER ')'
  462        | '(' '*' '*' NUMBER ')'

  463 virtual_pt: '(' NUMBER NUMBER ')'
  464           | '(' '*' NUMBER ')'
  465           | '(' NUMBER '*' ')'
  466           | '(' '*' '*' ')'

  467 rect_pts: '(' NUMBER NUMBER NUMBER NUMBER ')'

  468 opt_taper_style_s: %empty
  469                  | opt_taper_style_s opt_taper_style

  470 opt_taper_style: opt_style
  471                | opt_taper

  472 opt_taper: K_TAPER

  473 $@85: %empty

  474 opt_taper: K_TAPERRULE $@85 T_STRING

  475 opt_style: K_STYLE NUMBER

  476 opt_spaths: %empty
  477           | opt_spaths opt_shape_style

  478 opt_shape_style: '+' K_SHAPE shape_type
  479                | '+' K_STYLE NUMBER

  480 end_nets: K_END K_NETS

  481 shape_type: K_RING
  482           | K_STRIPE
  483           | K_FOLLOWPIN
  484           | K_IOWIRE
  485           | K_COREWIRE
  486           | K_BLOCKWIRE
  487           | K_FILLWIRE
  488           | K_FILLWIREOPC
  489           | K_DRCFILL
  490           | K_BLOCKAGEWIRE
  491           | K_PADRING
  492           | K_BLOCKRING

  493 snets_section: start_snets snet_rules end_snets

  494 snet_rules: %empty
  495           | snet_rules snet_rule

  496 snet_rule: net_and_connections snet_options ';'

  497 snet_options: %empty
  498             | snet_options snet_option

  499 snet_option: snet_width
  500            | snet_voltage
  501            | snet_spacing
  502            | snet_other_option

  503 snet_other_option: '+' net_type

  504 $@86: %empty

  505 snet_other_option: '+' net_type $@86 spaths

  506 $@87: %empty

  507 $@88: %empty

  508 snet_other_option: '+' K_SHIELD $@87 T_STRING $@88 shield_layer
  509                  | '+' K_SHAPE shape_type
  510                  | '+' K_MASK NUMBER

  511 $@89: %empty

  512 $@90: %empty

  513 snet_other_option: '+' K_POLYGON $@89 T_STRING $@90 firstPt nextPt nextPt otherPts

  514 $@91: %empty

  515 snet_other_option: '+' K_RECT $@91 T_STRING pt pt

  516 $@92: %empty

  517 $@93: %empty

  518 snet_other_option: '+' K_VIA $@92 T_STRING orient_pt $@93 firstPt otherPts
  519                  | '+' K_SOURCE source_type
  520                  | '+' K_FIXEDBUMP
  521                  | '+' K_FREQUENCY NUMBER

  522 $@94: %empty

  523 snet_other_option: '+' K_ORIGINAL $@94 T_STRING
  524                  | '+' K_PATTERN pattern_type
  525                  | '+' K_WEIGHT NUMBER
  526                  | '+' K_ESTCAP NUMBER
  527                  | '+' K_USE use_type
  528                  | '+' K_STYLE NUMBER

  529 $@95: %empty

  530 snet_other_option: '+' K_PROPERTY $@95 snet_prop_list
  531                  | extension_stmt

  532 orient_pt: %empty
  533          | K_N
  534          | K_W
  535          | K_S
  536          | K_E
  537          | K_FN
  538          | K_FW
  539          | K_FS
  540          | K_FE

  541 shield_layer: %empty

  542 $@96: %empty

  543 shield_layer: $@96 spaths

  544 $@97: %empty

  545 snet_width: '+' K_WIDTH $@97 T_STRING NUMBER

  546 $@98: %empty

  547 snet_voltage: '+' K_VOLTAGE $@98 T_STRING

  548 $@99: %empty

  549 $@100: %empty

  550 snet_spacing: '+' K_SPACING $@99 T_STRING NUMBER $@100 opt_snet_range

  551 snet_prop_list: snet_prop
  552               | snet_prop_list snet_prop

  553 snet_prop: T_STRING NUMBER
  554          | T_STRING QSTRING
  555          | T_STRING T_STRING

  556 opt_snet_range: %empty
  557               | K_RANGE NUMBER NUMBER

  558 opt_range: %empty
  559          | K_RANGE NUMBER NUMBER

  560 pattern_type: K_BALANCED
  561             | K_STEINER
  562             | K_TRUNK
  563             | K_WIREDLOGIC

  564 spaths: spath
  565       | spaths snew_path

  566 $@101: %empty

  567 snew_path: K_NEW $@101 spath

  568 $@102: %empty

  569 $@103: %empty

  570 spath: T_STRING $@102 width opt_spaths path_pt $@103 path_item_list

  571 width: NUMBER

  572 start_snets: K_SNETS NUMBER ';'

  573 end_snets: K_END K_SNETS

  574 groups_section: groups_start group_rules groups_end

  575 groups_start: K_GROUPS NUMBER ';'

  576 group_rules: %empty
  577            | group_rules group_rule

  578 group_rule: start_group group_members group_options ';'

  579 $@104: %empty

  580 start_group: '-' $@104 T_STRING

  581 group_members: %empty
  582              | group_members group_member

  583 group_member: T_STRING

  584 group_options: %empty
  585              | group_options group_option

  586 group_option: '+' K_SOFT group_soft_options

  587 $@105: %empty

  588 group_option: '+' K_PROPERTY $@105 group_prop_list

  589 $@106: %empty

  590 group_option: '+' K_REGION $@106 group_region
  591             | extension_stmt

  592 group_region: pt pt
  593             | T_STRING

  594 group_prop_list: %empty
  595                | group_prop_list group_prop

  596 group_prop: T_STRING NUMBER
  597           | T_STRING QSTRING
  598           | T_STRING T_STRING

  599 group_soft_options: %empty
  600                   | group_soft_options group_soft_option

  601 group_soft_option: K_MAXX NUMBER
  602                  | K_MAXY NUMBER
  603                  | K_MAXHALFPERIMETER NUMBER

  604 groups_end: K_END K_GROUPS

  605 assertions_section: assertions_start constraint_rules assertions_end

  606 constraint_section: constraints_start constraint_rules constraints_end

  607 assertions_start: K_ASSERTIONS NUMBER ';'

  608 constraints_start: K_CONSTRAINTS NUMBER ';'

  609 constraint_rules: %empty
  610                 | constraint_rules constraint_rule

  611 constraint_rule: operand_rule
  612                | wiredlogic_rule

  613 operand_rule: '-' operand delay_specs ';'

  614 $@107: %empty

  615 operand: K_NET $@107 T_STRING

  616 $@108: %empty

  617 operand: K_PATH $@108 T_STRING T_STRING T_STRING T_STRING
  618        | K_SUM '(' operand_list ')'
  619        | K_DIFF '(' operand_list ')'

  620 operand_list: operand
  621             | operand_list operand
  622             | operand_list ',' operand

  623 $@109: %empty

  624 wiredlogic_rule: '-' K_WIREDLOGIC $@109 T_STRING opt_plus K_MAXDIST NUMBER ';'

  625 opt_plus: %empty
  626         | '+'

  627 delay_specs: %empty
  628            | delay_specs delay_spec

  629 delay_spec: '+' K_RISEMIN NUMBER
  630           | '+' K_RISEMAX NUMBER
  631           | '+' K_FALLMIN NUMBER
  632           | '+' K_FALLMAX NUMBER

  633 constraints_end: K_END K_CONSTRAINTS

  634 assertions_end: K_END K_ASSERTIONS

  635 scanchains_section: scanchain_start scanchain_rules scanchain_end

  636 scanchain_start: K_SCANCHAINS NUMBER ';'

  637 scanchain_rules: %empty
  638                | scanchain_rules scan_rule

  639 scan_rule: start_scan scan_members ';'

  640 $@110: %empty

  641 start_scan: '-' $@110 T_STRING

  642 scan_members: %empty
  643             | scan_members scan_member

  644 opt_pin: %empty
  645        | T_STRING

  646 $@111: %empty

  647 scan_member: '+' K_START $@111 T_STRING opt_pin

  648 $@112: %empty

  649 scan_member: '+' K_FLOATING $@112 floating_inst_list

  650 $@113: %empty

  651 scan_member: '+' K_ORDERED $@113 ordered_inst_list

  652 $@114: %empty

  653 scan_member: '+' K_STOP $@114 T_STRING opt_pin

  654 $@115: %empty

  655 scan_member: '+' K_COMMONSCANPINS $@115 opt_common_pins

  656 $@116: %empty

  657 scan_member: '+' K_PARTITION $@116 T_STRING partition_maxbits
  658            | extension_stmt

  659 opt_common_pins: %empty
  660                | '(' T_STRING T_STRING ')'
  661                | '(' T_STRING T_STRING ')' '(' T_STRING T_STRING ')'

  662 floating_inst_list: %empty
  663                   | floating_inst_list one_floating_inst

  664 $@117: %empty

  665 one_floating_inst: T_STRING $@117 floating_pins

  666 floating_pins: %empty
  667              | '(' T_STRING T_STRING ')'
  668              | '(' T_STRING T_STRING ')' '(' T_STRING T_STRING ')'
  669              | '(' T_STRING T_STRING ')' '(' T_STRING T_STRING ')' '(' T_STRING T_STRING ')'

  670 ordered_inst_list: %empty
  671                  | ordered_inst_list one_ordered_inst

  672 $@118: %empty

  673 one_ordered_inst: T_STRING $@118 ordered_pins

  674 ordered_pins: %empty
  675             | '(' T_STRING T_STRING ')'
  676             | '(' T_STRING T_STRING ')' '(' T_STRING T_STRING ')'
  677             | '(' T_STRING T_STRING ')' '(' T_STRING T_STRING ')' '(' T_STRING T_STRING ')'

  678 partition_maxbits: %empty
  679                  | K_MAXBITS NUMBER

  680 scanchain_end: K_END K_SCANCHAINS

  681 iotiming_section: iotiming_start iotiming_rules iotiming_end

  682 iotiming_start: K_IOTIMINGS NUMBER ';'

  683 iotiming_rules: %empty
  684               | iotiming_rules iotiming_rule

  685 iotiming_rule: start_iotiming iotiming_members ';'

  686 $@119: %empty

  687 start_iotiming: '-' '(' $@119 T_STRING T_STRING ')'

  688 iotiming_members: %empty
  689                 | iotiming_members iotiming_member

  690 iotiming_member: '+' risefall K_VARIABLE NUMBER NUMBER
  691                | '+' risefall K_SLEWRATE NUMBER NUMBER
  692                | '+' K_CAPACITANCE NUMBER

  693 $@120: %empty

  694 $@121: %empty

  695 iotiming_member: '+' K_DRIVECELL $@120 T_STRING $@121 iotiming_drivecell_opt
  696                | extension_stmt

  697 $@122: %empty

  698 $@123: %empty

  699 iotiming_drivecell_opt: iotiming_frompin K_TOPIN $@122 T_STRING $@123 iotiming_parallel

  700 iotiming_frompin: %empty

  701 $@124: %empty

  702 iotiming_frompin: K_FROMPIN $@124 T_STRING

  703 iotiming_parallel: %empty
  704                  | K_PARALLEL NUMBER

  705 risefall: K_RISE
  706         | K_FALL

  707 iotiming_end: K_END K_IOTIMINGS

  708 floorplan_contraints_section: fp_start fp_stmts K_END K_FPC

  709 fp_start: K_FPC NUMBER ';'

  710 fp_stmts: %empty
  711         | fp_stmts fp_stmt

  712 $@125: %empty

  713 $@126: %empty

  714 fp_stmt: '-' $@125 T_STRING h_or_v $@126 constraint_type constrain_what_list ';'

  715 h_or_v: K_HORIZONTAL
  716       | K_VERTICAL

  717 constraint_type: K_ALIGN
  718                | K_MAX NUMBER
  719                | K_MIN NUMBER
  720                | K_EQUAL NUMBER

  721 constrain_what_list: %empty
  722                    | constrain_what_list constrain_what

  723 $@127: %empty

  724 constrain_what: '+' K_BOTTOMLEFT $@127 row_or_comp_list

  725 $@128: %empty

  726 constrain_what: '+' K_TOPRIGHT $@128 row_or_comp_list

  727 row_or_comp_list: %empty
  728                 | row_or_comp_list row_or_comp

  729 $@129: %empty

  730 row_or_comp: '(' K_ROWS $@129 T_STRING ')'

  731 $@130: %empty

  732 row_or_comp: '(' K_COMPS $@130 T_STRING ')'

  733 timingdisables_section: timingdisables_start timingdisables_rules timingdisables_end

  734 timingdisables_start: K_TIMINGDISABLES NUMBER ';'

  735 timingdisables_rules: %empty
  736                     | timingdisables_rules timingdisables_rule

  737 $@131: %empty

  738 $@132: %empty

  739 timingdisables_rule: '-' K_FROMPIN $@131 T_STRING T_STRING K_TOPIN $@132 T_STRING T_STRING ';'

  740 $@133: %empty

  741 timingdisables_rule: '-' K_THRUPIN $@133 T_STRING T_STRING ';'

  742 $@134: %empty

  743 timingdisables_rule: '-' K_MACRO $@134 T_STRING td_macro_option ';'
  744                    | '-' K_REENTRANTPATHS ';'

  745 $@135: %empty

  746 $@136: %empty

  747 td_macro_option: K_FROMPIN $@135 T_STRING K_TOPIN $@136 T_STRING

  748 $@137: %empty

  749 td_macro_option: K_THRUPIN $@137 T_STRING

  750 timingdisables_end: K_END K_TIMINGDISABLES

  751 partitions_section: partitions_start partition_rules partitions_end

  752 partitions_start: K_PARTITIONS NUMBER ';'

  753 partition_rules: %empty
  754                | partition_rules partition_rule

  755 partition_rule: start_partition partition_members ';'

  756 $@138: %empty

  757 start_partition: '-' $@138 T_STRING turnoff

  758 turnoff: %empty
  759        | K_TURNOFF turnoff_setup turnoff_hold

  760 turnoff_setup: %empty
  761              | K_SETUPRISE
  762              | K_SETUPFALL

  763 turnoff_hold: %empty
  764             | K_HOLDRISE
  765             | K_HOLDFALL

  766 partition_members: %empty
  767                  | partition_members partition_member

  768 $@139: %empty

  769 partition_member: '+' K_FROMCLOCKPIN $@139 T_STRING T_STRING risefall minmaxpins

  770 $@140: %empty

  771 partition_member: '+' K_FROMCOMPPIN $@140 T_STRING T_STRING risefallminmax2_list

  772 $@141: %empty

  773 partition_member: '+' K_FROMIOPIN $@141 T_STRING risefallminmax1_list

  774 $@142: %empty

  775 partition_member: '+' K_TOCLOCKPIN $@142 T_STRING T_STRING risefall minmaxpins

  776 $@143: %empty

  777 partition_member: '+' K_TOCOMPPIN $@143 T_STRING T_STRING risefallminmax2_list

  778 $@144: %empty

  779 partition_member: '+' K_TOIOPIN $@144 T_STRING risefallminmax1_list
  780                 | extension_stmt

  781 $@145: %empty

  782 minmaxpins: min_or_max_list K_PINS $@145 pin_list

  783 min_or_max_list: %empty
  784                | min_or_max_list min_or_max_member

  785 min_or_max_member: K_MIN NUMBER NUMBER
  786                  | K_MAX NUMBER NUMBER

  787 pin_list: %empty
  788         | pin_list T_STRING

  789 risefallminmax1_list: %empty
  790                     | risefallminmax1_list risefallminmax1

  791 risefallminmax1: K_RISEMIN NUMBER
  792                | K_FALLMIN NUMBER
  793                | K_RISEMAX NUMBER
  794                | K_FALLMAX NUMBER

  795 risefallminmax2_list: risefallminmax2
  796                     | risefallminmax2_list risefallminmax2

  797 risefallminmax2: K_RISEMIN NUMBER NUMBER
  798                | K_FALLMIN NUMBER NUMBER
  799                | K_RISEMAX NUMBER NUMBER
  800                | K_FALLMAX NUMBER NUMBER

  801 partitions_end: K_END K_PARTITIONS

  802 comp_names: %empty
  803           | comp_names comp_name

  804 $@146: %empty

  805 comp_name: '(' $@146 T_STRING T_STRING subnet_opt_syn ')'

  806 subnet_opt_syn: %empty
  807               | '+' K_SYNTHESIZED

  808 subnet_options: %empty
  809               | subnet_options subnet_option

  810 $@147: %empty

  811 subnet_option: subnet_type $@147 paths

  812 $@148: %empty

  813 subnet_option: K_NONDEFAULTRULE $@148 T_STRING

  814 subnet_type: K_FIXED
  815            | K_COVER
  816            | K_ROUTED
  817            | K_NOSHIELD

  818 pin_props_section: begin_pin_props pin_prop_list end_pin_props

  819 begin_pin_props: K_PINPROPERTIES NUMBER opt_semi

  820 opt_semi: %empty
  821         | ';'

  822 end_pin_props: K_END K_PINPROPERTIES

  823 pin_prop_list: %empty
  824              | pin_prop_list pin_prop_terminal

  825 $@149: %empty

  826 $@150: %empty

  827 pin_prop_terminal: '-' $@149 T_STRING T_STRING $@150 pin_prop_options ';'

  828 pin_prop_options: %empty
  829                 | pin_prop_options pin_prop

  830 $@151: %empty

  831 pin_prop: '+' K_PROPERTY $@151 pin_prop_name_value_list

  832 pin_prop_name_value_list: %empty
  833                         | pin_prop_name_value_list pin_prop_name_value

  834 pin_prop_name_value: T_STRING NUMBER
  835                    | T_STRING QSTRING
  836                    | T_STRING T_STRING

  837 blockage_section: blockage_start blockage_defs blockage_end

  838 blockage_start: K_BLOCKAGES NUMBER ';'

  839 blockage_end: K_END K_BLOCKAGES

  840 blockage_defs: %empty
  841              | blockage_defs blockage_def

  842 blockage_def: blockage_rule rectPoly_blockage rectPoly_blockage_rules ';'

  843 $@152: %empty

  844 $@153: %empty

  845 blockage_rule: '-' K_LAYER $@152 T_STRING $@153 layer_blockage_rules

  846 $@154: %empty

  847 blockage_rule: '-' K_PLACEMENT $@154 placement_comp_rules

  848 layer_blockage_rules: %empty
  849                     | layer_blockage_rules layer_blockage_rule

  850 layer_blockage_rule: '+' K_SPACING NUMBER
  851                    | '+' K_DESIGNRULEWIDTH NUMBER
  852                    | mask_blockage_rule
  853                    | comp_blockage_rule

  854 mask_blockage_rule: '+' K_MASK NUMBER

  855 $@155: %empty

  856 comp_blockage_rule: '+' K_COMPONENT $@155 T_STRING
  857                   | '+' K_SLOTS
  858                   | '+' K_FILLS
  859                   | '+' K_PUSHDOWN
  860                   | '+' K_EXCEPTPGNET

  861 placement_comp_rules: %empty
  862                     | placement_comp_rules placement_comp_rule

  863 $@156: %empty

  864 placement_comp_rule: '+' K_COMPONENT $@156 T_STRING
  865                    | '+' K_PUSHDOWN
  866                    | '+' K_SOFT
  867                    | '+' K_PARTIAL NUMBER

  868 rectPoly_blockage_rules: %empty
  869                        | rectPoly_blockage_rules rectPoly_blockage

  870 rectPoly_blockage: K_RECT pt pt

  871 $@157: %empty

  872 rectPoly_blockage: K_POLYGON $@157 firstPt nextPt nextPt otherPts

  873 slot_section: slot_start slot_defs slot_end

  874 slot_start: K_SLOTS NUMBER ';'

  875 slot_end: K_END K_SLOTS

  876 slot_defs: %empty
  877          | slot_defs slot_def

  878 slot_def: slot_rule geom_slot_rules ';'

  879 $@158: %empty

  880 $@159: %empty

  881 slot_rule: '-' K_LAYER $@158 T_STRING $@159 geom_slot

  882 geom_slot_rules: %empty
  883                | geom_slot_rules geom_slot

  884 geom_slot: K_RECT pt pt

  885 $@160: %empty

  886 geom_slot: K_POLYGON $@160 firstPt nextPt nextPt otherPts

  887 fill_section: fill_start fill_defs fill_end

  888 fill_start: K_FILLS NUMBER ';'

  889 fill_end: K_END K_FILLS

  890 fill_defs: %empty
  891          | fill_defs fill_def

  892 fill_def: fill_rule geom_fill_rules ';'

  893 $@161: %empty

  894 $@162: %empty

  895 fill_def: '-' K_VIA $@161 T_STRING $@162 fill_via_mask_opc_opt fill_via_pt ';'

  896 $@163: %empty

  897 $@164: %empty

  898 fill_rule: '-' K_LAYER $@163 T_STRING $@164 fill_layer_mask_opc_opt geom_fill

  899 geom_fill_rules: %empty
  900                | geom_fill_rules geom_fill

  901 geom_fill: K_RECT pt pt

  902 $@165: %empty

  903 geom_fill: K_POLYGON $@165 firstPt nextPt nextPt otherPts

  904 fill_layer_mask_opc_opt: %empty
  905                        | fill_layer_mask_opc_opt opt_mask_opc_l

  906 opt_mask_opc_l: fill_layer_opc
  907               | fill_mask

  908 fill_layer_opc: '+' K_OPC

  909 fill_via_pt: firstPt otherPts

  910 fill_via_mask_opc_opt: %empty
  911                      | fill_via_mask_opc_opt opt_mask_opc

  912 opt_mask_opc: fill_via_opc
  913             | fill_viaMask

  914 fill_via_opc: '+' K_OPC

  915 fill_mask: '+' K_MASK NUMBER

  916 fill_viaMask: '+' K_MASK NUMBER

  917 nondefaultrule_section: nondefault_start nondefault_def nondefault_defs nondefault_end

  918 nondefault_start: K_NONDEFAULTRULES NUMBER ';'

  919 nondefault_end: K_END K_NONDEFAULTRULES

  920 nondefault_defs: %empty
  921                | nondefault_defs nondefault_def

  922 $@166: %empty

  923 $@167: %empty

  924 nondefault_def: '-' $@166 T_STRING $@167 nondefault_options ';'

  925 nondefault_options: %empty
  926                   | nondefault_options nondefault_option

  927 nondefault_option: '+' K_HARDSPACING

  928 $@168: %empty

  929 $@169: %empty

  930 nondefault_option: '+' K_LAYER $@168 T_STRING K_WIDTH NUMBER $@169 nondefault_layer_options

  931 $@170: %empty

  932 nondefault_option: '+' K_VIA $@170 T_STRING

  933 $@171: %empty

  934 nondefault_option: '+' K_VIARULE $@171 T_STRING

  935 $@172: %empty

  936 nondefault_option: '+' K_MINCUTS $@172 T_STRING NUMBER
  937                  | nondefault_prop_opt

  938 nondefault_layer_options: %empty
  939                         | nondefault_layer_options nondefault_layer_option

  940 nondefault_layer_option: K_DIAGWIDTH NUMBER
  941                        | K_SPACING NUMBER
  942                        | K_WIREEXT NUMBER

  943 $@173: %empty

  944 nondefault_prop_opt: '+' K_PROPERTY $@173 nondefault_prop_list

  945 nondefault_prop_list: %empty
  946                     | nondefault_prop_list nondefault_prop

  947 nondefault_prop: T_STRING NUMBER
  948                | T_STRING QSTRING
  949                | T_STRING T_STRING

  950 styles_section: styles_start styles_rules styles_end

  951 styles_start: K_STYLES NUMBER ';'

  952 styles_end: K_END K_STYLES

  953 styles_rules: %empty
  954             | styles_rules styles_rule

  955 $@174: %empty

  956 styles_rule: '-' K_STYLE NUMBER $@174 firstPt nextPt otherPts ';'


Terminals, with rules where they appear

$end (0) 0
'(' (40) 142 249 250 251 252 361 365 367 369 451 455 456 457 458 459
    460 461 462 463 464 465 466 467 618 619 660 661 667 668 669 675
    676 677 687 730 732 805
')' (41) 142 249 250 251 252 361 365 367 369 451 455 456 457 458 459
    460 461 462 463 464 465 466 467 618 619 660 661 667 668 669 675
    676 677 687 730 732 805
'*' (42) 250 251 252 290 367 456 457 458 460 461 462 464 465 466
'+' (43) 122 125 127 128 130 132 133 134 137 140 142 144 145 146 147
    149 151 152 154 155 194 218 229 232 234 237 240 241 242 244 254
    268 269 308 310 313 323 327 329 335 337 341 342 344 345 346 347
    348 372 376 377 378 380 382 383 384 385 386 387 388 390 393 396
    400 402 417 478 479 503 505 508 509 510 513 515 518 519 520 521
    523 524 525 526 527 528 530 545 547 550 586 588 590 626 629 630
    631 632 647 649 651 653 655 657 690 691 692 695 724 726 769 771
    773 775 777 779 807 831 850 851 854 856 857 858 859 860 864 865
    866 867 908 914 915 916 927 930 932 934 936 944
',' (44) 622
'-' (45) 122 225 262 288 357 580 613 624 641 687 714 739 741 743 744
    757 827 845 847 881 895 898 924 956
';' (59) 4 6 7 51 54 56 58 65 67 69 71 73 75 77 79 81 82 92 93 94 96
    98 108 113 116 122 186 201 216 220 225 257 262 277 279 285 351
    354 496 572 575 578 607 608 613 624 636 639 682 685 709 714 734
    739 741 743 744 752 755 821 827 838 842 874 878 888 892 895 918
    924 951 956
error (256) 10 82
QSTRING (258) 88 93 94 128 198 273 333 407 554 597 835 948
T_STRING (259) 4 51 54 56 58 65 67 69 71 73 75 77 79 81 89 96 98 122
    127 130 132 137 140 142 149 151 154 182 186 197 198 199 215 225
    229 232 234 237 244 262 272 273 274 282 288 291 308 310 312 319
    327 332 333 334 337 344 359 361 365 367 369 382 390 393 400 406
    407 408 417 420 435 440 441 442 443 444 445 446 447 474 508 513
    515 518 523 545 547 550 553 554 555 570 580 583 593 596 597 598
    615 617 624 641 645 647 653 657 660 661 665 667 668 669 673 675
    676 677 687 695 699 702 714 730 732 739 741 743 747 749 757 769
    771 773 775 777 779 788 805 813 827 834 835 836 845 856 864 881
    895 898 924 930 932 934 936 947 948 949
SITE_PATTERN (260)
NUMBER (261) 91 92 96 98 110 113 116 142 144 145 146 147 149 151 152
    154 157 159 161 163 164 166 167 186 188 190 197 201 207 216 220
    237 240 241 242 249 250 251 254 257 272 279 323 327 332 339 348
    351 380 384 385 386 388 406 441 443 444 445 446 447 451 453 455
    456 457 459 460 461 462 463 464 465 467 475 479 510 521 525 526
    528 545 550 553 557 559 571 572 575 596 601 602 603 607 608 624
    629 630 631 632 636 679 682 690 691 692 704 709 718 719 720 734
    752 785 786 791 792 793 794 797 798 799 800 819 834 838 850 851
    854 867 874 888 915 916 918 930 936 940 941 942 947 951 956
K_HISTORY (262) 59
K_NAMESCASESENSITIVE (263) 6 7
K_DESIGN (264) 51 52 65
K_VIAS (265) 220 255
K_TECH (266) 54
K_UNITS (267) 92
K_ARRAY (268) 56
K_FLOORPLAN (269) 58
K_SITE (270)
K_CANPLACE (271) 96
K_CANNOTOCCUPY (272) 98
K_DIEAREA (273) 108
K_PINS (274) 116 183 782
K_DEFAULTCAP (275) 110 114
K_MINPINS (276) 113
K_WIRECAP (277) 113
K_TRACKS (278) 202
K_GCELLGRID (279) 216
K_DO (280) 96 98 188 201 216 444 445 446 447
K_BY (281) 96 98 188 444 445 446 447
K_STEP (282) 96 98 190 201 216 444 445 446 447
K_LAYER (283) 137 149 151 154 182 212 420 845 881 898 930
K_ROW (284) 77 186
K_RECT (285) 229 437 451 515 870 884 901
K_COMPS (286) 279 349 732
K_COMP_GEN (287) 310
K_SOURCE (288) 313 377 519
K_WEIGHT (289) 348 384 525
K_EEQMASTER (290) 308
K_FIXED (291) 345 424 426 814
K_COVER (292) 346 425 427 815
K_UNPLACED (293) 341 342
K_PLACED (294) 347 423
K_FOREIGN (295) 337
K_REGION (296) 71 335 590
K_REGIONS (297) 256 257
K_NETS (298) 351 480
K_START_NET (299)
K_MUSTJOIN (300) 361
K_ORIGINAL (301) 382 523
K_USE (302) 133 387 527
K_STYLE (303) 388 475 479 528 956
K_PATTERN (304) 244 383 524
K_PATTERNNAME (305) 234
K_ESTCAP (306) 386 526
K_ROUTED (307) 428 816
K_NEW (308) 432 567
K_SNETS (309) 572 573
K_SHAPE (310) 478 509
K_WIDTH (311) 545 930
K_VOLTAGE (312) 547
K_SPACING (313) 163 166 550 850 941
K_NONDEFAULTRULE (314) 81 390 813
K_NONDEFAULTRULES (315) 918 919
K_N (316) 99 533
K_S (317) 101 535
K_E (318) 102 536
K_W (319) 100 534
K_FN (320) 103 537
K_FE (321) 106 540
K_FS (322) 105 539
K_FW (323) 104 538
K_GROUPS (324) 575 604
K_GROUP (325) 73
K_SOFT (326) 325 586 866
K_MAXX (327) 601
K_MAXY (328) 602
K_MAXHALFPERIMETER (329) 603
K_CONSTRAINTS (330) 608 633
K_NET (331) 67 122 615
K_PATH (332) 617
K_SUM (333) 618
K_DIFF (334) 619
K_SCANCHAINS (335) 636 680
K_START (336) 647
K_FLOATING (337) 649
K_ORDERED (338) 651
K_STOP (339) 653
K_IN (340)
K_OUT (341)
K_RISEMIN (342) 629 791 797
K_RISEMAX (343) 630 793 799
K_FALLMIN (344) 631 792 798
K_FALLMAX (345) 632 794 800
K_WIREDLOGIC (346) 563 624
K_MAXDIST (347) 624
K_ASSERTIONS (348) 607 634
K_DISTANCE (349) 92
K_MICRONS (350) 92
K_END (351) 52 61 114 183 255 256 349 480 573 604 633 634 680 707 708
    750 801 822 839 875 889 919 952
K_IOTIMINGS (352) 682 707
K_RISE (353) 705
K_FALL (354) 706
K_VARIABLE (355) 690
K_SLEWRATE (356) 691
K_CAPACITANCE (357) 692
K_DRIVECELL (358) 695
K_FROMPIN (359) 702 739 747
K_TOPIN (360) 699 739 747
K_PARALLEL (361) 704
K_TIMINGDISABLES (362) 734 750
K_THRUPIN (363) 741 749
K_MACRO (364) 743
K_PARTITIONS (365) 752 801
K_TURNOFF (366) 759
K_FROMCLOCKPIN (367) 769
K_FROMCOMPPIN (368) 771
K_FROMIOPIN (369) 773
K_TOCLOCKPIN (370) 775
K_TOCOMPPIN (371) 777
K_TOIOPIN (372) 779
K_SETUPRISE (373) 761
K_SETUPFALL (374) 762
K_HOLDRISE (375) 764
K_HOLDFALL (376) 765
K_VPIN (377) 417
K_SUBNET (378) 400
K_XTALK (379) 385
K_PIN (380) 369
K_SYNTHESIZED (381) 372 807
K_DEFINE (382)
K_DEFINES (383)
K_DEFINEB (384)
K_IF (385)
K_THEN (386)
K_ELSE (387)
K_FALSE (388)
K_TRUE (389)
K_EQ (390)
K_NE (391)
K_LE (392)
K_LT (393)
K_GE (394)
K_GT (395)
K_OR (396)
K_AND (397)
K_NOT (398)
K_SPECIAL (399) 125
K_DIRECTION (400) 127
K_RANGE (401) 557 559
K_FPC (402) 708 709
K_HORIZONTAL (403) 715
K_VERTICAL (404) 716
K_ALIGN (405) 717
K_MIN (406) 719 785
K_MAX (407) 718 786
K_EQUAL (408) 720
K_BOTTOMLEFT (409) 724
K_TOPRIGHT (410) 726
K_ROWS (411) 730
K_TAPER (412) 472
K_TAPERRULE (413) 474
K_VERSION (414) 4
K_DIVIDERCHAR (415) 93
K_BUSBITCHARS (416) 94
K_PROPERTYDEFINITIONS (417) 61
K_STRING (418) 87 88
K_REAL (419) 86
K_INTEGER (420) 84
K_PROPERTY (421) 194 268 329 402 530 588 831 944
K_BEGINEXT (422) 217 218
K_ENDEXT (423)
K_NAMEMAPSTRING (424) 89
K_ON (425) 6
K_OFF (426) 7
K_X (427) 203
K_Y (428) 204
K_COMPONENT (429) 75 856 864
K_MASK (430) 157 159 161 207 254 441 443 444 447 451 453 510 854 915
    916
K_MASKSHIFT (431) 344
K_COMPSMASKSHIFT (432) 277
K_SAMEMASK (433) 209
K_PINPROPERTIES (434) 819 822
K_TEST (435) 413
K_COMMONSCANPINS (436) 655
K_SNET (437) 69
K_COMPONENTPIN (438) 79
K_REENTRANTPATHS (439) 744
K_SHIELD (440) 508
K_SHIELDNET (441) 393
K_NOSHIELD (442) 396 817
K_VIRTUAL (443) 436
K_ANTENNAPINPARTIALMETALAREA (444) 144
K_ANTENNAPINPARTIALMETALSIDEAREA (445) 145
K_ANTENNAPINGATEAREA (446) 146
K_ANTENNAPINDIFFAREA (447) 147
K_ANTENNAPINMAXAREACAR (448) 149
K_ANTENNAPINMAXSIDEAREACAR (449) 151
K_ANTENNAPINPARTIALCUTAREA (450) 152
K_ANTENNAPINMAXCUTCAR (451) 154
K_SIGNAL (452) 172
K_POWER (453) 173
K_GROUND (454) 174
K_CLOCK (455) 175
K_TIEOFF (456) 176
K_ANALOG (457) 177
K_SCAN (458) 178
K_RESET (459) 179
K_RING (460) 481
K_STRIPE (461) 482
K_FOLLOWPIN (462) 483
K_IOWIRE (463) 484
K_COREWIRE (464) 485
K_BLOCKWIRE (465) 486
K_FILLWIRE (466) 487
K_BLOCKAGEWIRE (467) 490
K_PADRING (468) 491
K_BLOCKRING (469) 492
K_BLOCKAGES (470) 838 839
K_PLACEMENT (471) 847
K_SLOTS (472) 857 874 875
K_FILLS (473) 858 888 889
K_PUSHDOWN (474) 859 865
K_NETLIST (475) 314 409
K_DIST (476) 315 410
K_USER (477) 316 411
K_TIMING (478) 317 412
K_BALANCED (479) 560
K_STEINER (480) 561
K_TRUNK (481) 562
K_FIXEDBUMP (482) 378 520
K_FENCE (483) 275
K_FREQUENCY (484) 380 521
K_GUIDE (485) 276
K_MAXBITS (486) 679
K_PARTITION (487) 657
K_TYPE (488) 269
K_ANTENNAMODEL (489) 155
K_DRCFILL (490) 489
K_OXIDE1 (491) 168
K_OXIDE2 (492) 169
K_OXIDE3 (493) 170
K_OXIDE4 (494) 171
K_CUTSIZE (495) 237
K_CUTSPACING (496) 237
K_DESIGNRULEWIDTH (497) 164 167 851
K_DIAGWIDTH (498) 940
K_ENCLOSURE (499) 237
K_HALO (500) 323
K_GROUNDSENSITIVITY (501) 132
K_HARDSPACING (502) 927
K_LAYERS (503) 237
K_MINCUTS (504) 936
K_NETEXPR (505) 128
K_OFFSET (506) 242
K_ORIGIN (507) 241
K_ROWCOL (508) 240
K_STYLES (509) 951 952
K_POLYGON (510) 140 232 513 872 886 903
K_PORT (511) 134
K_SUPPLYSENSITIVITY (512) 130
K_VIA (513) 142 518 895 932
K_VIARULE (514) 237 934
K_WIREEXT (515) 942
K_EXCEPTPGNET (516) 860
K_FILLWIREOPC (517) 488
K_OPC (518) 908 914
K_PARTIAL (519) 867
K_ROUTEHALO (520) 327


Nonterminals, with rules where they appear

$accept (273)
    on left: 0
def_file (274)
    on left: 1, on right: 0
version_stmt (275)
    on left: 2 4, on right: 1
$@1 (276)
    on left: 3, on right: 4
case_sens_stmt (277)
    on left: 5 6 7, on right: 1
rules (278)
    on left: 8 9 10, on right: 1 9
rule (279)
    on left: 11 12 13 14 15 16 17 18 19 20 21 22 23 24 25 26 27 28
    29 30 31 32, on right: 9
design_section (280)
    on left: 33 34 35 36 37 38 39 40 41 42 43 44 45 46 47 48 49, on right:
    11
design_name (281)
    on left: 51, on right: 37
$@2 (282)
    on left: 50, on right: 51
end_design (283)
    on left: 52, on right: 1
tech_name (284)
    on left: 54, on right: 47
$@3 (285)
    on left: 53, on right: 54
array_name (286)
    on left: 56, on right: 33
$@4 (287)
    on left: 55, on right: 56
floorplan_name (288)
    on left: 58, on right: 40
$@5 (289)
    on left: 57, on right: 58
history (290)
    on left: 59, on right: 42
prop_def_section (291)
    on left: 61, on right: 45
$@6 (292)
    on left: 60, on right: 61
property_defs (293)
    on left: 62 63, on right: 61 63
property_def (294)
    on left: 65 67 69 71 73 75 77 79 81 82, on right: 63
$@7 (295)
    on left: 64, on right: 65
$@8 (296)
    on left: 66, on right: 67
$@9 (297)
    on left: 68, on right: 69
$@10 (298)
    on left: 70, on right: 71
$@11 (299)
    on left: 72, on right: 73
$@12 (300)
    on left: 74, on right: 75
$@13 (301)
    on left: 76, on right: 77
$@14 (302)
    on left: 78, on right: 79
$@15 (303)
    on left: 80, on right: 81
property_type_and_val (304)
    on left: 84 86 87 88 89, on right: 65 67 69 71 73 75 77 79 81
$@16 (305)
    on left: 83, on right: 84
$@17 (306)
    on left: 85, on right: 86
opt_num_val (307)
    on left: 90 91, on right: 84 86
units (308)
    on left: 92, on right: 49
divider_char (309)
    on left: 93, on right: 39
bus_bit_chars (310)
    on left: 94, on right: 34
canplace (311)
    on left: 96, on right: 35
$@18 (312)
    on left: 95, on right: 96
cannotoccupy (313)
    on left: 98, on right: 36
$@19 (314)
    on left: 97, on right: 98
orient (315)
    on left: 99 100 101 102 103 104 105 106, on right: 96 98 143 186
    337 340 342 422 442 443 446 447
die_area (316)
    on left: 108, on right: 38
$@20 (317)
    on left: 107, on right: 108
pin_cap_rule (318)
    on left: 109, on right: 43
start_def_cap (319)
    on left: 110, on right: 109
pin_caps (320)
    on left: 111 112, on right: 109 112
pin_cap (321)
    on left: 113, on right: 112
end_def_cap (322)
    on left: 114, on right: 109
pin_rule (323)
    on left: 115, on right: 44
start_pins (324)
    on left: 116, on right: 115
pins (325)
    on left: 117 118, on right: 115 118
pin (326)
    on left: 122, on right: 118
$@21 (327)
    on left: 119, on right: 122
$@22 (328)
    on left: 120, on right: 122
$@23 (329)
    on left: 121, on right: 122
pin_options (330)
    on left: 123 124, on right: 122 124
pin_option (331)
    on left: 125 126 127 128 130 132 133 134 137 140 142 143 144 145
    146 147 149 151 152 154 155, on right: 124
$@24 (332)
    on left: 129, on right: 130
$@25 (333)
    on left: 131, on right: 132
$@26 (334)
    on left: 135, on right: 137
$@27 (335)
    on left: 136, on right: 137
$@28 (336)
    on left: 138, on right: 140
$@29 (337)
    on left: 139, on right: 140
$@30 (338)
    on left: 141, on right: 142
$@31 (339)
    on left: 148, on right: 149
$@32 (340)
    on left: 150, on right: 151
$@33 (341)
    on left: 153, on right: 154
pin_layer_mask_opt (342)
    on left: 156 157, on right: 137
pin_via_mask_opt (343)
    on left: 158 159, on right: 142
pin_poly_mask_opt (344)
    on left: 160 161, on right: 140
pin_layer_spacing_opt (345)
    on left: 162 163 164, on right: 137
pin_poly_spacing_opt (346)
    on left: 165 166 167, on right: 140
pin_oxide (347)
    on left: 168 169 170 171, on right: 155
use_type (348)
    on left: 172 173 174 175 176 177 178 179, on right: 133 387 527
pin_layer_opt (349)
    on left: 180 182, on right: 144 145 146 147 152
$@34 (350)
    on left: 181, on right: 182
end_pins (351)
    on left: 183, on right: 115
row_rule (352)
    on left: 186, on right: 46
$@35 (353)
    on left: 184, on right: 186
$@36 (354)
    on left: 185, on right: 186
row_do_option (355)
    on left: 187 188, on right: 186
row_step_option (356)
    on left: 189 190, on right: 188
row_options (357)
    on left: 191 192, on right: 186 192
row_option (358)
    on left: 194, on right: 192
$@37 (359)
    on left: 193, on right: 194
row_prop_list (360)
    on left: 195 196, on right: 194 196
row_prop (361)
    on left: 197 198 199, on right: 196
tracks_rule (362)
    on left: 201, on right: 48
$@38 (363)
    on left: 200, on right: 201
track_start (364)
    on left: 202, on right: 201
track_type (365)
    on left: 203 204, on right: 202 216
track_opts (366)
    on left: 205, on right: 201
track_mask_statement (367)
    on left: 206 207, on right: 205
same_mask (368)
    on left: 208 209, on right: 207
track_layer_statement (369)
    on left: 210 212, on right: 205
$@39 (370)
    on left: 211, on right: 212
track_layers (371)
    on left: 213 214, on right: 212 214
track_layer (372)
    on left: 215, on right: 212 214
gcellgrid (373)
    on left: 216, on right: 41
extension_section (374)
    on left: 217, on right: 16
extension_stmt (375)
    on left: 218, on right: 126 239 306 371 403 531 591 658 696 780
via_section (376)
    on left: 219, on right: 32
via (377)
    on left: 220, on right: 219
via_declarations (378)
    on left: 221 222, on right: 219 222
via_declaration (379)
    on left: 225, on right: 222
$@40 (380)
    on left: 223, on right: 225
$@41 (381)
    on left: 224, on right: 225
layer_stmts (382)
    on left: 226 227, on right: 225 227
layer_stmt (383)
    on left: 229 232 234 237 238 239, on right: 227
$@42 (384)
    on left: 228, on right: 229
$@43 (385)
    on left: 230, on right: 232
$@44 (386)
    on left: 231, on right: 232
$@45 (387)
    on left: 233, on right: 234
$@46 (388)
    on left: 235, on right: 237
$@47 (389)
    on left: 236, on right: 237
layer_viarule_opts (390)
    on left: 240 241 242 244, on right: 238
$@48 (391)
    on left: 243, on right: 244
firstPt (392)
    on left: 245, on right: 108 140 232 513 518 872 886 903 909 956
nextPt (393)
    on left: 246, on right: 108 140 232 248 513 872 886 903 956
otherPts (394)
    on left: 247 248, on right: 108 140 232 248 513 518 872 886 903
    909 956
pt (395)
    on left: 249 250 251 252, on right: 137 143 229 245 246 263 264
    320 321 338 340 342 415 422 515 592 870 884 901
mask (396)
    on left: 253 254, on right: 229 232
via_end (397)
    on left: 255, on right: 219
regions_section (398)
    on left: 256, on right: 26
regions_start (399)
    on left: 257, on right: 256
regions_stmts (400)
    on left: 258 259, on right: 256 259
regions_stmt (401)
    on left: 262, on right: 259
$@49 (402)
    on left: 260, on right: 262
$@50 (403)
    on left: 261, on right: 262
rect_list (404)
    on left: 263 264, on right: 262 264
region_options (405)
    on left: 265 266, on right: 262 266
region_option (406)
    on left: 268 269, on right: 266
$@51 (407)
    on left: 267, on right: 268
region_prop_list (408)
    on left: 270 271, on right: 268 271
region_prop (409)
    on left: 272 273 274, on right: 271
region_type (410)
    on left: 275 276, on right: 269
comps_maskShift_section (411)
    on left: 277, on right: 18
comps_section (412)
    on left: 278, on right: 14
start_comps (413)
    on left: 279, on right: 278
layer_statement (414)
    on left: 280 281, on right: 277 281
maskLayer (415)
    on left: 282, on right: 281
comps_rule (416)
    on left: 283 284, on right: 278 284
comp (417)
    on left: 285, on right: 284
comp_start (418)
    on left: 286, on right: 285
comp_id_and_name (419)
    on left: 288, on right: 286
$@52 (420)
    on left: 287, on right: 288
comp_net_list (421)
    on left: 289 290 291, on right: 286 290 291
comp_options (422)
    on left: 292 293, on right: 285 293
comp_option (423)
    on left: 294 295 296 297 298 299 300 301 302 303 304 305, on right:
    293
comp_extension_stmt (424)
    on left: 306, on right: 305
comp_eeq (425)
    on left: 308, on right: 301
$@53 (426)
    on left: 307, on right: 308
comp_generate (427)
    on left: 310, on right: 294
$@54 (428)
    on left: 309, on right: 310
opt_pattern (429)
    on left: 311 312, on right: 310
comp_source (430)
    on left: 313, on right: 295
source_type (431)
    on left: 314 315 316 317, on right: 313 519
comp_region (432)
    on left: 318 319, on right: 300
comp_pnt_list (433)
    on left: 320 321, on right: 318 321
comp_halo (434)
    on left: 323, on right: 302
$@55 (435)
    on left: 322, on right: 323
halo_soft (436)
    on left: 324 325, on right: 323
comp_routehalo (437)
    on left: 327, on right: 303
$@56 (438)
    on left: 326, on right: 327
comp_property (439)
    on left: 329, on right: 304
$@57 (440)
    on left: 328, on right: 329
comp_prop_list (441)
    on left: 330 331, on right: 329 331
comp_prop (442)
    on left: 332 333 334, on right: 330 331
comp_region_start (443)
    on left: 335, on right: 318 319
comp_foreign (444)
    on left: 337, on right: 299
$@58 (445)
    on left: 336, on right: 337
opt_paren (446)
    on left: 338 339, on right: 337
comp_type (447)
    on left: 340 341 342, on right: 296
maskShift (448)
    on left: 344, on right: 298
$@59 (449)
    on left: 343, on right: 344
placement_status (450)
    on left: 345 346 347, on right: 143 340
weight (451)
    on left: 348, on right: 297
end_comps (452)
    on left: 349, on right: 278
nets_section (453)
    on left: 350, on right: 22
start_nets (454)
    on left: 351, on right: 350
net_rules (455)
    on left: 352 353, on right: 350 353
one_net (456)
    on left: 354, on right: 353
net_and_connections (457)
    on left: 355, on right: 354 496
net_start (458)
    on left: 357, on right: 355
$@60 (459)
    on left: 356, on right: 357
net_name (460)
    on left: 359 361, on right: 357
$@61 (461)
    on left: 358, on right: 359
$@62 (462)
    on left: 360, on right: 361
net_connections (463)
    on left: 362 363, on right: 359 363
net_connection (464)
    on left: 365 367 369, on right: 363
$@63 (465)
    on left: 364, on right: 365
$@64 (466)
    on left: 366, on right: 367
$@65 (467)
    on left: 368, on right: 369
conn_opt (468)
    on left: 370 371 372, on right: 365 367 369
net_options (469)
    on left: 373 374, on right: 354 374
net_option (470)
    on left: 376 377 378 380 382 383 384 385 386 387 388 390 391 393
    396 400 402 403, on right: 374
$@66 (471)
    on left: 375, on right: 376
$@67 (472)
    on left: 379, on right: 380
$@68 (473)
    on left: 381, on right: 382
$@69 (474)
    on left: 389, on right: 390
$@70 (475)
    on left: 392, on right: 393
$@71 (476)
    on left: 394, on right: 396
$@72 (477)
    on left: 395, on right: 396
$@73 (478)
    on left: 397, on right: 400
$@74 (479)
    on left: 398, on right: 400
$@75 (480)
    on left: 399, on right: 400
$@76 (481)
    on left: 401, on right: 402
net_prop_list (482)
    on left: 404 405, on right: 402 405
net_prop (483)
    on left: 406 407 408, on right: 404 405
netsource_type (484)
    on left: 409 410 411 412 413, on right: 377
vpin_stmt (485)
    on left: 415, on right: 391
$@77 (486)
    on left: 414, on right: 415
vpin_begin (487)
    on left: 417, on right: 415
$@78 (488)
    on left: 416, on right: 417
vpin_layer_opt (489)
    on left: 418 420, on right: 415
$@79 (490)
    on left: 419, on right: 420
vpin_options (491)
    on left: 421 422, on right: 415
vpin_status (492)
    on left: 423 424 425, on right: 422
net_type (493)
    on left: 426 427 428, on right: 376 503 505
paths (494)
    on left: 429 430, on right: 376 396 430 811
new_path (495)
    on left: 432, on right: 430
$@80 (496)
    on left: 431, on right: 432
path (497)
    on left: 435, on right: 429 432
$@81 (498)
    on left: 433, on right: 435
$@82 (499)
    on left: 434, on right: 435
virtual_statement (500)
    on left: 436, on right: 448
rect_statement (501)
    on left: 437, on right: 449
path_item_list (502)
    on left: 438 439, on right: 435 439 570
path_item (503)
    on left: 440 441 442 443 444 445 446 447 448 449 451 453 454, on right:
    439
$@83 (504)
    on left: 450, on right: 451
$@84 (505)
    on left: 452, on right: 453
path_pt (506)
    on left: 455 456 457 458 459 460 461 462, on right: 435 453 454
    570
virtual_pt (507)
    on left: 463 464 465 466, on right: 436
rect_pts (508)
    on left: 467, on right: 437
opt_taper_style_s (509)
    on left: 468 469, on right: 435 469
opt_taper_style (510)
    on left: 470 471, on right: 469
opt_taper (511)
    on left: 472 474, on right: 471
$@85 (512)
    on left: 473, on right: 474
opt_style (513)
    on left: 475, on right: 470
opt_spaths (514)
    on left: 476 477, on right: 477 570
opt_shape_style (515)
    on left: 478 479, on right: 477
end_nets (516)
    on left: 480, on right: 350
shape_type (517)
    on left: 481 482 483 484 485 486 487 488 489 490 491 492, on right:
    478 509
snets_section (518)
    on left: 493, on right: 29
snet_rules (519)
    on left: 494 495, on right: 493 495
snet_rule (520)
    on left: 496, on right: 495
snet_options (521)
    on left: 497 498, on right: 496 498
snet_option (522)
    on left: 499 500 501 502, on right: 498
snet_other_option (523)
    on left: 503 505 508 509 510 513 515 518 519 520 521 523 524 525
    526 527 528 530 531, on right: 502
$@86 (524)
    on left: 504, on right: 505
$@87 (525)
    on left: 506, on right: 508
$@88 (526)
    on left: 507, on right: 508
$@89 (527)
    on left: 511, on right: 513
$@90 (528)
    on left: 512, on right: 513
$@91 (529)
    on left: 514, on right: 515
$@92 (530)
    on left: 516, on right: 518
$@93 (531)
    on left: 517, on right: 518
$@94 (532)
    on left: 522, on right: 523
$@95 (533)
    on left: 529, on right: 530
orient_pt (534)
    on left: 532 533 534 535 536 537 538 539 540, on right: 518
shield_layer (535)
    on left: 541 543, on right: 508
$@96 (536)
    on left: 542, on right: 543
snet_width (537)
    on left: 545, on right: 499
$@97 (538)
    on left: 544, on right: 545
snet_voltage (539)
    on left: 547, on right: 500
$@98 (540)
    on left: 546, on right: 547
snet_spacing (541)
    on left: 550, on right: 501
$@99 (542)
    on left: 548, on right: 550
$@100 (543)
    on left: 549, on right: 550
snet_prop_list (544)
    on left: 551 552, on right: 530 552
snet_prop (545)
    on left: 553 554 555, on right: 551 552
opt_snet_range (546)
    on left: 556 557, on right: 550
opt_range (547)
    on left: 558 559, on right: 84 86
pattern_type (548)
    on left: 560 561 562 563, on right: 383 524
spaths (549)
    on left: 564 565, on right: 505 543 565
snew_path (550)
    on left: 567, on right: 565
$@101 (551)
    on left: 566, on right: 567
spath (552)
    on left: 570, on right: 564 567
$@102 (553)
    on left: 568, on right: 570
$@103 (554)
    on left: 569, on right: 570
width (555)
    on left: 571, on right: 570
start_snets (556)
    on left: 572, on right: 493
end_snets (557)
    on left: 573, on right: 493
groups_section (558)
    on left: 574, on right: 20
groups_start (559)
    on left: 575, on right: 574
group_rules (560)
    on left: 576 577, on right: 574 577
group_rule (561)
    on left: 578, on right: 577
start_group (562)
    on left: 580, on right: 578
$@104 (563)
    on left: 579, on right: 580
group_members (564)
    on left: 581 582, on right: 578 582
group_member (565)
    on left: 583, on right: 582
group_options (566)
    on left: 584 585, on right: 578 585
group_option (567)
    on left: 586 588 590 591, on right: 585
$@105 (568)
    on left: 587, on right: 588
$@106 (569)
    on left: 589, on right: 590
group_region (570)
    on left: 592 593, on right: 590
group_prop_list (571)
    on left: 594 595, on right: 588 595
group_prop (572)
    on left: 596 597 598, on right: 595
group_soft_options (573)
    on left: 599 600, on right: 586 600
group_soft_option (574)
    on left: 601 602 603, on right: 600
groups_end (575)
    on left: 604, on right: 574
assertions_section (576)
    on left: 605, on right: 12
constraint_section (577)
    on left: 606, on right: 15
assertions_start (578)
    on left: 607, on right: 605
constraints_start (579)
    on left: 608, on right: 606
constraint_rules (580)
    on left: 609 610, on right: 605 606 610
constraint_rule (581)
    on left: 611 612, on right: 610
operand_rule (582)
    on left: 613, on right: 611
operand (583)
    on left: 615 617 618 619, on right: 613 620 621 622
$@107 (584)
    on left: 614, on right: 615
$@108 (585)
    on left: 616, on right: 617
operand_list (586)
    on left: 620 621 622, on right: 618 619 621 622
wiredlogic_rule (587)
    on left: 624, on right: 612
$@109 (588)
    on left: 623, on right: 624
opt_plus (589)
    on left: 625 626, on right: 624
delay_specs (590)
    on left: 627 628, on right: 613 628
delay_spec (591)
    on left: 629 630 631 632, on right: 628
constraints_end (592)
    on left: 633, on right: 606
assertions_end (593)
    on left: 634, on right: 605
scanchains_section (594)
    on left: 635, on right: 27
scanchain_start (595)
    on left: 636, on right: 635
scanchain_rules (596)
    on left: 637 638, on right: 635 638
scan_rule (597)
    on left: 639, on right: 638
start_scan (598)
    on left: 641, on right: 639
$@110 (599)
    on left: 640, on right: 641
scan_members (600)
    on left: 642 643, on right: 639 643
opt_pin (601)
    on left: 644 645, on right: 647 653
scan_member (602)
    on left: 647 649 651 653 655 657 658, on right: 643
$@111 (603)
    on left: 646, on right: 647
$@112 (604)
    on left: 648, on right: 649
$@113 (605)
    on left: 650, on right: 651
$@114 (606)
    on left: 652, on right: 653
$@115 (607)
    on left: 654, on right: 655
$@116 (608)
    on left: 656, on right: 657
opt_common_pins (609)
    on left: 659 660 661, on right: 655
floating_inst_list (610)
    on left: 662 663, on right: 649 663
one_floating_inst (611)
    on left: 665, on right: 663
$@117 (612)
    on left: 664, on right: 665
floating_pins (613)
    on left: 666 667 668 669, on right: 665
ordered_inst_list (614)
    on left: 670 671, on right: 651 671
one_ordered_inst (615)
    on left: 673, on right: 671
$@118 (616)
    on left: 672, on right: 673
ordered_pins (617)
    on left: 674 675 676 677, on right: 673
partition_maxbits (618)
    on left: 678 679, on right: 657
scanchain_end (619)
    on left: 680, on right: 635
iotiming_section (620)
    on left: 681, on right: 21
iotiming_start (621)
    on left: 682, on right: 681
iotiming_rules (622)
    on left: 683 684, on right: 681 684
iotiming_rule (623)
    on left: 685, on right: 684
start_iotiming (624)
    on left: 687, on right: 685
$@119 (625)
    on left: 686, on right: 687
iotiming_members (626)
    on left: 688 689, on right: 685 689
iotiming_member (627)
    on left: 690 691 692 695 696, on right: 689
$@120 (628)
    on left: 693, on right: 695
$@121 (629)
    on left: 694, on right: 695
iotiming_drivecell_opt (630)
    on left: 699, on right: 695
$@122 (631)
    on left: 697, on right: 699
$@123 (632)
    on left: 698, on right: 699
iotiming_frompin (633)
    on left: 700 702, on right: 699
$@124 (634)
    on left: 701, on right: 702
iotiming_parallel (635)
    on left: 703 704, on right: 699
risefall (636)
    on left: 705 706, on right: 690 691 769 775
iotiming_end (637)
    on left: 707, on right: 681
floorplan_contraints_section (638)
    on left: 708, on right: 19
fp_start (639)
    on left: 709, on right: 708
fp_stmts (640)
    on left: 710 711, on right: 708 711
fp_stmt (641)
    on left: 714, on right: 711
$@125 (642)
    on left: 712, on right: 714
$@126 (643)
    on left: 713, on right: 714
h_or_v (644)
    on left: 715 716, on right: 714
constraint_type (645)
    on left: 717 718 719 720, on right: 714
constrain_what_list (646)
    on left: 721 722, on right: 714 722
constrain_what (647)
    on left: 724 726, on right: 722
$@127 (648)
    on left: 723, on right: 724
$@128 (649)
    on left: 725, on right: 726
row_or_comp_list (650)
    on left: 727 728, on right: 724 726 728
row_or_comp (651)
    on left: 730 732, on right: 728
$@129 (652)
    on left: 729, on right: 730
$@130 (653)
    on left: 731, on right: 732
timingdisables_section (654)
    on left: 733, on right: 31
timingdisables_start (655)
    on left: 734, on right: 733
timingdisables_rules (656)
    on left: 735 736, on right: 733 736
timingdisables_rule (657)
    on left: 739 741 743 744, on right: 736
$@131 (658)
    on left: 737, on right: 739
$@132 (659)
    on left: 738, on right: 739
$@133 (660)
    on left: 740, on right: 741
$@134 (661)
    on left: 742, on right: 743
td_macro_option (662)
    on left: 747 749, on right: 743
$@135 (663)
    on left: 745, on right: 747
$@136 (664)
    on left: 746, on right: 747
$@137 (665)
    on left: 748, on right: 749
timingdisables_end (666)
    on left: 750, on right: 733
partitions_section (667)
    on left: 751, on right: 24
partitions_start (668)
    on left: 752, on right: 751
partition_rules (669)
    on left: 753 754, on right: 751 754
partition_rule (670)
    on left: 755, on right: 754
start_partition (671)
    on left: 757, on right: 755
$@138 (672)
    on left: 756, on right: 757
turnoff (673)
    on left: 758 759, on right: 757
turnoff_setup (674)
    on left: 760 761 762, on right: 759
turnoff_hold (675)
    on left: 763 764 765, on right: 759
partition_members (676)
    on left: 766 767, on right: 755 767
partition_member (677)
    on left: 769 771 773 775 777 779 780, on right: 767
$@139 (678)
    on left: 768, on right: 769
$@140 (679)
    on left: 770, on right: 771
$@141 (680)
    on left: 772, on right: 773
$@142 (681)
    on left: 774, on right: 775
$@143 (682)
    on left: 776, on right: 777
$@144 (683)
    on left: 778, on right: 779
minmaxpins (684)
    on left: 782, on right: 769 775
$@145 (685)
    on left: 781, on right: 782
min_or_max_list (686)
    on left: 783 784, on right: 782 784
min_or_max_member (687)
    on left: 785 786, on right: 784
pin_list (688)
    on left: 787 788, on right: 782 788
risefallminmax1_list (689)
    on left: 789 790, on right: 773 779 790
risefallminmax1 (690)
    on left: 791 792 793 794, on right: 790
risefallminmax2_list (691)
    on left: 795 796, on right: 771 777 796
risefallminmax2 (692)
    on left: 797 798 799 800, on right: 795 796
partitions_end (693)
    on left: 801, on right: 751
comp_names (694)
    on left: 802 803, on right: 400 803
comp_name (695)
    on left: 805, on right: 803
$@146 (696)
    on left: 804, on right: 805
subnet_opt_syn (697)
    on left: 806 807, on right: 805
subnet_options (698)
    on left: 808 809, on right: 400 809
subnet_option (699)
    on left: 811 813, on right: 809
$@147 (700)
    on left: 810, on right: 811
$@148 (701)
    on left: 812, on right: 813
subnet_type (702)
    on left: 814 815 816 817, on right: 811
pin_props_section (703)
    on left: 818, on right: 25
begin_pin_props (704)
    on left: 819, on right: 818
opt_semi (705)
    on left: 820 821, on right: 819
end_pin_props (706)
    on left: 822, on right: 818
pin_prop_list (707)
    on left: 823 824, on right: 818 824
pin_prop_terminal (708)
    on left: 827, on right: 824
$@149 (709)
    on left: 825, on right: 827
$@150 (710)
    on left: 826, on right: 827
pin_prop_options (711)
    on left: 828 829, on right: 827 829
pin_prop (712)
    on left: 831, on right: 829
$@151 (713)
    on left: 830, on right: 831
pin_prop_name_value_list (714)
    on left: 832 833, on right: 831 833
pin_prop_name_value (715)
    on left: 834 835 836, on right: 833
blockage_section (716)
    on left: 837, on right: 13
blockage_start (717)
    on left: 838, on right: 837
blockage_end (718)
    on left: 839, on right: 837
blockage_defs (719)
    on left: 840 841, on right: 837 841
blockage_def (720)
    on left: 842, on right: 841
blockage_rule (721)
    on left: 845 847, on right: 842
$@152 (722)
    on left: 843, on right: 845
$@153 (723)
    on left: 844, on right: 845
$@154 (724)
    on left: 846, on right: 847
layer_blockage_rules (725)
    on left: 848 849, on right: 845 849
layer_blockage_rule (726)
    on left: 850 851 852 853, on right: 849
mask_blockage_rule (727)
    on left: 854, on right: 852
comp_blockage_rule (728)
    on left: 856 857 858 859 860, on right: 853
$@155 (729)
    on left: 855, on right: 856
placement_comp_rules (730)
    on left: 861 862, on right: 847 862
placement_comp_rule (731)
    on left: 864 865 866 867, on right: 862
$@156 (732)
    on left: 863, on right: 864
rectPoly_blockage_rules (733)
    on left: 868 869, on right: 842 869
rectPoly_blockage (734)
    on left: 870 872, on right: 842 869
$@157 (735)
    on left: 871, on right: 872
slot_section (736)
    on left: 873, on right: 28
slot_start (737)
    on left: 874, on right: 873
slot_end (738)
    on left: 875, on right: 873
slot_defs (739)
    on left: 876 877, on right: 873 877
slot_def (740)
    on left: 878, on right: 877
slot_rule (741)
    on left: 881, on right: 878
$@158 (742)
    on left: 879, on right: 881
$@159 (743)
    on left: 880, on right: 881
geom_slot_rules (744)
    on left: 882 883, on right: 878 883
geom_slot (745)
    on left: 884 886, on right: 881 883
$@160 (746)
    on left: 885, on right: 886
fill_section (747)
    on left: 887, on right: 17
fill_start (748)
    on left: 888, on right: 887
fill_end (749)
    on left: 889, on right: 887
fill_defs (750)
    on left: 890 891, on right: 887 891
fill_def (751)
    on left: 892 895, on right: 891
$@161 (752)
    on left: 893, on right: 895
$@162 (753)
    on left: 894, on right: 895
fill_rule (754)
    on left: 898, on right: 892
$@163 (755)
    on left: 896, on right: 898
$@164 (756)
    on left: 897, on right: 898
geom_fill_rules (757)
    on left: 899 900, on right: 892 900
geom_fill (758)
    on left: 901 903, on right: 898 900
$@165 (759)
    on left: 902, on right: 903
fill_layer_mask_opc_opt (760)
    on left: 904 905, on right: 898 905
opt_mask_opc_l (761)
    on left: 906 907, on right: 905
fill_layer_opc (762)
    on left: 908, on right: 906
fill_via_pt (763)
    on left: 909, on right: 895
fill_via_mask_opc_opt (764)
    on left: 910 911, on right: 895 911
opt_mask_opc (765)
    on left: 912 913, on right: 911
fill_via_opc (766)
    on left: 914, on right: 912
fill_mask (767)
    on left: 915, on right: 907
fill_viaMask (768)
    on left: 916, on right: 913
nondefaultrule_section (769)
    on left: 917, on right: 23
nondefault_start (770)
    on left: 918, on right: 917
nondefault_end (771)
    on left: 919, on right: 917
nondefault_defs (772)
    on left: 920 921, on right: 917 921
nondefault_def (773)
    on left: 924, on right: 917 921
$@166 (774)
    on left: 922, on right: 924
$@167 (775)
    on left: 923, on right: 924
nondefault_options (776)
    on left: 925 926, on right: 924 926
nondefault_option (777)
    on left: 927 930 932 934 936 937, on right: 926
$@168 (778)
    on left: 928, on right: 930
$@169 (779)
    on left: 929, on right: 930
$@170 (780)
    on left: 931, on right: 932
$@171 (781)
    on left: 933, on right: 934
$@172 (782)
    on left: 935, on right: 936
nondefault_layer_options (783)
    on left: 938 939, on right: 930 939
nondefault_layer_option (784)
    on left: 940 941 942, on right: 939
nondefault_prop_opt (785)
    on left: 944, on right: 937
$@173 (786)
    on left: 943, on right: 944
nondefault_prop_list (787)
    on left: 945 946, on right: 944 946
nondefault_prop (788)
    on left: 947 948 949, on right: 946
styles_section (789)
    on left: 950, on right: 30
styles_start (790)
    on left: 951, on right: 950
styles_end (791)
    on left: 952, on right: 950
styles_rules (792)
    on left: 953 954, on right: 950 954
styles_rule (793)
    on left: 956, on right: 954
$@174 (794)
    on left: 955, on right: 956


State 0

    0 $accept: . def_file $end

    K_VERSION  shift, and go to state 1

    $default  reduce using rule 2 (version_stmt)

    def_file      go to state 2
    version_stmt  go to state 3


State 1

    4 version_stmt: K_VERSION . $@1 T_STRING ';'

    $default  reduce using rule 3 ($@1)

    $@1  go to state 4


State 2

    0 $accept: def_file . $end

    $end  shift, and go to state 5


State 3

    1 def_file: version_stmt . case_sens_stmt rules end_design

    K_NAMESCASESENSITIVE  shift, and go to state 6

    $default  reduce using rule 5 (case_sens_stmt)

    case_sens_stmt  go to state 7


State 4

    4 version_stmt: K_VERSION $@1 . T_STRING ';'

    T_STRING  shift, and go to state 8


State 5

    0 $accept: def_file $end .

    $default  accept


State 6

    6 case_sens_stmt: K_NAMESCASESENSITIVE . K_ON ';'
    7               | K_NAMESCASESENSITIVE . K_OFF ';'

    K_ON   shift, and go to state 9
    K_OFF  shift, and go to state 10


State 7

    1 def_file: version_stmt case_sens_stmt . rules end_design

    error  shift, and go to state 11

    K_HISTORY              reduce using rule 8 (rules)
    K_DESIGN               reduce using rule 8 (rules)
    K_VIAS                 reduce using rule 8 (rules)
    K_TECH                 reduce using rule 8 (rules)
    K_UNITS                reduce using rule 8 (rules)
    K_ARRAY                reduce using rule 8 (rules)
    K_FLOORPLAN            reduce using rule 8 (rules)
    K_CANPLACE             reduce using rule 8 (rules)
    K_CANNOTOCCUPY         reduce using rule 8 (rules)
    K_DIEAREA              reduce using rule 8 (rules)
    K_PINS                 reduce using rule 8 (rules)
    K_DEFAULTCAP           reduce using rule 8 (rules)
    K_TRACKS               reduce using rule 8 (rules)
    K_GCELLGRID            reduce using rule 8 (rules)
    K_ROW                  reduce using rule 8 (rules)
    K_COMPS                reduce using rule 8 (rules)
    K_REGIONS              reduce using rule 8 (rules)
    K_NETS                 reduce using rule 8 (rules)
    K_SNETS                reduce using rule 8 (rules)
    K_NONDEFAULTRULES      reduce using rule 8 (rules)
    K_GROUPS               reduce using rule 8 (rules)
    K_CONSTRAINTS          reduce using rule 8 (rules)
    K_SCANCHAINS           reduce using rule 8 (rules)
    K_ASSERTIONS           reduce using rule 8 (rules)
    K_END                  reduce using rule 8 (rules)
    K_IOTIMINGS            reduce using rule 8 (rules)
    K_TIMINGDISABLES       reduce using rule 8 (rules)
    K_PARTITIONS           reduce using rule 8 (rules)
    K_FPC                  reduce using rule 8 (rules)
    K_DIVIDERCHAR          reduce using rule 8 (rules)
    K_BUSBITCHARS          reduce using rule 8 (rules)
    K_PROPERTYDEFINITIONS  reduce using rule 8 (rules)
    K_BEGINEXT             reduce using rule 8 (rules)
    K_COMPSMASKSHIFT       reduce using rule 8 (rules)
    K_PINPROPERTIES        reduce using rule 8 (rules)
    K_BLOCKAGES            reduce using rule 8 (rules)
    K_SLOTS                reduce using rule 8 (rules)
    K_FILLS                reduce using rule 8 (rules)
    K_STYLES               reduce using rule 8 (rules)

    rules  go to state 12


State 8

    4 version_stmt: K_VERSION $@1 T_STRING . ';'

    ';'  shift, and go to state 13


State 9

    6 case_sens_stmt: K_NAMESCASESENSITIVE K_ON . ';'

    ';'  shift, and go to state 14


State 10

    7 case_sens_stmt: K_NAMESCASESENSITIVE K_OFF . ';'

    ';'  shift, and go to state 15


State 11

   10 rules: error .

    $default  reduce using rule 10 (rules)


State 12

    1 def_file: version_stmt case_sens_stmt rules . end_design
    9 rules: rules . rule

    K_HISTORY              shift, and go to state 16
    K_DESIGN               shift, and go to state 17
    K_VIAS                 shift, and go to state 18
    K_TECH                 shift, and go to state 19
    K_UNITS                shift, and go to state 20
    K_ARRAY                shift, and go to state 21
    K_FLOORPLAN            shift, and go to state 22
    K_CANPLACE             shift, and go to state 23
    K_CANNOTOCCUPY         shift, and go to state 24
    K_DIEAREA              shift, and go to state 25
    K_PINS                 shift, and go to state 26
    K_DEFAULTCAP           shift, and go to state 27
    K_TRACKS               shift, and go to state 28
    K_GCELLGRID            shift, and go to state 29
    K_ROW                  shift, and go to state 30
    K_COMPS                shift, and go to state 31
    K_REGIONS              shift, and go to state 32
    K_NETS                 shift, and go to state 33
    K_SNETS                shift, and go to state 34
    K_NONDEFAULTRULES      shift, and go to state 35
    K_GROUPS               shift, and go to state 36
    K_CONSTRAINTS          shift, and go to state 37
    K_SCANCHAINS           shift, and go to state 38
    K_ASSERTIONS           shift, and go to state 39
    K_END                  shift, and go to state 40
    K_IOTIMINGS            shift, and go to state 41
    K_TIMINGDISABLES       shift, and go to state 42
    K_PARTITIONS           shift, and go to state 43
    K_FPC                  shift, and go to state 44
    K_DIVIDERCHAR          shift, and go to state 45
    K_BUSBITCHARS          shift, and go to state 46
    K_PROPERTYDEFINITIONS  shift, and go to state 47
    K_BEGINEXT             shift, and go to state 48
    K_COMPSMASKSHIFT       shift, and go to state 49
    K_PINPROPERTIES        shift, and go to state 50
    K_BLOCKAGES            shift, and go to state 51
    K_SLOTS                shift, and go to state 52
    K_FILLS                shift, and go to state 53
    K_STYLES               shift, and go to state 54

    rule                          go to state 55
    design_section                go to state 56
    design_name                   go to state 57
    end_design                    go to state 58
    tech_name                     go to state 59
    array_name                    go to state 60
    floorplan_name                go to state 61
    history                       go to state 62
    prop_def_section              go to state 63
    units                         go to state 64
    divider_char                  go to state 65
    bus_bit_chars                 go to state 66
    canplace                      go to state 67
    cannotoccupy                  go to state 68
    die_area                      go to state 69
    pin_cap_rule                  go to state 70
    start_def_cap                 go to state 71
    pin_rule                      go to state 72
    start_pins                    go to state 73
    row_rule                      go to state 74
    tracks_rule                   go to state 75
    track_start                   go to state 76
    gcellgrid                     go to state 77
    extension_section             go to state 78
    via_section                   go to state 79
    via                           go to state 80
    regions_section               go to state 81
    regions_start                 go to state 82
    comps_maskShift_section       go to state 83
    comps_section                 go to state 84
    start_comps                   go to state 85
    nets_section                  go to state 86
    start_nets                    go to state 87
    snets_section                 go to state 88
    start_snets                   go to state 89
    groups_section                go to state 90
    groups_start                  go to state 91
    assertions_section            go to state 92
    constraint_section            go to state 93
    assertions_start              go to state 94
    constraints_start             go to state 95
    scanchains_section            go to state 96
    scanchain_start               go to state 97
    iotiming_section              go to state 98
    iotiming_start                go to state 99
    floorplan_contraints_section  go to state 100
    fp_start                      go to state 101
    timingdisables_section        go to state 102
    timingdisables_start          go to state 103
    partitions_section            go to state 104
    partitions_start              go to state 105
    pin_props_section             go to state 106
    begin_pin_props               go to state 107
    blockage_section              go to state 108
    blockage_start                go to state 109
    slot_section                  go to state 110
    slot_start                    go to state 111
    fill_section                  go to state 112
    fill_start                    go to state 113
    nondefaultrule_section        go to state 114
    nondefault_start              go to state 115
    styles_section                go to state 116
    styles_start                  go to state 117


State 13

    4 version_stmt: K_VERSION $@1 T_STRING ';' .

    $default  reduce using rule 4 (version_stmt)


State 14

    6 case_sens_stmt: K_NAMESCASESENSITIVE K_ON ';' .

    $default  reduce using rule 6 (case_sens_stmt)


State 15

    7 case_sens_stmt: K_NAMESCASESENSITIVE K_OFF ';' .

    $default  reduce using rule 7 (case_sens_stmt)


State 16

   59 history: K_HISTORY .

    $default  reduce using rule 59 (history)


State 17

   51 design_name: K_DESIGN . $@2 T_STRING ';'

    $default  reduce using rule 50 ($@2)

    $@2  go to state 118


State 18

  220 via: K_VIAS . NUMBER ';'

    NUMBER  shift, and go to state 119


State 19

   54 tech_name: K_TECH . $@3 T_STRING ';'

    $default  reduce using rule 53 ($@3)

    $@3  go to state 120


State 20

   92 units: K_UNITS . K_DISTANCE K_MICRONS NUMBER ';'

    K_DISTANCE  shift, and go to state 121


State 21

   56 array_name: K_ARRAY . $@4 T_STRING ';'

    $default  reduce using rule 55 ($@4)

    $@4  go to state 122


State 22

   58 floorplan_name: K_FLOORPLAN . $@5 T_STRING ';'

    $default  reduce using rule 57 ($@5)

    $@5  go to state 123


State 23

   96 canplace: K_CANPLACE . $@18 T_STRING NUMBER NUMBER orient K_DO NUMBER K_BY NUMBER K_STEP NUMBER NUMBER ';'

    $default  reduce using rule 95 ($@18)

    $@18  go to state 124


State 24

   98 cannotoccupy: K_CANNOTOCCUPY . $@19 T_STRING NUMBER NUMBER orient K_DO NUMBER K_BY NUMBER K_STEP NUMBER NUMBER ';'

    $default  reduce using rule 97 ($@19)

    $@19  go to state 125


State 25

  108 die_area: K_DIEAREA . $@20 firstPt nextPt otherPts ';'

    $default  reduce using rule 107 ($@20)

    $@20  go to state 126


State 26

  116 start_pins: K_PINS . NUMBER ';'

    NUMBER  shift, and go to state 127


State 27

  110 start_def_cap: K_DEFAULTCAP . NUMBER

    NUMBER  shift, and go to state 128


State 28

  202 track_start: K_TRACKS . track_type

    K_X  shift, and go to state 129
    K_Y  shift, and go to state 130

    track_type  go to state 131


State 29

  216 gcellgrid: K_GCELLGRID . track_type NUMBER K_DO NUMBER K_STEP NUMBER ';'

    K_X  shift, and go to state 129
    K_Y  shift, and go to state 130

    track_type  go to state 132


State 30

  186 row_rule: K_ROW . $@35 T_STRING T_STRING NUMBER NUMBER orient $@36 row_do_option row_options ';'

    $default  reduce using rule 184 ($@35)

    $@35  go to state 133


State 31

  279 start_comps: K_COMPS . NUMBER ';'

    NUMBER  shift, and go to state 134


State 32

  257 regions_start: K_REGIONS . NUMBER ';'

    NUMBER  shift, and go to state 135


State 33

  351 start_nets: K_NETS . NUMBER ';'

    NUMBER  shift, and go to state 136


State 34

  572 start_snets: K_SNETS . NUMBER ';'

    NUMBER  shift, and go to state 137


State 35

  918 nondefault_start: K_NONDEFAULTRULES . NUMBER ';'

    NUMBER  shift, and go to state 138


State 36

  575 groups_start: K_GROUPS . NUMBER ';'

    NUMBER  shift, and go to state 139


State 37

  608 constraints_start: K_CONSTRAINTS . NUMBER ';'

    NUMBER  shift, and go to state 140


State 38

  636 scanchain_start: K_SCANCHAINS . NUMBER ';'

    NUMBER  shift, and go to state 141


State 39

  607 assertions_start: K_ASSERTIONS . NUMBER ';'

    NUMBER  shift, and go to state 142


State 40

   52 end_design: K_END . K_DESIGN

    K_DESIGN  shift, and go to state 143


State 41

  682 iotiming_start: K_IOTIMINGS . NUMBER ';'

    NUMBER  shift, and go to state 144


State 42

  734 timingdisables_start: K_TIMINGDISABLES . NUMBER ';'

    NUMBER  shift, and go to state 145


State 43

  752 partitions_start: K_PARTITIONS . NUMBER ';'

    NUMBER  shift, and go to state 146


State 44

  709 fp_start: K_FPC . NUMBER ';'

    NUMBER  shift, and go to state 147


State 45

   93 divider_char: K_DIVIDERCHAR . QSTRING ';'

    QSTRING  shift, and go to state 148


State 46

   94 bus_bit_chars: K_BUSBITCHARS . QSTRING ';'

    QSTRING  shift, and go to state 149


State 47

   61 prop_def_section: K_PROPERTYDEFINITIONS . $@6 property_defs K_END K_PROPERTYDEFINITIONS

    $default  reduce using rule 60 ($@6)

    $@6  go to state 150


State 48

  217 extension_section: K_BEGINEXT .

    $default  reduce using rule 217 (extension_section)


State 49

  277 comps_maskShift_section: K_COMPSMASKSHIFT . layer_statement ';'

    $default  reduce using rule 280 (layer_statement)

    layer_statement  go to state 151


State 50

  819 begin_pin_props: K_PINPROPERTIES . NUMBER opt_semi

    NUMBER  shift, and go to state 152


State 51

  838 blockage_start: K_BLOCKAGES . NUMBER ';'

    NUMBER  shift, and go to state 153


State 52

  874 slot_start: K_SLOTS . NUMBER ';'

    NUMBER  shift, and go to state 154


State 53

  888 fill_start: K_FILLS . NUMBER ';'

    NUMBER  shift, and go to state 155


State 54

  951 styles_start: K_STYLES . NUMBER ';'

    NUMBER  shift, and go to state 156


State 55

    9 rules: rules rule .

    $default  reduce using rule 9 (rules)


State 56

   11 rule: design_section .

    $default  reduce using rule 11 (rule)


State 57

   37 design_section: design_name .

    $default  reduce using rule 37 (design_section)


State 58

    1 def_file: version_stmt case_sens_stmt rules end_design .

    $default  reduce using rule 1 (def_file)


State 59

   47 design_section: tech_name .

    $default  reduce using rule 47 (design_section)


State 60

   33 design_section: array_name .

    $default  reduce using rule 33 (design_section)


State 61

   40 design_section: floorplan_name .

    $default  reduce using rule 40 (design_section)


State 62

   42 design_section: history .

    $default  reduce using rule 42 (design_section)


State 63

   45 design_section: prop_def_section .

    $default  reduce using rule 45 (design_section)


State 64

   49 design_section: units .

    $default  reduce using rule 49 (design_section)


State 65

   39 design_section: divider_char .

    $default  reduce using rule 39 (design_section)


State 66

   34 design_section: bus_bit_chars .

    $default  reduce using rule 34 (design_section)


State 67

   35 design_section: canplace .

    $default  reduce using rule 35 (design_section)


State 68

   36 design_section: cannotoccupy .

    $default  reduce using rule 36 (design_section)


State 69

   38 design_section: die_area .

    $default  reduce using rule 38 (design_section)


State 70

   43 design_section: pin_cap_rule .

    $default  reduce using rule 43 (design_section)


State 71

  109 pin_cap_rule: start_def_cap . pin_caps end_def_cap

    $default  reduce using rule 111 (pin_caps)

    pin_caps  go to state 157


State 72

   44 design_section: pin_rule .

    $default  reduce using rule 44 (design_section)


State 73

  115 pin_rule: start_pins . pins end_pins

    $default  reduce using rule 117 (pins)

    pins  go to state 158


State 74

   46 design_section: row_rule .

    $default  reduce using rule 46 (design_section)


State 75

   48 design_section: tracks_rule .

    $default  reduce using rule 48 (design_section)


State 76

  201 tracks_rule: track_start . NUMBER $@38 K_DO NUMBER K_STEP NUMBER track_opts ';'

    NUMBER  shift, and go to state 159


State 77

   41 design_section: gcellgrid .

    $default  reduce using rule 41 (design_section)


State 78

   16 rule: extension_section .

    $default  reduce using rule 16 (rule)


State 79

   32 rule: via_section .

    $default  reduce using rule 32 (rule)


State 80

  219 via_section: via . via_declarations via_end

    $default  reduce using rule 221 (via_declarations)

    via_declarations  go to state 160


State 81

   26 rule: regions_section .

    $default  reduce using rule 26 (rule)


State 82

  256 regions_section: regions_start . regions_stmts K_END K_REGIONS

    $default  reduce using rule 258 (regions_stmts)

    regions_stmts  go to state 161


State 83

   18 rule: comps_maskShift_section .

    $default  reduce using rule 18 (rule)


State 84

   14 rule: comps_section .

    $default  reduce using rule 14 (rule)


State 85

  278 comps_section: start_comps . comps_rule end_comps

    $default  reduce using rule 283 (comps_rule)

    comps_rule  go to state 162


State 86

   22 rule: nets_section .

    $default  reduce using rule 22 (rule)


State 87

  350 nets_section: start_nets . net_rules end_nets

    $default  reduce using rule 352 (net_rules)

    net_rules  go to state 163


State 88

   29 rule: snets_section .

    $default  reduce using rule 29 (rule)


State 89

  493 snets_section: start_snets . snet_rules end_snets

    $default  reduce using rule 494 (snet_rules)

    snet_rules  go to state 164


State 90

   20 rule: groups_section .

    $default  reduce using rule 20 (rule)


State 91

  574 groups_section: groups_start . group_rules groups_end

    $default  reduce using rule 576 (group_rules)

    group_rules  go to state 165


State 92

   12 rule: assertions_section .

    $default  reduce using rule 12 (rule)


State 93

   15 rule: constraint_section .

    $default  reduce using rule 15 (rule)


State 94

  605 assertions_section: assertions_start . constraint_rules assertions_end

    $default  reduce using rule 609 (constraint_rules)

    constraint_rules  go to state 166


State 95

  606 constraint_section: constraints_start . constraint_rules constraints_end

    $default  reduce using rule 609 (constraint_rules)

    constraint_rules  go to state 167


State 96

   27 rule: scanchains_section .

    $default  reduce using rule 27 (rule)


State 97

  635 scanchains_section: scanchain_start . scanchain_rules scanchain_end

    $default  reduce using rule 637 (scanchain_rules)

    scanchain_rules  go to state 168


State 98

   21 rule: iotiming_section .

    $default  reduce using rule 21 (rule)


State 99

  681 iotiming_section: iotiming_start . iotiming_rules iotiming_end

    $default  reduce using rule 683 (iotiming_rules)

    iotiming_rules  go to state 169


State 100

   19 rule: floorplan_contraints_section .

    $default  reduce using rule 19 (rule)


State 101

  708 floorplan_contraints_section: fp_start . fp_stmts K_END K_FPC

    $default  reduce using rule 710 (fp_stmts)

    fp_stmts  go to state 170


State 102

   31 rule: timingdisables_section .

    $default  reduce using rule 31 (rule)


State 103

  733 timingdisables_section: timingdisables_start . timingdisables_rules timingdisables_end

    $default  reduce using rule 735 (timingdisables_rules)

    timingdisables_rules  go to state 171


State 104

   24 rule: partitions_section .

    $default  reduce using rule 24 (rule)


State 105

  751 partitions_section: partitions_start . partition_rules partitions_end

    $default  reduce using rule 753 (partition_rules)

    partition_rules  go to state 172


State 106

   25 rule: pin_props_section .

    $default  reduce using rule 25 (rule)


State 107

  818 pin_props_section: begin_pin_props . pin_prop_list end_pin_props

    $default  reduce using rule 823 (pin_prop_list)

    pin_prop_list  go to state 173


State 108

   13 rule: blockage_section .

    $default  reduce using rule 13 (rule)


State 109

  837 blockage_section: blockage_start . blockage_defs blockage_end

    $default  reduce using rule 840 (blockage_defs)

    blockage_defs  go to state 174


State 110

   28 rule: slot_section .

    $default  reduce using rule 28 (rule)


State 111

  873 slot_section: slot_start . slot_defs slot_end

    $default  reduce using rule 876 (slot_defs)

    slot_defs  go to state 175


State 112

   17 rule: fill_section .

    $default  reduce using rule 17 (rule)


State 113

  887 fill_section: fill_start . fill_defs fill_end

    $default  reduce using rule 890 (fill_defs)

    fill_defs  go to state 176


State 114

   23 rule: nondefaultrule_section .

    $default  reduce using rule 23 (rule)


State 115

  917 nondefaultrule_section: nondefault_start . nondefault_def nondefault_defs nondefault_end

    '-'  shift, and go to state 177

    nondefault_def  go to state 178


State 116

   30 rule: styles_section .

    $default  reduce using rule 30 (rule)


State 117

  950 styles_section: styles_start . styles_rules styles_end

    $default  reduce using rule 953 (styles_rules)

    styles_rules  go to state 179


State 118

   51 design_name: K_DESIGN $@2 . T_STRING ';'

    T_STRING  shift, and go to state 180


State 119

  220 via: K_VIAS NUMBER . ';'

    ';'  shift, and go to state 181


State 120

   54 tech_name: K_TECH $@3 . T_STRING ';'

    T_STRING  shift, and go to state 182


State 121

   92 units: K_UNITS K_DISTANCE . K_MICRONS NUMBER ';'

    K_MICRONS  shift, and go to state 183


State 122

   56 array_name: K_ARRAY $@4 . T_STRING ';'

    T_STRING  shift, and go to state 184


State 123

   58 floorplan_name: K_FLOORPLAN $@5 . T_STRING ';'

    T_STRING  shift, and go to state 185


State 124

   96 canplace: K_CANPLACE $@18 . T_STRING NUMBER NUMBER orient K_DO NUMBER K_BY NUMBER K_STEP NUMBER NUMBER ';'

    T_STRING  shift, and go to state 186


State 125

   98 cannotoccupy: K_CANNOTOCCUPY $@19 . T_STRING NUMBER NUMBER orient K_DO NUMBER K_BY NUMBER K_STEP NUMBER NUMBER ';'

    T_STRING  shift, and go to state 187


State 126

  108 die_area: K_DIEAREA $@20 . firstPt nextPt otherPts ';'

    '('  shift, and go to state 188

    firstPt  go to state 189
    pt       go to state 190


State 127

  116 start_pins: K_PINS NUMBER . ';'

    ';'  shift, and go to state 191


State 128

  110 start_def_cap: K_DEFAULTCAP NUMBER .

    $default  reduce using rule 110 (start_def_cap)


State 129

  203 track_type: K_X .

    $default  reduce using rule 203 (track_type)


State 130

  204 track_type: K_Y .

    $default  reduce using rule 204 (track_type)


State 131

  202 track_start: K_TRACKS track_type .

    $default  reduce using rule 202 (track_start)


State 132

  216 gcellgrid: K_GCELLGRID track_type . NUMBER K_DO NUMBER K_STEP NUMBER ';'

    NUMBER  shift, and go to state 192


State 133

  186 row_rule: K_ROW $@35 . T_STRING T_STRING NUMBER NUMBER orient $@36 row_do_option row_options ';'

    T_STRING  shift, and go to state 193


State 134

  279 start_comps: K_COMPS NUMBER . ';'

    ';'  shift, and go to state 194


State 135

  257 regions_start: K_REGIONS NUMBER . ';'

    ';'  shift, and go to state 195


State 136

  351 start_nets: K_NETS NUMBER . ';'

    ';'  shift, and go to state 196


State 137

  572 start_snets: K_SNETS NUMBER . ';'

    ';'  shift, and go to state 197


State 138

  918 nondefault_start: K_NONDEFAULTRULES NUMBER . ';'

    ';'  shift, and go to state 198


State 139

  575 groups_start: K_GROUPS NUMBER . ';'

    ';'  shift, and go to state 199


State 140

  608 constraints_start: K_CONSTRAINTS NUMBER . ';'

    ';'  shift, and go to state 200


State 141

  636 scanchain_start: K_SCANCHAINS NUMBER . ';'

    ';'  shift, and go to state 201


State 142

  607 assertions_start: K_ASSERTIONS NUMBER . ';'

    ';'  shift, and go to state 202


State 143

   52 end_design: K_END K_DESIGN .

    $default  reduce using rule 52 (end_design)


State 144

  682 iotiming_start: K_IOTIMINGS NUMBER . ';'

    ';'  shift, and go to state 203


State 145

  734 timingdisables_start: K_TIMINGDISABLES NUMBER . ';'

    ';'  shift, and go to state 204


State 146

  752 partitions_start: K_PARTITIONS NUMBER . ';'

    ';'  shift, and go to state 205


State 147

  709 fp_start: K_FPC NUMBER . ';'

    ';'  shift, and go to state 206


State 148

   93 divider_char: K_DIVIDERCHAR QSTRING . ';'

    ';'  shift, and go to state 207


State 149

   94 bus_bit_chars: K_BUSBITCHARS QSTRING . ';'

    ';'  shift, and go to state 208


State 150

   61 prop_def_section: K_PROPERTYDEFINITIONS $@6 . property_defs K_END K_PROPERTYDEFINITIONS

    $default  reduce using rule 62 (property_defs)

    property_defs  go to state 209


State 151

  277 comps_maskShift_section: K_COMPSMASKSHIFT layer_statement . ';'
  281 layer_statement: layer_statement . maskLayer

    T_STRING  shift, and go to state 210
    ';'       shift, and go to state 211

    maskLayer  go to state 212


State 152

  819 begin_pin_props: K_PINPROPERTIES NUMBER . opt_semi

    ';'  shift, and go to state 213

    $default  reduce using rule 820 (opt_semi)

    opt_semi  go to state 214


State 153

  838 blockage_start: K_BLOCKAGES NUMBER . ';'

    ';'  shift, and go to state 215


State 154

  874 slot_start: K_SLOTS NUMBER . ';'

    ';'  shift, and go to state 216


State 155

  888 fill_start: K_FILLS NUMBER . ';'

    ';'  shift, and go to state 217


State 156

  951 styles_start: K_STYLES NUMBER . ';'

    ';'  shift, and go to state 218


State 157

  109 pin_cap_rule: start_def_cap pin_caps . end_def_cap
  112 pin_caps: pin_caps . pin_cap

    K_MINPINS  shift, and go to state 219
    K_END      shift, and go to state 220

    pin_cap      go to state 221
    end_def_cap  go to state 222


State 158

  115 pin_rule: start_pins pins . end_pins
  118 pins: pins . pin

    K_END  shift, and go to state 223
    '-'    shift, and go to state 224

    pin       go to state 225
    end_pins  go to state 226


State 159

  201 tracks_rule: track_start NUMBER . $@38 K_DO NUMBER K_STEP NUMBER track_opts ';'

    $default  reduce using rule 200 ($@38)

    $@38  go to state 227


State 160

  219 via_section: via via_declarations . via_end
  222 via_declarations: via_declarations . via_declaration

    K_END  shift, and go to state 228
    '-'    shift, and go to state 229

    via_declaration  go to state 230
    via_end          go to state 231


State 161

  256 regions_section: regions_start regions_stmts . K_END K_REGIONS
  259 regions_stmts: regions_stmts . regions_stmt

    K_END  shift, and go to state 232
    '-'    shift, and go to state 233

    regions_stmt  go to state 234


State 162

  278 comps_section: start_comps comps_rule . end_comps
  284 comps_rule: comps_rule . comp

    K_END  shift, and go to state 235
    '-'    shift, and go to state 236

    comp              go to state 237
    comp_start        go to state 238
    comp_id_and_name  go to state 239
    end_comps         go to state 240


State 163

  350 nets_section: start_nets net_rules . end_nets
  353 net_rules: net_rules . one_net

    K_END  shift, and go to state 241
    '-'    shift, and go to state 242

    one_net              go to state 243
    net_and_connections  go to state 244
    net_start            go to state 245
    end_nets             go to state 246


State 164

  493 snets_section: start_snets snet_rules . end_snets
  495 snet_rules: snet_rules . snet_rule

    K_END  shift, and go to state 247
    '-'    shift, and go to state 242

    net_and_connections  go to state 248
    net_start            go to state 245
    snet_rule            go to state 249
    end_snets            go to state 250


State 165

  574 groups_section: groups_start group_rules . groups_end
  577 group_rules: group_rules . group_rule

    K_END  shift, and go to state 251
    '-'    shift, and go to state 252

    group_rule   go to state 253
    start_group  go to state 254
    groups_end   go to state 255


State 166

  605 assertions_section: assertions_start constraint_rules . assertions_end
  610 constraint_rules: constraint_rules . constraint_rule

    K_END  shift, and go to state 256
    '-'    shift, and go to state 257

    constraint_rule  go to state 258
    operand_rule     go to state 259
    wiredlogic_rule  go to state 260
    assertions_end   go to state 261


State 167

  606 constraint_section: constraints_start constraint_rules . constraints_end
  610 constraint_rules: constraint_rules . constraint_rule

    K_END  shift, and go to state 262
    '-'    shift, and go to state 257

    constraint_rule  go to state 258
    operand_rule     go to state 259
    wiredlogic_rule  go to state 260
    constraints_end  go to state 263


State 168

  635 scanchains_section: scanchain_start scanchain_rules . scanchain_end
  638 scanchain_rules: scanchain_rules . scan_rule

    K_END  shift, and go to state 264
    '-'    shift, and go to state 265

    scan_rule      go to state 266
    start_scan     go to state 267
    scanchain_end  go to state 268


State 169

  681 iotiming_section: iotiming_start iotiming_rules . iotiming_end
  684 iotiming_rules: iotiming_rules . iotiming_rule

    K_END  shift, and go to state 269
    '-'    shift, and go to state 270

    iotiming_rule   go to state 271
    start_iotiming  go to state 272
    iotiming_end    go to state 273


State 170

  708 floorplan_contraints_section: fp_start fp_stmts . K_END K_FPC
  711 fp_stmts: fp_stmts . fp_stmt

    K_END  shift, and go to state 274
    '-'    shift, and go to state 275

    fp_stmt  go to state 276


State 171

  733 timingdisables_section: timingdisables_start timingdisables_rules . timingdisables_end
  736 timingdisables_rules: timingdisables_rules . timingdisables_rule

    K_END  shift, and go to state 277
    '-'    shift, and go to state 278

    timingdisables_rule  go to state 279
    timingdisables_end   go to state 280


State 172

  751 partitions_section: partitions_start partition_rules . partitions_end
  754 partition_rules: partition_rules . partition_rule

    K_END  shift, and go to state 281
    '-'    shift, and go to state 282

    partition_rule   go to state 283
    start_partition  go to state 284
    partitions_end   go to state 285


State 173

  818 pin_props_section: begin_pin_props pin_prop_list . end_pin_props
  824 pin_prop_list: pin_prop_list . pin_prop_terminal

    K_END  shift, and go to state 286
    '-'    shift, and go to state 287

    end_pin_props      go to state 288
    pin_prop_terminal  go to state 289


State 174

  837 blockage_section: blockage_start blockage_defs . blockage_end
  841 blockage_defs: blockage_defs . blockage_def

    K_END  shift, and go to state 290
    '-'    shift, and go to state 291

    blockage_end   go to state 292
    blockage_def   go to state 293
    blockage_rule  go to state 294


State 175

  873 slot_section: slot_start slot_defs . slot_end
  877 slot_defs: slot_defs . slot_def

    K_END  shift, and go to state 295
    '-'    shift, and go to state 296

    slot_end   go to state 297
    slot_def   go to state 298
    slot_rule  go to state 299


State 176

  887 fill_section: fill_start fill_defs . fill_end
  891 fill_defs: fill_defs . fill_def

    K_END  shift, and go to state 300
    '-'    shift, and go to state 301

    fill_end   go to state 302
    fill_def   go to state 303
    fill_rule  go to state 304


State 177

  924 nondefault_def: '-' . $@166 T_STRING $@167 nondefault_options ';'

    $default  reduce using rule 922 ($@166)

    $@166  go to state 305


State 178

  917 nondefaultrule_section: nondefault_start nondefault_def . nondefault_defs nondefault_end

    $default  reduce using rule 920 (nondefault_defs)

    nondefault_defs  go to state 306


State 179

  950 styles_section: styles_start styles_rules . styles_end
  954 styles_rules: styles_rules . styles_rule

    K_END  shift, and go to state 307
    '-'    shift, and go to state 308

    styles_end   go to state 309
    styles_rule  go to state 310


State 180

   51 design_name: K_DESIGN $@2 T_STRING . ';'

    ';'  shift, and go to state 311


State 181

  220 via: K_VIAS NUMBER ';' .

    $default  reduce using rule 220 (via)


State 182

   54 tech_name: K_TECH $@3 T_STRING . ';'

    ';'  shift, and go to state 312


State 183

   92 units: K_UNITS K_DISTANCE K_MICRONS . NUMBER ';'

    NUMBER  shift, and go to state 313


State 184

   56 array_name: K_ARRAY $@4 T_STRING . ';'

    ';'  shift, and go to state 314


State 185

   58 floorplan_name: K_FLOORPLAN $@5 T_STRING . ';'

    ';'  shift, and go to state 315


State 186

   96 canplace: K_CANPLACE $@18 T_STRING . NUMBER NUMBER orient K_DO NUMBER K_BY NUMBER K_STEP NUMBER NUMBER ';'

    NUMBER  shift, and go to state 316


State 187

   98 cannotoccupy: K_CANNOTOCCUPY $@19 T_STRING . NUMBER NUMBER orient K_DO NUMBER K_BY NUMBER K_STEP NUMBER NUMBER ';'

    NUMBER  shift, and go to state 317


State 188

  249 pt: '(' . NUMBER NUMBER ')'
  250   | '(' . '*' NUMBER ')'
  251   | '(' . NUMBER '*' ')'
  252   | '(' . '*' '*' ')'

    NUMBER  shift, and go to state 318
    '*'     shift, and go to state 319


State 189

  108 die_area: K_DIEAREA $@20 firstPt . nextPt otherPts ';'

    '('  shift, and go to state 188

    nextPt  go to state 320
    pt      go to state 321


State 190

  245 firstPt: pt .

    $default  reduce using rule 245 (firstPt)


State 191

  116 start_pins: K_PINS NUMBER ';' .

    $default  reduce using rule 116 (start_pins)


State 192

  216 gcellgrid: K_GCELLGRID track_type NUMBER . K_DO NUMBER K_STEP NUMBER ';'

    K_DO  shift, and go to state 322


State 193

  186 row_rule: K_ROW $@35 T_STRING . T_STRING NUMBER NUMBER orient $@36 row_do_option row_options ';'

    T_STRING  shift, and go to state 323


State 194

  279 start_comps: K_COMPS NUMBER ';' .

    $default  reduce using rule 279 (start_comps)


State 195

  257 regions_start: K_REGIONS NUMBER ';' .

    $default  reduce using rule 257 (regions_start)


State 196

  351 start_nets: K_NETS NUMBER ';' .

    $default  reduce using rule 351 (start_nets)


State 197

  572 start_snets: K_SNETS NUMBER ';' .

    $default  reduce using rule 572 (start_snets)


State 198

  918 nondefault_start: K_NONDEFAULTRULES NUMBER ';' .

    $default  reduce using rule 918 (nondefault_start)


State 199

  575 groups_start: K_GROUPS NUMBER ';' .

    $default  reduce using rule 575 (groups_start)


State 200

  608 constraints_start: K_CONSTRAINTS NUMBER ';' .

    $default  reduce using rule 608 (constraints_start)


State 201

  636 scanchain_start: K_SCANCHAINS NUMBER ';' .

    $default  reduce using rule 636 (scanchain_start)


State 202

  607 assertions_start: K_ASSERTIONS NUMBER ';' .

    $default  reduce using rule 607 (assertions_start)


State 203

  682 iotiming_start: K_IOTIMINGS NUMBER ';' .

    $default  reduce using rule 682 (iotiming_start)


State 204

  734 timingdisables_start: K_TIMINGDISABLES NUMBER ';' .

    $default  reduce using rule 734 (timingdisables_start)


State 205

  752 partitions_start: K_PARTITIONS NUMBER ';' .

    $default  reduce using rule 752 (partitions_start)


State 206

  709 fp_start: K_FPC NUMBER ';' .

    $default  reduce using rule 709 (fp_start)


State 207

   93 divider_char: K_DIVIDERCHAR QSTRING ';' .

    $default  reduce using rule 93 (divider_char)


State 208

   94 bus_bit_chars: K_BUSBITCHARS QSTRING ';' .

    $default  reduce using rule 94 (bus_bit_chars)


State 209

   61 prop_def_section: K_PROPERTYDEFINITIONS $@6 property_defs . K_END K_PROPERTYDEFINITIONS
   63 property_defs: property_defs . property_def

    error             shift, and go to state 324
    K_DESIGN          shift, and go to state 325
    K_ROW             shift, and go to state 326
    K_REGION          shift, and go to state 327
    K_NONDEFAULTRULE  shift, and go to state 328
    K_GROUP           shift, and go to state 329
    K_NET             shift, and go to state 330
    K_END             shift, and go to state 331
    K_COMPONENT       shift, and go to state 332
    K_SNET            shift, and go to state 333
    K_COMPONENTPIN    shift, and go to state 334

    property_def  go to state 335


State 210

  282 maskLayer: T_STRING .

    $default  reduce using rule 282 (maskLayer)


State 211

  277 comps_maskShift_section: K_COMPSMASKSHIFT layer_statement ';' .

    $default  reduce using rule 277 (comps_maskShift_section)


State 212

  281 layer_statement: layer_statement maskLayer .

    $default  reduce using rule 281 (layer_statement)


State 213

  821 opt_semi: ';' .

    $default  reduce using rule 821 (opt_semi)


State 214

  819 begin_pin_props: K_PINPROPERTIES NUMBER opt_semi .

    $default  reduce using rule 819 (begin_pin_props)


State 215

  838 blockage_start: K_BLOCKAGES NUMBER ';' .

    $default  reduce using rule 838 (blockage_start)


State 216

  874 slot_start: K_SLOTS NUMBER ';' .

    $default  reduce using rule 874 (slot_start)


State 217

  888 fill_start: K_FILLS NUMBER ';' .

    $default  reduce using rule 888 (fill_start)


State 218

  951 styles_start: K_STYLES NUMBER ';' .

    $default  reduce using rule 951 (styles_start)


State 219

  113 pin_cap: K_MINPINS . NUMBER K_WIRECAP NUMBER ';'

    NUMBER  shift, and go to state 336


State 220

  114 end_def_cap: K_END . K_DEFAULTCAP

    K_DEFAULTCAP  shift, and go to state 337


State 221

  112 pin_caps: pin_caps pin_cap .

    $default  reduce using rule 112 (pin_caps)


State 222

  109 pin_cap_rule: start_def_cap pin_caps end_def_cap .

    $default  reduce using rule 109 (pin_cap_rule)


State 223

  183 end_pins: K_END . K_PINS

    K_PINS  shift, and go to state 338


State 224

  122 pin: '-' . $@21 T_STRING '+' K_NET $@22 T_STRING $@23 pin_options ';'

    $default  reduce using rule 119 ($@21)

    $@21  go to state 339


State 225

  118 pins: pins pin .

    $default  reduce using rule 118 (pins)


State 226

  115 pin_rule: start_pins pins end_pins .

    $default  reduce using rule 115 (pin_rule)


State 227

  201 tracks_rule: track_start NUMBER $@38 . K_DO NUMBER K_STEP NUMBER track_opts ';'

    K_DO  shift, and go to state 340


State 228

  255 via_end: K_END . K_VIAS

    K_VIAS  shift, and go to state 341


State 229

  225 via_declaration: '-' . $@40 T_STRING $@41 layer_stmts ';'

    $default  reduce using rule 223 ($@40)

    $@40  go to state 342


State 230

  222 via_declarations: via_declarations via_declaration .

    $default  reduce using rule 222 (via_declarations)


State 231

  219 via_section: via via_declarations via_end .

    $default  reduce using rule 219 (via_section)


State 232

  256 regions_section: regions_start regions_stmts K_END . K_REGIONS

    K_REGIONS  shift, and go to state 343


State 233

  262 regions_stmt: '-' . $@49 T_STRING $@50 rect_list region_options ';'

    $default  reduce using rule 260 ($@49)

    $@49  go to state 344


State 234

  259 regions_stmts: regions_stmts regions_stmt .

    $default  reduce using rule 259 (regions_stmts)


State 235

  349 end_comps: K_END . K_COMPS

    K_COMPS  shift, and go to state 345


State 236

  288 comp_id_and_name: '-' . $@52 T_STRING T_STRING

    $default  reduce using rule 287 ($@52)

    $@52  go to state 346


State 237

  284 comps_rule: comps_rule comp .

    $default  reduce using rule 284 (comps_rule)


State 238

  285 comp: comp_start . comp_options ';'

    $default  reduce using rule 292 (comp_options)

    comp_options  go to state 347


State 239

  286 comp_start: comp_id_and_name . comp_net_list

    $default  reduce using rule 289 (comp_net_list)

    comp_net_list  go to state 348


State 240

  278 comps_section: start_comps comps_rule end_comps .

    $default  reduce using rule 278 (comps_section)


State 241

  480 end_nets: K_END . K_NETS

    K_NETS  shift, and go to state 349


State 242

  357 net_start: '-' . $@60 net_name

    $default  reduce using rule 356 ($@60)

    $@60  go to state 350


State 243

  353 net_rules: net_rules one_net .

    $default  reduce using rule 353 (net_rules)


State 244

  354 one_net: net_and_connections . net_options ';'

    $default  reduce using rule 373 (net_options)

    net_options  go to state 351


State 245

  355 net_and_connections: net_start .

    $default  reduce using rule 355 (net_and_connections)


State 246

  350 nets_section: start_nets net_rules end_nets .

    $default  reduce using rule 350 (nets_section)


State 247

  573 end_snets: K_END . K_SNETS

    K_SNETS  shift, and go to state 352


State 248

  496 snet_rule: net_and_connections . snet_options ';'

    $default  reduce using rule 497 (snet_options)

    snet_options  go to state 353


State 249

  495 snet_rules: snet_rules snet_rule .

    $default  reduce using rule 495 (snet_rules)


State 250

  493 snets_section: start_snets snet_rules end_snets .

    $default  reduce using rule 493 (snets_section)


State 251

  604 groups_end: K_END . K_GROUPS

    K_GROUPS  shift, and go to state 354


State 252

  580 start_group: '-' . $@104 T_STRING

    $default  reduce using rule 579 ($@104)

    $@104  go to state 355


State 253

  577 group_rules: group_rules group_rule .

    $default  reduce using rule 577 (group_rules)


State 254

  578 group_rule: start_group . group_members group_options ';'

    $default  reduce using rule 581 (group_members)

    group_members  go to state 356


State 255

  574 groups_section: groups_start group_rules groups_end .

    $default  reduce using rule 574 (groups_section)


State 256

  634 assertions_end: K_END . K_ASSERTIONS

    K_ASSERTIONS  shift, and go to state 357


State 257

  613 operand_rule: '-' . operand delay_specs ';'
  624 wiredlogic_rule: '-' . K_WIREDLOGIC $@109 T_STRING opt_plus K_MAXDIST NUMBER ';'

    K_NET         shift, and go to state 358
    K_PATH        shift, and go to state 359
    K_SUM         shift, and go to state 360
    K_DIFF        shift, and go to state 361
    K_WIREDLOGIC  shift, and go to state 362

    operand  go to state 363


State 258

  610 constraint_rules: constraint_rules constraint_rule .

    $default  reduce using rule 610 (constraint_rules)


State 259

  611 constraint_rule: operand_rule .

    $default  reduce using rule 611 (constraint_rule)


State 260

  612 constraint_rule: wiredlogic_rule .

    $default  reduce using rule 612 (constraint_rule)


State 261

  605 assertions_section: assertions_start constraint_rules assertions_end .

    $default  reduce using rule 605 (assertions_section)


State 262

  633 constraints_end: K_END . K_CONSTRAINTS

    K_CONSTRAINTS  shift, and go to state 364


State 263

  606 constraint_section: constraints_start constraint_rules constraints_end .

    $default  reduce using rule 606 (constraint_section)


State 264

  680 scanchain_end: K_END . K_SCANCHAINS

    K_SCANCHAINS  shift, and go to state 365


State 265

  641 start_scan: '-' . $@110 T_STRING

    $default  reduce using rule 640 ($@110)

    $@110  go to state 366


State 266

  638 scanchain_rules: scanchain_rules scan_rule .

    $default  reduce using rule 638 (scanchain_rules)


State 267

  639 scan_rule: start_scan . scan_members ';'

    $default  reduce using rule 642 (scan_members)

    scan_members  go to state 367


State 268

  635 scanchains_section: scanchain_start scanchain_rules scanchain_end .

    $default  reduce using rule 635 (scanchains_section)


State 269

  707 iotiming_end: K_END . K_IOTIMINGS

    K_IOTIMINGS  shift, and go to state 368


State 270

  687 start_iotiming: '-' . '(' $@119 T_STRING T_STRING ')'

    '('  shift, and go to state 369


State 271

  684 iotiming_rules: iotiming_rules iotiming_rule .

    $default  reduce using rule 684 (iotiming_rules)


State 272

  685 iotiming_rule: start_iotiming . iotiming_members ';'

    $default  reduce using rule 688 (iotiming_members)

    iotiming_members  go to state 370


State 273

  681 iotiming_section: iotiming_start iotiming_rules iotiming_end .

    $default  reduce using rule 681 (iotiming_section)


State 274

  708 floorplan_contraints_section: fp_start fp_stmts K_END . K_FPC

    K_FPC  shift, and go to state 371


State 275

  714 fp_stmt: '-' . $@125 T_STRING h_or_v $@126 constraint_type constrain_what_list ';'

    $default  reduce using rule 712 ($@125)

    $@125  go to state 372


State 276

  711 fp_stmts: fp_stmts fp_stmt .

    $default  reduce using rule 711 (fp_stmts)


State 277

  750 timingdisables_end: K_END . K_TIMINGDISABLES

    K_TIMINGDISABLES  shift, and go to state 373


State 278

  739 timingdisables_rule: '-' . K_FROMPIN $@131 T_STRING T_STRING K_TOPIN $@132 T_STRING T_STRING ';'
  741                    | '-' . K_THRUPIN $@133 T_STRING T_STRING ';'
  743                    | '-' . K_MACRO $@134 T_STRING td_macro_option ';'
  744                    | '-' . K_REENTRANTPATHS ';'

    K_FROMPIN         shift, and go to state 374
    K_THRUPIN         shift, and go to state 375
    K_MACRO           shift, and go to state 376
    K_REENTRANTPATHS  shift, and go to state 377


State 279

  736 timingdisables_rules: timingdisables_rules timingdisables_rule .

    $default  reduce using rule 736 (timingdisables_rules)


State 280

  733 timingdisables_section: timingdisables_start timingdisables_rules timingdisables_end .

    $default  reduce using rule 733 (timingdisables_section)


State 281

  801 partitions_end: K_END . K_PARTITIONS

    K_PARTITIONS  shift, and go to state 378


State 282

  757 start_partition: '-' . $@138 T_STRING turnoff

    $default  reduce using rule 756 ($@138)

    $@138  go to state 379


State 283

  754 partition_rules: partition_rules partition_rule .

    $default  reduce using rule 754 (partition_rules)


State 284

  755 partition_rule: start_partition . partition_members ';'

    $default  reduce using rule 766 (partition_members)

    partition_members  go to state 380


State 285

  751 partitions_section: partitions_start partition_rules partitions_end .

    $default  reduce using rule 751 (partitions_section)


State 286

  822 end_pin_props: K_END . K_PINPROPERTIES

    K_PINPROPERTIES  shift, and go to state 381


State 287

  827 pin_prop_terminal: '-' . $@149 T_STRING T_STRING $@150 pin_prop_options ';'

    $default  reduce using rule 825 ($@149)

    $@149  go to state 382


State 288

  818 pin_props_section: begin_pin_props pin_prop_list end_pin_props .

    $default  reduce using rule 818 (pin_props_section)


State 289

  824 pin_prop_list: pin_prop_list pin_prop_terminal .

    $default  reduce using rule 824 (pin_prop_list)


State 290

  839 blockage_end: K_END . K_BLOCKAGES

    K_BLOCKAGES  shift, and go to state 383


State 291

  845 blockage_rule: '-' . K_LAYER $@152 T_STRING $@153 layer_blockage_rules
  847              | '-' . K_PLACEMENT $@154 placement_comp_rules

    K_LAYER      shift, and go to state 384
    K_PLACEMENT  shift, and go to state 385


State 292

  837 blockage_section: blockage_start blockage_defs blockage_end .

    $default  reduce using rule 837 (blockage_section)


State 293

  841 blockage_defs: blockage_defs blockage_def .

    $default  reduce using rule 841 (blockage_defs)


State 294

  842 blockage_def: blockage_rule . rectPoly_blockage rectPoly_blockage_rules ';'

    K_RECT     shift, and go to state 386
    K_POLYGON  shift, and go to state 387

    rectPoly_blockage  go to state 388


State 295

  875 slot_end: K_END . K_SLOTS

    K_SLOTS  shift, and go to state 389


State 296

  881 slot_rule: '-' . K_LAYER $@158 T_STRING $@159 geom_slot

    K_LAYER  shift, and go to state 390


State 297

  873 slot_section: slot_start slot_defs slot_end .

    $default  reduce using rule 873 (slot_section)


State 298

  877 slot_defs: slot_defs slot_def .

    $default  reduce using rule 877 (slot_defs)


State 299

  878 slot_def: slot_rule . geom_slot_rules ';'

    $default  reduce using rule 882 (geom_slot_rules)

    geom_slot_rules  go to state 391


State 300

  889 fill_end: K_END . K_FILLS

    K_FILLS  shift, and go to state 392


State 301

  895 fill_def: '-' . K_VIA $@161 T_STRING $@162 fill_via_mask_opc_opt fill_via_pt ';'
  898 fill_rule: '-' . K_LAYER $@163 T_STRING $@164 fill_layer_mask_opc_opt geom_fill

    K_LAYER  shift, and go to state 393
    K_VIA    shift, and go to state 394


State 302

  887 fill_section: fill_start fill_defs fill_end .

    $default  reduce using rule 887 (fill_section)


State 303

  891 fill_defs: fill_defs fill_def .

    $default  reduce using rule 891 (fill_defs)


State 304

  892 fill_def: fill_rule . geom_fill_rules ';'

    $default  reduce using rule 899 (geom_fill_rules)

    geom_fill_rules  go to state 395


State 305

  924 nondefault_def: '-' $@166 . T_STRING $@167 nondefault_options ';'

    T_STRING  shift, and go to state 396


State 306

  917 nondefaultrule_section: nondefault_start nondefault_def nondefault_defs . nondefault_end
  921 nondefault_defs: nondefault_defs . nondefault_def

    K_END  shift, and go to state 397
    '-'    shift, and go to state 177

    nondefault_end  go to state 398
    nondefault_def  go to state 399


State 307

  952 styles_end: K_END . K_STYLES

    K_STYLES  shift, and go to state 400


State 308

  956 styles_rule: '-' . K_STYLE NUMBER $@174 firstPt nextPt otherPts ';'

    K_STYLE  shift, and go to state 401


State 309

  950 styles_section: styles_start styles_rules styles_end .

    $default  reduce using rule 950 (styles_section)


State 310

  954 styles_rules: styles_rules styles_rule .

    $default  reduce using rule 954 (styles_rules)


State 311

   51 design_name: K_DESIGN $@2 T_STRING ';' .

    $default  reduce using rule 51 (design_name)


State 312

   54 tech_name: K_TECH $@3 T_STRING ';' .

    $default  reduce using rule 54 (tech_name)


State 313

   92 units: K_UNITS K_DISTANCE K_MICRONS NUMBER . ';'

    ';'  shift, and go to state 402


State 314

   56 array_name: K_ARRAY $@4 T_STRING ';' .

    $default  reduce using rule 56 (array_name)


State 315

   58 floorplan_name: K_FLOORPLAN $@5 T_STRING ';' .

    $default  reduce using rule 58 (floorplan_name)


State 316

   96 canplace: K_CANPLACE $@18 T_STRING NUMBER . NUMBER orient K_DO NUMBER K_BY NUMBER K_STEP NUMBER NUMBER ';'

    NUMBER  shift, and go to state 403


State 317

   98 cannotoccupy: K_CANNOTOCCUPY $@19 T_STRING NUMBER . NUMBER orient K_DO NUMBER K_BY NUMBER K_STEP NUMBER NUMBER ';'

    NUMBER  shift, and go to state 404


State 318

  249 pt: '(' NUMBER . NUMBER ')'
  251   | '(' NUMBER . '*' ')'

    NUMBER  shift, and go to state 405
    '*'     shift, and go to state 406


State 319

  250 pt: '(' '*' . NUMBER ')'
  252   | '(' '*' . '*' ')'

    NUMBER  shift, and go to state 407
    '*'     shift, and go to state 408


State 320

  108 die_area: K_DIEAREA $@20 firstPt nextPt . otherPts ';'

    $default  reduce using rule 247 (otherPts)

    otherPts  go to state 409


State 321

  246 nextPt: pt .

    $default  reduce using rule 246 (nextPt)


State 322

  216 gcellgrid: K_GCELLGRID track_type NUMBER K_DO . NUMBER K_STEP NUMBER ';'

    NUMBER  shift, and go to state 410


State 323

  186 row_rule: K_ROW $@35 T_STRING T_STRING . NUMBER NUMBER orient $@36 row_do_option row_options ';'

    NUMBER  shift, and go to state 411


State 324

   82 property_def: error . ';'

    ';'  shift, and go to state 412


State 325

   65 property_def: K_DESIGN . $@7 T_STRING property_type_and_val ';'

    $default  reduce using rule 64 ($@7)

    $@7  go to state 413


State 326

   77 property_def: K_ROW . $@13 T_STRING property_type_and_val ';'

    $default  reduce using rule 76 ($@13)

    $@13  go to state 414


State 327

   71 property_def: K_REGION . $@10 T_STRING property_type_and_val ';'

    $default  reduce using rule 70 ($@10)

    $@10  go to state 415


State 328

   81 property_def: K_NONDEFAULTRULE . $@15 T_STRING property_type_and_val ';'

    $default  reduce using rule 80 ($@15)

    $@15  go to state 416


State 329

   73 property_def: K_GROUP . $@11 T_STRING property_type_and_val ';'

    $default  reduce using rule 72 ($@11)

    $@11  go to state 417


State 330

   67 property_def: K_NET . $@8 T_STRING property_type_and_val ';'

    $default  reduce using rule 66 ($@8)

    $@8  go to state 418


State 331

   61 prop_def_section: K_PROPERTYDEFINITIONS $@6 property_defs K_END . K_PROPERTYDEFINITIONS

    K_PROPERTYDEFINITIONS  shift, and go to state 419


State 332

   75 property_def: K_COMPONENT . $@12 T_STRING property_type_and_val ';'

    $default  reduce using rule 74 ($@12)

    $@12  go to state 420


State 333

   69 property_def: K_SNET . $@9 T_STRING property_type_and_val ';'

    $default  reduce using rule 68 ($@9)

    $@9  go to state 421


State 334

   79 property_def: K_COMPONENTPIN . $@14 T_STRING property_type_and_val ';'

    $default  reduce using rule 78 ($@14)

    $@14  go to state 422


State 335

   63 property_defs: property_defs property_def .

    $default  reduce using rule 63 (property_defs)


State 336

  113 pin_cap: K_MINPINS NUMBER . K_WIRECAP NUMBER ';'

    K_WIRECAP  shift, and go to state 423


State 337

  114 end_def_cap: K_END K_DEFAULTCAP .

    $default  reduce using rule 114 (end_def_cap)


State 338

  183 end_pins: K_END K_PINS .

    $default  reduce using rule 183 (end_pins)


State 339

  122 pin: '-' $@21 . T_STRING '+' K_NET $@22 T_STRING $@23 pin_options ';'

    T_STRING  shift, and go to state 424


State 340

  201 tracks_rule: track_start NUMBER $@38 K_DO . NUMBER K_STEP NUMBER track_opts ';'

    NUMBER  shift, and go to state 425


State 341

  255 via_end: K_END K_VIAS .

    $default  reduce using rule 255 (via_end)


State 342

  225 via_declaration: '-' $@40 . T_STRING $@41 layer_stmts ';'

    T_STRING  shift, and go to state 426


State 343

  256 regions_section: regions_start regions_stmts K_END K_REGIONS .

    $default  reduce using rule 256 (regions_section)


State 344

  262 regions_stmt: '-' $@49 . T_STRING $@50 rect_list region_options ';'

    T_STRING  shift, and go to state 427


State 345

  349 end_comps: K_END K_COMPS .

    $default  reduce using rule 349 (end_comps)


State 346

  288 comp_id_and_name: '-' $@52 . T_STRING T_STRING

    T_STRING  shift, and go to state 428


State 347

  285 comp: comp_start comp_options . ';'
  293 comp_options: comp_options . comp_option

    ';'  shift, and go to state 429
    '+'  shift, and go to state 430

    extension_stmt       go to state 431
    comp_option          go to state 432
    comp_extension_stmt  go to state 433
    comp_eeq             go to state 434
    comp_generate        go to state 435
    comp_source          go to state 436
    comp_region          go to state 437
    comp_halo            go to state 438
    comp_routehalo       go to state 439
    comp_property        go to state 440
    comp_region_start    go to state 441
    comp_foreign         go to state 442
    comp_type            go to state 443
    maskShift            go to state 444
    placement_status     go to state 445
    weight               go to state 446


State 348

  286 comp_start: comp_id_and_name comp_net_list .
  290 comp_net_list: comp_net_list . '*'
  291              | comp_net_list . T_STRING

    T_STRING  shift, and go to state 447
    '*'       shift, and go to state 448

    $default  reduce using rule 286 (comp_start)


State 349

  480 end_nets: K_END K_NETS .

    $default  reduce using rule 480 (end_nets)


State 350

  357 net_start: '-' $@60 . net_name

    T_STRING    shift, and go to state 449
    K_MUSTJOIN  shift, and go to state 450

    net_name  go to state 451


State 351

  354 one_net: net_and_connections net_options . ';'
  374 net_options: net_options . net_option

    ';'  shift, and go to state 452
    '+'  shift, and go to state 453

    extension_stmt  go to state 454
    net_option      go to state 455
    vpin_stmt       go to state 456
    vpin_begin      go to state 457


State 352

  573 end_snets: K_END K_SNETS .

    $default  reduce using rule 573 (end_snets)


State 353

  496 snet_rule: net_and_connections snet_options . ';'
  498 snet_options: snet_options . snet_option

    ';'  shift, and go to state 458
    '+'  shift, and go to state 459

    extension_stmt     go to state 460
    snet_option        go to state 461
    snet_other_option  go to state 462
    snet_width         go to state 463
    snet_voltage       go to state 464
    snet_spacing       go to state 465


State 354

  604 groups_end: K_END K_GROUPS .

    $default  reduce using rule 604 (groups_end)


State 355

  580 start_group: '-' $@104 . T_STRING

    T_STRING  shift, and go to state 466


State 356

  578 group_rule: start_group group_members . group_options ';'
  582 group_members: group_members . group_member

    T_STRING  shift, and go to state 467

    $default  reduce using rule 584 (group_options)

    group_member   go to state 468
    group_options  go to state 469


State 357

  634 assertions_end: K_END K_ASSERTIONS .

    $default  reduce using rule 634 (assertions_end)


State 358

  615 operand: K_NET . $@107 T_STRING

    $default  reduce using rule 614 ($@107)

    $@107  go to state 470


State 359

  617 operand: K_PATH . $@108 T_STRING T_STRING T_STRING T_STRING

    $default  reduce using rule 616 ($@108)

    $@108  go to state 471


State 360

  618 operand: K_SUM . '(' operand_list ')'

    '('  shift, and go to state 472


State 361

  619 operand: K_DIFF . '(' operand_list ')'

    '('  shift, and go to state 473


State 362

  624 wiredlogic_rule: '-' K_WIREDLOGIC . $@109 T_STRING opt_plus K_MAXDIST NUMBER ';'

    $default  reduce using rule 623 ($@109)

    $@109  go to state 474


State 363

  613 operand_rule: '-' operand . delay_specs ';'

    $default  reduce using rule 627 (delay_specs)

    delay_specs  go to state 475


State 364

  633 constraints_end: K_END K_CONSTRAINTS .

    $default  reduce using rule 633 (constraints_end)


State 365

  680 scanchain_end: K_END K_SCANCHAINS .

    $default  reduce using rule 680 (scanchain_end)


State 366

  641 start_scan: '-' $@110 . T_STRING

    T_STRING  shift, and go to state 476


State 367

  639 scan_rule: start_scan scan_members . ';'
  643 scan_members: scan_members . scan_member

    ';'  shift, and go to state 477
    '+'  shift, and go to state 478

    extension_stmt  go to state 479
    scan_member     go to state 480


State 368

  707 iotiming_end: K_END K_IOTIMINGS .

    $default  reduce using rule 707 (iotiming_end)


State 369

  687 start_iotiming: '-' '(' . $@119 T_STRING T_STRING ')'

    $default  reduce using rule 686 ($@119)

    $@119  go to state 481


State 370

  685 iotiming_rule: start_iotiming iotiming_members . ';'
  689 iotiming_members: iotiming_members . iotiming_member

    ';'  shift, and go to state 482
    '+'  shift, and go to state 483

    extension_stmt   go to state 484
    iotiming_member  go to state 485


State 371

  708 floorplan_contraints_section: fp_start fp_stmts K_END K_FPC .

    $default  reduce using rule 708 (floorplan_contraints_section)


State 372

  714 fp_stmt: '-' $@125 . T_STRING h_or_v $@126 constraint_type constrain_what_list ';'

    T_STRING  shift, and go to state 486


State 373

  750 timingdisables_end: K_END K_TIMINGDISABLES .

    $default  reduce using rule 750 (timingdisables_end)


State 374

  739 timingdisables_rule: '-' K_FROMPIN . $@131 T_STRING T_STRING K_TOPIN $@132 T_STRING T_STRING ';'

    $default  reduce using rule 737 ($@131)

    $@131  go to state 487


State 375

  741 timingdisables_rule: '-' K_THRUPIN . $@133 T_STRING T_STRING ';'

    $default  reduce using rule 740 ($@133)

    $@133  go to state 488


State 376

  743 timingdisables_rule: '-' K_MACRO . $@134 T_STRING td_macro_option ';'

    $default  reduce using rule 742 ($@134)

    $@134  go to state 489


State 377

  744 timingdisables_rule: '-' K_REENTRANTPATHS . ';'

    ';'  shift, and go to state 490


State 378

  801 partitions_end: K_END K_PARTITIONS .

    $default  reduce using rule 801 (partitions_end)


State 379

  757 start_partition: '-' $@138 . T_STRING turnoff

    T_STRING  shift, and go to state 491


State 380

  755 partition_rule: start_partition partition_members . ';'
  767 partition_members: partition_members . partition_member

    ';'  shift, and go to state 492
    '+'  shift, and go to state 493

    extension_stmt    go to state 494
    partition_member  go to state 495


State 381

  822 end_pin_props: K_END K_PINPROPERTIES .

    $default  reduce using rule 822 (end_pin_props)


State 382

  827 pin_prop_terminal: '-' $@149 . T_STRING T_STRING $@150 pin_prop_options ';'

    T_STRING  shift, and go to state 496


State 383

  839 blockage_end: K_END K_BLOCKAGES .

    $default  reduce using rule 839 (blockage_end)


State 384

  845 blockage_rule: '-' K_LAYER . $@152 T_STRING $@153 layer_blockage_rules

    $default  reduce using rule 843 ($@152)

    $@152  go to state 497


State 385

  847 blockage_rule: '-' K_PLACEMENT . $@154 placement_comp_rules

    $default  reduce using rule 846 ($@154)

    $@154  go to state 498


State 386

  870 rectPoly_blockage: K_RECT . pt pt

    '('  shift, and go to state 188

    pt  go to state 499


State 387

  872 rectPoly_blockage: K_POLYGON . $@157 firstPt nextPt nextPt otherPts

    $default  reduce using rule 871 ($@157)

    $@157  go to state 500


State 388

  842 blockage_def: blockage_rule rectPoly_blockage . rectPoly_blockage_rules ';'

    $default  reduce using rule 868 (rectPoly_blockage_rules)

    rectPoly_blockage_rules  go to state 501


State 389

  875 slot_end: K_END K_SLOTS .

    $default  reduce using rule 875 (slot_end)


State 390

  881 slot_rule: '-' K_LAYER . $@158 T_STRING $@159 geom_slot

    $default  reduce using rule 879 ($@158)

    $@158  go to state 502


State 391

  878 slot_def: slot_rule geom_slot_rules . ';'
  883 geom_slot_rules: geom_slot_rules . geom_slot

    K_RECT     shift, and go to state 503
    K_POLYGON  shift, and go to state 504
    ';'        shift, and go to state 505

    geom_slot  go to state 506


State 392

  889 fill_end: K_END K_FILLS .

    $default  reduce using rule 889 (fill_end)


State 393

  898 fill_rule: '-' K_LAYER . $@163 T_STRING $@164 fill_layer_mask_opc_opt geom_fill

    $default  reduce using rule 896 ($@163)

    $@163  go to state 507


State 394

  895 fill_def: '-' K_VIA . $@161 T_STRING $@162 fill_via_mask_opc_opt fill_via_pt ';'

    $default  reduce using rule 893 ($@161)

    $@161  go to state 508


State 395

  892 fill_def: fill_rule geom_fill_rules . ';'
  900 geom_fill_rules: geom_fill_rules . geom_fill

    K_RECT     shift, and go to state 509
    K_POLYGON  shift, and go to state 510
    ';'        shift, and go to state 511

    geom_fill  go to state 512


State 396

  924 nondefault_def: '-' $@166 T_STRING . $@167 nondefault_options ';'

    $default  reduce using rule 923 ($@167)

    $@167  go to state 513


State 397

  919 nondefault_end: K_END . K_NONDEFAULTRULES

    K_NONDEFAULTRULES  shift, and go to state 514


State 398

  917 nondefaultrule_section: nondefault_start nondefault_def nondefault_defs nondefault_end .

    $default  reduce using rule 917 (nondefaultrule_section)


State 399

  921 nondefault_defs: nondefault_defs nondefault_def .

    $default  reduce using rule 921 (nondefault_defs)


State 400

  952 styles_end: K_END K_STYLES .

    $default  reduce using rule 952 (styles_end)


State 401

  956 styles_rule: '-' K_STYLE . NUMBER $@174 firstPt nextPt otherPts ';'

    NUMBER  shift, and go to state 515


State 402

   92 units: K_UNITS K_DISTANCE K_MICRONS NUMBER ';' .

    $default  reduce using rule 92 (units)


State 403

   96 canplace: K_CANPLACE $@18 T_STRING NUMBER NUMBER . orient K_DO NUMBER K_BY NUMBER K_STEP NUMBER NUMBER ';'

    K_N   shift, and go to state 516
    K_S   shift, and go to state 517
    K_E   shift, and go to state 518
    K_W   shift, and go to state 519
    K_FN  shift, and go to state 520
    K_FE  shift, and go to state 521
    K_FS  shift, and go to state 522
    K_FW  shift, and go to state 523

    orient  go to state 524


State 404

   98 cannotoccupy: K_CANNOTOCCUPY $@19 T_STRING NUMBER NUMBER . orient K_DO NUMBER K_BY NUMBER K_STEP NUMBER NUMBER ';'

    K_N   shift, and go to state 516
    K_S   shift, and go to state 517
    K_E   shift, and go to state 518
    K_W   shift, and go to state 519
    K_FN  shift, and go to state 520
    K_FE  shift, and go to state 521
    K_FS  shift, and go to state 522
    K_FW  shift, and go to state 523

    orient  go to state 525


State 405

  249 pt: '(' NUMBER NUMBER . ')'

    ')'  shift, and go to state 526


State 406

  251 pt: '(' NUMBER '*' . ')'

    ')'  shift, and go to state 527


State 407

  250 pt: '(' '*' NUMBER . ')'

    ')'  shift, and go to state 528


State 408

  252 pt: '(' '*' '*' . ')'

    ')'  shift, and go to state 529


State 409

  108 die_area: K_DIEAREA $@20 firstPt nextPt otherPts . ';'
  248 otherPts: otherPts . nextPt

    ';'  shift, and go to state 530
    '('  shift, and go to state 188

    nextPt  go to state 531
    pt      go to state 321


State 410

  216 gcellgrid: K_GCELLGRID track_type NUMBER K_DO NUMBER . K_STEP NUMBER ';'

    K_STEP  shift, and go to state 532


State 411

  186 row_rule: K_ROW $@35 T_STRING T_STRING NUMBER . NUMBER orient $@36 row_do_option row_options ';'

    NUMBER  shift, and go to state 533


State 412

   82 property_def: error ';' .

    $default  reduce using rule 82 (property_def)


State 413

   65 property_def: K_DESIGN $@7 . T_STRING property_type_and_val ';'

    T_STRING  shift, and go to state 534


State 414

   77 property_def: K_ROW $@13 . T_STRING property_type_and_val ';'

    T_STRING  shift, and go to state 535


State 415

   71 property_def: K_REGION $@10 . T_STRING property_type_and_val ';'

    T_STRING  shift, and go to state 536


State 416

   81 property_def: K_NONDEFAULTRULE $@15 . T_STRING property_type_and_val ';'

    T_STRING  shift, and go to state 537


State 417

   73 property_def: K_GROUP $@11 . T_STRING property_type_and_val ';'

    T_STRING  shift, and go to state 538


State 418

   67 property_def: K_NET $@8 . T_STRING property_type_and_val ';'

    T_STRING  shift, and go to state 539


State 419

   61 prop_def_section: K_PROPERTYDEFINITIONS $@6 property_defs K_END K_PROPERTYDEFINITIONS .

    $default  reduce using rule 61 (prop_def_section)


State 420

   75 property_def: K_COMPONENT $@12 . T_STRING property_type_and_val ';'

    T_STRING  shift, and go to state 540


State 421

   69 property_def: K_SNET $@9 . T_STRING property_type_and_val ';'

    T_STRING  shift, and go to state 541


State 422

   79 property_def: K_COMPONENTPIN $@14 . T_STRING property_type_and_val ';'

    T_STRING  shift, and go to state 542


State 423

  113 pin_cap: K_MINPINS NUMBER K_WIRECAP . NUMBER ';'

    NUMBER  shift, and go to state 543


State 424

  122 pin: '-' $@21 T_STRING . '+' K_NET $@22 T_STRING $@23 pin_options ';'

    '+'  shift, and go to state 544


State 425

  201 tracks_rule: track_start NUMBER $@38 K_DO NUMBER . K_STEP NUMBER track_opts ';'

    K_STEP  shift, and go to state 545


State 426

  225 via_declaration: '-' $@40 T_STRING . $@41 layer_stmts ';'

    $default  reduce using rule 224 ($@41)

    $@41  go to state 546


State 427

  262 regions_stmt: '-' $@49 T_STRING . $@50 rect_list region_options ';'

    $default  reduce using rule 261 ($@50)

    $@50  go to state 547


State 428

  288 comp_id_and_name: '-' $@52 T_STRING . T_STRING

    T_STRING  shift, and go to state 548


State 429

  285 comp: comp_start comp_options ';' .

    $default  reduce using rule 285 (comp)


State 430

  218 extension_stmt: '+' . K_BEGINEXT
  308 comp_eeq: '+' . K_EEQMASTER $@53 T_STRING
  310 comp_generate: '+' . K_COMP_GEN $@54 T_STRING opt_pattern
  313 comp_source: '+' . K_SOURCE source_type
  323 comp_halo: '+' . K_HALO $@55 halo_soft NUMBER NUMBER NUMBER NUMBER
  327 comp_routehalo: '+' . K_ROUTEHALO NUMBER $@56 T_STRING T_STRING
  329 comp_property: '+' . K_PROPERTY $@57 comp_prop_list
  335 comp_region_start: '+' . K_REGION
  337 comp_foreign: '+' . K_FOREIGN $@58 T_STRING opt_paren orient
  341 comp_type: '+' . K_UNPLACED
  342          | '+' . K_UNPLACED pt orient
  344 maskShift: '+' . K_MASKSHIFT $@59 T_STRING
  345 placement_status: '+' . K_FIXED
  346                 | '+' . K_COVER
  347                 | '+' . K_PLACED
  348 weight: '+' . K_WEIGHT NUMBER

    K_COMP_GEN   shift, and go to state 549
    K_SOURCE     shift, and go to state 550
    K_WEIGHT     shift, and go to state 551
    K_EEQMASTER  shift, and go to state 552
    K_FIXED      shift, and go to state 553
    K_COVER      shift, and go to state 554
    K_UNPLACED   shift, and go to state 555
    K_PLACED     shift, and go to state 556
    K_FOREIGN    shift, and go to state 557
    K_REGION     shift, and go to state 558
    K_PROPERTY   shift, and go to state 559
    K_BEGINEXT   shift, and go to state 560
    K_MASKSHIFT  shift, and go to state 561
    K_HALO       shift, and go to state 562
    K_ROUTEHALO  shift, and go to state 563


State 431

  306 comp_extension_stmt: extension_stmt .

    $default  reduce using rule 306 (comp_extension_stmt)


State 432

  293 comp_options: comp_options comp_option .

    $default  reduce using rule 293 (comp_options)


State 433

  305 comp_option: comp_extension_stmt .

    $default  reduce using rule 305 (comp_option)


State 434

  301 comp_option: comp_eeq .

    $default  reduce using rule 301 (comp_option)


State 435

  294 comp_option: comp_generate .

    $default  reduce using rule 294 (comp_option)


State 436

  295 comp_option: comp_source .

    $default  reduce using rule 295 (comp_option)


State 437

  300 comp_option: comp_region .

    $default  reduce using rule 300 (comp_option)


State 438

  302 comp_option: comp_halo .

    $default  reduce using rule 302 (comp_option)


State 439

  303 comp_option: comp_routehalo .

    $default  reduce using rule 303 (comp_option)


State 440

  304 comp_option: comp_property .

    $default  reduce using rule 304 (comp_option)


State 441

  318 comp_region: comp_region_start . comp_pnt_list
  319            | comp_region_start . T_STRING

    T_STRING  shift, and go to state 564
    '('       shift, and go to state 188

    pt             go to state 565
    comp_pnt_list  go to state 566


State 442

  299 comp_option: comp_foreign .

    $default  reduce using rule 299 (comp_option)


State 443

  296 comp_option: comp_type .

    $default  reduce using rule 296 (comp_option)


State 444

  298 comp_option: maskShift .

    $default  reduce using rule 298 (comp_option)


State 445

  340 comp_type: placement_status . pt orient

    '('  shift, and go to state 188

    pt  go to state 567


State 446

  297 comp_option: weight .

    $default  reduce using rule 297 (comp_option)


State 447

  291 comp_net_list: comp_net_list T_STRING .

    $default  reduce using rule 291 (comp_net_list)


State 448

  290 comp_net_list: comp_net_list '*' .

    $default  reduce using rule 290 (comp_net_list)


State 449

  359 net_name: T_STRING . $@61 net_connections

    $default  reduce using rule 358 ($@61)

    $@61  go to state 568


State 450

  361 net_name: K_MUSTJOIN . '(' T_STRING $@62 T_STRING ')'

    '('  shift, and go to state 569


State 451

  357 net_start: '-' $@60 net_name .

    $default  reduce using rule 357 (net_start)


State 452

  354 one_net: net_and_connections net_options ';' .

    $default  reduce using rule 354 (one_net)


State 453

  218 extension_stmt: '+' . K_BEGINEXT
  376 net_option: '+' . net_type $@66 paths
  377           | '+' . K_SOURCE netsource_type
  378           | '+' . K_FIXEDBUMP
  380           | '+' . K_FREQUENCY $@67 NUMBER
  382           | '+' . K_ORIGINAL $@68 T_STRING
  383           | '+' . K_PATTERN pattern_type
  384           | '+' . K_WEIGHT NUMBER
  385           | '+' . K_XTALK NUMBER
  386           | '+' . K_ESTCAP NUMBER
  387           | '+' . K_USE use_type
  388           | '+' . K_STYLE NUMBER
  390           | '+' . K_NONDEFAULTRULE $@69 T_STRING
  393           | '+' . K_SHIELDNET $@70 T_STRING
  396           | '+' . K_NOSHIELD $@71 $@72 paths
  400           | '+' . K_SUBNET $@73 T_STRING $@74 comp_names $@75 subnet_options
  402           | '+' . K_PROPERTY $@76 net_prop_list
  417 vpin_begin: '+' . K_VPIN $@78 T_STRING

    K_SOURCE          shift, and go to state 570
    K_WEIGHT          shift, and go to state 571
    K_FIXED           shift, and go to state 572
    K_COVER           shift, and go to state 573
    K_ORIGINAL        shift, and go to state 574
    K_USE             shift, and go to state 575
    K_STYLE           shift, and go to state 576
    K_PATTERN         shift, and go to state 577
    K_ESTCAP          shift, and go to state 578
    K_ROUTED          shift, and go to state 579
    K_NONDEFAULTRULE  shift, and go to state 580
    K_VPIN            shift, and go to state 581
    K_SUBNET          shift, and go to state 582
    K_XTALK           shift, and go to state 583
    K_PROPERTY        shift, and go to state 584
    K_BEGINEXT        shift, and go to state 560
    K_SHIELDNET       shift, and go to state 585
    K_NOSHIELD        shift, and go to state 586
    K_FIXEDBUMP       shift, and go to state 587
    K_FREQUENCY       shift, and go to state 588

    net_type  go to state 589


State 454

  403 net_option: extension_stmt .

    $default  reduce using rule 403 (net_option)


State 455

  374 net_options: net_options net_option .

    $default  reduce using rule 374 (net_options)


State 456

  391 net_option: vpin_stmt .

    $default  reduce using rule 391 (net_option)


State 457

  415 vpin_stmt: vpin_begin . vpin_layer_opt pt pt $@77 vpin_options

    K_LAYER  shift, and go to state 590

    $default  reduce using rule 418 (vpin_layer_opt)

    vpin_layer_opt  go to state 591


State 458

  496 snet_rule: net_and_connections snet_options ';' .

    $default  reduce using rule 496 (snet_rule)


State 459

  218 extension_stmt: '+' . K_BEGINEXT
  503 snet_other_option: '+' . net_type
  505                  | '+' . net_type $@86 spaths
  508                  | '+' . K_SHIELD $@87 T_STRING $@88 shield_layer
  509                  | '+' . K_SHAPE shape_type
  510                  | '+' . K_MASK NUMBER
  513                  | '+' . K_POLYGON $@89 T_STRING $@90 firstPt nextPt nextPt otherPts
  515                  | '+' . K_RECT $@91 T_STRING pt pt
  518                  | '+' . K_VIA $@92 T_STRING orient_pt $@93 firstPt otherPts
  519                  | '+' . K_SOURCE source_type
  520                  | '+' . K_FIXEDBUMP
  521                  | '+' . K_FREQUENCY NUMBER
  523                  | '+' . K_ORIGINAL $@94 T_STRING
  524                  | '+' . K_PATTERN pattern_type
  525                  | '+' . K_WEIGHT NUMBER
  526                  | '+' . K_ESTCAP NUMBER
  527                  | '+' . K_USE use_type
  528                  | '+' . K_STYLE NUMBER
  530                  | '+' . K_PROPERTY $@95 snet_prop_list
  545 snet_width: '+' . K_WIDTH $@97 T_STRING NUMBER
  547 snet_voltage: '+' . K_VOLTAGE $@98 T_STRING
  550 snet_spacing: '+' . K_SPACING $@99 T_STRING NUMBER $@100 opt_snet_range

    K_RECT       shift, and go to state 592
    K_SOURCE     shift, and go to state 593
    K_WEIGHT     shift, and go to state 594
    K_FIXED      shift, and go to state 572
    K_COVER      shift, and go to state 573
    K_ORIGINAL   shift, and go to state 595
    K_USE        shift, and go to state 596
    K_STYLE      shift, and go to state 597
    K_PATTERN    shift, and go to state 598
    K_ESTCAP     shift, and go to state 599
    K_ROUTED     shift, and go to state 579
    K_SHAPE      shift, and go to state 600
    K_WIDTH      shift, and go to state 601
    K_VOLTAGE    shift, and go to state 602
    K_SPACING    shift, and go to state 603
    K_PROPERTY   shift, and go to state 604
    K_BEGINEXT   shift, and go to state 560
    K_MASK       shift, and go to state 605
    K_SHIELD     shift, and go to state 606
    K_FIXEDBUMP  shift, and go to state 607
    K_FREQUENCY  shift, and go to state 608
    K_POLYGON    shift, and go to state 609
    K_VIA        shift, and go to state 610

    net_type  go to state 611


State 460

  531 snet_other_option: extension_stmt .

    $default  reduce using rule 531 (snet_other_option)


State 461

  498 snet_options: snet_options snet_option .

    $default  reduce using rule 498 (snet_options)


State 462

  502 snet_option: snet_other_option .

    $default  reduce using rule 502 (snet_option)


State 463

  499 snet_option: snet_width .

    $default  reduce using rule 499 (snet_option)


State 464

  500 snet_option: snet_voltage .

    $default  reduce using rule 500 (snet_option)


State 465

  501 snet_option: snet_spacing .

    $default  reduce using rule 501 (snet_option)


State 466

  580 start_group: '-' $@104 T_STRING .

    $default  reduce using rule 580 (start_group)


State 467

  583 group_member: T_STRING .

    $default  reduce using rule 583 (group_member)


State 468

  582 group_members: group_members group_member .

    $default  reduce using rule 582 (group_members)


State 469

  578 group_rule: start_group group_members group_options . ';'
  585 group_options: group_options . group_option

    ';'  shift, and go to state 612
    '+'  shift, and go to state 613

    extension_stmt  go to state 614
    group_option    go to state 615


State 470

  615 operand: K_NET $@107 . T_STRING

    T_STRING  shift, and go to state 616


State 471

  617 operand: K_PATH $@108 . T_STRING T_STRING T_STRING T_STRING

    T_STRING  shift, and go to state 617


State 472

  618 operand: K_SUM '(' . operand_list ')'

    K_NET   shift, and go to state 358
    K_PATH  shift, and go to state 359
    K_SUM   shift, and go to state 360
    K_DIFF  shift, and go to state 361

    operand       go to state 618
    operand_list  go to state 619


State 473

  619 operand: K_DIFF '(' . operand_list ')'

    K_NET   shift, and go to state 358
    K_PATH  shift, and go to state 359
    K_SUM   shift, and go to state 360
    K_DIFF  shift, and go to state 361

    operand       go to state 618
    operand_list  go to state 620


State 474

  624 wiredlogic_rule: '-' K_WIREDLOGIC $@109 . T_STRING opt_plus K_MAXDIST NUMBER ';'

    T_STRING  shift, and go to state 621


State 475

  613 operand_rule: '-' operand delay_specs . ';'
  628 delay_specs: delay_specs . delay_spec

    ';'  shift, and go to state 622
    '+'  shift, and go to state 623

    delay_spec  go to state 624


State 476

  641 start_scan: '-' $@110 T_STRING .

    $default  reduce using rule 641 (start_scan)


State 477

  639 scan_rule: start_scan scan_members ';' .

    $default  reduce using rule 639 (scan_rule)


State 478

  218 extension_stmt: '+' . K_BEGINEXT
  647 scan_member: '+' . K_START $@111 T_STRING opt_pin
  649            | '+' . K_FLOATING $@112 floating_inst_list
  651            | '+' . K_ORDERED $@113 ordered_inst_list
  653            | '+' . K_STOP $@114 T_STRING opt_pin
  655            | '+' . K_COMMONSCANPINS $@115 opt_common_pins
  657            | '+' . K_PARTITION $@116 T_STRING partition_maxbits

    K_START           shift, and go to state 625
    K_FLOATING        shift, and go to state 626
    K_ORDERED         shift, and go to state 627
    K_STOP            shift, and go to state 628
    K_BEGINEXT        shift, and go to state 560
    K_COMMONSCANPINS  shift, and go to state 629
    K_PARTITION       shift, and go to state 630


State 479

  658 scan_member: extension_stmt .

    $default  reduce using rule 658 (scan_member)


State 480

  643 scan_members: scan_members scan_member .

    $default  reduce using rule 643 (scan_members)


State 481

  687 start_iotiming: '-' '(' $@119 . T_STRING T_STRING ')'

    T_STRING  shift, and go to state 631


State 482

  685 iotiming_rule: start_iotiming iotiming_members ';' .

    $default  reduce using rule 685 (iotiming_rule)


State 483

  218 extension_stmt: '+' . K_BEGINEXT
  690 iotiming_member: '+' . risefall K_VARIABLE NUMBER NUMBER
  691                | '+' . risefall K_SLEWRATE NUMBER NUMBER
  692                | '+' . K_CAPACITANCE NUMBER
  695                | '+' . K_DRIVECELL $@120 T_STRING $@121 iotiming_drivecell_opt

    K_RISE         shift, and go to state 632
    K_FALL         shift, and go to state 633
    K_CAPACITANCE  shift, and go to state 634
    K_DRIVECELL    shift, and go to state 635
    K_BEGINEXT     shift, and go to state 560

    risefall  go to state 636


State 484

  696 iotiming_member: extension_stmt .

    $default  reduce using rule 696 (iotiming_member)


State 485

  689 iotiming_members: iotiming_members iotiming_member .

    $default  reduce using rule 689 (iotiming_members)


State 486

  714 fp_stmt: '-' $@125 T_STRING . h_or_v $@126 constraint_type constrain_what_list ';'

    K_HORIZONTAL  shift, and go to state 637
    K_VERTICAL    shift, and go to state 638

    h_or_v  go to state 639


State 487

  739 timingdisables_rule: '-' K_FROMPIN $@131 . T_STRING T_STRING K_TOPIN $@132 T_STRING T_STRING ';'

    T_STRING  shift, and go to state 640


State 488

  741 timingdisables_rule: '-' K_THRUPIN $@133 . T_STRING T_STRING ';'

    T_STRING  shift, and go to state 641


State 489

  743 timingdisables_rule: '-' K_MACRO $@134 . T_STRING td_macro_option ';'

    T_STRING  shift, and go to state 642


State 490

  744 timingdisables_rule: '-' K_REENTRANTPATHS ';' .

    $default  reduce using rule 744 (timingdisables_rule)


State 491

  757 start_partition: '-' $@138 T_STRING . turnoff

    K_TURNOFF  shift, and go to state 643

    $default  reduce using rule 758 (turnoff)

    turnoff  go to state 644


State 492

  755 partition_rule: start_partition partition_members ';' .

    $default  reduce using rule 755 (partition_rule)


State 493

  218 extension_stmt: '+' . K_BEGINEXT
  769 partition_member: '+' . K_FROMCLOCKPIN $@139 T_STRING T_STRING risefall minmaxpins
  771                 | '+' . K_FROMCOMPPIN $@140 T_STRING T_STRING risefallminmax2_list
  773                 | '+' . K_FROMIOPIN $@141 T_STRING risefallminmax1_list
  775                 | '+' . K_TOCLOCKPIN $@142 T_STRING T_STRING risefall minmaxpins
  777                 | '+' . K_TOCOMPPIN $@143 T_STRING T_STRING risefallminmax2_list
  779                 | '+' . K_TOIOPIN $@144 T_STRING risefallminmax1_list

    K_FROMCLOCKPIN  shift, and go to state 645
    K_FROMCOMPPIN   shift, and go to state 646
    K_FROMIOPIN     shift, and go to state 647
    K_TOCLOCKPIN    shift, and go to state 648
    K_TOCOMPPIN     shift, and go to state 649
    K_TOIOPIN       shift, and go to state 650
    K_BEGINEXT      shift, and go to state 560


State 494

  780 partition_member: extension_stmt .

    $default  reduce using rule 780 (partition_member)


State 495

  767 partition_members: partition_members partition_member .

    $default  reduce using rule 767 (partition_members)


State 496

  827 pin_prop_terminal: '-' $@149 T_STRING . T_STRING $@150 pin_prop_options ';'

    T_STRING  shift, and go to state 651


State 497

  845 blockage_rule: '-' K_LAYER $@152 . T_STRING $@153 layer_blockage_rules

    T_STRING  shift, and go to state 652


State 498

  847 blockage_rule: '-' K_PLACEMENT $@154 . placement_comp_rules

    $default  reduce using rule 861 (placement_comp_rules)

    placement_comp_rules  go to state 653


State 499

  870 rectPoly_blockage: K_RECT pt . pt

    '('  shift, and go to state 188

    pt  go to state 654


State 500

  872 rectPoly_blockage: K_POLYGON $@157 . firstPt nextPt nextPt otherPts

    '('  shift, and go to state 188

    firstPt  go to state 655
    pt       go to state 190


State 501

  842 blockage_def: blockage_rule rectPoly_blockage rectPoly_blockage_rules . ';'
  869 rectPoly_blockage_rules: rectPoly_blockage_rules . rectPoly_blockage

    K_RECT     shift, and go to state 386
    K_POLYGON  shift, and go to state 387
    ';'        shift, and go to state 656

    rectPoly_blockage  go to state 657


State 502

  881 slot_rule: '-' K_LAYER $@158 . T_STRING $@159 geom_slot

    T_STRING  shift, and go to state 658


State 503

  884 geom_slot: K_RECT . pt pt

    '('  shift, and go to state 188

    pt  go to state 659


State 504

  886 geom_slot: K_POLYGON . $@160 firstPt nextPt nextPt otherPts

    $default  reduce using rule 885 ($@160)

    $@160  go to state 660


State 505

  878 slot_def: slot_rule geom_slot_rules ';' .

    $default  reduce using rule 878 (slot_def)


State 506

  883 geom_slot_rules: geom_slot_rules geom_slot .

    $default  reduce using rule 883 (geom_slot_rules)


State 507

  898 fill_rule: '-' K_LAYER $@163 . T_STRING $@164 fill_layer_mask_opc_opt geom_fill

    T_STRING  shift, and go to state 661


State 508

  895 fill_def: '-' K_VIA $@161 . T_STRING $@162 fill_via_mask_opc_opt fill_via_pt ';'

    T_STRING  shift, and go to state 662


State 509

  901 geom_fill: K_RECT . pt pt

    '('  shift, and go to state 188

    pt  go to state 663


State 510

  903 geom_fill: K_POLYGON . $@165 firstPt nextPt nextPt otherPts

    $default  reduce using rule 902 ($@165)

    $@165  go to state 664


State 511

  892 fill_def: fill_rule geom_fill_rules ';' .

    $default  reduce using rule 892 (fill_def)


State 512

  900 geom_fill_rules: geom_fill_rules geom_fill .

    $default  reduce using rule 900 (geom_fill_rules)


State 513

  924 nondefault_def: '-' $@166 T_STRING $@167 . nondefault_options ';'

    $default  reduce using rule 925 (nondefault_options)

    nondefault_options  go to state 665


State 514

  919 nondefault_end: K_END K_NONDEFAULTRULES .

    $default  reduce using rule 919 (nondefault_end)


State 515

  956 styles_rule: '-' K_STYLE NUMBER . $@174 firstPt nextPt otherPts ';'

    $default  reduce using rule 955 ($@174)

    $@174  go to state 666


State 516

   99 orient: K_N .

    $default  reduce using rule 99 (orient)


State 517

  101 orient: K_S .

    $default  reduce using rule 101 (orient)


State 518

  102 orient: K_E .

    $default  reduce using rule 102 (orient)


State 519

  100 orient: K_W .

    $default  reduce using rule 100 (orient)


State 520

  103 orient: K_FN .

    $default  reduce using rule 103 (orient)


State 521

  106 orient: K_FE .

    $default  reduce using rule 106 (orient)


State 522

  105 orient: K_FS .

    $default  reduce using rule 105 (orient)


State 523

  104 orient: K_FW .

    $default  reduce using rule 104 (orient)


State 524

   96 canplace: K_CANPLACE $@18 T_STRING NUMBER NUMBER orient . K_DO NUMBER K_BY NUMBER K_STEP NUMBER NUMBER ';'

    K_DO  shift, and go to state 667


State 525

   98 cannotoccupy: K_CANNOTOCCUPY $@19 T_STRING NUMBER NUMBER orient . K_DO NUMBER K_BY NUMBER K_STEP NUMBER NUMBER ';'

    K_DO  shift, and go to state 668


State 526

  249 pt: '(' NUMBER NUMBER ')' .

    $default  reduce using rule 249 (pt)


State 527

  251 pt: '(' NUMBER '*' ')' .

    $default  reduce using rule 251 (pt)


State 528

  250 pt: '(' '*' NUMBER ')' .

    $default  reduce using rule 250 (pt)


State 529

  252 pt: '(' '*' '*' ')' .

    $default  reduce using rule 252 (pt)


State 530

  108 die_area: K_DIEAREA $@20 firstPt nextPt otherPts ';' .

    $default  reduce using rule 108 (die_area)


State 531

  248 otherPts: otherPts nextPt .

    $default  reduce using rule 248 (otherPts)


State 532

  216 gcellgrid: K_GCELLGRID track_type NUMBER K_DO NUMBER K_STEP . NUMBER ';'

    NUMBER  shift, and go to state 669


State 533

  186 row_rule: K_ROW $@35 T_STRING T_STRING NUMBER NUMBER . orient $@36 row_do_option row_options ';'

    K_N   shift, and go to state 516
    K_S   shift, and go to state 517
    K_E   shift, and go to state 518
    K_W   shift, and go to state 519
    K_FN  shift, and go to state 520
    K_FE  shift, and go to state 521
    K_FS  shift, and go to state 522
    K_FW  shift, and go to state 523

    orient  go to state 670


State 534

   65 property_def: K_DESIGN $@7 T_STRING . property_type_and_val ';'

    K_STRING         shift, and go to state 671
    K_REAL           shift, and go to state 672
    K_INTEGER        shift, and go to state 673
    K_NAMEMAPSTRING  shift, and go to state 674

    property_type_and_val  go to state 675


State 535

   77 property_def: K_ROW $@13 T_STRING . property_type_and_val ';'

    K_STRING         shift, and go to state 671
    K_REAL           shift, and go to state 672
    K_INTEGER        shift, and go to state 673
    K_NAMEMAPSTRING  shift, and go to state 674

    property_type_and_val  go to state 676


State 536

   71 property_def: K_REGION $@10 T_STRING . property_type_and_val ';'

    K_STRING         shift, and go to state 671
    K_REAL           shift, and go to state 672
    K_INTEGER        shift, and go to state 673
    K_NAMEMAPSTRING  shift, and go to state 674

    property_type_and_val  go to state 677


State 537

   81 property_def: K_NONDEFAULTRULE $@15 T_STRING . property_type_and_val ';'

    K_STRING         shift, and go to state 671
    K_REAL           shift, and go to state 672
    K_INTEGER        shift, and go to state 673
    K_NAMEMAPSTRING  shift, and go to state 674

    property_type_and_val  go to state 678


State 538

   73 property_def: K_GROUP $@11 T_STRING . property_type_and_val ';'

    K_STRING         shift, and go to state 671
    K_REAL           shift, and go to state 672
    K_INTEGER        shift, and go to state 673
    K_NAMEMAPSTRING  shift, and go to state 674

    property_type_and_val  go to state 679


State 539

   67 property_def: K_NET $@8 T_STRING . property_type_and_val ';'

    K_STRING         shift, and go to state 671
    K_REAL           shift, and go to state 672
    K_INTEGER        shift, and go to state 673
    K_NAMEMAPSTRING  shift, and go to state 674

    property_type_and_val  go to state 680


State 540

   75 property_def: K_COMPONENT $@12 T_STRING . property_type_and_val ';'

    K_STRING         shift, and go to state 671
    K_REAL           shift, and go to state 672
    K_INTEGER        shift, and go to state 673
    K_NAMEMAPSTRING  shift, and go to state 674

    property_type_and_val  go to state 681


State 541

   69 property_def: K_SNET $@9 T_STRING . property_type_and_val ';'

    K_STRING         shift, and go to state 671
    K_REAL           shift, and go to state 672
    K_INTEGER        shift, and go to state 673
    K_NAMEMAPSTRING  shift, and go to state 674

    property_type_and_val  go to state 682


State 542

   79 property_def: K_COMPONENTPIN $@14 T_STRING . property_type_and_val ';'

    K_STRING         shift, and go to state 671
    K_REAL           shift, and go to state 672
    K_INTEGER        shift, and go to state 673
    K_NAMEMAPSTRING  shift, and go to state 674

    property_type_and_val  go to state 683


State 543

  113 pin_cap: K_MINPINS NUMBER K_WIRECAP NUMBER . ';'

    ';'  shift, and go to state 684


State 544

  122 pin: '-' $@21 T_STRING '+' . K_NET $@22 T_STRING $@23 pin_options ';'

    K_NET  shift, and go to state 685


State 545

  201 tracks_rule: track_start NUMBER $@38 K_DO NUMBER K_STEP . NUMBER track_opts ';'

    NUMBER  shift, and go to state 686


State 546

  225 via_declaration: '-' $@40 T_STRING $@41 . layer_stmts ';'

    $default  reduce using rule 226 (layer_stmts)

    layer_stmts  go to state 687


State 547

  262 regions_stmt: '-' $@49 T_STRING $@50 . rect_list region_options ';'

    '('  shift, and go to state 188

    pt         go to state 688
    rect_list  go to state 689


State 548

  288 comp_id_and_name: '-' $@52 T_STRING T_STRING .

    $default  reduce using rule 288 (comp_id_and_name)


State 549

  310 comp_generate: '+' K_COMP_GEN . $@54 T_STRING opt_pattern

    $default  reduce using rule 309 ($@54)

    $@54  go to state 690


State 550

  313 comp_source: '+' K_SOURCE . source_type

    K_NETLIST  shift, and go to state 691
    K_DIST     shift, and go to state 692
    K_USER     shift, and go to state 693
    K_TIMING   shift, and go to state 694

    source_type  go to state 695


State 551

  348 weight: '+' K_WEIGHT . NUMBER

    NUMBER  shift, and go to state 696


State 552

  308 comp_eeq: '+' K_EEQMASTER . $@53 T_STRING

    $default  reduce using rule 307 ($@53)

    $@53  go to state 697


State 553

  345 placement_status: '+' K_FIXED .

    $default  reduce using rule 345 (placement_status)


State 554

  346 placement_status: '+' K_COVER .

    $default  reduce using rule 346 (placement_status)


State 555

  341 comp_type: '+' K_UNPLACED .
  342          | '+' K_UNPLACED . pt orient

    '('  shift, and go to state 188

    $default  reduce using rule 341 (comp_type)

    pt  go to state 698


State 556

  347 placement_status: '+' K_PLACED .

    $default  reduce using rule 347 (placement_status)


State 557

  337 comp_foreign: '+' K_FOREIGN . $@58 T_STRING opt_paren orient

    $default  reduce using rule 336 ($@58)

    $@58  go to state 699


State 558

  335 comp_region_start: '+' K_REGION .

    $default  reduce using rule 335 (comp_region_start)


State 559

  329 comp_property: '+' K_PROPERTY . $@57 comp_prop_list

    $default  reduce using rule 328 ($@57)

    $@57  go to state 700


State 560

  218 extension_stmt: '+' K_BEGINEXT .

    $default  reduce using rule 218 (extension_stmt)


State 561

  344 maskShift: '+' K_MASKSHIFT . $@59 T_STRING

    $default  reduce using rule 343 ($@59)

    $@59  go to state 701


State 562

  323 comp_halo: '+' K_HALO . $@55 halo_soft NUMBER NUMBER NUMBER NUMBER

    $default  reduce using rule 322 ($@55)

    $@55  go to state 702


State 563

  327 comp_routehalo: '+' K_ROUTEHALO . NUMBER $@56 T_STRING T_STRING

    NUMBER  shift, and go to state 703


State 564

  319 comp_region: comp_region_start T_STRING .

    $default  reduce using rule 319 (comp_region)


State 565

  320 comp_pnt_list: pt . pt

    '('  shift, and go to state 188

    pt  go to state 704


State 566

  318 comp_region: comp_region_start comp_pnt_list .
  321 comp_pnt_list: comp_pnt_list . pt pt

    '('  shift, and go to state 188

    $default  reduce using rule 318 (comp_region)

    pt  go to state 705


State 567

  340 comp_type: placement_status pt . orient

    K_N   shift, and go to state 516
    K_S   shift, and go to state 517
    K_E   shift, and go to state 518
    K_W   shift, and go to state 519
    K_FN  shift, and go to state 520
    K_FE  shift, and go to state 521
    K_FS  shift, and go to state 522
    K_FW  shift, and go to state 523

    orient  go to state 706


State 568

  359 net_name: T_STRING $@61 . net_connections

    $default  reduce using rule 362 (net_connections)

    net_connections  go to state 707


State 569

  361 net_name: K_MUSTJOIN '(' . T_STRING $@62 T_STRING ')'

    T_STRING  shift, and go to state 708


State 570

  377 net_option: '+' K_SOURCE . netsource_type

    K_TEST     shift, and go to state 709
    K_NETLIST  shift, and go to state 710
    K_DIST     shift, and go to state 711
    K_USER     shift, and go to state 712
    K_TIMING   shift, and go to state 713

    netsource_type  go to state 714


State 571

  384 net_option: '+' K_WEIGHT . NUMBER

    NUMBER  shift, and go to state 715


State 572

  426 net_type: K_FIXED .

    $default  reduce using rule 426 (net_type)


State 573

  427 net_type: K_COVER .

    $default  reduce using rule 427 (net_type)


State 574

  382 net_option: '+' K_ORIGINAL . $@68 T_STRING

    $default  reduce using rule 381 ($@68)

    $@68  go to state 716


State 575

  387 net_option: '+' K_USE . use_type

    K_SIGNAL  shift, and go to state 717
    K_POWER   shift, and go to state 718
    K_GROUND  shift, and go to state 719
    K_CLOCK   shift, and go to state 720
    K_TIEOFF  shift, and go to state 721
    K_ANALOG  shift, and go to state 722
    K_SCAN    shift, and go to state 723
    K_RESET   shift, and go to state 724

    use_type  go to state 725


State 576

  388 net_option: '+' K_STYLE . NUMBER

    NUMBER  shift, and go to state 726


State 577

  383 net_option: '+' K_PATTERN . pattern_type

    K_WIREDLOGIC  shift, and go to state 727
    K_BALANCED    shift, and go to state 728
    K_STEINER     shift, and go to state 729
    K_TRUNK       shift, and go to state 730

    pattern_type  go to state 731


State 578

  386 net_option: '+' K_ESTCAP . NUMBER

    NUMBER  shift, and go to state 732


State 579

  428 net_type: K_ROUTED .

    $default  reduce using rule 428 (net_type)


State 580

  390 net_option: '+' K_NONDEFAULTRULE . $@69 T_STRING

    $default  reduce using rule 389 ($@69)

    $@69  go to state 733


State 581

  417 vpin_begin: '+' K_VPIN . $@78 T_STRING

    $default  reduce using rule 416 ($@78)

    $@78  go to state 734


State 582

  400 net_option: '+' K_SUBNET . $@73 T_STRING $@74 comp_names $@75 subnet_options

    $default  reduce using rule 397 ($@73)

    $@73  go to state 735


State 583

  385 net_option: '+' K_XTALK . NUMBER

    NUMBER  shift, and go to state 736


State 584

  402 net_option: '+' K_PROPERTY . $@76 net_prop_list

    $default  reduce using rule 401 ($@76)

    $@76  go to state 737


State 585

  393 net_option: '+' K_SHIELDNET . $@70 T_STRING

    $default  reduce using rule 392 ($@70)

    $@70  go to state 738


State 586

  396 net_option: '+' K_NOSHIELD . $@71 $@72 paths

    $default  reduce using rule 394 ($@71)

    $@71  go to state 739


State 587

  378 net_option: '+' K_FIXEDBUMP .

    $default  reduce using rule 378 (net_option)


State 588

  380 net_option: '+' K_FREQUENCY . $@67 NUMBER

    $default  reduce using rule 379 ($@67)

    $@67  go to state 740


State 589

  376 net_option: '+' net_type . $@66 paths

    $default  reduce using rule 375 ($@66)

    $@66  go to state 741


State 590

  420 vpin_layer_opt: K_LAYER . $@79 T_STRING

    $default  reduce using rule 419 ($@79)

    $@79  go to state 742


State 591

  415 vpin_stmt: vpin_begin vpin_layer_opt . pt pt $@77 vpin_options

    '('  shift, and go to state 188

    pt  go to state 743


State 592

  515 snet_other_option: '+' K_RECT . $@91 T_STRING pt pt

    $default  reduce using rule 514 ($@91)

    $@91  go to state 744


State 593

  519 snet_other_option: '+' K_SOURCE . source_type

    K_NETLIST  shift, and go to state 691
    K_DIST     shift, and go to state 692
    K_USER     shift, and go to state 693
    K_TIMING   shift, and go to state 694

    source_type  go to state 745


State 594

  525 snet_other_option: '+' K_WEIGHT . NUMBER

    NUMBER  shift, and go to state 746


State 595

  523 snet_other_option: '+' K_ORIGINAL . $@94 T_STRING

    $default  reduce using rule 522 ($@94)

    $@94  go to state 747


State 596

  527 snet_other_option: '+' K_USE . use_type

    K_SIGNAL  shift, and go to state 717
    K_POWER   shift, and go to state 718
    K_GROUND  shift, and go to state 719
    K_CLOCK   shift, and go to state 720
    K_TIEOFF  shift, and go to state 721
    K_ANALOG  shift, and go to state 722
    K_SCAN    shift, and go to state 723
    K_RESET   shift, and go to state 724

    use_type  go to state 748


State 597

  528 snet_other_option: '+' K_STYLE . NUMBER

    NUMBER  shift, and go to state 749


State 598

  524 snet_other_option: '+' K_PATTERN . pattern_type

    K_WIREDLOGIC  shift, and go to state 727
    K_BALANCED    shift, and go to state 728
    K_STEINER     shift, and go to state 729
    K_TRUNK       shift, and go to state 730

    pattern_type  go to state 750


State 599

  526 snet_other_option: '+' K_ESTCAP . NUMBER

    NUMBER  shift, and go to state 751


State 600

  509 snet_other_option: '+' K_SHAPE . shape_type

    K_RING          shift, and go to state 752
    K_STRIPE        shift, and go to state 753
    K_FOLLOWPIN     shift, and go to state 754
    K_IOWIRE        shift, and go to state 755
    K_COREWIRE      shift, and go to state 756
    K_BLOCKWIRE     shift, and go to state 757
    K_FILLWIRE      shift, and go to state 758
    K_BLOCKAGEWIRE  shift, and go to state 759
    K_PADRING       shift, and go to state 760
    K_BLOCKRING     shift, and go to state 761
    K_DRCFILL       shift, and go to state 762
    K_FILLWIREOPC   shift, and go to state 763

    shape_type  go to state 764


State 601

  545 snet_width: '+' K_WIDTH . $@97 T_STRING NUMBER

    $default  reduce using rule 544 ($@97)

    $@97  go to state 765


State 602

  547 snet_voltage: '+' K_VOLTAGE . $@98 T_STRING

    $default  reduce using rule 546 ($@98)

    $@98  go to state 766


State 603

  550 snet_spacing: '+' K_SPACING . $@99 T_STRING NUMBER $@100 opt_snet_range

    $default  reduce using rule 548 ($@99)

    $@99  go to state 767


State 604

  530 snet_other_option: '+' K_PROPERTY . $@95 snet_prop_list

    $default  reduce using rule 529 ($@95)

    $@95  go to state 768


State 605

  510 snet_other_option: '+' K_MASK . NUMBER

    NUMBER  shift, and go to state 769


State 606

  508 snet_other_option: '+' K_SHIELD . $@87 T_STRING $@88 shield_layer

    $default  reduce using rule 506 ($@87)

    $@87  go to state 770


State 607

  520 snet_other_option: '+' K_FIXEDBUMP .

    $default  reduce using rule 520 (snet_other_option)


State 608

  521 snet_other_option: '+' K_FREQUENCY . NUMBER

    NUMBER  shift, and go to state 771


State 609

  513 snet_other_option: '+' K_POLYGON . $@89 T_STRING $@90 firstPt nextPt nextPt otherPts

    $default  reduce using rule 511 ($@89)

    $@89  go to state 772


State 610

  518 snet_other_option: '+' K_VIA . $@92 T_STRING orient_pt $@93 firstPt otherPts

    $default  reduce using rule 516 ($@92)

    $@92  go to state 773


State 611

  503 snet_other_option: '+' net_type .
  505                  | '+' net_type . $@86 spaths

    T_STRING  reduce using rule 504 ($@86)
    $default  reduce using rule 503 (snet_other_option)

    $@86  go to state 774


State 612

  578 group_rule: start_group group_members group_options ';' .

    $default  reduce using rule 578 (group_rule)


State 613

  218 extension_stmt: '+' . K_BEGINEXT
  586 group_option: '+' . K_SOFT group_soft_options
  588             | '+' . K_PROPERTY $@105 group_prop_list
  590             | '+' . K_REGION $@106 group_region

    K_REGION    shift, and go to state 775
    K_SOFT      shift, and go to state 776
    K_PROPERTY  shift, and go to state 777
    K_BEGINEXT  shift, and go to state 560


State 614

  591 group_option: extension_stmt .

    $default  reduce using rule 591 (group_option)


State 615

  585 group_options: group_options group_option .

    $default  reduce using rule 585 (group_options)


State 616

  615 operand: K_NET $@107 T_STRING .

    $default  reduce using rule 615 (operand)


State 617

  617 operand: K_PATH $@108 T_STRING . T_STRING T_STRING T_STRING

    T_STRING  shift, and go to state 778


State 618

  620 operand_list: operand .

    $default  reduce using rule 620 (operand_list)


State 619

  618 operand: K_SUM '(' operand_list . ')'
  621 operand_list: operand_list . operand
  622             | operand_list . ',' operand

    K_NET   shift, and go to state 358
    K_PATH  shift, and go to state 359
    K_SUM   shift, and go to state 360
    K_DIFF  shift, and go to state 361
    ')'     shift, and go to state 779
    ','     shift, and go to state 780

    operand  go to state 781


State 620

  619 operand: K_DIFF '(' operand_list . ')'
  621 operand_list: operand_list . operand
  622             | operand_list . ',' operand

    K_NET   shift, and go to state 358
    K_PATH  shift, and go to state 359
    K_SUM   shift, and go to state 360
    K_DIFF  shift, and go to state 361
    ')'     shift, and go to state 782
    ','     shift, and go to state 780

    operand  go to state 781


State 621

  624 wiredlogic_rule: '-' K_WIREDLOGIC $@109 T_STRING . opt_plus K_MAXDIST NUMBER ';'

    '+'  shift, and go to state 783

    $default  reduce using rule 625 (opt_plus)

    opt_plus  go to state 784


State 622

  613 operand_rule: '-' operand delay_specs ';' .

    $default  reduce using rule 613 (operand_rule)


State 623

  629 delay_spec: '+' . K_RISEMIN NUMBER
  630           | '+' . K_RISEMAX NUMBER
  631           | '+' . K_FALLMIN NUMBER
  632           | '+' . K_FALLMAX NUMBER

    K_RISEMIN  shift, and go to state 785
    K_RISEMAX  shift, and go to state 786
    K_FALLMIN  shift, and go to state 787
    K_FALLMAX  shift, and go to state 788


State 624

  628 delay_specs: delay_specs delay_spec .

    $default  reduce using rule 628 (delay_specs)


State 625

  647 scan_member: '+' K_START . $@111 T_STRING opt_pin

    $default  reduce using rule 646 ($@111)

    $@111  go to state 789


State 626

  649 scan_member: '+' K_FLOATING . $@112 floating_inst_list

    $default  reduce using rule 648 ($@112)

    $@112  go to state 790


State 627

  651 scan_member: '+' K_ORDERED . $@113 ordered_inst_list

    $default  reduce using rule 650 ($@113)

    $@113  go to state 791


State 628

  653 scan_member: '+' K_STOP . $@114 T_STRING opt_pin

    $default  reduce using rule 652 ($@114)

    $@114  go to state 792


State 629

  655 scan_member: '+' K_COMMONSCANPINS . $@115 opt_common_pins

    $default  reduce using rule 654 ($@115)

    $@115  go to state 793


State 630

  657 scan_member: '+' K_PARTITION . $@116 T_STRING partition_maxbits

    $default  reduce using rule 656 ($@116)

    $@116  go to state 794


State 631

  687 start_iotiming: '-' '(' $@119 T_STRING . T_STRING ')'

    T_STRING  shift, and go to state 795


State 632

  705 risefall: K_RISE .

    $default  reduce using rule 705 (risefall)


State 633

  706 risefall: K_FALL .

    $default  reduce using rule 706 (risefall)


State 634

  692 iotiming_member: '+' K_CAPACITANCE . NUMBER

    NUMBER  shift, and go to state 796


State 635

  695 iotiming_member: '+' K_DRIVECELL . $@120 T_STRING $@121 iotiming_drivecell_opt

    $default  reduce using rule 693 ($@120)

    $@120  go to state 797


State 636

  690 iotiming_member: '+' risefall . K_VARIABLE NUMBER NUMBER
  691                | '+' risefall . K_SLEWRATE NUMBER NUMBER

    K_VARIABLE  shift, and go to state 798
    K_SLEWRATE  shift, and go to state 799


State 637

  715 h_or_v: K_HORIZONTAL .

    $default  reduce using rule 715 (h_or_v)


State 638

  716 h_or_v: K_VERTICAL .

    $default  reduce using rule 716 (h_or_v)


State 639

  714 fp_stmt: '-' $@125 T_STRING h_or_v . $@126 constraint_type constrain_what_list ';'

    $default  reduce using rule 713 ($@126)

    $@126  go to state 800


State 640

  739 timingdisables_rule: '-' K_FROMPIN $@131 T_STRING . T_STRING K_TOPIN $@132 T_STRING T_STRING ';'

    T_STRING  shift, and go to state 801


State 641

  741 timingdisables_rule: '-' K_THRUPIN $@133 T_STRING . T_STRING ';'

    T_STRING  shift, and go to state 802


State 642

  743 timingdisables_rule: '-' K_MACRO $@134 T_STRING . td_macro_option ';'

    K_FROMPIN  shift, and go to state 803
    K_THRUPIN  shift, and go to state 804

    td_macro_option  go to state 805


State 643

  759 turnoff: K_TURNOFF . turnoff_setup turnoff_hold

    K_SETUPRISE  shift, and go to state 806
    K_SETUPFALL  shift, and go to state 807

    $default  reduce using rule 760 (turnoff_setup)

    turnoff_setup  go to state 808


State 644

  757 start_partition: '-' $@138 T_STRING turnoff .

    $default  reduce using rule 757 (start_partition)


State 645

  769 partition_member: '+' K_FROMCLOCKPIN . $@139 T_STRING T_STRING risefall minmaxpins

    $default  reduce using rule 768 ($@139)

    $@139  go to state 809


State 646

  771 partition_member: '+' K_FROMCOMPPIN . $@140 T_STRING T_STRING risefallminmax2_list

    $default  reduce using rule 770 ($@140)

    $@140  go to state 810


State 647

  773 partition_member: '+' K_FROMIOPIN . $@141 T_STRING risefallminmax1_list

    $default  reduce using rule 772 ($@141)

    $@141  go to state 811


State 648

  775 partition_member: '+' K_TOCLOCKPIN . $@142 T_STRING T_STRING risefall minmaxpins

    $default  reduce using rule 774 ($@142)

    $@142  go to state 812


State 649

  777 partition_member: '+' K_TOCOMPPIN . $@143 T_STRING T_STRING risefallminmax2_list

    $default  reduce using rule 776 ($@143)

    $@143  go to state 813


State 650

  779 partition_member: '+' K_TOIOPIN . $@144 T_STRING risefallminmax1_list

    $default  reduce using rule 778 ($@144)

    $@144  go to state 814


State 651

  827 pin_prop_terminal: '-' $@149 T_STRING T_STRING . $@150 pin_prop_options ';'

    $default  reduce using rule 826 ($@150)

    $@150  go to state 815


State 652

  845 blockage_rule: '-' K_LAYER $@152 T_STRING . $@153 layer_blockage_rules

    $default  reduce using rule 844 ($@153)

    $@153  go to state 816


State 653

  847 blockage_rule: '-' K_PLACEMENT $@154 placement_comp_rules .
  862 placement_comp_rules: placement_comp_rules . placement_comp_rule

    '+'  shift, and go to state 817

    $default  reduce using rule 847 (blockage_rule)

    placement_comp_rule  go to state 818


State 654

  870 rectPoly_blockage: K_RECT pt pt .

    $default  reduce using rule 870 (rectPoly_blockage)


State 655

  872 rectPoly_blockage: K_POLYGON $@157 firstPt . nextPt nextPt otherPts

    '('  shift, and go to state 188

    nextPt  go to state 819
    pt      go to state 321


State 656

  842 blockage_def: blockage_rule rectPoly_blockage rectPoly_blockage_rules ';' .

    $default  reduce using rule 842 (blockage_def)


State 657

  869 rectPoly_blockage_rules: rectPoly_blockage_rules rectPoly_blockage .

    $default  reduce using rule 869 (rectPoly_blockage_rules)


State 658

  881 slot_rule: '-' K_LAYER $@158 T_STRING . $@159 geom_slot

    $default  reduce using rule 880 ($@159)

    $@159  go to state 820


State 659

  884 geom_slot: K_RECT pt . pt

    '('  shift, and go to state 188

    pt  go to state 821


State 660

  886 geom_slot: K_POLYGON $@160 . firstPt nextPt nextPt otherPts

    '('  shift, and go to state 188

    firstPt  go to state 822
    pt       go to state 190


State 661

  898 fill_rule: '-' K_LAYER $@163 T_STRING . $@164 fill_layer_mask_opc_opt geom_fill

    $default  reduce using rule 897 ($@164)

    $@164  go to state 823


State 662

  895 fill_def: '-' K_VIA $@161 T_STRING . $@162 fill_via_mask_opc_opt fill_via_pt ';'

    $default  reduce using rule 894 ($@162)

    $@162  go to state 824


State 663

  901 geom_fill: K_RECT pt . pt

    '('  shift, and go to state 188

    pt  go to state 825


State 664

  903 geom_fill: K_POLYGON $@165 . firstPt nextPt nextPt otherPts

    '('  shift, and go to state 188

    firstPt  go to state 826
    pt       go to state 190


State 665

  924 nondefault_def: '-' $@166 T_STRING $@167 nondefault_options . ';'
  926 nondefault_options: nondefault_options . nondefault_option

    ';'  shift, and go to state 827
    '+'  shift, and go to state 828

    nondefault_option    go to state 829
    nondefault_prop_opt  go to state 830


State 666

  956 styles_rule: '-' K_STYLE NUMBER $@174 . firstPt nextPt otherPts ';'

    '('  shift, and go to state 188

    firstPt  go to state 831
    pt       go to state 190


State 667

   96 canplace: K_CANPLACE $@18 T_STRING NUMBER NUMBER orient K_DO . NUMBER K_BY NUMBER K_STEP NUMBER NUMBER ';'

    NUMBER  shift, and go to state 832


State 668

   98 cannotoccupy: K_CANNOTOCCUPY $@19 T_STRING NUMBER NUMBER orient K_DO . NUMBER K_BY NUMBER K_STEP NUMBER NUMBER ';'

    NUMBER  shift, and go to state 833


State 669

  216 gcellgrid: K_GCELLGRID track_type NUMBER K_DO NUMBER K_STEP NUMBER . ';'

    ';'  shift, and go to state 834


State 670

  186 row_rule: K_ROW $@35 T_STRING T_STRING NUMBER NUMBER orient . $@36 row_do_option row_options ';'

    $default  reduce using rule 185 ($@36)

    $@36  go to state 835


State 671

   87 property_type_and_val: K_STRING .
   88                      | K_STRING . QSTRING

    QSTRING  shift, and go to state 836

    $default  reduce using rule 87 (property_type_and_val)


State 672

   86 property_type_and_val: K_REAL . $@17 opt_range opt_num_val

    $default  reduce using rule 85 ($@17)

    $@17  go to state 837


State 673

   84 property_type_and_val: K_INTEGER . $@16 opt_range opt_num_val

    $default  reduce using rule 83 ($@16)

    $@16  go to state 838


State 674

   89 property_type_and_val: K_NAMEMAPSTRING . T_STRING

    T_STRING  shift, and go to state 839


State 675

   65 property_def: K_DESIGN $@7 T_STRING property_type_and_val . ';'

    ';'  shift, and go to state 840


State 676

   77 property_def: K_ROW $@13 T_STRING property_type_and_val . ';'

    ';'  shift, and go to state 841


State 677

   71 property_def: K_REGION $@10 T_STRING property_type_and_val . ';'

    ';'  shift, and go to state 842


State 678

   81 property_def: K_NONDEFAULTRULE $@15 T_STRING property_type_and_val . ';'

    ';'  shift, and go to state 843


State 679

   73 property_def: K_GROUP $@11 T_STRING property_type_and_val . ';'

    ';'  shift, and go to state 844


State 680

   67 property_def: K_NET $@8 T_STRING property_type_and_val . ';'

    ';'  shift, and go to state 845


State 681

   75 property_def: K_COMPONENT $@12 T_STRING property_type_and_val . ';'

    ';'  shift, and go to state 846


State 682

   69 property_def: K_SNET $@9 T_STRING property_type_and_val . ';'

    ';'  shift, and go to state 847


State 683

   79 property_def: K_COMPONENTPIN $@14 T_STRING property_type_and_val . ';'

    ';'  shift, and go to state 848


State 684

  113 pin_cap: K_MINPINS NUMBER K_WIRECAP NUMBER ';' .

    $default  reduce using rule 113 (pin_cap)


State 685

  122 pin: '-' $@21 T_STRING '+' K_NET . $@22 T_STRING $@23 pin_options ';'

    $default  reduce using rule 120 ($@22)

    $@22  go to state 849


State 686

  201 tracks_rule: track_start NUMBER $@38 K_DO NUMBER K_STEP NUMBER . track_opts ';'

    K_MASK  shift, and go to state 850

    $default  reduce using rule 206 (track_mask_statement)

    track_opts            go to state 851
    track_mask_statement  go to state 852


State 687

  225 via_declaration: '-' $@40 T_STRING $@41 layer_stmts . ';'
  227 layer_stmts: layer_stmts . layer_stmt

    ';'  shift, and go to state 853
    '+'  shift, and go to state 854

    extension_stmt      go to state 855
    layer_stmt          go to state 856
    layer_viarule_opts  go to state 857


State 688

  263 rect_list: pt . pt

    '('  shift, and go to state 188

    pt  go to state 858


State 689

  262 regions_stmt: '-' $@49 T_STRING $@50 rect_list . region_options ';'
  264 rect_list: rect_list . pt pt

    '('  shift, and go to state 188

    $default  reduce using rule 265 (region_options)

    pt              go to state 859
    region_options  go to state 860


State 690

  310 comp_generate: '+' K_COMP_GEN $@54 . T_STRING opt_pattern

    T_STRING  shift, and go to state 861


State 691

  314 source_type: K_NETLIST .

    $default  reduce using rule 314 (source_type)


State 692

  315 source_type: K_DIST .

    $default  reduce using rule 315 (source_type)


State 693

  316 source_type: K_USER .

    $default  reduce using rule 316 (source_type)


State 694

  317 source_type: K_TIMING .

    $default  reduce using rule 317 (source_type)


State 695

  313 comp_source: '+' K_SOURCE source_type .

    $default  reduce using rule 313 (comp_source)


State 696

  348 weight: '+' K_WEIGHT NUMBER .

    $default  reduce using rule 348 (weight)


State 697

  308 comp_eeq: '+' K_EEQMASTER $@53 . T_STRING

    T_STRING  shift, and go to state 862


State 698

  342 comp_type: '+' K_UNPLACED pt . orient

    K_N   shift, and go to state 516
    K_S   shift, and go to state 517
    K_E   shift, and go to state 518
    K_W   shift, and go to state 519
    K_FN  shift, and go to state 520
    K_FE  shift, and go to state 521
    K_FS  shift, and go to state 522
    K_FW  shift, and go to state 523

    orient  go to state 863


State 699

  337 comp_foreign: '+' K_FOREIGN $@58 . T_STRING opt_paren orient

    T_STRING  shift, and go to state 864


State 700

  329 comp_property: '+' K_PROPERTY $@57 . comp_prop_list

    T_STRING  shift, and go to state 865

    comp_prop_list  go to state 866
    comp_prop       go to state 867


State 701

  344 maskShift: '+' K_MASKSHIFT $@59 . T_STRING

    T_STRING  shift, and go to state 868


State 702

  323 comp_halo: '+' K_HALO $@55 . halo_soft NUMBER NUMBER NUMBER NUMBER

    K_SOFT  shift, and go to state 869

    $default  reduce using rule 324 (halo_soft)

    halo_soft  go to state 870


State 703

  327 comp_routehalo: '+' K_ROUTEHALO NUMBER . $@56 T_STRING T_STRING

    $default  reduce using rule 326 ($@56)

    $@56  go to state 871


State 704

  320 comp_pnt_list: pt pt .

    $default  reduce using rule 320 (comp_pnt_list)


State 705

  321 comp_pnt_list: comp_pnt_list pt . pt

    '('  shift, and go to state 188

    pt  go to state 872


State 706

  340 comp_type: placement_status pt orient .

    $default  reduce using rule 340 (comp_type)


State 707

  359 net_name: T_STRING $@61 net_connections .
  363 net_connections: net_connections . net_connection

    '('  shift, and go to state 873

    $default  reduce using rule 359 (net_name)

    net_connection  go to state 874


State 708

  361 net_name: K_MUSTJOIN '(' T_STRING . $@62 T_STRING ')'

    $default  reduce using rule 360 ($@62)

    $@62  go to state 875


State 709

  413 netsource_type: K_TEST .

    $default  reduce using rule 413 (netsource_type)


State 710

  409 netsource_type: K_NETLIST .

    $default  reduce using rule 409 (netsource_type)


State 711

  410 netsource_type: K_DIST .

    $default  reduce using rule 410 (netsource_type)


State 712

  411 netsource_type: K_USER .

    $default  reduce using rule 411 (netsource_type)


State 713

  412 netsource_type: K_TIMING .

    $default  reduce using rule 412 (netsource_type)


State 714

  377 net_option: '+' K_SOURCE netsource_type .

    $default  reduce using rule 377 (net_option)


State 715

  384 net_option: '+' K_WEIGHT NUMBER .

    $default  reduce using rule 384 (net_option)


State 716

  382 net_option: '+' K_ORIGINAL $@68 . T_STRING

    T_STRING  shift, and go to state 876


State 717

  172 use_type: K_SIGNAL .

    $default  reduce using rule 172 (use_type)


State 718

  173 use_type: K_POWER .

    $default  reduce using rule 173 (use_type)


State 719

  174 use_type: K_GROUND .

    $default  reduce using rule 174 (use_type)


State 720

  175 use_type: K_CLOCK .

    $default  reduce using rule 175 (use_type)


State 721

  176 use_type: K_TIEOFF .

    $default  reduce using rule 176 (use_type)


State 722

  177 use_type: K_ANALOG .

    $default  reduce using rule 177 (use_type)


State 723

  178 use_type: K_SCAN .

    $default  reduce using rule 178 (use_type)


State 724

  179 use_type: K_RESET .

    $default  reduce using rule 179 (use_type)


State 725

  387 net_option: '+' K_USE use_type .

    $default  reduce using rule 387 (net_option)


State 726

  388 net_option: '+' K_STYLE NUMBER .

    $default  reduce using rule 388 (net_option)


State 727

  563 pattern_type: K_WIREDLOGIC .

    $default  reduce using rule 563 (pattern_type)


State 728

  560 pattern_type: K_BALANCED .

    $default  reduce using rule 560 (pattern_type)


State 729

  561 pattern_type: K_STEINER .

    $default  reduce using rule 561 (pattern_type)


State 730

  562 pattern_type: K_TRUNK .

    $default  reduce using rule 562 (pattern_type)


State 731

  383 net_option: '+' K_PATTERN pattern_type .

    $default  reduce using rule 383 (net_option)


State 732

  386 net_option: '+' K_ESTCAP NUMBER .

    $default  reduce using rule 386 (net_option)


State 733

  390 net_option: '+' K_NONDEFAULTRULE $@69 . T_STRING

    T_STRING  shift, and go to state 877


State 734

  417 vpin_begin: '+' K_VPIN $@78 . T_STRING

    T_STRING  shift, and go to state 878


State 735

  400 net_option: '+' K_SUBNET $@73 . T_STRING $@74 comp_names $@75 subnet_options

    T_STRING  shift, and go to state 879


State 736

  385 net_option: '+' K_XTALK NUMBER .

    $default  reduce using rule 385 (net_option)


State 737

  402 net_option: '+' K_PROPERTY $@76 . net_prop_list

    T_STRING  shift, and go to state 880

    net_prop_list  go to state 881
    net_prop       go to state 882


State 738

  393 net_option: '+' K_SHIELDNET $@70 . T_STRING

    T_STRING  shift, and go to state 883


State 739

  396 net_option: '+' K_NOSHIELD $@71 . $@72 paths

    $default  reduce using rule 395 ($@72)

    $@72  go to state 884


State 740

  380 net_option: '+' K_FREQUENCY $@67 . NUMBER

    NUMBER  shift, and go to state 885


State 741

  376 net_option: '+' net_type $@66 . paths

    T_STRING  shift, and go to state 886

    paths  go to state 887
    path   go to state 888


State 742

  420 vpin_layer_opt: K_LAYER $@79 . T_STRING

    T_STRING  shift, and go to state 889


State 743

  415 vpin_stmt: vpin_begin vpin_layer_opt pt . pt $@77 vpin_options

    '('  shift, and go to state 188

    pt  go to state 890


State 744

  515 snet_other_option: '+' K_RECT $@91 . T_STRING pt pt

    T_STRING  shift, and go to state 891


State 745

  519 snet_other_option: '+' K_SOURCE source_type .

    $default  reduce using rule 519 (snet_other_option)


State 746

  525 snet_other_option: '+' K_WEIGHT NUMBER .

    $default  reduce using rule 525 (snet_other_option)


State 747

  523 snet_other_option: '+' K_ORIGINAL $@94 . T_STRING

    T_STRING  shift, and go to state 892


State 748

  527 snet_other_option: '+' K_USE use_type .

    $default  reduce using rule 527 (snet_other_option)


State 749

  528 snet_other_option: '+' K_STYLE NUMBER .

    $default  reduce using rule 528 (snet_other_option)


State 750

  524 snet_other_option: '+' K_PATTERN pattern_type .

    $default  reduce using rule 524 (snet_other_option)


State 751

  526 snet_other_option: '+' K_ESTCAP NUMBER .

    $default  reduce using rule 526 (snet_other_option)


State 752

  481 shape_type: K_RING .

    $default  reduce using rule 481 (shape_type)


State 753

  482 shape_type: K_STRIPE .

    $default  reduce using rule 482 (shape_type)


State 754

  483 shape_type: K_FOLLOWPIN .

    $default  reduce using rule 483 (shape_type)


State 755

  484 shape_type: K_IOWIRE .

    $default  reduce using rule 484 (shape_type)


State 756

  485 shape_type: K_COREWIRE .

    $default  reduce using rule 485 (shape_type)


State 757

  486 shape_type: K_BLOCKWIRE .

    $default  reduce using rule 486 (shape_type)


State 758

  487 shape_type: K_FILLWIRE .

    $default  reduce using rule 487 (shape_type)


State 759

  490 shape_type: K_BLOCKAGEWIRE .

    $default  reduce using rule 490 (shape_type)


State 760

  491 shape_type: K_PADRING .

    $default  reduce using rule 491 (shape_type)


State 761

  492 shape_type: K_BLOCKRING .

    $default  reduce using rule 492 (shape_type)


State 762

  489 shape_type: K_DRCFILL .

    $default  reduce using rule 489 (shape_type)


State 763

  488 shape_type: K_FILLWIREOPC .

    $default  reduce using rule 488 (shape_type)


State 764

  509 snet_other_option: '+' K_SHAPE shape_type .

    $default  reduce using rule 509 (snet_other_option)


State 765

  545 snet_width: '+' K_WIDTH $@97 . T_STRING NUMBER

    T_STRING  shift, and go to state 893


State 766

  547 snet_voltage: '+' K_VOLTAGE $@98 . T_STRING

    T_STRING  shift, and go to state 894


State 767

  550 snet_spacing: '+' K_SPACING $@99 . T_STRING NUMBER $@100 opt_snet_range

    T_STRING  shift, and go to state 895


State 768

  530 snet_other_option: '+' K_PROPERTY $@95 . snet_prop_list

    T_STRING  shift, and go to state 896

    snet_prop_list  go to state 897
    snet_prop       go to state 898


State 769

  510 snet_other_option: '+' K_MASK NUMBER .

    $default  reduce using rule 510 (snet_other_option)


State 770

  508 snet_other_option: '+' K_SHIELD $@87 . T_STRING $@88 shield_layer

    T_STRING  shift, and go to state 899


State 771

  521 snet_other_option: '+' K_FREQUENCY NUMBER .

    $default  reduce using rule 521 (snet_other_option)


State 772

  513 snet_other_option: '+' K_POLYGON $@89 . T_STRING $@90 firstPt nextPt nextPt otherPts

    T_STRING  shift, and go to state 900


State 773

  518 snet_other_option: '+' K_VIA $@92 . T_STRING orient_pt $@93 firstPt otherPts

    T_STRING  shift, and go to state 901


State 774

  505 snet_other_option: '+' net_type $@86 . spaths

    T_STRING  shift, and go to state 902

    spaths  go to state 903
    spath   go to state 904


State 775

  590 group_option: '+' K_REGION . $@106 group_region

    $default  reduce using rule 589 ($@106)

    $@106  go to state 905


State 776

  586 group_option: '+' K_SOFT . group_soft_options

    $default  reduce using rule 599 (group_soft_options)

    group_soft_options  go to state 906


State 777

  588 group_option: '+' K_PROPERTY . $@105 group_prop_list

    $default  reduce using rule 587 ($@105)

    $@105  go to state 907


State 778

  617 operand: K_PATH $@108 T_STRING T_STRING . T_STRING T_STRING

    T_STRING  shift, and go to state 908


State 779

  618 operand: K_SUM '(' operand_list ')' .

    $default  reduce using rule 618 (operand)


State 780

  622 operand_list: operand_list ',' . operand

    K_NET   shift, and go to state 358
    K_PATH  shift, and go to state 359
    K_SUM   shift, and go to state 360
    K_DIFF  shift, and go to state 361

    operand  go to state 909


State 781

  621 operand_list: operand_list operand .

    $default  reduce using rule 621 (operand_list)


State 782

  619 operand: K_DIFF '(' operand_list ')' .

    $default  reduce using rule 619 (operand)


State 783

  626 opt_plus: '+' .

    $default  reduce using rule 626 (opt_plus)


State 784

  624 wiredlogic_rule: '-' K_WIREDLOGIC $@109 T_STRING opt_plus . K_MAXDIST NUMBER ';'

    K_MAXDIST  shift, and go to state 910


State 785

  629 delay_spec: '+' K_RISEMIN . NUMBER

    NUMBER  shift, and go to state 911


State 786

  630 delay_spec: '+' K_RISEMAX . NUMBER

    NUMBER  shift, and go to state 912


State 787

  631 delay_spec: '+' K_FALLMIN . NUMBER

    NUMBER  shift, and go to state 913


State 788

  632 delay_spec: '+' K_FALLMAX . NUMBER

    NUMBER  shift, and go to state 914


State 789

  647 scan_member: '+' K_START $@111 . T_STRING opt_pin

    T_STRING  shift, and go to state 915


State 790

  649 scan_member: '+' K_FLOATING $@112 . floating_inst_list

    $default  reduce using rule 662 (floating_inst_list)

    floating_inst_list  go to state 916


State 791

  651 scan_member: '+' K_ORDERED $@113 . ordered_inst_list

    $default  reduce using rule 670 (ordered_inst_list)

    ordered_inst_list  go to state 917


State 792

  653 scan_member: '+' K_STOP $@114 . T_STRING opt_pin

    T_STRING  shift, and go to state 918


State 793

  655 scan_member: '+' K_COMMONSCANPINS $@115 . opt_common_pins

    '('  shift, and go to state 919

    $default  reduce using rule 659 (opt_common_pins)

    opt_common_pins  go to state 920


State 794

  657 scan_member: '+' K_PARTITION $@116 . T_STRING partition_maxbits

    T_STRING  shift, and go to state 921


State 795

  687 start_iotiming: '-' '(' $@119 T_STRING T_STRING . ')'

    ')'  shift, and go to state 922


State 796

  692 iotiming_member: '+' K_CAPACITANCE NUMBER .

    $default  reduce using rule 692 (iotiming_member)


State 797

  695 iotiming_member: '+' K_DRIVECELL $@120 . T_STRING $@121 iotiming_drivecell_opt

    T_STRING  shift, and go to state 923


State 798

  690 iotiming_member: '+' risefall K_VARIABLE . NUMBER NUMBER

    NUMBER  shift, and go to state 924


State 799

  691 iotiming_member: '+' risefall K_SLEWRATE . NUMBER NUMBER

    NUMBER  shift, and go to state 925


State 800

  714 fp_stmt: '-' $@125 T_STRING h_or_v $@126 . constraint_type constrain_what_list ';'

    K_ALIGN  shift, and go to state 926
    K_MIN    shift, and go to state 927
    K_MAX    shift, and go to state 928
    K_EQUAL  shift, and go to state 929

    constraint_type  go to state 930


State 801

  739 timingdisables_rule: '-' K_FROMPIN $@131 T_STRING T_STRING . K_TOPIN $@132 T_STRING T_STRING ';'

    K_TOPIN  shift, and go to state 931


State 802

  741 timingdisables_rule: '-' K_THRUPIN $@133 T_STRING T_STRING . ';'

    ';'  shift, and go to state 932


State 803

  747 td_macro_option: K_FROMPIN . $@135 T_STRING K_TOPIN $@136 T_STRING

    $default  reduce using rule 745 ($@135)

    $@135  go to state 933


State 804

  749 td_macro_option: K_THRUPIN . $@137 T_STRING

    $default  reduce using rule 748 ($@137)

    $@137  go to state 934


State 805

  743 timingdisables_rule: '-' K_MACRO $@134 T_STRING td_macro_option . ';'

    ';'  shift, and go to state 935


State 806

  761 turnoff_setup: K_SETUPRISE .

    $default  reduce using rule 761 (turnoff_setup)


State 807

  762 turnoff_setup: K_SETUPFALL .

    $default  reduce using rule 762 (turnoff_setup)


State 808

  759 turnoff: K_TURNOFF turnoff_setup . turnoff_hold

    K_HOLDRISE  shift, and go to state 936
    K_HOLDFALL  shift, and go to state 937

    $default  reduce using rule 763 (turnoff_hold)

    turnoff_hold  go to state 938


State 809

  769 partition_member: '+' K_FROMCLOCKPIN $@139 . T_STRING T_STRING risefall minmaxpins

    T_STRING  shift, and go to state 939


State 810

  771 partition_member: '+' K_FROMCOMPPIN $@140 . T_STRING T_STRING risefallminmax2_list

    T_STRING  shift, and go to state 940


State 811

  773 partition_member: '+' K_FROMIOPIN $@141 . T_STRING risefallminmax1_list

    T_STRING  shift, and go to state 941


State 812

  775 partition_member: '+' K_TOCLOCKPIN $@142 . T_STRING T_STRING risefall minmaxpins

    T_STRING  shift, and go to state 942


State 813

  777 partition_member: '+' K_TOCOMPPIN $@143 . T_STRING T_STRING risefallminmax2_list

    T_STRING  shift, and go to state 943


State 814

  779 partition_member: '+' K_TOIOPIN $@144 . T_STRING risefallminmax1_list

    T_STRING  shift, and go to state 944


State 815

  827 pin_prop_terminal: '-' $@149 T_STRING T_STRING $@150 . pin_prop_options ';'

    $default  reduce using rule 828 (pin_prop_options)

    pin_prop_options  go to state 945


State 816

  845 blockage_rule: '-' K_LAYER $@152 T_STRING $@153 . layer_blockage_rules

    $default  reduce using rule 848 (layer_blockage_rules)

    layer_blockage_rules  go to state 946


State 817

  864 placement_comp_rule: '+' . K_COMPONENT $@156 T_STRING
  865                    | '+' . K_PUSHDOWN
  866                    | '+' . K_SOFT
  867                    | '+' . K_PARTIAL NUMBER

    K_SOFT       shift, and go to state 947
    K_COMPONENT  shift, and go to state 948
    K_PUSHDOWN   shift, and go to state 949
    K_PARTIAL    shift, and go to state 950


State 818

  862 placement_comp_rules: placement_comp_rules placement_comp_rule .

    $default  reduce using rule 862 (placement_comp_rules)


State 819

  872 rectPoly_blockage: K_POLYGON $@157 firstPt nextPt . nextPt otherPts

    '('  shift, and go to state 188

    nextPt  go to state 951
    pt      go to state 321


State 820

  881 slot_rule: '-' K_LAYER $@158 T_STRING $@159 . geom_slot

    K_RECT     shift, and go to state 503
    K_POLYGON  shift, and go to state 504

    geom_slot  go to state 952


State 821

  884 geom_slot: K_RECT pt pt .

    $default  reduce using rule 884 (geom_slot)


State 822

  886 geom_slot: K_POLYGON $@160 firstPt . nextPt nextPt otherPts

    '('  shift, and go to state 188

    nextPt  go to state 953
    pt      go to state 321


State 823

  898 fill_rule: '-' K_LAYER $@163 T_STRING $@164 . fill_layer_mask_opc_opt geom_fill

    $default  reduce using rule 904 (fill_layer_mask_opc_opt)

    fill_layer_mask_opc_opt  go to state 954


State 824

  895 fill_def: '-' K_VIA $@161 T_STRING $@162 . fill_via_mask_opc_opt fill_via_pt ';'

    $default  reduce using rule 910 (fill_via_mask_opc_opt)

    fill_via_mask_opc_opt  go to state 955


State 825

  901 geom_fill: K_RECT pt pt .

    $default  reduce using rule 901 (geom_fill)


State 826

  903 geom_fill: K_POLYGON $@165 firstPt . nextPt nextPt otherPts

    '('  shift, and go to state 188

    nextPt  go to state 956
    pt      go to state 321


State 827

  924 nondefault_def: '-' $@166 T_STRING $@167 nondefault_options ';' .

    $default  reduce using rule 924 (nondefault_def)


State 828

  927 nondefault_option: '+' . K_HARDSPACING
  930                  | '+' . K_LAYER $@168 T_STRING K_WIDTH NUMBER $@169 nondefault_layer_options
  932                  | '+' . K_VIA $@170 T_STRING
  934                  | '+' . K_VIARULE $@171 T_STRING
  936                  | '+' . K_MINCUTS $@172 T_STRING NUMBER
  944 nondefault_prop_opt: '+' . K_PROPERTY $@173 nondefault_prop_list

    K_LAYER        shift, and go to state 957
    K_PROPERTY     shift, and go to state 958
    K_HARDSPACING  shift, and go to state 959
    K_MINCUTS      shift, and go to state 960
    K_VIA          shift, and go to state 961
    K_VIARULE      shift, and go to state 962


State 829

  926 nondefault_options: nondefault_options nondefault_option .

    $default  reduce using rule 926 (nondefault_options)


State 830

  937 nondefault_option: nondefault_prop_opt .

    $default  reduce using rule 937 (nondefault_option)


State 831

  956 styles_rule: '-' K_STYLE NUMBER $@174 firstPt . nextPt otherPts ';'

    '('  shift, and go to state 188

    nextPt  go to state 963
    pt      go to state 321


State 832

   96 canplace: K_CANPLACE $@18 T_STRING NUMBER NUMBER orient K_DO NUMBER . K_BY NUMBER K_STEP NUMBER NUMBER ';'

    K_BY  shift, and go to state 964


State 833

   98 cannotoccupy: K_CANNOTOCCUPY $@19 T_STRING NUMBER NUMBER orient K_DO NUMBER . K_BY NUMBER K_STEP NUMBER NUMBER ';'

    K_BY  shift, and go to state 965


State 834

  216 gcellgrid: K_GCELLGRID track_type NUMBER K_DO NUMBER K_STEP NUMBER ';' .

    $default  reduce using rule 216 (gcellgrid)


State 835

  186 row_rule: K_ROW $@35 T_STRING T_STRING NUMBER NUMBER orient $@36 . row_do_option row_options ';'

    K_DO  shift, and go to state 966

    $default  reduce using rule 187 (row_do_option)

    row_do_option  go to state 967


State 836

   88 property_type_and_val: K_STRING QSTRING .

    $default  reduce using rule 88 (property_type_and_val)


State 837

   86 property_type_and_val: K_REAL $@17 . opt_range opt_num_val

    K_RANGE  shift, and go to state 968

    $default  reduce using rule 558 (opt_range)

    opt_range  go to state 969


State 838

   84 property_type_and_val: K_INTEGER $@16 . opt_range opt_num_val

    K_RANGE  shift, and go to state 968

    $default  reduce using rule 558 (opt_range)

    opt_range  go to state 970


State 839

   89 property_type_and_val: K_NAMEMAPSTRING T_STRING .

    $default  reduce using rule 89 (property_type_and_val)


State 840

   65 property_def: K_DESIGN $@7 T_STRING property_type_and_val ';' .

    $default  reduce using rule 65 (property_def)


State 841

   77 property_def: K_ROW $@13 T_STRING property_type_and_val ';' .

    $default  reduce using rule 77 (property_def)


State 842

   71 property_def: K_REGION $@10 T_STRING property_type_and_val ';' .

    $default  reduce using rule 71 (property_def)


State 843

   81 property_def: K_NONDEFAULTRULE $@15 T_STRING property_type_and_val ';' .

    $default  reduce using rule 81 (property_def)


State 844

   73 property_def: K_GROUP $@11 T_STRING property_type_and_val ';' .

    $default  reduce using rule 73 (property_def)


State 845

   67 property_def: K_NET $@8 T_STRING property_type_and_val ';' .

    $default  reduce using rule 67 (property_def)


State 846

   75 property_def: K_COMPONENT $@12 T_STRING property_type_and_val ';' .

    $default  reduce using rule 75 (property_def)


State 847

   69 property_def: K_SNET $@9 T_STRING property_type_and_val ';' .

    $default  reduce using rule 69 (property_def)


State 848

   79 property_def: K_COMPONENTPIN $@14 T_STRING property_type_and_val ';' .

    $default  reduce using rule 79 (property_def)


State 849

  122 pin: '-' $@21 T_STRING '+' K_NET $@22 . T_STRING $@23 pin_options ';'

    T_STRING  shift, and go to state 971


State 850

  207 track_mask_statement: K_MASK . NUMBER same_mask

    NUMBER  shift, and go to state 972


State 851

  201 tracks_rule: track_start NUMBER $@38 K_DO NUMBER K_STEP NUMBER track_opts . ';'

    ';'  shift, and go to state 973


State 852

  205 track_opts: track_mask_statement . track_layer_statement

    K_LAYER  shift, and go to state 974

    $default  reduce using rule 210 (track_layer_statement)

    track_layer_statement  go to state 975


State 853

  225 via_declaration: '-' $@40 T_STRING $@41 layer_stmts ';' .

    $default  reduce using rule 225 (via_declaration)


State 854

  218 extension_stmt: '+' . K_BEGINEXT
  229 layer_stmt: '+' . K_RECT $@42 T_STRING mask pt pt
  232           | '+' . K_POLYGON $@43 T_STRING mask $@44 firstPt nextPt nextPt otherPts
  234           | '+' . K_PATTERNNAME $@45 T_STRING
  237           | '+' . K_VIARULE $@46 T_STRING '+' K_CUTSIZE NUMBER NUMBER '+' K_LAYERS $@47 T_STRING T_STRING T_STRING '+' K_CUTSPACING NUMBER NUMBER '+' K_ENCLOSURE NUMBER NUMBER NUMBER NUMBER
  240 layer_viarule_opts: '+' . K_ROWCOL NUMBER NUMBER
  241                   | '+' . K_ORIGIN NUMBER NUMBER
  242                   | '+' . K_OFFSET NUMBER NUMBER NUMBER NUMBER
  244                   | '+' . K_PATTERN $@48 T_STRING

    K_RECT         shift, and go to state 976
    K_PATTERN      shift, and go to state 977
    K_PATTERNNAME  shift, and go to state 978
    K_BEGINEXT     shift, and go to state 560
    K_OFFSET       shift, and go to state 979
    K_ORIGIN       shift, and go to state 980
    K_ROWCOL       shift, and go to state 981
    K_POLYGON      shift, and go to state 982
    K_VIARULE      shift, and go to state 983


State 855

  239 layer_stmt: extension_stmt .

    $default  reduce using rule 239 (layer_stmt)


State 856

  227 layer_stmts: layer_stmts layer_stmt .

    $default  reduce using rule 227 (layer_stmts)


State 857

  238 layer_stmt: layer_viarule_opts .

    $default  reduce using rule 238 (layer_stmt)


State 858

  263 rect_list: pt pt .

    $default  reduce using rule 263 (rect_list)


State 859

  264 rect_list: rect_list pt . pt

    '('  shift, and go to state 188

    pt  go to state 984


State 860

  262 regions_stmt: '-' $@49 T_STRING $@50 rect_list region_options . ';'
  266 region_options: region_options . region_option

    ';'  shift, and go to state 985
    '+'  shift, and go to state 986

    region_option  go to state 987


State 861

  310 comp_generate: '+' K_COMP_GEN $@54 T_STRING . opt_pattern

    T_STRING  shift, and go to state 988

    $default  reduce using rule 311 (opt_pattern)

    opt_pattern  go to state 989


State 862

  308 comp_eeq: '+' K_EEQMASTER $@53 T_STRING .

    $default  reduce using rule 308 (comp_eeq)


State 863

  342 comp_type: '+' K_UNPLACED pt orient .

    $default  reduce using rule 342 (comp_type)


State 864

  337 comp_foreign: '+' K_FOREIGN $@58 T_STRING . opt_paren orient

    NUMBER  shift, and go to state 990
    '('     shift, and go to state 188

    pt         go to state 991
    opt_paren  go to state 992


State 865

  332 comp_prop: T_STRING . NUMBER
  333          | T_STRING . QSTRING
  334          | T_STRING . T_STRING

    QSTRING   shift, and go to state 993
    T_STRING  shift, and go to state 994
    NUMBER    shift, and go to state 995


State 866

  329 comp_property: '+' K_PROPERTY $@57 comp_prop_list .
  331 comp_prop_list: comp_prop_list . comp_prop

    T_STRING  shift, and go to state 865

    $default  reduce using rule 329 (comp_property)

    comp_prop  go to state 996


State 867

  330 comp_prop_list: comp_prop .

    $default  reduce using rule 330 (comp_prop_list)


State 868

  344 maskShift: '+' K_MASKSHIFT $@59 T_STRING .

    $default  reduce using rule 344 (maskShift)


State 869

  325 halo_soft: K_SOFT .

    $default  reduce using rule 325 (halo_soft)


State 870

  323 comp_halo: '+' K_HALO $@55 halo_soft . NUMBER NUMBER NUMBER NUMBER

    NUMBER  shift, and go to state 997


State 871

  327 comp_routehalo: '+' K_ROUTEHALO NUMBER $@56 . T_STRING T_STRING

    T_STRING  shift, and go to state 998


State 872

  321 comp_pnt_list: comp_pnt_list pt pt .

    $default  reduce using rule 321 (comp_pnt_list)


State 873

  365 net_connection: '(' . T_STRING $@63 T_STRING conn_opt ')'
  367               | '(' . '*' $@64 T_STRING conn_opt ')'
  369               | '(' . K_PIN $@65 T_STRING conn_opt ')'

    T_STRING  shift, and go to state 999
    K_PIN     shift, and go to state 1000
    '*'       shift, and go to state 1001


State 874

  363 net_connections: net_connections net_connection .

    $default  reduce using rule 363 (net_connections)


State 875

  361 net_name: K_MUSTJOIN '(' T_STRING $@62 . T_STRING ')'

    T_STRING  shift, and go to state 1002


State 876

  382 net_option: '+' K_ORIGINAL $@68 T_STRING .

    $default  reduce using rule 382 (net_option)


State 877

  390 net_option: '+' K_NONDEFAULTRULE $@69 T_STRING .

    $default  reduce using rule 390 (net_option)


State 878

  417 vpin_begin: '+' K_VPIN $@78 T_STRING .

    $default  reduce using rule 417 (vpin_begin)


State 879

  400 net_option: '+' K_SUBNET $@73 T_STRING . $@74 comp_names $@75 subnet_options

    $default  reduce using rule 398 ($@74)

    $@74  go to state 1003


State 880

  406 net_prop: T_STRING . NUMBER
  407         | T_STRING . QSTRING
  408         | T_STRING . T_STRING

    QSTRING   shift, and go to state 1004
    T_STRING  shift, and go to state 1005
    NUMBER    shift, and go to state 1006


State 881

  402 net_option: '+' K_PROPERTY $@76 net_prop_list .
  405 net_prop_list: net_prop_list . net_prop

    T_STRING  shift, and go to state 880

    $default  reduce using rule 402 (net_option)

    net_prop  go to state 1007


State 882

  404 net_prop_list: net_prop .

    $default  reduce using rule 404 (net_prop_list)


State 883

  393 net_option: '+' K_SHIELDNET $@70 T_STRING .

    $default  reduce using rule 393 (net_option)


State 884

  396 net_option: '+' K_NOSHIELD $@71 $@72 . paths

    T_STRING  shift, and go to state 886

    paths  go to state 1008
    path   go to state 888


State 885

  380 net_option: '+' K_FREQUENCY $@67 NUMBER .

    $default  reduce using rule 380 (net_option)


State 886

  435 path: T_STRING . $@81 opt_taper_style_s path_pt $@82 path_item_list

    $default  reduce using rule 433 ($@81)

    $@81  go to state 1009


State 887

  376 net_option: '+' net_type $@66 paths .
  430 paths: paths . new_path

    K_NEW  shift, and go to state 1010

    $default  reduce using rule 376 (net_option)

    new_path  go to state 1011


State 888

  429 paths: path .

    $default  reduce using rule 429 (paths)


State 889

  420 vpin_layer_opt: K_LAYER $@79 T_STRING .

    $default  reduce using rule 420 (vpin_layer_opt)


State 890

  415 vpin_stmt: vpin_begin vpin_layer_opt pt pt . $@77 vpin_options

    $default  reduce using rule 414 ($@77)

    $@77  go to state 1012


State 891

  515 snet_other_option: '+' K_RECT $@91 T_STRING . pt pt

    '('  shift, and go to state 188

    pt  go to state 1013


State 892

  523 snet_other_option: '+' K_ORIGINAL $@94 T_STRING .

    $default  reduce using rule 523 (snet_other_option)


State 893

  545 snet_width: '+' K_WIDTH $@97 T_STRING . NUMBER

    NUMBER  shift, and go to state 1014


State 894

  547 snet_voltage: '+' K_VOLTAGE $@98 T_STRING .

    $default  reduce using rule 547 (snet_voltage)


State 895

  550 snet_spacing: '+' K_SPACING $@99 T_STRING . NUMBER $@100 opt_snet_range

    NUMBER  shift, and go to state 1015


State 896

  553 snet_prop: T_STRING . NUMBER
  554          | T_STRING . QSTRING
  555          | T_STRING . T_STRING

    QSTRING   shift, and go to state 1016
    T_STRING  shift, and go to state 1017
    NUMBER    shift, and go to state 1018


State 897

  530 snet_other_option: '+' K_PROPERTY $@95 snet_prop_list .
  552 snet_prop_list: snet_prop_list . snet_prop

    T_STRING  shift, and go to state 896

    $default  reduce using rule 530 (snet_other_option)

    snet_prop  go to state 1019


State 898

  551 snet_prop_list: snet_prop .

    $default  reduce using rule 551 (snet_prop_list)


State 899

  508 snet_other_option: '+' K_SHIELD $@87 T_STRING . $@88 shield_layer

    $default  reduce using rule 507 ($@88)

    $@88  go to state 1020


State 900

  513 snet_other_option: '+' K_POLYGON $@89 T_STRING . $@90 firstPt nextPt nextPt otherPts

    $default  reduce using rule 512 ($@90)

    $@90  go to state 1021


State 901

  518 snet_other_option: '+' K_VIA $@92 T_STRING . orient_pt $@93 firstPt otherPts

    K_N   shift, and go to state 1022
    K_S   shift, and go to state 1023
    K_E   shift, and go to state 1024
    K_W   shift, and go to state 1025
    K_FN  shift, and go to state 1026
    K_FE  shift, and go to state 1027
    K_FS  shift, and go to state 1028
    K_FW  shift, and go to state 1029

    $default  reduce using rule 532 (orient_pt)

    orient_pt  go to state 1030


State 902

  570 spath: T_STRING . $@102 width opt_spaths path_pt $@103 path_item_list

    $default  reduce using rule 568 ($@102)

    $@102  go to state 1031


State 903

  505 snet_other_option: '+' net_type $@86 spaths .
  565 spaths: spaths . snew_path

    K_NEW  shift, and go to state 1032

    $default  reduce using rule 505 (snet_other_option)

    snew_path  go to state 1033


State 904

  564 spaths: spath .

    $default  reduce using rule 564 (spaths)


State 905

  590 group_option: '+' K_REGION $@106 . group_region

    T_STRING  shift, and go to state 1034
    '('       shift, and go to state 188

    pt            go to state 1035
    group_region  go to state 1036


State 906

  586 group_option: '+' K_SOFT group_soft_options .
  600 group_soft_options: group_soft_options . group_soft_option

    K_MAXX              shift, and go to state 1037
    K_MAXY              shift, and go to state 1038
    K_MAXHALFPERIMETER  shift, and go to state 1039

    $default  reduce using rule 586 (group_option)

    group_soft_option  go to state 1040


State 907

  588 group_option: '+' K_PROPERTY $@105 . group_prop_list

    $default  reduce using rule 594 (group_prop_list)

    group_prop_list  go to state 1041


State 908

  617 operand: K_PATH $@108 T_STRING T_STRING T_STRING . T_STRING

    T_STRING  shift, and go to state 1042


State 909

  622 operand_list: operand_list ',' operand .

    $default  reduce using rule 622 (operand_list)


State 910

  624 wiredlogic_rule: '-' K_WIREDLOGIC $@109 T_STRING opt_plus K_MAXDIST . NUMBER ';'

    NUMBER  shift, and go to state 1043


State 911

  629 delay_spec: '+' K_RISEMIN NUMBER .

    $default  reduce using rule 629 (delay_spec)


State 912

  630 delay_spec: '+' K_RISEMAX NUMBER .

    $default  reduce using rule 630 (delay_spec)


State 913

  631 delay_spec: '+' K_FALLMIN NUMBER .

    $default  reduce using rule 631 (delay_spec)


State 914

  632 delay_spec: '+' K_FALLMAX NUMBER .

    $default  reduce using rule 632 (delay_spec)


State 915

  647 scan_member: '+' K_START $@111 T_STRING . opt_pin

    T_STRING  shift, and go to state 1044

    $default  reduce using rule 644 (opt_pin)

    opt_pin  go to state 1045


State 916

  649 scan_member: '+' K_FLOATING $@112 floating_inst_list .
  663 floating_inst_list: floating_inst_list . one_floating_inst

    T_STRING  shift, and go to state 1046

    $default  reduce using rule 649 (scan_member)

    one_floating_inst  go to state 1047


State 917

  651 scan_member: '+' K_ORDERED $@113 ordered_inst_list .
  671 ordered_inst_list: ordered_inst_list . one_ordered_inst

    T_STRING  shift, and go to state 1048

    $default  reduce using rule 651 (scan_member)

    one_ordered_inst  go to state 1049


State 918

  653 scan_member: '+' K_STOP $@114 T_STRING . opt_pin

    T_STRING  shift, and go to state 1044

    $default  reduce using rule 644 (opt_pin)

    opt_pin  go to state 1050


State 919

  660 opt_common_pins: '(' . T_STRING T_STRING ')'
  661                | '(' . T_STRING T_STRING ')' '(' T_STRING T_STRING ')'

    T_STRING  shift, and go to state 1051


State 920

  655 scan_member: '+' K_COMMONSCANPINS $@115 opt_common_pins .

    $default  reduce using rule 655 (scan_member)


State 921

  657 scan_member: '+' K_PARTITION $@116 T_STRING . partition_maxbits

    K_MAXBITS  shift, and go to state 1052

    $default  reduce using rule 678 (partition_maxbits)

    partition_maxbits  go to state 1053


State 922

  687 start_iotiming: '-' '(' $@119 T_STRING T_STRING ')' .

    $default  reduce using rule 687 (start_iotiming)


State 923

  695 iotiming_member: '+' K_DRIVECELL $@120 T_STRING . $@121 iotiming_drivecell_opt

    $default  reduce using rule 694 ($@121)

    $@121  go to state 1054


State 924

  690 iotiming_member: '+' risefall K_VARIABLE NUMBER . NUMBER

    NUMBER  shift, and go to state 1055


State 925

  691 iotiming_member: '+' risefall K_SLEWRATE NUMBER . NUMBER

    NUMBER  shift, and go to state 1056


State 926

  717 constraint_type: K_ALIGN .

    $default  reduce using rule 717 (constraint_type)


State 927

  719 constraint_type: K_MIN . NUMBER

    NUMBER  shift, and go to state 1057


State 928

  718 constraint_type: K_MAX . NUMBER

    NUMBER  shift, and go to state 1058


State 929

  720 constraint_type: K_EQUAL . NUMBER

    NUMBER  shift, and go to state 1059


State 930

  714 fp_stmt: '-' $@125 T_STRING h_or_v $@126 constraint_type . constrain_what_list ';'

    $default  reduce using rule 721 (constrain_what_list)

    constrain_what_list  go to state 1060


State 931

  739 timingdisables_rule: '-' K_FROMPIN $@131 T_STRING T_STRING K_TOPIN . $@132 T_STRING T_STRING ';'

    $default  reduce using rule 738 ($@132)

    $@132  go to state 1061


State 932

  741 timingdisables_rule: '-' K_THRUPIN $@133 T_STRING T_STRING ';' .

    $default  reduce using rule 741 (timingdisables_rule)


State 933

  747 td_macro_option: K_FROMPIN $@135 . T_STRING K_TOPIN $@136 T_STRING

    T_STRING  shift, and go to state 1062


State 934

  749 td_macro_option: K_THRUPIN $@137 . T_STRING

    T_STRING  shift, and go to state 1063


State 935

  743 timingdisables_rule: '-' K_MACRO $@134 T_STRING td_macro_option ';' .

    $default  reduce using rule 743 (timingdisables_rule)


State 936

  764 turnoff_hold: K_HOLDRISE .

    $default  reduce using rule 764 (turnoff_hold)


State 937

  765 turnoff_hold: K_HOLDFALL .

    $default  reduce using rule 765 (turnoff_hold)


State 938

  759 turnoff: K_TURNOFF turnoff_setup turnoff_hold .

    $default  reduce using rule 759 (turnoff)


State 939

  769 partition_member: '+' K_FROMCLOCKPIN $@139 T_STRING . T_STRING risefall minmaxpins

    T_STRING  shift, and go to state 1064


State 940

  771 partition_member: '+' K_FROMCOMPPIN $@140 T_STRING . T_STRING risefallminmax2_list

    T_STRING  shift, and go to state 1065


State 941

  773 partition_member: '+' K_FROMIOPIN $@141 T_STRING . risefallminmax1_list

    $default  reduce using rule 789 (risefallminmax1_list)

    risefallminmax1_list  go to state 1066


State 942

  775 partition_member: '+' K_TOCLOCKPIN $@142 T_STRING . T_STRING risefall minmaxpins

    T_STRING  shift, and go to state 1067


State 943

  777 partition_member: '+' K_TOCOMPPIN $@143 T_STRING . T_STRING risefallminmax2_list

    T_STRING  shift, and go to state 1068


State 944

  779 partition_member: '+' K_TOIOPIN $@144 T_STRING . risefallminmax1_list

    $default  reduce using rule 789 (risefallminmax1_list)

    risefallminmax1_list  go to state 1069


State 945

  827 pin_prop_terminal: '-' $@149 T_STRING T_STRING $@150 pin_prop_options . ';'
  829 pin_prop_options: pin_prop_options . pin_prop

    ';'  shift, and go to state 1070
    '+'  shift, and go to state 1071

    pin_prop  go to state 1072


State 946

  845 blockage_rule: '-' K_LAYER $@152 T_STRING $@153 layer_blockage_rules .
  849 layer_blockage_rules: layer_blockage_rules . layer_blockage_rule

    '+'  shift, and go to state 1073

    $default  reduce using rule 845 (blockage_rule)

    layer_blockage_rule  go to state 1074
    mask_blockage_rule   go to state 1075
    comp_blockage_rule   go to state 1076


State 947

  866 placement_comp_rule: '+' K_SOFT .

    $default  reduce using rule 866 (placement_comp_rule)


State 948

  864 placement_comp_rule: '+' K_COMPONENT . $@156 T_STRING

    $default  reduce using rule 863 ($@156)

    $@156  go to state 1077


State 949

  865 placement_comp_rule: '+' K_PUSHDOWN .

    $default  reduce using rule 865 (placement_comp_rule)


State 950

  867 placement_comp_rule: '+' K_PARTIAL . NUMBER

    NUMBER  shift, and go to state 1078


State 951

  872 rectPoly_blockage: K_POLYGON $@157 firstPt nextPt nextPt . otherPts

    $default  reduce using rule 247 (otherPts)

    otherPts  go to state 1079


State 952

  881 slot_rule: '-' K_LAYER $@158 T_STRING $@159 geom_slot .

    $default  reduce using rule 881 (slot_rule)


State 953

  886 geom_slot: K_POLYGON $@160 firstPt nextPt . nextPt otherPts

    '('  shift, and go to state 188

    nextPt  go to state 1080
    pt      go to state 321


State 954

  898 fill_rule: '-' K_LAYER $@163 T_STRING $@164 fill_layer_mask_opc_opt . geom_fill
  905 fill_layer_mask_opc_opt: fill_layer_mask_opc_opt . opt_mask_opc_l

    K_RECT     shift, and go to state 509
    K_POLYGON  shift, and go to state 510
    '+'        shift, and go to state 1081

    geom_fill       go to state 1082
    opt_mask_opc_l  go to state 1083
    fill_layer_opc  go to state 1084
    fill_mask       go to state 1085


State 955

  895 fill_def: '-' K_VIA $@161 T_STRING $@162 fill_via_mask_opc_opt . fill_via_pt ';'
  911 fill_via_mask_opc_opt: fill_via_mask_opc_opt . opt_mask_opc

    '+'  shift, and go to state 1086
    '('  shift, and go to state 188

    firstPt       go to state 1087
    pt            go to state 190
    fill_via_pt   go to state 1088
    opt_mask_opc  go to state 1089
    fill_via_opc  go to state 1090
    fill_viaMask  go to state 1091


State 956

  903 geom_fill: K_POLYGON $@165 firstPt nextPt . nextPt otherPts

    '('  shift, and go to state 188

    nextPt  go to state 1092
    pt      go to state 321


State 957

  930 nondefault_option: '+' K_LAYER . $@168 T_STRING K_WIDTH NUMBER $@169 nondefault_layer_options

    $default  reduce using rule 928 ($@168)

    $@168  go to state 1093


State 958

  944 nondefault_prop_opt: '+' K_PROPERTY . $@173 nondefault_prop_list

    $default  reduce using rule 943 ($@173)

    $@173  go to state 1094


State 959

  927 nondefault_option: '+' K_HARDSPACING .

    $default  reduce using rule 927 (nondefault_option)


State 960

  936 nondefault_option: '+' K_MINCUTS . $@172 T_STRING NUMBER

    $default  reduce using rule 935 ($@172)

    $@172  go to state 1095


State 961

  932 nondefault_option: '+' K_VIA . $@170 T_STRING

    $default  reduce using rule 931 ($@170)

    $@170  go to state 1096


State 962

  934 nondefault_option: '+' K_VIARULE . $@171 T_STRING

    $default  reduce using rule 933 ($@171)

    $@171  go to state 1097


State 963

  956 styles_rule: '-' K_STYLE NUMBER $@174 firstPt nextPt . otherPts ';'

    $default  reduce using rule 247 (otherPts)

    otherPts  go to state 1098


State 964

   96 canplace: K_CANPLACE $@18 T_STRING NUMBER NUMBER orient K_DO NUMBER K_BY . NUMBER K_STEP NUMBER NUMBER ';'

    NUMBER  shift, and go to state 1099


State 965

   98 cannotoccupy: K_CANNOTOCCUPY $@19 T_STRING NUMBER NUMBER orient K_DO NUMBER K_BY . NUMBER K_STEP NUMBER NUMBER ';'

    NUMBER  shift, and go to state 1100


State 966

  188 row_do_option: K_DO . NUMBER K_BY NUMBER row_step_option

    NUMBER  shift, and go to state 1101


State 967

  186 row_rule: K_ROW $@35 T_STRING T_STRING NUMBER NUMBER orient $@36 row_do_option . row_options ';'

    $default  reduce using rule 191 (row_options)

    row_options  go to state 1102


State 968

  559 opt_range: K_RANGE . NUMBER NUMBER

    NUMBER  shift, and go to state 1103


State 969

   86 property_type_and_val: K_REAL $@17 opt_range . opt_num_val

    NUMBER  shift, and go to state 1104

    $default  reduce using rule 90 (opt_num_val)

    opt_num_val  go to state 1105


State 970

   84 property_type_and_val: K_INTEGER $@16 opt_range . opt_num_val

    NUMBER  shift, and go to state 1104

    $default  reduce using rule 90 (opt_num_val)

    opt_num_val  go to state 1106


State 971

  122 pin: '-' $@21 T_STRING '+' K_NET $@22 T_STRING . $@23 pin_options ';'

    $default  reduce using rule 121 ($@23)

    $@23  go to state 1107


State 972

  207 track_mask_statement: K_MASK NUMBER . same_mask

    K_SAMEMASK  shift, and go to state 1108

    $default  reduce using rule 208 (same_mask)

    same_mask  go to state 1109


State 973

  201 tracks_rule: track_start NUMBER $@38 K_DO NUMBER K_STEP NUMBER track_opts ';' .

    $default  reduce using rule 201 (tracks_rule)


State 974

  212 track_layer_statement: K_LAYER . $@39 track_layer track_layers

    $default  reduce using rule 211 ($@39)

    $@39  go to state 1110


State 975

  205 track_opts: track_mask_statement track_layer_statement .

    $default  reduce using rule 205 (track_opts)


State 976

  229 layer_stmt: '+' K_RECT . $@42 T_STRING mask pt pt

    $default  reduce using rule 228 ($@42)

    $@42  go to state 1111


State 977

  244 layer_viarule_opts: '+' K_PATTERN . $@48 T_STRING

    $default  reduce using rule 243 ($@48)

    $@48  go to state 1112


State 978

  234 layer_stmt: '+' K_PATTERNNAME . $@45 T_STRING

    $default  reduce using rule 233 ($@45)

    $@45  go to state 1113


State 979

  242 layer_viarule_opts: '+' K_OFFSET . NUMBER NUMBER NUMBER NUMBER

    NUMBER  shift, and go to state 1114


State 980

  241 layer_viarule_opts: '+' K_ORIGIN . NUMBER NUMBER

    NUMBER  shift, and go to state 1115


State 981

  240 layer_viarule_opts: '+' K_ROWCOL . NUMBER NUMBER

    NUMBER  shift, and go to state 1116


State 982

  232 layer_stmt: '+' K_POLYGON . $@43 T_STRING mask $@44 firstPt nextPt nextPt otherPts

    $default  reduce using rule 230 ($@43)

    $@43  go to state 1117


State 983

  237 layer_stmt: '+' K_VIARULE . $@46 T_STRING '+' K_CUTSIZE NUMBER NUMBER '+' K_LAYERS $@47 T_STRING T_STRING T_STRING '+' K_CUTSPACING NUMBER NUMBER '+' K_ENCLOSURE NUMBER NUMBER NUMBER NUMBER

    $default  reduce using rule 235 ($@46)

    $@46  go to state 1118


State 984

  264 rect_list: rect_list pt pt .

    $default  reduce using rule 264 (rect_list)


State 985

  262 regions_stmt: '-' $@49 T_STRING $@50 rect_list region_options ';' .

    $default  reduce using rule 262 (regions_stmt)


State 986

  268 region_option: '+' . K_PROPERTY $@51 region_prop_list
  269              | '+' . K_TYPE region_type

    K_PROPERTY  shift, and go to state 1119
    K_TYPE      shift, and go to state 1120


State 987

  266 region_options: region_options region_option .

    $default  reduce using rule 266 (region_options)


State 988

  312 opt_pattern: T_STRING .

    $default  reduce using rule 312 (opt_pattern)


State 989

  310 comp_generate: '+' K_COMP_GEN $@54 T_STRING opt_pattern .

    $default  reduce using rule 310 (comp_generate)


State 990

  339 opt_paren: NUMBER . NUMBER

    NUMBER  shift, and go to state 1121


State 991

  338 opt_paren: pt .

    $default  reduce using rule 338 (opt_paren)


State 992

  337 comp_foreign: '+' K_FOREIGN $@58 T_STRING opt_paren . orient

    K_N   shift, and go to state 516
    K_S   shift, and go to state 517
    K_E   shift, and go to state 518
    K_W   shift, and go to state 519
    K_FN  shift, and go to state 520
    K_FE  shift, and go to state 521
    K_FS  shift, and go to state 522
    K_FW  shift, and go to state 523

    orient  go to state 1122


State 993

  333 comp_prop: T_STRING QSTRING .

    $default  reduce using rule 333 (comp_prop)


State 994

  334 comp_prop: T_STRING T_STRING .

    $default  reduce using rule 334 (comp_prop)


State 995

  332 comp_prop: T_STRING NUMBER .

    $default  reduce using rule 332 (comp_prop)


State 996

  331 comp_prop_list: comp_prop_list comp_prop .

    $default  reduce using rule 331 (comp_prop_list)


State 997

  323 comp_halo: '+' K_HALO $@55 halo_soft NUMBER . NUMBER NUMBER NUMBER

    NUMBER  shift, and go to state 1123


State 998

  327 comp_routehalo: '+' K_ROUTEHALO NUMBER $@56 T_STRING . T_STRING

    T_STRING  shift, and go to state 1124


State 999

  365 net_connection: '(' T_STRING . $@63 T_STRING conn_opt ')'

    $default  reduce using rule 364 ($@63)

    $@63  go to state 1125


State 1000

  369 net_connection: '(' K_PIN . $@65 T_STRING conn_opt ')'

    $default  reduce using rule 368 ($@65)

    $@65  go to state 1126


State 1001

  367 net_connection: '(' '*' . $@64 T_STRING conn_opt ')'

    $default  reduce using rule 366 ($@64)

    $@64  go to state 1127


State 1002

  361 net_name: K_MUSTJOIN '(' T_STRING $@62 T_STRING . ')'

    ')'  shift, and go to state 1128


State 1003

  400 net_option: '+' K_SUBNET $@73 T_STRING $@74 . comp_names $@75 subnet_options

    $default  reduce using rule 802 (comp_names)

    comp_names  go to state 1129


State 1004

  407 net_prop: T_STRING QSTRING .

    $default  reduce using rule 407 (net_prop)


State 1005

  408 net_prop: T_STRING T_STRING .

    $default  reduce using rule 408 (net_prop)


State 1006

  406 net_prop: T_STRING NUMBER .

    $default  reduce using rule 406 (net_prop)


State 1007

  405 net_prop_list: net_prop_list net_prop .

    $default  reduce using rule 405 (net_prop_list)


State 1008

  396 net_option: '+' K_NOSHIELD $@71 $@72 paths .
  430 paths: paths . new_path

    K_NEW  shift, and go to state 1010

    $default  reduce using rule 396 (net_option)

    new_path  go to state 1011


State 1009

  435 path: T_STRING $@81 . opt_taper_style_s path_pt $@82 path_item_list

    $default  reduce using rule 468 (opt_taper_style_s)

    opt_taper_style_s  go to state 1130


State 1010

  432 new_path: K_NEW . $@80 path

    $default  reduce using rule 431 ($@80)

    $@80  go to state 1131


State 1011

  430 paths: paths new_path .

    $default  reduce using rule 430 (paths)


State 1012

  415 vpin_stmt: vpin_begin vpin_layer_opt pt pt $@77 . vpin_options

    K_FIXED   shift, and go to state 1132
    K_COVER   shift, and go to state 1133
    K_PLACED  shift, and go to state 1134

    $default  reduce using rule 421 (vpin_options)

    vpin_options  go to state 1135
    vpin_status   go to state 1136


State 1013

  515 snet_other_option: '+' K_RECT $@91 T_STRING pt . pt

    '('  shift, and go to state 188

    pt  go to state 1137


State 1014

  545 snet_width: '+' K_WIDTH $@97 T_STRING NUMBER .

    $default  reduce using rule 545 (snet_width)


State 1015

  550 snet_spacing: '+' K_SPACING $@99 T_STRING NUMBER . $@100 opt_snet_range

    $default  reduce using rule 549 ($@100)

    $@100  go to state 1138


State 1016

  554 snet_prop: T_STRING QSTRING .

    $default  reduce using rule 554 (snet_prop)


State 1017

  555 snet_prop: T_STRING T_STRING .

    $default  reduce using rule 555 (snet_prop)


State 1018

  553 snet_prop: T_STRING NUMBER .

    $default  reduce using rule 553 (snet_prop)


State 1019

  552 snet_prop_list: snet_prop_list snet_prop .

    $default  reduce using rule 552 (snet_prop_list)


State 1020

  508 snet_other_option: '+' K_SHIELD $@87 T_STRING $@88 . shield_layer

    T_STRING  reduce using rule 542 ($@96)
    $default  reduce using rule 541 (shield_layer)

    shield_layer  go to state 1139
    $@96          go to state 1140


State 1021

  513 snet_other_option: '+' K_POLYGON $@89 T_STRING $@90 . firstPt nextPt nextPt otherPts

    '('  shift, and go to state 188

    firstPt  go to state 1141
    pt       go to state 190


State 1022

  533 orient_pt: K_N .

    $default  reduce using rule 533 (orient_pt)


State 1023

  535 orient_pt: K_S .

    $default  reduce using rule 535 (orient_pt)


State 1024

  536 orient_pt: K_E .

    $default  reduce using rule 536 (orient_pt)


State 1025

  534 orient_pt: K_W .

    $default  reduce using rule 534 (orient_pt)


State 1026

  537 orient_pt: K_FN .

    $default  reduce using rule 537 (orient_pt)


State 1027

  540 orient_pt: K_FE .

    $default  reduce using rule 540 (orient_pt)


State 1028

  539 orient_pt: K_FS .

    $default  reduce using rule 539 (orient_pt)


State 1029

  538 orient_pt: K_FW .

    $default  reduce using rule 538 (orient_pt)


State 1030

  518 snet_other_option: '+' K_VIA $@92 T_STRING orient_pt . $@93 firstPt otherPts

    $default  reduce using rule 517 ($@93)

    $@93  go to state 1142


State 1031

  570 spath: T_STRING $@102 . width opt_spaths path_pt $@103 path_item_list

    NUMBER  shift, and go to state 1143

    width  go to state 1144


State 1032

  567 snew_path: K_NEW . $@101 spath

    $default  reduce using rule 566 ($@101)

    $@101  go to state 1145


State 1033

  565 spaths: spaths snew_path .

    $default  reduce using rule 565 (spaths)


State 1034

  593 group_region: T_STRING .

    $default  reduce using rule 593 (group_region)


State 1035

  592 group_region: pt . pt

    '('  shift, and go to state 188

    pt  go to state 1146


State 1036

  590 group_option: '+' K_REGION $@106 group_region .

    $default  reduce using rule 590 (group_option)


State 1037

  601 group_soft_option: K_MAXX . NUMBER

    NUMBER  shift, and go to state 1147


State 1038

  602 group_soft_option: K_MAXY . NUMBER

    NUMBER  shift, and go to state 1148


State 1039

  603 group_soft_option: K_MAXHALFPERIMETER . NUMBER

    NUMBER  shift, and go to state 1149


State 1040

  600 group_soft_options: group_soft_options group_soft_option .

    $default  reduce using rule 600 (group_soft_options)


State 1041

  588 group_option: '+' K_PROPERTY $@105 group_prop_list .
  595 group_prop_list: group_prop_list . group_prop

    T_STRING  shift, and go to state 1150

    $default  reduce using rule 588 (group_option)

    group_prop  go to state 1151


State 1042

  617 operand: K_PATH $@108 T_STRING T_STRING T_STRING T_STRING .

    $default  reduce using rule 617 (operand)


State 1043

  624 wiredlogic_rule: '-' K_WIREDLOGIC $@109 T_STRING opt_plus K_MAXDIST NUMBER . ';'

    ';'  shift, and go to state 1152


State 1044

  645 opt_pin: T_STRING .

    $default  reduce using rule 645 (opt_pin)


State 1045

  647 scan_member: '+' K_START $@111 T_STRING opt_pin .

    $default  reduce using rule 647 (scan_member)


State 1046

  665 one_floating_inst: T_STRING . $@117 floating_pins

    $default  reduce using rule 664 ($@117)

    $@117  go to state 1153


State 1047

  663 floating_inst_list: floating_inst_list one_floating_inst .

    $default  reduce using rule 663 (floating_inst_list)


State 1048

  673 one_ordered_inst: T_STRING . $@118 ordered_pins

    $default  reduce using rule 672 ($@118)

    $@118  go to state 1154


State 1049

  671 ordered_inst_list: ordered_inst_list one_ordered_inst .

    $default  reduce using rule 671 (ordered_inst_list)


State 1050

  653 scan_member: '+' K_STOP $@114 T_STRING opt_pin .

    $default  reduce using rule 653 (scan_member)


State 1051

  660 opt_common_pins: '(' T_STRING . T_STRING ')'
  661                | '(' T_STRING . T_STRING ')' '(' T_STRING T_STRING ')'

    T_STRING  shift, and go to state 1155


State 1052

  679 partition_maxbits: K_MAXBITS . NUMBER

    NUMBER  shift, and go to state 1156


State 1053

  657 scan_member: '+' K_PARTITION $@116 T_STRING partition_maxbits .

    $default  reduce using rule 657 (scan_member)


State 1054

  695 iotiming_member: '+' K_DRIVECELL $@120 T_STRING $@121 . iotiming_drivecell_opt

    K_FROMPIN  shift, and go to state 1157

    $default  reduce using rule 700 (iotiming_frompin)

    iotiming_drivecell_opt  go to state 1158
    iotiming_frompin        go to state 1159


State 1055

  690 iotiming_member: '+' risefall K_VARIABLE NUMBER NUMBER .

    $default  reduce using rule 690 (iotiming_member)


State 1056

  691 iotiming_member: '+' risefall K_SLEWRATE NUMBER NUMBER .

    $default  reduce using rule 691 (iotiming_member)


State 1057

  719 constraint_type: K_MIN NUMBER .

    $default  reduce using rule 719 (constraint_type)


State 1058

  718 constraint_type: K_MAX NUMBER .

    $default  reduce using rule 718 (constraint_type)


State 1059

  720 constraint_type: K_EQUAL NUMBER .

    $default  reduce using rule 720 (constraint_type)


State 1060

  714 fp_stmt: '-' $@125 T_STRING h_or_v $@126 constraint_type constrain_what_list . ';'
  722 constrain_what_list: constrain_what_list . constrain_what

    ';'  shift, and go to state 1160
    '+'  shift, and go to state 1161

    constrain_what  go to state 1162


State 1061

  739 timingdisables_rule: '-' K_FROMPIN $@131 T_STRING T_STRING K_TOPIN $@132 . T_STRING T_STRING ';'

    T_STRING  shift, and go to state 1163


State 1062

  747 td_macro_option: K_FROMPIN $@135 T_STRING . K_TOPIN $@136 T_STRING

    K_TOPIN  shift, and go to state 1164


State 1063

  749 td_macro_option: K_THRUPIN $@137 T_STRING .

    $default  reduce using rule 749 (td_macro_option)


State 1064

  769 partition_member: '+' K_FROMCLOCKPIN $@139 T_STRING T_STRING . risefall minmaxpins

    K_RISE  shift, and go to state 632
    K_FALL  shift, and go to state 633

    risefall  go to state 1165


State 1065

  771 partition_member: '+' K_FROMCOMPPIN $@140 T_STRING T_STRING . risefallminmax2_list

    K_RISEMIN  shift, and go to state 1166
    K_RISEMAX  shift, and go to state 1167
    K_FALLMIN  shift, and go to state 1168
    K_FALLMAX  shift, and go to state 1169

    risefallminmax2_list  go to state 1170
    risefallminmax2       go to state 1171


State 1066

  773 partition_member: '+' K_FROMIOPIN $@141 T_STRING risefallminmax1_list .
  790 risefallminmax1_list: risefallminmax1_list . risefallminmax1

    K_RISEMIN  shift, and go to state 1172
    K_RISEMAX  shift, and go to state 1173
    K_FALLMIN  shift, and go to state 1174
    K_FALLMAX  shift, and go to state 1175

    $default  reduce using rule 773 (partition_member)

    risefallminmax1  go to state 1176


State 1067

  775 partition_member: '+' K_TOCLOCKPIN $@142 T_STRING T_STRING . risefall minmaxpins

    K_RISE  shift, and go to state 632
    K_FALL  shift, and go to state 633

    risefall  go to state 1177


State 1068

  777 partition_member: '+' K_TOCOMPPIN $@143 T_STRING T_STRING . risefallminmax2_list

    K_RISEMIN  shift, and go to state 1166
    K_RISEMAX  shift, and go to state 1167
    K_FALLMIN  shift, and go to state 1168
    K_FALLMAX  shift, and go to state 1169

    risefallminmax2_list  go to state 1178
    risefallminmax2       go to state 1171


State 1069

  779 partition_member: '+' K_TOIOPIN $@144 T_STRING risefallminmax1_list .
  790 risefallminmax1_list: risefallminmax1_list . risefallminmax1

    K_RISEMIN  shift, and go to state 1172
    K_RISEMAX  shift, and go to state 1173
    K_FALLMIN  shift, and go to state 1174
    K_FALLMAX  shift, and go to state 1175

    $default  reduce using rule 779 (partition_member)

    risefallminmax1  go to state 1176


State 1070

  827 pin_prop_terminal: '-' $@149 T_STRING T_STRING $@150 pin_prop_options ';' .

    $default  reduce using rule 827 (pin_prop_terminal)


State 1071

  831 pin_prop: '+' . K_PROPERTY $@151 pin_prop_name_value_list

    K_PROPERTY  shift, and go to state 1179


State 1072

  829 pin_prop_options: pin_prop_options pin_prop .

    $default  reduce using rule 829 (pin_prop_options)


State 1073

  850 layer_blockage_rule: '+' . K_SPACING NUMBER
  851                    | '+' . K_DESIGNRULEWIDTH NUMBER
  854 mask_blockage_rule: '+' . K_MASK NUMBER
  856 comp_blockage_rule: '+' . K_COMPONENT $@155 T_STRING
  857                   | '+' . K_SLOTS
  858                   | '+' . K_FILLS
  859                   | '+' . K_PUSHDOWN
  860                   | '+' . K_EXCEPTPGNET

    K_SPACING          shift, and go to state 1180
    K_COMPONENT        shift, and go to state 1181
    K_MASK             shift, and go to state 1182
    K_SLOTS            shift, and go to state 1183
    K_FILLS            shift, and go to state 1184
    K_PUSHDOWN         shift, and go to state 1185
    K_DESIGNRULEWIDTH  shift, and go to state 1186
    K_EXCEPTPGNET      shift, and go to state 1187


State 1074

  849 layer_blockage_rules: layer_blockage_rules layer_blockage_rule .

    $default  reduce using rule 849 (layer_blockage_rules)


State 1075

  852 layer_blockage_rule: mask_blockage_rule .

    $default  reduce using rule 852 (layer_blockage_rule)


State 1076

  853 layer_blockage_rule: comp_blockage_rule .

    $default  reduce using rule 853 (layer_blockage_rule)


State 1077

  864 placement_comp_rule: '+' K_COMPONENT $@156 . T_STRING

    T_STRING  shift, and go to state 1188


State 1078

  867 placement_comp_rule: '+' K_PARTIAL NUMBER .

    $default  reduce using rule 867 (placement_comp_rule)


State 1079

  248 otherPts: otherPts . nextPt
  872 rectPoly_blockage: K_POLYGON $@157 firstPt nextPt nextPt otherPts .

    '('  shift, and go to state 188

    $default  reduce using rule 872 (rectPoly_blockage)

    nextPt  go to state 531
    pt      go to state 321


State 1080

  886 geom_slot: K_POLYGON $@160 firstPt nextPt nextPt . otherPts

    $default  reduce using rule 247 (otherPts)

    otherPts  go to state 1189


State 1081

  908 fill_layer_opc: '+' . K_OPC
  915 fill_mask: '+' . K_MASK NUMBER

    K_MASK  shift, and go to state 1190
    K_OPC   shift, and go to state 1191


State 1082

  898 fill_rule: '-' K_LAYER $@163 T_STRING $@164 fill_layer_mask_opc_opt geom_fill .

    $default  reduce using rule 898 (fill_rule)


State 1083

  905 fill_layer_mask_opc_opt: fill_layer_mask_opc_opt opt_mask_opc_l .

    $default  reduce using rule 905 (fill_layer_mask_opc_opt)


State 1084

  906 opt_mask_opc_l: fill_layer_opc .

    $default  reduce using rule 906 (opt_mask_opc_l)


State 1085

  907 opt_mask_opc_l: fill_mask .

    $default  reduce using rule 907 (opt_mask_opc_l)


State 1086

  914 fill_via_opc: '+' . K_OPC
  916 fill_viaMask: '+' . K_MASK NUMBER

    K_MASK  shift, and go to state 1192
    K_OPC   shift, and go to state 1193


State 1087

  909 fill_via_pt: firstPt . otherPts

    $default  reduce using rule 247 (otherPts)

    otherPts  go to state 1194


State 1088

  895 fill_def: '-' K_VIA $@161 T_STRING $@162 fill_via_mask_opc_opt fill_via_pt . ';'

    ';'  shift, and go to state 1195


State 1089

  911 fill_via_mask_opc_opt: fill_via_mask_opc_opt opt_mask_opc .

    $default  reduce using rule 911 (fill_via_mask_opc_opt)


State 1090

  912 opt_mask_opc: fill_via_opc .

    $default  reduce using rule 912 (opt_mask_opc)


State 1091

  913 opt_mask_opc: fill_viaMask .

    $default  reduce using rule 913 (opt_mask_opc)


State 1092

  903 geom_fill: K_POLYGON $@165 firstPt nextPt nextPt . otherPts

    $default  reduce using rule 247 (otherPts)

    otherPts  go to state 1196


State 1093

  930 nondefault_option: '+' K_LAYER $@168 . T_STRING K_WIDTH NUMBER $@169 nondefault_layer_options

    T_STRING  shift, and go to state 1197


State 1094

  944 nondefault_prop_opt: '+' K_PROPERTY $@173 . nondefault_prop_list

    $default  reduce using rule 945 (nondefault_prop_list)

    nondefault_prop_list  go to state 1198


State 1095

  936 nondefault_option: '+' K_MINCUTS $@172 . T_STRING NUMBER

    T_STRING  shift, and go to state 1199


State 1096

  932 nondefault_option: '+' K_VIA $@170 . T_STRING

    T_STRING  shift, and go to state 1200


State 1097

  934 nondefault_option: '+' K_VIARULE $@171 . T_STRING

    T_STRING  shift, and go to state 1201


State 1098

  248 otherPts: otherPts . nextPt
  956 styles_rule: '-' K_STYLE NUMBER $@174 firstPt nextPt otherPts . ';'

    ';'  shift, and go to state 1202
    '('  shift, and go to state 188

    nextPt  go to state 531
    pt      go to state 321


State 1099

   96 canplace: K_CANPLACE $@18 T_STRING NUMBER NUMBER orient K_DO NUMBER K_BY NUMBER . K_STEP NUMBER NUMBER ';'

    K_STEP  shift, and go to state 1203


State 1100

   98 cannotoccupy: K_CANNOTOCCUPY $@19 T_STRING NUMBER NUMBER orient K_DO NUMBER K_BY NUMBER . K_STEP NUMBER NUMBER ';'

    K_STEP  shift, and go to state 1204


State 1101

  188 row_do_option: K_DO NUMBER . K_BY NUMBER row_step_option

    K_BY  shift, and go to state 1205


State 1102

  186 row_rule: K_ROW $@35 T_STRING T_STRING NUMBER NUMBER orient $@36 row_do_option row_options . ';'
  192 row_options: row_options . row_option

    ';'  shift, and go to state 1206
    '+'  shift, and go to state 1207

    row_option  go to state 1208


State 1103

  559 opt_range: K_RANGE NUMBER . NUMBER

    NUMBER  shift, and go to state 1209


State 1104

   91 opt_num_val: NUMBER .

    $default  reduce using rule 91 (opt_num_val)


State 1105

   86 property_type_and_val: K_REAL $@17 opt_range opt_num_val .

    $default  reduce using rule 86 (property_type_and_val)


State 1106

   84 property_type_and_val: K_INTEGER $@16 opt_range opt_num_val .

    $default  reduce using rule 84 (property_type_and_val)


State 1107

  122 pin: '-' $@21 T_STRING '+' K_NET $@22 T_STRING $@23 . pin_options ';'

    $default  reduce using rule 123 (pin_options)

    pin_options  go to state 1210


State 1108

  209 same_mask: K_SAMEMASK .

    $default  reduce using rule 209 (same_mask)


State 1109

  207 track_mask_statement: K_MASK NUMBER same_mask .

    $default  reduce using rule 207 (track_mask_statement)


State 1110

  212 track_layer_statement: K_LAYER $@39 . track_layer track_layers

    T_STRING  shift, and go to state 1211

    track_layer  go to state 1212


State 1111

  229 layer_stmt: '+' K_RECT $@42 . T_STRING mask pt pt

    T_STRING  shift, and go to state 1213


State 1112

  244 layer_viarule_opts: '+' K_PATTERN $@48 . T_STRING

    T_STRING  shift, and go to state 1214


State 1113

  234 layer_stmt: '+' K_PATTERNNAME $@45 . T_STRING

    T_STRING  shift, and go to state 1215


State 1114

  242 layer_viarule_opts: '+' K_OFFSET NUMBER . NUMBER NUMBER NUMBER

    NUMBER  shift, and go to state 1216


State 1115

  241 layer_viarule_opts: '+' K_ORIGIN NUMBER . NUMBER

    NUMBER  shift, and go to state 1217


State 1116

  240 layer_viarule_opts: '+' K_ROWCOL NUMBER . NUMBER

    NUMBER  shift, and go to state 1218


State 1117

  232 layer_stmt: '+' K_POLYGON $@43 . T_STRING mask $@44 firstPt nextPt nextPt otherPts

    T_STRING  shift, and go to state 1219


State 1118

  237 layer_stmt: '+' K_VIARULE $@46 . T_STRING '+' K_CUTSIZE NUMBER NUMBER '+' K_LAYERS $@47 T_STRING T_STRING T_STRING '+' K_CUTSPACING NUMBER NUMBER '+' K_ENCLOSURE NUMBER NUMBER NUMBER NUMBER

    T_STRING  shift, and go to state 1220


State 1119

  268 region_option: '+' K_PROPERTY . $@51 region_prop_list

    $default  reduce using rule 267 ($@51)

    $@51  go to state 1221


State 1120

  269 region_option: '+' K_TYPE . region_type

    K_FENCE  shift, and go to state 1222
    K_GUIDE  shift, and go to state 1223

    region_type  go to state 1224


State 1121

  339 opt_paren: NUMBER NUMBER .

    $default  reduce using rule 339 (opt_paren)


State 1122

  337 comp_foreign: '+' K_FOREIGN $@58 T_STRING opt_paren orient .

    $default  reduce using rule 337 (comp_foreign)


State 1123

  323 comp_halo: '+' K_HALO $@55 halo_soft NUMBER NUMBER . NUMBER NUMBER

    NUMBER  shift, and go to state 1225


State 1124

  327 comp_routehalo: '+' K_ROUTEHALO NUMBER $@56 T_STRING T_STRING .

    $default  reduce using rule 327 (comp_routehalo)


State 1125

  365 net_connection: '(' T_STRING $@63 . T_STRING conn_opt ')'

    T_STRING  shift, and go to state 1226


State 1126

  369 net_connection: '(' K_PIN $@65 . T_STRING conn_opt ')'

    T_STRING  shift, and go to state 1227


State 1127

  367 net_connection: '(' '*' $@64 . T_STRING conn_opt ')'

    T_STRING  shift, and go to state 1228


State 1128

  361 net_name: K_MUSTJOIN '(' T_STRING $@62 T_STRING ')' .

    $default  reduce using rule 361 (net_name)


State 1129

  400 net_option: '+' K_SUBNET $@73 T_STRING $@74 comp_names . $@75 subnet_options
  803 comp_names: comp_names . comp_name

    '('  shift, and go to state 1229

    $default  reduce using rule 399 ($@75)

    $@75       go to state 1230
    comp_name  go to state 1231


State 1130

  435 path: T_STRING $@81 opt_taper_style_s . path_pt $@82 path_item_list
  469 opt_taper_style_s: opt_taper_style_s . opt_taper_style

    K_STYLE      shift, and go to state 1232
    K_TAPER      shift, and go to state 1233
    K_TAPERRULE  shift, and go to state 1234
    '('          shift, and go to state 1235

    path_pt          go to state 1236
    opt_taper_style  go to state 1237
    opt_taper        go to state 1238
    opt_style        go to state 1239


State 1131

  432 new_path: K_NEW $@80 . path

    T_STRING  shift, and go to state 886

    path  go to state 1240


State 1132

  424 vpin_status: K_FIXED .

    $default  reduce using rule 424 (vpin_status)


State 1133

  425 vpin_status: K_COVER .

    $default  reduce using rule 425 (vpin_status)


State 1134

  423 vpin_status: K_PLACED .

    $default  reduce using rule 423 (vpin_status)


State 1135

  415 vpin_stmt: vpin_begin vpin_layer_opt pt pt $@77 vpin_options .

    $default  reduce using rule 415 (vpin_stmt)


State 1136

  422 vpin_options: vpin_status . pt orient

    '('  shift, and go to state 188

    pt  go to state 1241


State 1137

  515 snet_other_option: '+' K_RECT $@91 T_STRING pt pt .

    $default  reduce using rule 515 (snet_other_option)


State 1138

  550 snet_spacing: '+' K_SPACING $@99 T_STRING NUMBER $@100 . opt_snet_range

    K_RANGE  shift, and go to state 1242

    $default  reduce using rule 556 (opt_snet_range)

    opt_snet_range  go to state 1243


State 1139

  508 snet_other_option: '+' K_SHIELD $@87 T_STRING $@88 shield_layer .

    $default  reduce using rule 508 (snet_other_option)


State 1140

  543 shield_layer: $@96 . spaths

    T_STRING  shift, and go to state 902

    spaths  go to state 1244
    spath   go to state 904


State 1141

  513 snet_other_option: '+' K_POLYGON $@89 T_STRING $@90 firstPt . nextPt nextPt otherPts

    '('  shift, and go to state 188

    nextPt  go to state 1245
    pt      go to state 321


State 1142

  518 snet_other_option: '+' K_VIA $@92 T_STRING orient_pt $@93 . firstPt otherPts

    '('  shift, and go to state 188

    firstPt  go to state 1246
    pt       go to state 190


State 1143

  571 width: NUMBER .

    $default  reduce using rule 571 (width)


State 1144

  570 spath: T_STRING $@102 width . opt_spaths path_pt $@103 path_item_list

    $default  reduce using rule 476 (opt_spaths)

    opt_spaths  go to state 1247


State 1145

  567 snew_path: K_NEW $@101 . spath

    T_STRING  shift, and go to state 902

    spath  go to state 1248


State 1146

  592 group_region: pt pt .

    $default  reduce using rule 592 (group_region)


State 1147

  601 group_soft_option: K_MAXX NUMBER .

    $default  reduce using rule 601 (group_soft_option)


State 1148

  602 group_soft_option: K_MAXY NUMBER .

    $default  reduce using rule 602 (group_soft_option)


State 1149

  603 group_soft_option: K_MAXHALFPERIMETER NUMBER .

    $default  reduce using rule 603 (group_soft_option)


State 1150

  596 group_prop: T_STRING . NUMBER
  597           | T_STRING . QSTRING
  598           | T_STRING . T_STRING

    QSTRING   shift, and go to state 1249
    T_STRING  shift, and go to state 1250
    NUMBER    shift, and go to state 1251


State 1151

  595 group_prop_list: group_prop_list group_prop .

    $default  reduce using rule 595 (group_prop_list)


State 1152

  624 wiredlogic_rule: '-' K_WIREDLOGIC $@109 T_STRING opt_plus K_MAXDIST NUMBER ';' .

    $default  reduce using rule 624 (wiredlogic_rule)


State 1153

  665 one_floating_inst: T_STRING $@117 . floating_pins

    '('  shift, and go to state 1252

    $default  reduce using rule 666 (floating_pins)

    floating_pins  go to state 1253


State 1154

  673 one_ordered_inst: T_STRING $@118 . ordered_pins

    '('  shift, and go to state 1254

    $default  reduce using rule 674 (ordered_pins)

    ordered_pins  go to state 1255


State 1155

  660 opt_common_pins: '(' T_STRING T_STRING . ')'
  661                | '(' T_STRING T_STRING . ')' '(' T_STRING T_STRING ')'

    ')'  shift, and go to state 1256


State 1156

  679 partition_maxbits: K_MAXBITS NUMBER .

    $default  reduce using rule 679 (partition_maxbits)


State 1157

  702 iotiming_frompin: K_FROMPIN . $@124 T_STRING

    $default  reduce using rule 701 ($@124)

    $@124  go to state 1257


State 1158

  695 iotiming_member: '+' K_DRIVECELL $@120 T_STRING $@121 iotiming_drivecell_opt .

    $default  reduce using rule 695 (iotiming_member)


State 1159

  699 iotiming_drivecell_opt: iotiming_frompin . K_TOPIN $@122 T_STRING $@123 iotiming_parallel

    K_TOPIN  shift, and go to state 1258


State 1160

  714 fp_stmt: '-' $@125 T_STRING h_or_v $@126 constraint_type constrain_what_list ';' .

    $default  reduce using rule 714 (fp_stmt)


State 1161

  724 constrain_what: '+' . K_BOTTOMLEFT $@127 row_or_comp_list
  726               | '+' . K_TOPRIGHT $@128 row_or_comp_list

    K_BOTTOMLEFT  shift, and go to state 1259
    K_TOPRIGHT    shift, and go to state 1260


State 1162

  722 constrain_what_list: constrain_what_list constrain_what .

    $default  reduce using rule 722 (constrain_what_list)


State 1163

  739 timingdisables_rule: '-' K_FROMPIN $@131 T_STRING T_STRING K_TOPIN $@132 T_STRING . T_STRING ';'

    T_STRING  shift, and go to state 1261


State 1164

  747 td_macro_option: K_FROMPIN $@135 T_STRING K_TOPIN . $@136 T_STRING

    $default  reduce using rule 746 ($@136)

    $@136  go to state 1262


State 1165

  769 partition_member: '+' K_FROMCLOCKPIN $@139 T_STRING T_STRING risefall . minmaxpins

    $default  reduce using rule 783 (min_or_max_list)

    minmaxpins       go to state 1263
    min_or_max_list  go to state 1264


State 1166

  797 risefallminmax2: K_RISEMIN . NUMBER NUMBER

    NUMBER  shift, and go to state 1265


State 1167

  799 risefallminmax2: K_RISEMAX . NUMBER NUMBER

    NUMBER  shift, and go to state 1266


State 1168

  798 risefallminmax2: K_FALLMIN . NUMBER NUMBER

    NUMBER  shift, and go to state 1267


State 1169

  800 risefallminmax2: K_FALLMAX . NUMBER NUMBER

    NUMBER  shift, and go to state 1268


State 1170

  771 partition_member: '+' K_FROMCOMPPIN $@140 T_STRING T_STRING risefallminmax2_list .
  796 risefallminmax2_list: risefallminmax2_list . risefallminmax2

    K_RISEMIN  shift, and go to state 1166
    K_RISEMAX  shift, and go to state 1167
    K_FALLMIN  shift, and go to state 1168
    K_FALLMAX  shift, and go to state 1169

    $default  reduce using rule 771 (partition_member)

    risefallminmax2  go to state 1269


State 1171

  795 risefallminmax2_list: risefallminmax2 .

    $default  reduce using rule 795 (risefallminmax2_list)


State 1172

  791 risefallminmax1: K_RISEMIN . NUMBER

    NUMBER  shift, and go to state 1270


State 1173

  793 risefallminmax1: K_RISEMAX . NUMBER

    NUMBER  shift, and go to state 1271


State 1174

  792 risefallminmax1: K_FALLMIN . NUMBER

    NUMBER  shift, and go to state 1272


State 1175

  794 risefallminmax1: K_FALLMAX . NUMBER

    NUMBER  shift, and go to state 1273


State 1176

  790 risefallminmax1_list: risefallminmax1_list risefallminmax1 .

    $default  reduce using rule 790 (risefallminmax1_list)


State 1177

  775 partition_member: '+' K_TOCLOCKPIN $@142 T_STRING T_STRING risefall . minmaxpins

    $default  reduce using rule 783 (min_or_max_list)

    minmaxpins       go to state 1274
    min_or_max_list  go to state 1264


State 1178

  777 partition_member: '+' K_TOCOMPPIN $@143 T_STRING T_STRING risefallminmax2_list .
  796 risefallminmax2_list: risefallminmax2_list . risefallminmax2

    K_RISEMIN  shift, and go to state 1166
    K_RISEMAX  shift, and go to state 1167
    K_FALLMIN  shift, and go to state 1168
    K_FALLMAX  shift, and go to state 1169

    $default  reduce using rule 777 (partition_member)

    risefallminmax2  go to state 1269


State 1179

  831 pin_prop: '+' K_PROPERTY . $@151 pin_prop_name_value_list

    $default  reduce using rule 830 ($@151)

    $@151  go to state 1275


State 1180

  850 layer_blockage_rule: '+' K_SPACING . NUMBER

    NUMBER  shift, and go to state 1276


State 1181

  856 comp_blockage_rule: '+' K_COMPONENT . $@155 T_STRING

    $default  reduce using rule 855 ($@155)

    $@155  go to state 1277


State 1182

  854 mask_blockage_rule: '+' K_MASK . NUMBER

    NUMBER  shift, and go to state 1278


State 1183

  857 comp_blockage_rule: '+' K_SLOTS .

    $default  reduce using rule 857 (comp_blockage_rule)


State 1184

  858 comp_blockage_rule: '+' K_FILLS .

    $default  reduce using rule 858 (comp_blockage_rule)


State 1185

  859 comp_blockage_rule: '+' K_PUSHDOWN .

    $default  reduce using rule 859 (comp_blockage_rule)


State 1186

  851 layer_blockage_rule: '+' K_DESIGNRULEWIDTH . NUMBER

    NUMBER  shift, and go to state 1279


State 1187

  860 comp_blockage_rule: '+' K_EXCEPTPGNET .

    $default  reduce using rule 860 (comp_blockage_rule)


State 1188

  864 placement_comp_rule: '+' K_COMPONENT $@156 T_STRING .

    $default  reduce using rule 864 (placement_comp_rule)


State 1189

  248 otherPts: otherPts . nextPt
  886 geom_slot: K_POLYGON $@160 firstPt nextPt nextPt otherPts .

    '('  shift, and go to state 188

    $default  reduce using rule 886 (geom_slot)

    nextPt  go to state 531
    pt      go to state 321


State 1190

  915 fill_mask: '+' K_MASK . NUMBER

    NUMBER  shift, and go to state 1280


State 1191

  908 fill_layer_opc: '+' K_OPC .

    $default  reduce using rule 908 (fill_layer_opc)


State 1192

  916 fill_viaMask: '+' K_MASK . NUMBER

    NUMBER  shift, and go to state 1281


State 1193

  914 fill_via_opc: '+' K_OPC .

    $default  reduce using rule 914 (fill_via_opc)


State 1194

  248 otherPts: otherPts . nextPt
  909 fill_via_pt: firstPt otherPts .

    '('  shift, and go to state 188

    $default  reduce using rule 909 (fill_via_pt)

    nextPt  go to state 531
    pt      go to state 321


State 1195

  895 fill_def: '-' K_VIA $@161 T_STRING $@162 fill_via_mask_opc_opt fill_via_pt ';' .

    $default  reduce using rule 895 (fill_def)


State 1196

  248 otherPts: otherPts . nextPt
  903 geom_fill: K_POLYGON $@165 firstPt nextPt nextPt otherPts .

    '('  shift, and go to state 188

    $default  reduce using rule 903 (geom_fill)

    nextPt  go to state 531
    pt      go to state 321


State 1197

  930 nondefault_option: '+' K_LAYER $@168 T_STRING . K_WIDTH NUMBER $@169 nondefault_layer_options

    K_WIDTH  shift, and go to state 1282


State 1198

  944 nondefault_prop_opt: '+' K_PROPERTY $@173 nondefault_prop_list .
  946 nondefault_prop_list: nondefault_prop_list . nondefault_prop

    T_STRING  shift, and go to state 1283

    $default  reduce using rule 944 (nondefault_prop_opt)

    nondefault_prop  go to state 1284


State 1199

  936 nondefault_option: '+' K_MINCUTS $@172 T_STRING . NUMBER

    NUMBER  shift, and go to state 1285


State 1200

  932 nondefault_option: '+' K_VIA $@170 T_STRING .

    $default  reduce using rule 932 (nondefault_option)


State 1201

  934 nondefault_option: '+' K_VIARULE $@171 T_STRING .

    $default  reduce using rule 934 (nondefault_option)


State 1202

  956 styles_rule: '-' K_STYLE NUMBER $@174 firstPt nextPt otherPts ';' .

    $default  reduce using rule 956 (styles_rule)


State 1203

   96 canplace: K_CANPLACE $@18 T_STRING NUMBER NUMBER orient K_DO NUMBER K_BY NUMBER K_STEP . NUMBER NUMBER ';'

    NUMBER  shift, and go to state 1286


State 1204

   98 cannotoccupy: K_CANNOTOCCUPY $@19 T_STRING NUMBER NUMBER orient K_DO NUMBER K_BY NUMBER K_STEP . NUMBER NUMBER ';'

    NUMBER  shift, and go to state 1287


State 1205

  188 row_do_option: K_DO NUMBER K_BY . NUMBER row_step_option

    NUMBER  shift, and go to state 1288


State 1206

  186 row_rule: K_ROW $@35 T_STRING T_STRING NUMBER NUMBER orient $@36 row_do_option row_options ';' .

    $default  reduce using rule 186 (row_rule)


State 1207

  194 row_option: '+' . K_PROPERTY $@37 row_prop_list

    K_PROPERTY  shift, and go to state 1289


State 1208

  192 row_options: row_options row_option .

    $default  reduce using rule 192 (row_options)


State 1209

  559 opt_range: K_RANGE NUMBER NUMBER .

    $default  reduce using rule 559 (opt_range)


State 1210

  122 pin: '-' $@21 T_STRING '+' K_NET $@22 T_STRING $@23 pin_options . ';'
  124 pin_options: pin_options . pin_option

    ';'  shift, and go to state 1290
    '+'  shift, and go to state 1291

    pin_option        go to state 1292
    extension_stmt    go to state 1293
    placement_status  go to state 1294


State 1211

  215 track_layer: T_STRING .

    $default  reduce using rule 215 (track_layer)


State 1212

  212 track_layer_statement: K_LAYER $@39 track_layer . track_layers

    T_STRING  shift, and go to state 1211

    $default  reduce using rule 213 (track_layers)

    track_layers  go to state 1295
    track_layer   go to state 1296


State 1213

  229 layer_stmt: '+' K_RECT $@42 T_STRING . mask pt pt

    '+'  shift, and go to state 1297

    $default  reduce using rule 253 (mask)

    mask  go to state 1298


State 1214

  244 layer_viarule_opts: '+' K_PATTERN $@48 T_STRING .

    $default  reduce using rule 244 (layer_viarule_opts)


State 1215

  234 layer_stmt: '+' K_PATTERNNAME $@45 T_STRING .

    $default  reduce using rule 234 (layer_stmt)


State 1216

  242 layer_viarule_opts: '+' K_OFFSET NUMBER NUMBER . NUMBER NUMBER

    NUMBER  shift, and go to state 1299


State 1217

  241 layer_viarule_opts: '+' K_ORIGIN NUMBER NUMBER .

    $default  reduce using rule 241 (layer_viarule_opts)


State 1218

  240 layer_viarule_opts: '+' K_ROWCOL NUMBER NUMBER .

    $default  reduce using rule 240 (layer_viarule_opts)


State 1219

  232 layer_stmt: '+' K_POLYGON $@43 T_STRING . mask $@44 firstPt nextPt nextPt otherPts

    '+'  shift, and go to state 1297

    $default  reduce using rule 253 (mask)

    mask  go to state 1300


State 1220

  237 layer_stmt: '+' K_VIARULE $@46 T_STRING . '+' K_CUTSIZE NUMBER NUMBER '+' K_LAYERS $@47 T_STRING T_STRING T_STRING '+' K_CUTSPACING NUMBER NUMBER '+' K_ENCLOSURE NUMBER NUMBER NUMBER NUMBER

    '+'  shift, and go to state 1301


State 1221

  268 region_option: '+' K_PROPERTY $@51 . region_prop_list

    $default  reduce using rule 270 (region_prop_list)

    region_prop_list  go to state 1302


State 1222

  275 region_type: K_FENCE .

    $default  reduce using rule 275 (region_type)


State 1223

  276 region_type: K_GUIDE .

    $default  reduce using rule 276 (region_type)


State 1224

  269 region_option: '+' K_TYPE region_type .

    $default  reduce using rule 269 (region_option)


State 1225

  323 comp_halo: '+' K_HALO $@55 halo_soft NUMBER NUMBER NUMBER . NUMBER

    NUMBER  shift, and go to state 1303


State 1226

  365 net_connection: '(' T_STRING $@63 T_STRING . conn_opt ')'

    '+'  shift, and go to state 1304

    $default  reduce using rule 370 (conn_opt)

    extension_stmt  go to state 1305
    conn_opt        go to state 1306


State 1227

  369 net_connection: '(' K_PIN $@65 T_STRING . conn_opt ')'

    '+'  shift, and go to state 1304

    $default  reduce using rule 370 (conn_opt)

    extension_stmt  go to state 1305
    conn_opt        go to state 1307


State 1228

  367 net_connection: '(' '*' $@64 T_STRING . conn_opt ')'

    '+'  shift, and go to state 1304

    $default  reduce using rule 370 (conn_opt)

    extension_stmt  go to state 1305
    conn_opt        go to state 1308


State 1229

  805 comp_name: '(' . $@146 T_STRING T_STRING subnet_opt_syn ')'

    $default  reduce using rule 804 ($@146)

    $@146  go to state 1309


State 1230

  400 net_option: '+' K_SUBNET $@73 T_STRING $@74 comp_names $@75 . subnet_options

    $default  reduce using rule 808 (subnet_options)

    subnet_options  go to state 1310


State 1231

  803 comp_names: comp_names comp_name .

    $default  reduce using rule 803 (comp_names)


State 1232

  475 opt_style: K_STYLE . NUMBER

    NUMBER  shift, and go to state 1311


State 1233

  472 opt_taper: K_TAPER .

    $default  reduce using rule 472 (opt_taper)


State 1234

  474 opt_taper: K_TAPERRULE . $@85 T_STRING

    $default  reduce using rule 473 ($@85)

    $@85  go to state 1312


State 1235

  455 path_pt: '(' . NUMBER NUMBER ')'
  456        | '(' . '*' NUMBER ')'
  457        | '(' . NUMBER '*' ')'
  458        | '(' . '*' '*' ')'
  459        | '(' . NUMBER NUMBER NUMBER ')'
  460        | '(' . '*' NUMBER NUMBER ')'
  461        | '(' . NUMBER '*' NUMBER ')'
  462        | '(' . '*' '*' NUMBER ')'

    NUMBER  shift, and go to state 1313
    '*'     shift, and go to state 1314


State 1236

  435 path: T_STRING $@81 opt_taper_style_s path_pt . $@82 path_item_list

    $default  reduce using rule 434 ($@82)

    $@82  go to state 1315


State 1237

  469 opt_taper_style_s: opt_taper_style_s opt_taper_style .

    $default  reduce using rule 469 (opt_taper_style_s)


State 1238

  471 opt_taper_style: opt_taper .

    $default  reduce using rule 471 (opt_taper_style)


State 1239

  470 opt_taper_style: opt_style .

    $default  reduce using rule 470 (opt_taper_style)


State 1240

  432 new_path: K_NEW $@80 path .

    $default  reduce using rule 432 (new_path)


State 1241

  422 vpin_options: vpin_status pt . orient

    K_N   shift, and go to state 516
    K_S   shift, and go to state 517
    K_E   shift, and go to state 518
    K_W   shift, and go to state 519
    K_FN  shift, and go to state 520
    K_FE  shift, and go to state 521
    K_FS  shift, and go to state 522
    K_FW  shift, and go to state 523

    orient  go to state 1316


State 1242

  557 opt_snet_range: K_RANGE . NUMBER NUMBER

    NUMBER  shift, and go to state 1317


State 1243

  550 snet_spacing: '+' K_SPACING $@99 T_STRING NUMBER $@100 opt_snet_range .

    $default  reduce using rule 550 (snet_spacing)


State 1244

  543 shield_layer: $@96 spaths .
  565 spaths: spaths . snew_path

    K_NEW  shift, and go to state 1032

    $default  reduce using rule 543 (shield_layer)

    snew_path  go to state 1033


State 1245

  513 snet_other_option: '+' K_POLYGON $@89 T_STRING $@90 firstPt nextPt . nextPt otherPts

    '('  shift, and go to state 188

    nextPt  go to state 1318
    pt      go to state 321


State 1246

  518 snet_other_option: '+' K_VIA $@92 T_STRING orient_pt $@93 firstPt . otherPts

    $default  reduce using rule 247 (otherPts)

    otherPts  go to state 1319


State 1247

  477 opt_spaths: opt_spaths . opt_shape_style
  570 spath: T_STRING $@102 width opt_spaths . path_pt $@103 path_item_list

    '+'  shift, and go to state 1320
    '('  shift, and go to state 1235

    path_pt          go to state 1321
    opt_shape_style  go to state 1322


State 1248

  567 snew_path: K_NEW $@101 spath .

    $default  reduce using rule 567 (snew_path)


State 1249

  597 group_prop: T_STRING QSTRING .

    $default  reduce using rule 597 (group_prop)


State 1250

  598 group_prop: T_STRING T_STRING .

    $default  reduce using rule 598 (group_prop)


State 1251

  596 group_prop: T_STRING NUMBER .

    $default  reduce using rule 596 (group_prop)


State 1252

  667 floating_pins: '(' . T_STRING T_STRING ')'
  668              | '(' . T_STRING T_STRING ')' '(' T_STRING T_STRING ')'
  669              | '(' . T_STRING T_STRING ')' '(' T_STRING T_STRING ')' '(' T_STRING T_STRING ')'

    T_STRING  shift, and go to state 1323


State 1253

  665 one_floating_inst: T_STRING $@117 floating_pins .

    $default  reduce using rule 665 (one_floating_inst)


State 1254

  675 ordered_pins: '(' . T_STRING T_STRING ')'
  676             | '(' . T_STRING T_STRING ')' '(' T_STRING T_STRING ')'
  677             | '(' . T_STRING T_STRING ')' '(' T_STRING T_STRING ')' '(' T_STRING T_STRING ')'

    T_STRING  shift, and go to state 1324


State 1255

  673 one_ordered_inst: T_STRING $@118 ordered_pins .

    $default  reduce using rule 673 (one_ordered_inst)


State 1256

  660 opt_common_pins: '(' T_STRING T_STRING ')' .
  661                | '(' T_STRING T_STRING ')' . '(' T_STRING T_STRING ')'

    '('  shift, and go to state 1325

    $default  reduce using rule 660 (opt_common_pins)


State 1257

  702 iotiming_frompin: K_FROMPIN $@124 . T_STRING

    T_STRING  shift, and go to state 1326


State 1258

  699 iotiming_drivecell_opt: iotiming_frompin K_TOPIN . $@122 T_STRING $@123 iotiming_parallel

    $default  reduce using rule 697 ($@122)

    $@122  go to state 1327


State 1259

  724 constrain_what: '+' K_BOTTOMLEFT . $@127 row_or_comp_list

    $default  reduce using rule 723 ($@127)

    $@127  go to state 1328


State 1260

  726 constrain_what: '+' K_TOPRIGHT . $@128 row_or_comp_list

    $default  reduce using rule 725 ($@128)

    $@128  go to state 1329


State 1261

  739 timingdisables_rule: '-' K_FROMPIN $@131 T_STRING T_STRING K_TOPIN $@132 T_STRING T_STRING . ';'

    ';'  shift, and go to state 1330


State 1262

  747 td_macro_option: K_FROMPIN $@135 T_STRING K_TOPIN $@136 . T_STRING

    T_STRING  shift, and go to state 1331


State 1263

  769 partition_member: '+' K_FROMCLOCKPIN $@139 T_STRING T_STRING risefall minmaxpins .

    $default  reduce using rule 769 (partition_member)


State 1264

  782 minmaxpins: min_or_max_list . K_PINS $@145 pin_list
  784 min_or_max_list: min_or_max_list . min_or_max_member

    K_PINS  shift, and go to state 1332
    K_MIN   shift, and go to state 1333
    K_MAX   shift, and go to state 1334

    min_or_max_member  go to state 1335


State 1265

  797 risefallminmax2: K_RISEMIN NUMBER . NUMBER

    NUMBER  shift, and go to state 1336


State 1266

  799 risefallminmax2: K_RISEMAX NUMBER . NUMBER

    NUMBER  shift, and go to state 1337


State 1267

  798 risefallminmax2: K_FALLMIN NUMBER . NUMBER

    NUMBER  shift, and go to state 1338


State 1268

  800 risefallminmax2: K_FALLMAX NUMBER . NUMBER

    NUMBER  shift, and go to state 1339


State 1269

  796 risefallminmax2_list: risefallminmax2_list risefallminmax2 .

    $default  reduce using rule 796 (risefallminmax2_list)


State 1270

  791 risefallminmax1: K_RISEMIN NUMBER .

    $default  reduce using rule 791 (risefallminmax1)


State 1271

  793 risefallminmax1: K_RISEMAX NUMBER .

    $default  reduce using rule 793 (risefallminmax1)


State 1272

  792 risefallminmax1: K_FALLMIN NUMBER .

    $default  reduce using rule 792 (risefallminmax1)


State 1273

  794 risefallminmax1: K_FALLMAX NUMBER .

    $default  reduce using rule 794 (risefallminmax1)


State 1274

  775 partition_member: '+' K_TOCLOCKPIN $@142 T_STRING T_STRING risefall minmaxpins .

    $default  reduce using rule 775 (partition_member)


State 1275

  831 pin_prop: '+' K_PROPERTY $@151 . pin_prop_name_value_list

    $default  reduce using rule 832 (pin_prop_name_value_list)

    pin_prop_name_value_list  go to state 1340


State 1276

  850 layer_blockage_rule: '+' K_SPACING NUMBER .

    $default  reduce using rule 850 (layer_blockage_rule)


State 1277

  856 comp_blockage_rule: '+' K_COMPONENT $@155 . T_STRING

    T_STRING  shift, and go to state 1341


State 1278

  854 mask_blockage_rule: '+' K_MASK NUMBER .

    $default  reduce using rule 854 (mask_blockage_rule)


State 1279

  851 layer_blockage_rule: '+' K_DESIGNRULEWIDTH NUMBER .

    $default  reduce using rule 851 (layer_blockage_rule)


State 1280

  915 fill_mask: '+' K_MASK NUMBER .

    $default  reduce using rule 915 (fill_mask)


State 1281

  916 fill_viaMask: '+' K_MASK NUMBER .

    $default  reduce using rule 916 (fill_viaMask)


State 1282

  930 nondefault_option: '+' K_LAYER $@168 T_STRING K_WIDTH . NUMBER $@169 nondefault_layer_options

    NUMBER  shift, and go to state 1342


State 1283

  947 nondefault_prop: T_STRING . NUMBER
  948                | T_STRING . QSTRING
  949                | T_STRING . T_STRING

    QSTRING   shift, and go to state 1343
    T_STRING  shift, and go to state 1344
    NUMBER    shift, and go to state 1345


State 1284

  946 nondefault_prop_list: nondefault_prop_list nondefault_prop .

    $default  reduce using rule 946 (nondefault_prop_list)


State 1285

  936 nondefault_option: '+' K_MINCUTS $@172 T_STRING NUMBER .

    $default  reduce using rule 936 (nondefault_option)


State 1286

   96 canplace: K_CANPLACE $@18 T_STRING NUMBER NUMBER orient K_DO NUMBER K_BY NUMBER K_STEP NUMBER . NUMBER ';'

    NUMBER  shift, and go to state 1346


State 1287

   98 cannotoccupy: K_CANNOTOCCUPY $@19 T_STRING NUMBER NUMBER orient K_DO NUMBER K_BY NUMBER K_STEP NUMBER . NUMBER ';'

    NUMBER  shift, and go to state 1347


State 1288

  188 row_do_option: K_DO NUMBER K_BY NUMBER . row_step_option

    K_STEP  shift, and go to state 1348

    $default  reduce using rule 189 (row_step_option)

    row_step_option  go to state 1349


State 1289

  194 row_option: '+' K_PROPERTY . $@37 row_prop_list

    $default  reduce using rule 193 ($@37)

    $@37  go to state 1350


State 1290

  122 pin: '-' $@21 T_STRING '+' K_NET $@22 T_STRING $@23 pin_options ';' .

    $default  reduce using rule 122 (pin)


State 1291

  125 pin_option: '+' . K_SPECIAL
  127           | '+' . K_DIRECTION T_STRING
  128           | '+' . K_NETEXPR QSTRING
  130           | '+' . K_SUPPLYSENSITIVITY $@24 T_STRING
  132           | '+' . K_GROUNDSENSITIVITY $@25 T_STRING
  133           | '+' . K_USE use_type
  134           | '+' . K_PORT
  137           | '+' . K_LAYER $@26 T_STRING $@27 pin_layer_mask_opt pin_layer_spacing_opt pt pt
  140           | '+' . K_POLYGON $@28 T_STRING $@29 pin_poly_mask_opt pin_poly_spacing_opt firstPt nextPt nextPt otherPts
  142           | '+' . K_VIA $@30 T_STRING pin_via_mask_opt '(' NUMBER NUMBER ')'
  144           | '+' . K_ANTENNAPINPARTIALMETALAREA NUMBER pin_layer_opt
  145           | '+' . K_ANTENNAPINPARTIALMETALSIDEAREA NUMBER pin_layer_opt
  146           | '+' . K_ANTENNAPINGATEAREA NUMBER pin_layer_opt
  147           | '+' . K_ANTENNAPINDIFFAREA NUMBER pin_layer_opt
  149           | '+' . K_ANTENNAPINMAXAREACAR NUMBER K_LAYER $@31 T_STRING
  151           | '+' . K_ANTENNAPINMAXSIDEAREACAR NUMBER K_LAYER $@32 T_STRING
  152           | '+' . K_ANTENNAPINPARTIALCUTAREA NUMBER pin_layer_opt
  154           | '+' . K_ANTENNAPINMAXCUTCAR NUMBER K_LAYER $@33 T_STRING
  155           | '+' . K_ANTENNAMODEL pin_oxide
  218 extension_stmt: '+' . K_BEGINEXT
  345 placement_status: '+' . K_FIXED
  346                 | '+' . K_COVER
  347                 | '+' . K_PLACED

    K_LAYER                           shift, and go to state 1351
    K_FIXED                           shift, and go to state 553
    K_COVER                           shift, and go to state 554
    K_PLACED                          shift, and go to state 556
    K_USE                             shift, and go to state 1352
    K_SPECIAL                         shift, and go to state 1353
    K_DIRECTION                       shift, and go to state 1354
    K_BEGINEXT                        shift, and go to state 560
    K_ANTENNAPINPARTIALMETALAREA      shift, and go to state 1355
    K_ANTENNAPINPARTIALMETALSIDEAREA  shift, and go to state 1356
    K_ANTENNAPINGATEAREA              shift, and go to state 1357
    K_ANTENNAPINDIFFAREA              shift, and go to state 1358
    K_ANTENNAPINMAXAREACAR            shift, and go to state 1359
    K_ANTENNAPINMAXSIDEAREACAR        shift, and go to state 1360
    K_ANTENNAPINPARTIALCUTAREA        shift, and go to state 1361
    K_ANTENNAPINMAXCUTCAR             shift, and go to state 1362
    K_ANTENNAMODEL                    shift, and go to state 1363
    K_GROUNDSENSITIVITY               shift, and go to state 1364
    K_NETEXPR                         shift, and go to state 1365
    K_POLYGON                         shift, and go to state 1366
    K_PORT                            shift, and go to state 1367
    K_SUPPLYSENSITIVITY               shift, and go to state 1368
    K_VIA                             shift, and go to state 1369


State 1292

  124 pin_options: pin_options pin_option .

    $default  reduce using rule 124 (pin_options)


State 1293

  126 pin_option: extension_stmt .

    $default  reduce using rule 126 (pin_option)


State 1294

  143 pin_option: placement_status . pt orient

    '('  shift, and go to state 188

    pt  go to state 1370


State 1295

  212 track_layer_statement: K_LAYER $@39 track_layer track_layers .

    $default  reduce using rule 212 (track_layer_statement)


State 1296

  214 track_layers: track_layer . track_layers

    T_STRING  shift, and go to state 1211

    $default  reduce using rule 213 (track_layers)

    track_layers  go to state 1371
    track_layer   go to state 1296


State 1297

  254 mask: '+' . K_MASK NUMBER

    K_MASK  shift, and go to state 1372


State 1298

  229 layer_stmt: '+' K_RECT $@42 T_STRING mask . pt pt

    '('  shift, and go to state 188

    pt  go to state 1373


State 1299

  242 layer_viarule_opts: '+' K_OFFSET NUMBER NUMBER NUMBER . NUMBER

    NUMBER  shift, and go to state 1374


State 1300

  232 layer_stmt: '+' K_POLYGON $@43 T_STRING mask . $@44 firstPt nextPt nextPt otherPts

    $default  reduce using rule 231 ($@44)

    $@44  go to state 1375


State 1301

  237 layer_stmt: '+' K_VIARULE $@46 T_STRING '+' . K_CUTSIZE NUMBER NUMBER '+' K_LAYERS $@47 T_STRING T_STRING T_STRING '+' K_CUTSPACING NUMBER NUMBER '+' K_ENCLOSURE NUMBER NUMBER NUMBER NUMBER

    K_CUTSIZE  shift, and go to state 1376


State 1302

  268 region_option: '+' K_PROPERTY $@51 region_prop_list .
  271 region_prop_list: region_prop_list . region_prop

    T_STRING  shift, and go to state 1377

    $default  reduce using rule 268 (region_option)

    region_prop  go to state 1378


State 1303

  323 comp_halo: '+' K_HALO $@55 halo_soft NUMBER NUMBER NUMBER NUMBER .

    $default  reduce using rule 323 (comp_halo)


State 1304

  218 extension_stmt: '+' . K_BEGINEXT
  372 conn_opt: '+' . K_SYNTHESIZED

    K_SYNTHESIZED  shift, and go to state 1379
    K_BEGINEXT     shift, and go to state 560


State 1305

  371 conn_opt: extension_stmt .

    $default  reduce using rule 371 (conn_opt)


State 1306

  365 net_connection: '(' T_STRING $@63 T_STRING conn_opt . ')'

    ')'  shift, and go to state 1380


State 1307

  369 net_connection: '(' K_PIN $@65 T_STRING conn_opt . ')'

    ')'  shift, and go to state 1381


State 1308

  367 net_connection: '(' '*' $@64 T_STRING conn_opt . ')'

    ')'  shift, and go to state 1382


State 1309

  805 comp_name: '(' $@146 . T_STRING T_STRING subnet_opt_syn ')'

    T_STRING  shift, and go to state 1383


State 1310

  400 net_option: '+' K_SUBNET $@73 T_STRING $@74 comp_names $@75 subnet_options .
  809 subnet_options: subnet_options . subnet_option

    K_FIXED           shift, and go to state 1384
    K_COVER           shift, and go to state 1385
    K_ROUTED          shift, and go to state 1386
    K_NONDEFAULTRULE  shift, and go to state 1387
    K_NOSHIELD        shift, and go to state 1388

    $default  reduce using rule 400 (net_option)

    subnet_option  go to state 1389
    subnet_type    go to state 1390


State 1311

  475 opt_style: K_STYLE NUMBER .

    $default  reduce using rule 475 (opt_style)


State 1312

  474 opt_taper: K_TAPERRULE $@85 . T_STRING

    T_STRING  shift, and go to state 1391


State 1313

  455 path_pt: '(' NUMBER . NUMBER ')'
  457        | '(' NUMBER . '*' ')'
  459        | '(' NUMBER . NUMBER NUMBER ')'
  461        | '(' NUMBER . '*' NUMBER ')'

    NUMBER  shift, and go to state 1392
    '*'     shift, and go to state 1393


State 1314

  456 path_pt: '(' '*' . NUMBER ')'
  458        | '(' '*' . '*' ')'
  460        | '(' '*' . NUMBER NUMBER ')'
  462        | '(' '*' . '*' NUMBER ')'

    NUMBER  shift, and go to state 1394
    '*'     shift, and go to state 1395


State 1315

  435 path: T_STRING $@81 opt_taper_style_s path_pt $@82 . path_item_list

    $default  reduce using rule 438 (path_item_list)

    path_item_list  go to state 1396


State 1316

  422 vpin_options: vpin_status pt orient .

    $default  reduce using rule 422 (vpin_options)


State 1317

  557 opt_snet_range: K_RANGE NUMBER . NUMBER

    NUMBER  shift, and go to state 1397


State 1318

  513 snet_other_option: '+' K_POLYGON $@89 T_STRING $@90 firstPt nextPt nextPt . otherPts

    $default  reduce using rule 247 (otherPts)

    otherPts  go to state 1398


State 1319

  248 otherPts: otherPts . nextPt
  518 snet_other_option: '+' K_VIA $@92 T_STRING orient_pt $@93 firstPt otherPts .

    '('  shift, and go to state 188

    $default  reduce using rule 518 (snet_other_option)

    nextPt  go to state 531
    pt      go to state 321


State 1320

  478 opt_shape_style: '+' . K_SHAPE shape_type
  479                | '+' . K_STYLE NUMBER

    K_STYLE  shift, and go to state 1399
    K_SHAPE  shift, and go to state 1400


State 1321

  570 spath: T_STRING $@102 width opt_spaths path_pt . $@103 path_item_list

    $default  reduce using rule 569 ($@103)

    $@103  go to state 1401


State 1322

  477 opt_spaths: opt_spaths opt_shape_style .

    $default  reduce using rule 477 (opt_spaths)


State 1323

  667 floating_pins: '(' T_STRING . T_STRING ')'
  668              | '(' T_STRING . T_STRING ')' '(' T_STRING T_STRING ')'
  669              | '(' T_STRING . T_STRING ')' '(' T_STRING T_STRING ')' '(' T_STRING T_STRING ')'

    T_STRING  shift, and go to state 1402


State 1324

  675 ordered_pins: '(' T_STRING . T_STRING ')'
  676             | '(' T_STRING . T_STRING ')' '(' T_STRING T_STRING ')'
  677             | '(' T_STRING . T_STRING ')' '(' T_STRING T_STRING ')' '(' T_STRING T_STRING ')'

    T_STRING  shift, and go to state 1403


State 1325

  661 opt_common_pins: '(' T_STRING T_STRING ')' '(' . T_STRING T_STRING ')'

    T_STRING  shift, and go to state 1404


State 1326

  702 iotiming_frompin: K_FROMPIN $@124 T_STRING .

    $default  reduce using rule 702 (iotiming_frompin)


State 1327

  699 iotiming_drivecell_opt: iotiming_frompin K_TOPIN $@122 . T_STRING $@123 iotiming_parallel

    T_STRING  shift, and go to state 1405


State 1328

  724 constrain_what: '+' K_BOTTOMLEFT $@127 . row_or_comp_list

    $default  reduce using rule 727 (row_or_comp_list)

    row_or_comp_list  go to state 1406


State 1329

  726 constrain_what: '+' K_TOPRIGHT $@128 . row_or_comp_list

    $default  reduce using rule 727 (row_or_comp_list)

    row_or_comp_list  go to state 1407


State 1330

  739 timingdisables_rule: '-' K_FROMPIN $@131 T_STRING T_STRING K_TOPIN $@132 T_STRING T_STRING ';' .

    $default  reduce using rule 739 (timingdisables_rule)


State 1331

  747 td_macro_option: K_FROMPIN $@135 T_STRING K_TOPIN $@136 T_STRING .

    $default  reduce using rule 747 (td_macro_option)


State 1332

  782 minmaxpins: min_or_max_list K_PINS . $@145 pin_list

    $default  reduce using rule 781 ($@145)

    $@145  go to state 1408


State 1333

  785 min_or_max_member: K_MIN . NUMBER NUMBER

    NUMBER  shift, and go to state 1409


State 1334

  786 min_or_max_member: K_MAX . NUMBER NUMBER

    NUMBER  shift, and go to state 1410


State 1335

  784 min_or_max_list: min_or_max_list min_or_max_member .

    $default  reduce using rule 784 (min_or_max_list)


State 1336

  797 risefallminmax2: K_RISEMIN NUMBER NUMBER .

    $default  reduce using rule 797 (risefallminmax2)


State 1337

  799 risefallminmax2: K_RISEMAX NUMBER NUMBER .

    $default  reduce using rule 799 (risefallminmax2)


State 1338

  798 risefallminmax2: K_FALLMIN NUMBER NUMBER .

    $default  reduce using rule 798 (risefallminmax2)


State 1339

  800 risefallminmax2: K_FALLMAX NUMBER NUMBER .

    $default  reduce using rule 800 (risefallminmax2)


State 1340

  831 pin_prop: '+' K_PROPERTY $@151 pin_prop_name_value_list .
  833 pin_prop_name_value_list: pin_prop_name_value_list . pin_prop_name_value

    T_STRING  shift, and go to state 1411

    $default  reduce using rule 831 (pin_prop)

    pin_prop_name_value  go to state 1412


State 1341

  856 comp_blockage_rule: '+' K_COMPONENT $@155 T_STRING .

    $default  reduce using rule 856 (comp_blockage_rule)


State 1342

  930 nondefault_option: '+' K_LAYER $@168 T_STRING K_WIDTH NUMBER . $@169 nondefault_layer_options

    $default  reduce using rule 929 ($@169)

    $@169  go to state 1413


State 1343

  948 nondefault_prop: T_STRING QSTRING .

    $default  reduce using rule 948 (nondefault_prop)


State 1344

  949 nondefault_prop: T_STRING T_STRING .

    $default  reduce using rule 949 (nondefault_prop)


State 1345

  947 nondefault_prop: T_STRING NUMBER .

    $default  reduce using rule 947 (nondefault_prop)


State 1346

   96 canplace: K_CANPLACE $@18 T_STRING NUMBER NUMBER orient K_DO NUMBER K_BY NUMBER K_STEP NUMBER NUMBER . ';'

    ';'  shift, and go to state 1414


State 1347

   98 cannotoccupy: K_CANNOTOCCUPY $@19 T_STRING NUMBER NUMBER orient K_DO NUMBER K_BY NUMBER K_STEP NUMBER NUMBER . ';'

    ';'  shift, and go to state 1415


State 1348

  190 row_step_option: K_STEP . NUMBER NUMBER

    NUMBER  shift, and go to state 1416


State 1349

  188 row_do_option: K_DO NUMBER K_BY NUMBER row_step_option .

    $default  reduce using rule 188 (row_do_option)


State 1350

  194 row_option: '+' K_PROPERTY $@37 . row_prop_list

    $default  reduce using rule 195 (row_prop_list)

    row_prop_list  go to state 1417


State 1351

  137 pin_option: '+' K_LAYER . $@26 T_STRING $@27 pin_layer_mask_opt pin_layer_spacing_opt pt pt

    $default  reduce using rule 135 ($@26)

    $@26  go to state 1418


State 1352

  133 pin_option: '+' K_USE . use_type

    K_SIGNAL  shift, and go to state 717
    K_POWER   shift, and go to state 718
    K_GROUND  shift, and go to state 719
    K_CLOCK   shift, and go to state 720
    K_TIEOFF  shift, and go to state 721
    K_ANALOG  shift, and go to state 722
    K_SCAN    shift, and go to state 723
    K_RESET   shift, and go to state 724

    use_type  go to state 1419


State 1353

  125 pin_option: '+' K_SPECIAL .

    $default  reduce using rule 125 (pin_option)


State 1354

  127 pin_option: '+' K_DIRECTION . T_STRING

    T_STRING  shift, and go to state 1420


State 1355

  144 pin_option: '+' K_ANTENNAPINPARTIALMETALAREA . NUMBER pin_layer_opt

    NUMBER  shift, and go to state 1421


State 1356

  145 pin_option: '+' K_ANTENNAPINPARTIALMETALSIDEAREA . NUMBER pin_layer_opt

    NUMBER  shift, and go to state 1422


State 1357

  146 pin_option: '+' K_ANTENNAPINGATEAREA . NUMBER pin_layer_opt

    NUMBER  shift, and go to state 1423


State 1358

  147 pin_option: '+' K_ANTENNAPINDIFFAREA . NUMBER pin_layer_opt

    NUMBER  shift, and go to state 1424


State 1359

  149 pin_option: '+' K_ANTENNAPINMAXAREACAR . NUMBER K_LAYER $@31 T_STRING

    NUMBER  shift, and go to state 1425


State 1360

  151 pin_option: '+' K_ANTENNAPINMAXSIDEAREACAR . NUMBER K_LAYER $@32 T_STRING

    NUMBER  shift, and go to state 1426


State 1361

  152 pin_option: '+' K_ANTENNAPINPARTIALCUTAREA . NUMBER pin_layer_opt

    NUMBER  shift, and go to state 1427


State 1362

  154 pin_option: '+' K_ANTENNAPINMAXCUTCAR . NUMBER K_LAYER $@33 T_STRING

    NUMBER  shift, and go to state 1428


State 1363

  155 pin_option: '+' K_ANTENNAMODEL . pin_oxide

    K_OXIDE1  shift, and go to state 1429
    K_OXIDE2  shift, and go to state 1430
    K_OXIDE3  shift, and go to state 1431
    K_OXIDE4  shift, and go to state 1432

    pin_oxide  go to state 1433


State 1364

  132 pin_option: '+' K_GROUNDSENSITIVITY . $@25 T_STRING

    $default  reduce using rule 131 ($@25)

    $@25  go to state 1434


State 1365

  128 pin_option: '+' K_NETEXPR . QSTRING

    QSTRING  shift, and go to state 1435


State 1366

  140 pin_option: '+' K_POLYGON . $@28 T_STRING $@29 pin_poly_mask_opt pin_poly_spacing_opt firstPt nextPt nextPt otherPts

    $default  reduce using rule 138 ($@28)

    $@28  go to state 1436


State 1367

  134 pin_option: '+' K_PORT .

    $default  reduce using rule 134 (pin_option)


State 1368

  130 pin_option: '+' K_SUPPLYSENSITIVITY . $@24 T_STRING

    $default  reduce using rule 129 ($@24)

    $@24  go to state 1437


State 1369

  142 pin_option: '+' K_VIA . $@30 T_STRING pin_via_mask_opt '(' NUMBER NUMBER ')'

    $default  reduce using rule 141 ($@30)

    $@30  go to state 1438


State 1370

  143 pin_option: placement_status pt . orient

    K_N   shift, and go to state 516
    K_S   shift, and go to state 517
    K_E   shift, and go to state 518
    K_W   shift, and go to state 519
    K_FN  shift, and go to state 520
    K_FE  shift, and go to state 521
    K_FS  shift, and go to state 522
    K_FW  shift, and go to state 523

    orient  go to state 1439


State 1371

  214 track_layers: track_layer track_layers .

    $default  reduce using rule 214 (track_layers)


State 1372

  254 mask: '+' K_MASK . NUMBER

    NUMBER  shift, and go to state 1440


State 1373

  229 layer_stmt: '+' K_RECT $@42 T_STRING mask pt . pt

    '('  shift, and go to state 188

    pt  go to state 1441


State 1374

  242 layer_viarule_opts: '+' K_OFFSET NUMBER NUMBER NUMBER NUMBER .

    $default  reduce using rule 242 (layer_viarule_opts)


State 1375

  232 layer_stmt: '+' K_POLYGON $@43 T_STRING mask $@44 . firstPt nextPt nextPt otherPts

    '('  shift, and go to state 188

    firstPt  go to state 1442
    pt       go to state 190


State 1376

  237 layer_stmt: '+' K_VIARULE $@46 T_STRING '+' K_CUTSIZE . NUMBER NUMBER '+' K_LAYERS $@47 T_STRING T_STRING T_STRING '+' K_CUTSPACING NUMBER NUMBER '+' K_ENCLOSURE NUMBER NUMBER NUMBER NUMBER

    NUMBER  shift, and go to state 1443


State 1377

  272 region_prop: T_STRING . NUMBER
  273            | T_STRING . QSTRING
  274            | T_STRING . T_STRING

    QSTRING   shift, and go to state 1444
    T_STRING  shift, and go to state 1445
    NUMBER    shift, and go to state 1446


State 1378

  271 region_prop_list: region_prop_list region_prop .

    $default  reduce using rule 271 (region_prop_list)


State 1379

  372 conn_opt: '+' K_SYNTHESIZED .

    $default  reduce using rule 372 (conn_opt)


State 1380

  365 net_connection: '(' T_STRING $@63 T_STRING conn_opt ')' .

    $default  reduce using rule 365 (net_connection)


State 1381

  369 net_connection: '(' K_PIN $@65 T_STRING conn_opt ')' .

    $default  reduce using rule 369 (net_connection)


State 1382

  367 net_connection: '(' '*' $@64 T_STRING conn_opt ')' .

    $default  reduce using rule 367 (net_connection)


State 1383

  805 comp_name: '(' $@146 T_STRING . T_STRING subnet_opt_syn ')'

    T_STRING  shift, and go to state 1447


State 1384

  814 subnet_type: K_FIXED .

    $default  reduce using rule 814 (subnet_type)


State 1385

  815 subnet_type: K_COVER .

    $default  reduce using rule 815 (subnet_type)


State 1386

  816 subnet_type: K_ROUTED .

    $default  reduce using rule 816 (subnet_type)


State 1387

  813 subnet_option: K_NONDEFAULTRULE . $@148 T_STRING

    $default  reduce using rule 812 ($@148)

    $@148  go to state 1448


State 1388

  817 subnet_type: K_NOSHIELD .

    $default  reduce using rule 817 (subnet_type)


State 1389

  809 subnet_options: subnet_options subnet_option .

    $default  reduce using rule 809 (subnet_options)


State 1390

  811 subnet_option: subnet_type . $@147 paths

    $default  reduce using rule 810 ($@147)

    $@147  go to state 1449


State 1391

  474 opt_taper: K_TAPERRULE $@85 T_STRING .

    $default  reduce using rule 474 (opt_taper)


State 1392

  455 path_pt: '(' NUMBER NUMBER . ')'
  459        | '(' NUMBER NUMBER . NUMBER ')'

    NUMBER  shift, and go to state 1450
    ')'     shift, and go to state 1451


State 1393

  457 path_pt: '(' NUMBER '*' . ')'
  461        | '(' NUMBER '*' . NUMBER ')'

    NUMBER  shift, and go to state 1452
    ')'     shift, and go to state 1453


State 1394

  456 path_pt: '(' '*' NUMBER . ')'
  460        | '(' '*' NUMBER . NUMBER ')'

    NUMBER  shift, and go to state 1454
    ')'     shift, and go to state 1455


State 1395

  458 path_pt: '(' '*' '*' . ')'
  462        | '(' '*' '*' . NUMBER ')'

    NUMBER  shift, and go to state 1456
    ')'     shift, and go to state 1457


State 1396

  435 path: T_STRING $@81 opt_taper_style_s path_pt $@82 path_item_list .
  439 path_item_list: path_item_list . path_item

    T_STRING   shift, and go to state 1458
    K_RECT     shift, and go to state 1459
    K_MASK     shift, and go to state 1460
    K_VIRTUAL  shift, and go to state 1461
    '('        shift, and go to state 1235

    $default  reduce using rule 435 (path)

    virtual_statement  go to state 1462
    rect_statement     go to state 1463
    path_item          go to state 1464
    path_pt            go to state 1465


State 1397

  557 opt_snet_range: K_RANGE NUMBER NUMBER .

    $default  reduce using rule 557 (opt_snet_range)


State 1398

  248 otherPts: otherPts . nextPt
  513 snet_other_option: '+' K_POLYGON $@89 T_STRING $@90 firstPt nextPt nextPt otherPts .

    '('  shift, and go to state 188

    $default  reduce using rule 513 (snet_other_option)

    nextPt  go to state 531
    pt      go to state 321


State 1399

  479 opt_shape_style: '+' K_STYLE . NUMBER

    NUMBER  shift, and go to state 1466


State 1400

  478 opt_shape_style: '+' K_SHAPE . shape_type

    K_RING          shift, and go to state 752
    K_STRIPE        shift, and go to state 753
    K_FOLLOWPIN     shift, and go to state 754
    K_IOWIRE        shift, and go to state 755
    K_COREWIRE      shift, and go to state 756
    K_BLOCKWIRE     shift, and go to state 757
    K_FILLWIRE      shift, and go to state 758
    K_BLOCKAGEWIRE  shift, and go to state 759
    K_PADRING       shift, and go to state 760
    K_BLOCKRING     shift, and go to state 761
    K_DRCFILL       shift, and go to state 762
    K_FILLWIREOPC   shift, and go to state 763

    shape_type  go to state 1467


State 1401

  570 spath: T_STRING $@102 width opt_spaths path_pt $@103 . path_item_list

    $default  reduce using rule 438 (path_item_list)

    path_item_list  go to state 1468


State 1402

  667 floating_pins: '(' T_STRING T_STRING . ')'
  668              | '(' T_STRING T_STRING . ')' '(' T_STRING T_STRING ')'
  669              | '(' T_STRING T_STRING . ')' '(' T_STRING T_STRING ')' '(' T_STRING T_STRING ')'

    ')'  shift, and go to state 1469


State 1403

  675 ordered_pins: '(' T_STRING T_STRING . ')'
  676             | '(' T_STRING T_STRING . ')' '(' T_STRING T_STRING ')'
  677             | '(' T_STRING T_STRING . ')' '(' T_STRING T_STRING ')' '(' T_STRING T_STRING ')'

    ')'  shift, and go to state 1470


State 1404

  661 opt_common_pins: '(' T_STRING T_STRING ')' '(' T_STRING . T_STRING ')'

    T_STRING  shift, and go to state 1471


State 1405

  699 iotiming_drivecell_opt: iotiming_frompin K_TOPIN $@122 T_STRING . $@123 iotiming_parallel

    $default  reduce using rule 698 ($@123)

    $@123  go to state 1472


State 1406

  724 constrain_what: '+' K_BOTTOMLEFT $@127 row_or_comp_list .
  728 row_or_comp_list: row_or_comp_list . row_or_comp

    '('  shift, and go to state 1473

    $default  reduce using rule 724 (constrain_what)

    row_or_comp  go to state 1474


State 1407

  726 constrain_what: '+' K_TOPRIGHT $@128 row_or_comp_list .
  728 row_or_comp_list: row_or_comp_list . row_or_comp

    '('  shift, and go to state 1473

    $default  reduce using rule 726 (constrain_what)

    row_or_comp  go to state 1474


State 1408

  782 minmaxpins: min_or_max_list K_PINS $@145 . pin_list

    $default  reduce using rule 787 (pin_list)

    pin_list  go to state 1475


State 1409

  785 min_or_max_member: K_MIN NUMBER . NUMBER

    NUMBER  shift, and go to state 1476


State 1410

  786 min_or_max_member: K_MAX NUMBER . NUMBER

    NUMBER  shift, and go to state 1477


State 1411

  834 pin_prop_name_value: T_STRING . NUMBER
  835                    | T_STRING . QSTRING
  836                    | T_STRING . T_STRING

    QSTRING   shift, and go to state 1478
    T_STRING  shift, and go to state 1479
    NUMBER    shift, and go to state 1480


State 1412

  833 pin_prop_name_value_list: pin_prop_name_value_list pin_prop_name_value .

    $default  reduce using rule 833 (pin_prop_name_value_list)


State 1413

  930 nondefault_option: '+' K_LAYER $@168 T_STRING K_WIDTH NUMBER $@169 . nondefault_layer_options

    $default  reduce using rule 938 (nondefault_layer_options)

    nondefault_layer_options  go to state 1481


State 1414

   96 canplace: K_CANPLACE $@18 T_STRING NUMBER NUMBER orient K_DO NUMBER K_BY NUMBER K_STEP NUMBER NUMBER ';' .

    $default  reduce using rule 96 (canplace)


State 1415

   98 cannotoccupy: K_CANNOTOCCUPY $@19 T_STRING NUMBER NUMBER orient K_DO NUMBER K_BY NUMBER K_STEP NUMBER NUMBER ';' .

    $default  reduce using rule 98 (cannotoccupy)


State 1416

  190 row_step_option: K_STEP NUMBER . NUMBER

    NUMBER  shift, and go to state 1482


State 1417

  194 row_option: '+' K_PROPERTY $@37 row_prop_list .
  196 row_prop_list: row_prop_list . row_prop

    T_STRING  shift, and go to state 1483

    $default  reduce using rule 194 (row_option)

    row_prop  go to state 1484


State 1418

  137 pin_option: '+' K_LAYER $@26 . T_STRING $@27 pin_layer_mask_opt pin_layer_spacing_opt pt pt

    T_STRING  shift, and go to state 1485


State 1419

  133 pin_option: '+' K_USE use_type .

    $default  reduce using rule 133 (pin_option)


State 1420

  127 pin_option: '+' K_DIRECTION T_STRING .

    $default  reduce using rule 127 (pin_option)


State 1421

  144 pin_option: '+' K_ANTENNAPINPARTIALMETALAREA NUMBER . pin_layer_opt

    K_LAYER  shift, and go to state 1486

    $default  reduce using rule 180 (pin_layer_opt)

    pin_layer_opt  go to state 1487


State 1422

  145 pin_option: '+' K_ANTENNAPINPARTIALMETALSIDEAREA NUMBER . pin_layer_opt

    K_LAYER  shift, and go to state 1486

    $default  reduce using rule 180 (pin_layer_opt)

    pin_layer_opt  go to state 1488


State 1423

  146 pin_option: '+' K_ANTENNAPINGATEAREA NUMBER . pin_layer_opt

    K_LAYER  shift, and go to state 1486

    $default  reduce using rule 180 (pin_layer_opt)

    pin_layer_opt  go to state 1489


State 1424

  147 pin_option: '+' K_ANTENNAPINDIFFAREA NUMBER . pin_layer_opt

    K_LAYER  shift, and go to state 1486

    $default  reduce using rule 180 (pin_layer_opt)

    pin_layer_opt  go to state 1490


State 1425

  149 pin_option: '+' K_ANTENNAPINMAXAREACAR NUMBER . K_LAYER $@31 T_STRING

    K_LAYER  shift, and go to state 1491


State 1426

  151 pin_option: '+' K_ANTENNAPINMAXSIDEAREACAR NUMBER . K_LAYER $@32 T_STRING

    K_LAYER  shift, and go to state 1492


State 1427

  152 pin_option: '+' K_ANTENNAPINPARTIALCUTAREA NUMBER . pin_layer_opt

    K_LAYER  shift, and go to state 1486

    $default  reduce using rule 180 (pin_layer_opt)

    pin_layer_opt  go to state 1493


State 1428

  154 pin_option: '+' K_ANTENNAPINMAXCUTCAR NUMBER . K_LAYER $@33 T_STRING

    K_LAYER  shift, and go to state 1494


State 1429

  168 pin_oxide: K_OXIDE1 .

    $default  reduce using rule 168 (pin_oxide)


State 1430

  169 pin_oxide: K_OXIDE2 .

    $default  reduce using rule 169 (pin_oxide)


State 1431

  170 pin_oxide: K_OXIDE3 .

    $default  reduce using rule 170 (pin_oxide)


State 1432

  171 pin_oxide: K_OXIDE4 .

    $default  reduce using rule 171 (pin_oxide)


State 1433

  155 pin_option: '+' K_ANTENNAMODEL pin_oxide .

    $default  reduce using rule 155 (pin_option)


State 1434

  132 pin_option: '+' K_GROUNDSENSITIVITY $@25 . T_STRING

    T_STRING  shift, and go to state 1495


State 1435

  128 pin_option: '+' K_NETEXPR QSTRING .

    $default  reduce using rule 128 (pin_option)


State 1436

  140 pin_option: '+' K_POLYGON $@28 . T_STRING $@29 pin_poly_mask_opt pin_poly_spacing_opt firstPt nextPt nextPt otherPts

    T_STRING  shift, and go to state 1496


State 1437

  130 pin_option: '+' K_SUPPLYSENSITIVITY $@24 . T_STRING

    T_STRING  shift, and go to state 1497


State 1438

  142 pin_option: '+' K_VIA $@30 . T_STRING pin_via_mask_opt '(' NUMBER NUMBER ')'

    T_STRING  shift, and go to state 1498


State 1439

  143 pin_option: placement_status pt orient .

    $default  reduce using rule 143 (pin_option)


State 1440

  254 mask: '+' K_MASK NUMBER .

    $default  reduce using rule 254 (mask)


State 1441

  229 layer_stmt: '+' K_RECT $@42 T_STRING mask pt pt .

    $default  reduce using rule 229 (layer_stmt)


State 1442

  232 layer_stmt: '+' K_POLYGON $@43 T_STRING mask $@44 firstPt . nextPt nextPt otherPts

    '('  shift, and go to state 188

    nextPt  go to state 1499
    pt      go to state 321


State 1443

  237 layer_stmt: '+' K_VIARULE $@46 T_STRING '+' K_CUTSIZE NUMBER . NUMBER '+' K_LAYERS $@47 T_STRING T_STRING T_STRING '+' K_CUTSPACING NUMBER NUMBER '+' K_ENCLOSURE NUMBER NUMBER NUMBER NUMBER

    NUMBER  shift, and go to state 1500


State 1444

  273 region_prop: T_STRING QSTRING .

    $default  reduce using rule 273 (region_prop)


State 1445

  274 region_prop: T_STRING T_STRING .

    $default  reduce using rule 274 (region_prop)


State 1446

  272 region_prop: T_STRING NUMBER .

    $default  reduce using rule 272 (region_prop)


State 1447

  805 comp_name: '(' $@146 T_STRING T_STRING . subnet_opt_syn ')'

    '+'  shift, and go to state 1501

    $default  reduce using rule 806 (subnet_opt_syn)

    subnet_opt_syn  go to state 1502


State 1448

  813 subnet_option: K_NONDEFAULTRULE $@148 . T_STRING

    T_STRING  shift, and go to state 1503


State 1449

  811 subnet_option: subnet_type $@147 . paths

    T_STRING  shift, and go to state 886

    paths  go to state 1504
    path   go to state 888


State 1450

  459 path_pt: '(' NUMBER NUMBER NUMBER . ')'

    ')'  shift, and go to state 1505


State 1451

  455 path_pt: '(' NUMBER NUMBER ')' .

    $default  reduce using rule 455 (path_pt)


State 1452

  461 path_pt: '(' NUMBER '*' NUMBER . ')'

    ')'  shift, and go to state 1506


State 1453

  457 path_pt: '(' NUMBER '*' ')' .

    $default  reduce using rule 457 (path_pt)


State 1454

  460 path_pt: '(' '*' NUMBER NUMBER . ')'

    ')'  shift, and go to state 1507


State 1455

  456 path_pt: '(' '*' NUMBER ')' .

    $default  reduce using rule 456 (path_pt)


State 1456

  462 path_pt: '(' '*' '*' NUMBER . ')'

    ')'  shift, and go to state 1508


State 1457

  458 path_pt: '(' '*' '*' ')' .

    $default  reduce using rule 458 (path_pt)


State 1458

  440 path_item: T_STRING .
  442          | T_STRING . orient
  445          | T_STRING . K_DO NUMBER K_BY NUMBER K_STEP NUMBER NUMBER
  446          | T_STRING . orient K_DO NUMBER K_BY NUMBER K_STEP NUMBER NUMBER

    K_DO  shift, and go to state 1509
    K_N   shift, and go to state 516
    K_S   shift, and go to state 517
    K_E   shift, and go to state 518
    K_W   shift, and go to state 519
    K_FN  shift, and go to state 520
    K_FE  shift, and go to state 521
    K_FS  shift, and go to state 522
    K_FW  shift, and go to state 523

    $default  reduce using rule 440 (path_item)

    orient  go to state 1510


State 1459

  437 rect_statement: K_RECT . rect_pts

    '('  shift, and go to state 1511

    rect_pts  go to state 1512


State 1460

  441 path_item: K_MASK . NUMBER T_STRING
  443          | K_MASK . NUMBER T_STRING orient
  444          | K_MASK . NUMBER T_STRING K_DO NUMBER K_BY NUMBER K_STEP NUMBER NUMBER
  447          | K_MASK . NUMBER T_STRING orient K_DO NUMBER K_BY NUMBER K_STEP NUMBER NUMBER
  451          | K_MASK . NUMBER K_RECT $@83 '(' NUMBER NUMBER NUMBER NUMBER ')'
  453          | K_MASK . NUMBER $@84 path_pt

    NUMBER  shift, and go to state 1513


State 1461

  436 virtual_statement: K_VIRTUAL . virtual_pt

    '('  shift, and go to state 1514

    virtual_pt  go to state 1515


State 1462

  448 path_item: virtual_statement .

    $default  reduce using rule 448 (path_item)


State 1463

  449 path_item: rect_statement .

    $default  reduce using rule 449 (path_item)


State 1464

  439 path_item_list: path_item_list path_item .

    $default  reduce using rule 439 (path_item_list)


State 1465

  454 path_item: path_pt .

    $default  reduce using rule 454 (path_item)


State 1466

  479 opt_shape_style: '+' K_STYLE NUMBER .

    $default  reduce using rule 479 (opt_shape_style)


State 1467

  478 opt_shape_style: '+' K_SHAPE shape_type .

    $default  reduce using rule 478 (opt_shape_style)


State 1468

  439 path_item_list: path_item_list . path_item
  570 spath: T_STRING $@102 width opt_spaths path_pt $@103 path_item_list .

    T_STRING   shift, and go to state 1458
    K_RECT     shift, and go to state 1459
    K_MASK     shift, and go to state 1460
    K_VIRTUAL  shift, and go to state 1461
    '('        shift, and go to state 1235

    $default  reduce using rule 570 (spath)

    virtual_statement  go to state 1462
    rect_statement     go to state 1463
    path_item          go to state 1464
    path_pt            go to state 1465


State 1469

  667 floating_pins: '(' T_STRING T_STRING ')' .
  668              | '(' T_STRING T_STRING ')' . '(' T_STRING T_STRING ')'
  669              | '(' T_STRING T_STRING ')' . '(' T_STRING T_STRING ')' '(' T_STRING T_STRING ')'

    '('  shift, and go to state 1516

    $default  reduce using rule 667 (floating_pins)


State 1470

  675 ordered_pins: '(' T_STRING T_STRING ')' .
  676             | '(' T_STRING T_STRING ')' . '(' T_STRING T_STRING ')'
  677             | '(' T_STRING T_STRING ')' . '(' T_STRING T_STRING ')' '(' T_STRING T_STRING ')'

    '('  shift, and go to state 1517

    $default  reduce using rule 675 (ordered_pins)


State 1471

  661 opt_common_pins: '(' T_STRING T_STRING ')' '(' T_STRING T_STRING . ')'

    ')'  shift, and go to state 1518


State 1472

  699 iotiming_drivecell_opt: iotiming_frompin K_TOPIN $@122 T_STRING $@123 . iotiming_parallel

    K_PARALLEL  shift, and go to state 1519

    $default  reduce using rule 703 (iotiming_parallel)

    iotiming_parallel  go to state 1520


State 1473

  730 row_or_comp: '(' . K_ROWS $@129 T_STRING ')'
  732            | '(' . K_COMPS $@130 T_STRING ')'

    K_COMPS  shift, and go to state 1521
    K_ROWS   shift, and go to state 1522


State 1474

  728 row_or_comp_list: row_or_comp_list row_or_comp .

    $default  reduce using rule 728 (row_or_comp_list)


State 1475

  782 minmaxpins: min_or_max_list K_PINS $@145 pin_list .
  788 pin_list: pin_list . T_STRING

    T_STRING  shift, and go to state 1523

    $default  reduce using rule 782 (minmaxpins)


State 1476

  785 min_or_max_member: K_MIN NUMBER NUMBER .

    $default  reduce using rule 785 (min_or_max_member)


State 1477

  786 min_or_max_member: K_MAX NUMBER NUMBER .

    $default  reduce using rule 786 (min_or_max_member)


State 1478

  835 pin_prop_name_value: T_STRING QSTRING .

    $default  reduce using rule 835 (pin_prop_name_value)


State 1479

  836 pin_prop_name_value: T_STRING T_STRING .

    $default  reduce using rule 836 (pin_prop_name_value)


State 1480

  834 pin_prop_name_value: T_STRING NUMBER .

    $default  reduce using rule 834 (pin_prop_name_value)


State 1481

  930 nondefault_option: '+' K_LAYER $@168 T_STRING K_WIDTH NUMBER $@169 nondefault_layer_options .
  939 nondefault_layer_options: nondefault_layer_options . nondefault_layer_option

    K_SPACING    shift, and go to state 1524
    K_DIAGWIDTH  shift, and go to state 1525
    K_WIREEXT    shift, and go to state 1526

    $default  reduce using rule 930 (nondefault_option)

    nondefault_layer_option  go to state 1527


State 1482

  190 row_step_option: K_STEP NUMBER NUMBER .

    $default  reduce using rule 190 (row_step_option)


State 1483

  197 row_prop: T_STRING . NUMBER
  198         | T_STRING . QSTRING
  199         | T_STRING . T_STRING

    QSTRING   shift, and go to state 1528
    T_STRING  shift, and go to state 1529
    NUMBER    shift, and go to state 1530


State 1484

  196 row_prop_list: row_prop_list row_prop .

    $default  reduce using rule 196 (row_prop_list)


State 1485

  137 pin_option: '+' K_LAYER $@26 T_STRING . $@27 pin_layer_mask_opt pin_layer_spacing_opt pt pt

    $default  reduce using rule 136 ($@27)

    $@27  go to state 1531


State 1486

  182 pin_layer_opt: K_LAYER . $@34 T_STRING

    $default  reduce using rule 181 ($@34)

    $@34  go to state 1532


State 1487

  144 pin_option: '+' K_ANTENNAPINPARTIALMETALAREA NUMBER pin_layer_opt .

    $default  reduce using rule 144 (pin_option)


State 1488

  145 pin_option: '+' K_ANTENNAPINPARTIALMETALSIDEAREA NUMBER pin_layer_opt .

    $default  reduce using rule 145 (pin_option)


State 1489

  146 pin_option: '+' K_ANTENNAPINGATEAREA NUMBER pin_layer_opt .

    $default  reduce using rule 146 (pin_option)


State 1490

  147 pin_option: '+' K_ANTENNAPINDIFFAREA NUMBER pin_layer_opt .

    $default  reduce using rule 147 (pin_option)


State 1491

  149 pin_option: '+' K_ANTENNAPINMAXAREACAR NUMBER K_LAYER . $@31 T_STRING

    $default  reduce using rule 148 ($@31)

    $@31  go to state 1533


State 1492

  151 pin_option: '+' K_ANTENNAPINMAXSIDEAREACAR NUMBER K_LAYER . $@32 T_STRING

    $default  reduce using rule 150 ($@32)

    $@32  go to state 1534


State 1493

  152 pin_option: '+' K_ANTENNAPINPARTIALCUTAREA NUMBER pin_layer_opt .

    $default  reduce using rule 152 (pin_option)


State 1494

  154 pin_option: '+' K_ANTENNAPINMAXCUTCAR NUMBER K_LAYER . $@33 T_STRING

    $default  reduce using rule 153 ($@33)

    $@33  go to state 1535


State 1495

  132 pin_option: '+' K_GROUNDSENSITIVITY $@25 T_STRING .

    $default  reduce using rule 132 (pin_option)


State 1496

  140 pin_option: '+' K_POLYGON $@28 T_STRING . $@29 pin_poly_mask_opt pin_poly_spacing_opt firstPt nextPt nextPt otherPts

    $default  reduce using rule 139 ($@29)

    $@29  go to state 1536


State 1497

  130 pin_option: '+' K_SUPPLYSENSITIVITY $@24 T_STRING .

    $default  reduce using rule 130 (pin_option)


State 1498

  142 pin_option: '+' K_VIA $@30 T_STRING . pin_via_mask_opt '(' NUMBER NUMBER ')'

    K_MASK  shift, and go to state 1537

    $default  reduce using rule 158 (pin_via_mask_opt)

    pin_via_mask_opt  go to state 1538


State 1499

  232 layer_stmt: '+' K_POLYGON $@43 T_STRING mask $@44 firstPt nextPt . nextPt otherPts

    '('  shift, and go to state 188

    nextPt  go to state 1539
    pt      go to state 321


State 1500

  237 layer_stmt: '+' K_VIARULE $@46 T_STRING '+' K_CUTSIZE NUMBER NUMBER . '+' K_LAYERS $@47 T_STRING T_STRING T_STRING '+' K_CUTSPACING NUMBER NUMBER '+' K_ENCLOSURE NUMBER NUMBER NUMBER NUMBER

    '+'  shift, and go to state 1540


State 1501

  807 subnet_opt_syn: '+' . K_SYNTHESIZED

    K_SYNTHESIZED  shift, and go to state 1541


State 1502

  805 comp_name: '(' $@146 T_STRING T_STRING subnet_opt_syn . ')'

    ')'  shift, and go to state 1542


State 1503

  813 subnet_option: K_NONDEFAULTRULE $@148 T_STRING .

    $default  reduce using rule 813 (subnet_option)


State 1504

  430 paths: paths . new_path
  811 subnet_option: subnet_type $@147 paths .

    K_NEW  shift, and go to state 1010

    $default  reduce using rule 811 (subnet_option)

    new_path  go to state 1011


State 1505

  459 path_pt: '(' NUMBER NUMBER NUMBER ')' .

    $default  reduce using rule 459 (path_pt)


State 1506

  461 path_pt: '(' NUMBER '*' NUMBER ')' .

    $default  reduce using rule 461 (path_pt)


State 1507

  460 path_pt: '(' '*' NUMBER NUMBER ')' .

    $default  reduce using rule 460 (path_pt)


State 1508

  462 path_pt: '(' '*' '*' NUMBER ')' .

    $default  reduce using rule 462 (path_pt)


State 1509

  445 path_item: T_STRING K_DO . NUMBER K_BY NUMBER K_STEP NUMBER NUMBER

    NUMBER  shift, and go to state 1543


State 1510

  442 path_item: T_STRING orient .
  446          | T_STRING orient . K_DO NUMBER K_BY NUMBER K_STEP NUMBER NUMBER

    K_DO  shift, and go to state 1544

    $default  reduce using rule 442 (path_item)


State 1511

  467 rect_pts: '(' . NUMBER NUMBER NUMBER NUMBER ')'

    NUMBER  shift, and go to state 1545


State 1512

  437 rect_statement: K_RECT rect_pts .

    $default  reduce using rule 437 (rect_statement)


State 1513

  441 path_item: K_MASK NUMBER . T_STRING
  443          | K_MASK NUMBER . T_STRING orient
  444          | K_MASK NUMBER . T_STRING K_DO NUMBER K_BY NUMBER K_STEP NUMBER NUMBER
  447          | K_MASK NUMBER . T_STRING orient K_DO NUMBER K_BY NUMBER K_STEP NUMBER NUMBER
  451          | K_MASK NUMBER . K_RECT $@83 '(' NUMBER NUMBER NUMBER NUMBER ')'
  453          | K_MASK NUMBER . $@84 path_pt

    T_STRING  shift, and go to state 1546
    K_RECT    shift, and go to state 1547

    $default  reduce using rule 452 ($@84)

    $@84  go to state 1548


State 1514

  463 virtual_pt: '(' . NUMBER NUMBER ')'
  464           | '(' . '*' NUMBER ')'
  465           | '(' . NUMBER '*' ')'
  466           | '(' . '*' '*' ')'

    NUMBER  shift, and go to state 1549
    '*'     shift, and go to state 1550


State 1515

  436 virtual_statement: K_VIRTUAL virtual_pt .

    $default  reduce using rule 436 (virtual_statement)


State 1516

  668 floating_pins: '(' T_STRING T_STRING ')' '(' . T_STRING T_STRING ')'
  669              | '(' T_STRING T_STRING ')' '(' . T_STRING T_STRING ')' '(' T_STRING T_STRING ')'

    T_STRING  shift, and go to state 1551


State 1517

  676 ordered_pins: '(' T_STRING T_STRING ')' '(' . T_STRING T_STRING ')'
  677             | '(' T_STRING T_STRING ')' '(' . T_STRING T_STRING ')' '(' T_STRING T_STRING ')'

    T_STRING  shift, and go to state 1552


State 1518

  661 opt_common_pins: '(' T_STRING T_STRING ')' '(' T_STRING T_STRING ')' .

    $default  reduce using rule 661 (opt_common_pins)


State 1519

  704 iotiming_parallel: K_PARALLEL . NUMBER

    NUMBER  shift, and go to state 1553


State 1520

  699 iotiming_drivecell_opt: iotiming_frompin K_TOPIN $@122 T_STRING $@123 iotiming_parallel .

    $default  reduce using rule 699 (iotiming_drivecell_opt)


State 1521

  732 row_or_comp: '(' K_COMPS . $@130 T_STRING ')'

    $default  reduce using rule 731 ($@130)

    $@130  go to state 1554


State 1522

  730 row_or_comp: '(' K_ROWS . $@129 T_STRING ')'

    $default  reduce using rule 729 ($@129)

    $@129  go to state 1555


State 1523

  788 pin_list: pin_list T_STRING .

    $default  reduce using rule 788 (pin_list)


State 1524

  941 nondefault_layer_option: K_SPACING . NUMBER

    NUMBER  shift, and go to state 1556


State 1525

  940 nondefault_layer_option: K_DIAGWIDTH . NUMBER

    NUMBER  shift, and go to state 1557


State 1526

  942 nondefault_layer_option: K_WIREEXT . NUMBER

    NUMBER  shift, and go to state 1558


State 1527

  939 nondefault_layer_options: nondefault_layer_options nondefault_layer_option .

    $default  reduce using rule 939 (nondefault_layer_options)


State 1528

  198 row_prop: T_STRING QSTRING .

    $default  reduce using rule 198 (row_prop)


State 1529

  199 row_prop: T_STRING T_STRING .

    $default  reduce using rule 199 (row_prop)


State 1530

  197 row_prop: T_STRING NUMBER .

    $default  reduce using rule 197 (row_prop)


State 1531

  137 pin_option: '+' K_LAYER $@26 T_STRING $@27 . pin_layer_mask_opt pin_layer_spacing_opt pt pt

    K_MASK  shift, and go to state 1559

    $default  reduce using rule 156 (pin_layer_mask_opt)

    pin_layer_mask_opt  go to state 1560


State 1532

  182 pin_layer_opt: K_LAYER $@34 . T_STRING

    T_STRING  shift, and go to state 1561


State 1533

  149 pin_option: '+' K_ANTENNAPINMAXAREACAR NUMBER K_LAYER $@31 . T_STRING

    T_STRING  shift, and go to state 1562


State 1534

  151 pin_option: '+' K_ANTENNAPINMAXSIDEAREACAR NUMBER K_LAYER $@32 . T_STRING

    T_STRING  shift, and go to state 1563


State 1535

  154 pin_option: '+' K_ANTENNAPINMAXCUTCAR NUMBER K_LAYER $@33 . T_STRING

    T_STRING  shift, and go to state 1564


State 1536

  140 pin_option: '+' K_POLYGON $@28 T_STRING $@29 . pin_poly_mask_opt pin_poly_spacing_opt firstPt nextPt nextPt otherPts

    K_MASK  shift, and go to state 1565

    $default  reduce using rule 160 (pin_poly_mask_opt)

    pin_poly_mask_opt  go to state 1566


State 1537

  159 pin_via_mask_opt: K_MASK . NUMBER

    NUMBER  shift, and go to state 1567


State 1538

  142 pin_option: '+' K_VIA $@30 T_STRING pin_via_mask_opt . '(' NUMBER NUMBER ')'

    '('  shift, and go to state 1568


State 1539

  232 layer_stmt: '+' K_POLYGON $@43 T_STRING mask $@44 firstPt nextPt nextPt . otherPts

    $default  reduce using rule 247 (otherPts)

    otherPts  go to state 1569


State 1540

  237 layer_stmt: '+' K_VIARULE $@46 T_STRING '+' K_CUTSIZE NUMBER NUMBER '+' . K_LAYERS $@47 T_STRING T_STRING T_STRING '+' K_CUTSPACING NUMBER NUMBER '+' K_ENCLOSURE NUMBER NUMBER NUMBER NUMBER

    K_LAYERS  shift, and go to state 1570


State 1541

  807 subnet_opt_syn: '+' K_SYNTHESIZED .

    $default  reduce using rule 807 (subnet_opt_syn)


State 1542

  805 comp_name: '(' $@146 T_STRING T_STRING subnet_opt_syn ')' .

    $default  reduce using rule 805 (comp_name)


State 1543

  445 path_item: T_STRING K_DO NUMBER . K_BY NUMBER K_STEP NUMBER NUMBER

    K_BY  shift, and go to state 1571


State 1544

  446 path_item: T_STRING orient K_DO . NUMBER K_BY NUMBER K_STEP NUMBER NUMBER

    NUMBER  shift, and go to state 1572


State 1545

  467 rect_pts: '(' NUMBER . NUMBER NUMBER NUMBER ')'

    NUMBER  shift, and go to state 1573


State 1546

  441 path_item: K_MASK NUMBER T_STRING .
  443          | K_MASK NUMBER T_STRING . orient
  444          | K_MASK NUMBER T_STRING . K_DO NUMBER K_BY NUMBER K_STEP NUMBER NUMBER
  447          | K_MASK NUMBER T_STRING . orient K_DO NUMBER K_BY NUMBER K_STEP NUMBER NUMBER

    K_DO  shift, and go to state 1574
    K_N   shift, and go to state 516
    K_S   shift, and go to state 517
    K_E   shift, and go to state 518
    K_W   shift, and go to state 519
    K_FN  shift, and go to state 520
    K_FE  shift, and go to state 521
    K_FS  shift, and go to state 522
    K_FW  shift, and go to state 523

    $default  reduce using rule 441 (path_item)

    orient  go to state 1575


State 1547

  451 path_item: K_MASK NUMBER K_RECT . $@83 '(' NUMBER NUMBER NUMBER NUMBER ')'

    $default  reduce using rule 450 ($@83)

    $@83  go to state 1576


State 1548

  453 path_item: K_MASK NUMBER $@84 . path_pt

    '('  shift, and go to state 1235

    path_pt  go to state 1577


State 1549

  463 virtual_pt: '(' NUMBER . NUMBER ')'
  465           | '(' NUMBER . '*' ')'

    NUMBER  shift, and go to state 1578
    '*'     shift, and go to state 1579


State 1550

  464 virtual_pt: '(' '*' . NUMBER ')'
  466           | '(' '*' . '*' ')'

    NUMBER  shift, and go to state 1580
    '*'     shift, and go to state 1581


State 1551

  668 floating_pins: '(' T_STRING T_STRING ')' '(' T_STRING . T_STRING ')'
  669              | '(' T_STRING T_STRING ')' '(' T_STRING . T_STRING ')' '(' T_STRING T_STRING ')'

    T_STRING  shift, and go to state 1582


State 1552

  676 ordered_pins: '(' T_STRING T_STRING ')' '(' T_STRING . T_STRING ')'
  677             | '(' T_STRING T_STRING ')' '(' T_STRING . T_STRING ')' '(' T_STRING T_STRING ')'

    T_STRING  shift, and go to state 1583


State 1553

  704 iotiming_parallel: K_PARALLEL NUMBER .

    $default  reduce using rule 704 (iotiming_parallel)


State 1554

  732 row_or_comp: '(' K_COMPS $@130 . T_STRING ')'

    T_STRING  shift, and go to state 1584


State 1555

  730 row_or_comp: '(' K_ROWS $@129 . T_STRING ')'

    T_STRING  shift, and go to state 1585


State 1556

  941 nondefault_layer_option: K_SPACING NUMBER .

    $default  reduce using rule 941 (nondefault_layer_option)


State 1557

  940 nondefault_layer_option: K_DIAGWIDTH NUMBER .

    $default  reduce using rule 940 (nondefault_layer_option)


State 1558

  942 nondefault_layer_option: K_WIREEXT NUMBER .

    $default  reduce using rule 942 (nondefault_layer_option)


State 1559

  157 pin_layer_mask_opt: K_MASK . NUMBER

    NUMBER  shift, and go to state 1586


State 1560

  137 pin_option: '+' K_LAYER $@26 T_STRING $@27 pin_layer_mask_opt . pin_layer_spacing_opt pt pt

    K_SPACING          shift, and go to state 1587
    K_DESIGNRULEWIDTH  shift, and go to state 1588

    $default  reduce using rule 162 (pin_layer_spacing_opt)

    pin_layer_spacing_opt  go to state 1589


State 1561

  182 pin_layer_opt: K_LAYER $@34 T_STRING .

    $default  reduce using rule 182 (pin_layer_opt)


State 1562

  149 pin_option: '+' K_ANTENNAPINMAXAREACAR NUMBER K_LAYER $@31 T_STRING .

    $default  reduce using rule 149 (pin_option)


State 1563

  151 pin_option: '+' K_ANTENNAPINMAXSIDEAREACAR NUMBER K_LAYER $@32 T_STRING .

    $default  reduce using rule 151 (pin_option)


State 1564

  154 pin_option: '+' K_ANTENNAPINMAXCUTCAR NUMBER K_LAYER $@33 T_STRING .

    $default  reduce using rule 154 (pin_option)


State 1565

  161 pin_poly_mask_opt: K_MASK . NUMBER

    NUMBER  shift, and go to state 1590


State 1566

  140 pin_option: '+' K_POLYGON $@28 T_STRING $@29 pin_poly_mask_opt . pin_poly_spacing_opt firstPt nextPt nextPt otherPts

    K_SPACING          shift, and go to state 1591
    K_DESIGNRULEWIDTH  shift, and go to state 1592

    $default  reduce using rule 165 (pin_poly_spacing_opt)

    pin_poly_spacing_opt  go to state 1593


State 1567

  159 pin_via_mask_opt: K_MASK NUMBER .

    $default  reduce using rule 159 (pin_via_mask_opt)


State 1568

  142 pin_option: '+' K_VIA $@30 T_STRING pin_via_mask_opt '(' . NUMBER NUMBER ')'

    NUMBER  shift, and go to state 1594


State 1569

  232 layer_stmt: '+' K_POLYGON $@43 T_STRING mask $@44 firstPt nextPt nextPt otherPts .
  248 otherPts: otherPts . nextPt

    '('  shift, and go to state 188

    $default  reduce using rule 232 (layer_stmt)

    nextPt  go to state 531
    pt      go to state 321


State 1570

  237 layer_stmt: '+' K_VIARULE $@46 T_STRING '+' K_CUTSIZE NUMBER NUMBER '+' K_LAYERS . $@47 T_STRING T_STRING T_STRING '+' K_CUTSPACING NUMBER NUMBER '+' K_ENCLOSURE NUMBER NUMBER NUMBER NUMBER

    $default  reduce using rule 236 ($@47)

    $@47  go to state 1595


State 1571

  445 path_item: T_STRING K_DO NUMBER K_BY . NUMBER K_STEP NUMBER NUMBER

    NUMBER  shift, and go to state 1596


State 1572

  446 path_item: T_STRING orient K_DO NUMBER . K_BY NUMBER K_STEP NUMBER NUMBER

    K_BY  shift, and go to state 1597


State 1573

  467 rect_pts: '(' NUMBER NUMBER . NUMBER NUMBER ')'

    NUMBER  shift, and go to state 1598


State 1574

  444 path_item: K_MASK NUMBER T_STRING K_DO . NUMBER K_BY NUMBER K_STEP NUMBER NUMBER

    NUMBER  shift, and go to state 1599


State 1575

  443 path_item: K_MASK NUMBER T_STRING orient .
  447          | K_MASK NUMBER T_STRING orient . K_DO NUMBER K_BY NUMBER K_STEP NUMBER NUMBER

    K_DO  shift, and go to state 1600

    $default  reduce using rule 443 (path_item)


State 1576

  451 path_item: K_MASK NUMBER K_RECT $@83 . '(' NUMBER NUMBER NUMBER NUMBER ')'

    '('  shift, and go to state 1601


State 1577

  453 path_item: K_MASK NUMBER $@84 path_pt .

    $default  reduce using rule 453 (path_item)


State 1578

  463 virtual_pt: '(' NUMBER NUMBER . ')'

    ')'  shift, and go to state 1602


State 1579

  465 virtual_pt: '(' NUMBER '*' . ')'

    ')'  shift, and go to state 1603


State 1580

  464 virtual_pt: '(' '*' NUMBER . ')'

    ')'  shift, and go to state 1604


State 1581

  466 virtual_pt: '(' '*' '*' . ')'

    ')'  shift, and go to state 1605


State 1582

  668 floating_pins: '(' T_STRING T_STRING ')' '(' T_STRING T_STRING . ')'
  669              | '(' T_STRING T_STRING ')' '(' T_STRING T_STRING . ')' '(' T_STRING T_STRING ')'

    ')'  shift, and go to state 1606


State 1583

  676 ordered_pins: '(' T_STRING T_STRING ')' '(' T_STRING T_STRING . ')'
  677             | '(' T_STRING T_STRING ')' '(' T_STRING T_STRING . ')' '(' T_STRING T_STRING ')'

    ')'  shift, and go to state 1607


State 1584

  732 row_or_comp: '(' K_COMPS $@130 T_STRING . ')'

    ')'  shift, and go to state 1608


State 1585

  730 row_or_comp: '(' K_ROWS $@129 T_STRING . ')'

    ')'  shift, and go to state 1609


State 1586

  157 pin_layer_mask_opt: K_MASK NUMBER .

    $default  reduce using rule 157 (pin_layer_mask_opt)


State 1587

  163 pin_layer_spacing_opt: K_SPACING . NUMBER

    NUMBER  shift, and go to state 1610


State 1588

  164 pin_layer_spacing_opt: K_DESIGNRULEWIDTH . NUMBER

    NUMBER  shift, and go to state 1611


State 1589

  137 pin_option: '+' K_LAYER $@26 T_STRING $@27 pin_layer_mask_opt pin_layer_spacing_opt . pt pt

    '('  shift, and go to state 188

    pt  go to state 1612


State 1590

  161 pin_poly_mask_opt: K_MASK NUMBER .

    $default  reduce using rule 161 (pin_poly_mask_opt)


State 1591

  166 pin_poly_spacing_opt: K_SPACING . NUMBER

    NUMBER  shift, and go to state 1613


State 1592

  167 pin_poly_spacing_opt: K_DESIGNRULEWIDTH . NUMBER

    NUMBER  shift, and go to state 1614


State 1593

  140 pin_option: '+' K_POLYGON $@28 T_STRING $@29 pin_poly_mask_opt pin_poly_spacing_opt . firstPt nextPt nextPt otherPts

    '('  shift, and go to state 188

    firstPt  go to state 1615
    pt       go to state 190


State 1594

  142 pin_option: '+' K_VIA $@30 T_STRING pin_via_mask_opt '(' NUMBER . NUMBER ')'

    NUMBER  shift, and go to state 1616


State 1595

  237 layer_stmt: '+' K_VIARULE $@46 T_STRING '+' K_CUTSIZE NUMBER NUMBER '+' K_LAYERS $@47 . T_STRING T_STRING T_STRING '+' K_CUTSPACING NUMBER NUMBER '+' K_ENCLOSURE NUMBER NUMBER NUMBER NUMBER

    T_STRING  shift, and go to state 1617


State 1596

  445 path_item: T_STRING K_DO NUMBER K_BY NUMBER . K_STEP NUMBER NUMBER

    K_STEP  shift, and go to state 1618


State 1597

  446 path_item: T_STRING orient K_DO NUMBER K_BY . NUMBER K_STEP NUMBER NUMBER

    NUMBER  shift, and go to state 1619


State 1598

  467 rect_pts: '(' NUMBER NUMBER NUMBER . NUMBER ')'

    NUMBER  shift, and go to state 1620


State 1599

  444 path_item: K_MASK NUMBER T_STRING K_DO NUMBER . K_BY NUMBER K_STEP NUMBER NUMBER

    K_BY  shift, and go to state 1621


State 1600

  447 path_item: K_MASK NUMBER T_STRING orient K_DO . NUMBER K_BY NUMBER K_STEP NUMBER NUMBER

    NUMBER  shift, and go to state 1622


State 1601

  451 path_item: K_MASK NUMBER K_RECT $@83 '(' . NUMBER NUMBER NUMBER NUMBER ')'

    NUMBER  shift, and go to state 1623


State 1602

  463 virtual_pt: '(' NUMBER NUMBER ')' .

    $default  reduce using rule 463 (virtual_pt)


State 1603

  465 virtual_pt: '(' NUMBER '*' ')' .

    $default  reduce using rule 465 (virtual_pt)


State 1604

  464 virtual_pt: '(' '*' NUMBER ')' .

    $default  reduce using rule 464 (virtual_pt)


State 1605

  466 virtual_pt: '(' '*' '*' ')' .

    $default  reduce using rule 466 (virtual_pt)


State 1606

  668 floating_pins: '(' T_STRING T_STRING ')' '(' T_STRING T_STRING ')' .
  669              | '(' T_STRING T_STRING ')' '(' T_STRING T_STRING ')' . '(' T_STRING T_STRING ')'

    '('  shift, and go to state 1624

    $default  reduce using rule 668 (floating_pins)


State 1607

  676 ordered_pins: '(' T_STRING T_STRING ')' '(' T_STRING T_STRING ')' .
  677             | '(' T_STRING T_STRING ')' '(' T_STRING T_STRING ')' . '(' T_STRING T_STRING ')'

    '('  shift, and go to state 1625

    $default  reduce using rule 676 (ordered_pins)


State 1608

  732 row_or_comp: '(' K_COMPS $@130 T_STRING ')' .

    $default  reduce using rule 732 (row_or_comp)


State 1609

  730 row_or_comp: '(' K_ROWS $@129 T_STRING ')' .

    $default  reduce using rule 730 (row_or_comp)


State 1610

  163 pin_layer_spacing_opt: K_SPACING NUMBER .

    $default  reduce using rule 163 (pin_layer_spacing_opt)


State 1611

  164 pin_layer_spacing_opt: K_DESIGNRULEWIDTH NUMBER .

    $default  reduce using rule 164 (pin_layer_spacing_opt)


State 1612

  137 pin_option: '+' K_LAYER $@26 T_STRING $@27 pin_layer_mask_opt pin_layer_spacing_opt pt . pt

    '('  shift, and go to state 188

    pt  go to state 1626


State 1613

  166 pin_poly_spacing_opt: K_SPACING NUMBER .

    $default  reduce using rule 166 (pin_poly_spacing_opt)


State 1614

  167 pin_poly_spacing_opt: K_DESIGNRULEWIDTH NUMBER .

    $default  reduce using rule 167 (pin_poly_spacing_opt)


State 1615

  140 pin_option: '+' K_POLYGON $@28 T_STRING $@29 pin_poly_mask_opt pin_poly_spacing_opt firstPt . nextPt nextPt otherPts

    '('  shift, and go to state 188

    nextPt  go to state 1627
    pt      go to state 321


State 1616

  142 pin_option: '+' K_VIA $@30 T_STRING pin_via_mask_opt '(' NUMBER NUMBER . ')'

    ')'  shift, and go to state 1628


State 1617

  237 layer_stmt: '+' K_VIARULE $@46 T_STRING '+' K_CUTSIZE NUMBER NUMBER '+' K_LAYERS $@47 T_STRING . T_STRING T_STRING '+' K_CUTSPACING NUMBER NUMBER '+' K_ENCLOSURE NUMBER NUMBER NUMBER NUMBER

    T_STRING  shift, and go to state 1629


State 1618

  445 path_item: T_STRING K_DO NUMBER K_BY NUMBER K_STEP . NUMBER NUMBER

    NUMBER  shift, and go to state 1630


State 1619

  446 path_item: T_STRING orient K_DO NUMBER K_BY NUMBER . K_STEP NUMBER NUMBER

    K_STEP  shift, and go to state 1631


State 1620

  467 rect_pts: '(' NUMBER NUMBER NUMBER NUMBER . ')'

    ')'  shift, and go to state 1632


State 1621

  444 path_item: K_MASK NUMBER T_STRING K_DO NUMBER K_BY . NUMBER K_STEP NUMBER NUMBER

    NUMBER  shift, and go to state 1633


State 1622

  447 path_item: K_MASK NUMBER T_STRING orient K_DO NUMBER . K_BY NUMBER K_STEP NUMBER NUMBER

    K_BY  shift, and go to state 1634


State 1623

  451 path_item: K_MASK NUMBER K_RECT $@83 '(' NUMBER . NUMBER NUMBER NUMBER ')'

    NUMBER  shift, and go to state 1635


State 1624

  669 floating_pins: '(' T_STRING T_STRING ')' '(' T_STRING T_STRING ')' '(' . T_STRING T_STRING ')'

    T_STRING  shift, and go to state 1636


State 1625

  677 ordered_pins: '(' T_STRING T_STRING ')' '(' T_STRING T_STRING ')' '(' . T_STRING T_STRING ')'

    T_STRING  shift, and go to state 1637


State 1626

  137 pin_option: '+' K_LAYER $@26 T_STRING $@27 pin_layer_mask_opt pin_layer_spacing_opt pt pt .

    $default  reduce using rule 137 (pin_option)


State 1627

  140 pin_option: '+' K_POLYGON $@28 T_STRING $@29 pin_poly_mask_opt pin_poly_spacing_opt firstPt nextPt . nextPt otherPts

    '('  shift, and go to state 188

    nextPt  go to state 1638
    pt      go to state 321


State 1628

  142 pin_option: '+' K_VIA $@30 T_STRING pin_via_mask_opt '(' NUMBER NUMBER ')' .

    $default  reduce using rule 142 (pin_option)


State 1629

  237 layer_stmt: '+' K_VIARULE $@46 T_STRING '+' K_CUTSIZE NUMBER NUMBER '+' K_LAYERS $@47 T_STRING T_STRING . T_STRING '+' K_CUTSPACING NUMBER NUMBER '+' K_ENCLOSURE NUMBER NUMBER NUMBER NUMBER

    T_STRING  shift, and go to state 1639


State 1630

  445 path_item: T_STRING K_DO NUMBER K_BY NUMBER K_STEP NUMBER . NUMBER

    NUMBER  shift, and go to state 1640


State 1631

  446 path_item: T_STRING orient K_DO NUMBER K_BY NUMBER K_STEP . NUMBER NUMBER

    NUMBER  shift, and go to state 1641


State 1632

  467 rect_pts: '(' NUMBER NUMBER NUMBER NUMBER ')' .

    $default  reduce using rule 467 (rect_pts)


State 1633

  444 path_item: K_MASK NUMBER T_STRING K_DO NUMBER K_BY NUMBER . K_STEP NUMBER NUMBER

    K_STEP  shift, and go to state 1642


State 1634

  447 path_item: K_MASK NUMBER T_STRING orient K_DO NUMBER K_BY . NUMBER K_STEP NUMBER NUMBER

    NUMBER  shift, and go to state 1643


State 1635

  451 path_item: K_MASK NUMBER K_RECT $@83 '(' NUMBER NUMBER . NUMBER NUMBER ')'

    NUMBER  shift, and go to state 1644


State 1636

  669 floating_pins: '(' T_STRING T_STRING ')' '(' T_STRING T_STRING ')' '(' T_STRING . T_STRING ')'

    T_STRING  shift, and go to state 1645


State 1637

  677 ordered_pins: '(' T_STRING T_STRING ')' '(' T_STRING T_STRING ')' '(' T_STRING . T_STRING ')'

    T_STRING  shift, and go to state 1646


State 1638

  140 pin_option: '+' K_POLYGON $@28 T_STRING $@29 pin_poly_mask_opt pin_poly_spacing_opt firstPt nextPt nextPt . otherPts

    $default  reduce using rule 247 (otherPts)

    otherPts  go to state 1647


State 1639

  237 layer_stmt: '+' K_VIARULE $@46 T_STRING '+' K_CUTSIZE NUMBER NUMBER '+' K_LAYERS $@47 T_STRING T_STRING T_STRING . '+' K_CUTSPACING NUMBER NUMBER '+' K_ENCLOSURE NUMBER NUMBER NUMBER NUMBER

    '+'  shift, and go to state 1648


State 1640

  445 path_item: T_STRING K_DO NUMBER K_BY NUMBER K_STEP NUMBER NUMBER .

    $default  reduce using rule 445 (path_item)


State 1641

  446 path_item: T_STRING orient K_DO NUMBER K_BY NUMBER K_STEP NUMBER . NUMBER

    NUMBER  shift, and go to state 1649


State 1642

  444 path_item: K_MASK NUMBER T_STRING K_DO NUMBER K_BY NUMBER K_STEP . NUMBER NUMBER

    NUMBER  shift, and go to state 1650


State 1643

  447 path_item: K_MASK NUMBER T_STRING orient K_DO NUMBER K_BY NUMBER . K_STEP NUMBER NUMBER

    K_STEP  shift, and go to state 1651


State 1644

  451 path_item: K_MASK NUMBER K_RECT $@83 '(' NUMBER NUMBER NUMBER . NUMBER ')'

    NUMBER  shift, and go to state 1652


State 1645

  669 floating_pins: '(' T_STRING T_STRING ')' '(' T_STRING T_STRING ')' '(' T_STRING T_STRING . ')'

    ')'  shift, and go to state 1653


State 1646

  677 ordered_pins: '(' T_STRING T_STRING ')' '(' T_STRING T_STRING ')' '(' T_STRING T_STRING . ')'

    ')'  shift, and go to state 1654


State 1647

  140 pin_option: '+' K_POLYGON $@28 T_STRING $@29 pin_poly_mask_opt pin_poly_spacing_opt firstPt nextPt nextPt otherPts .
  248 otherPts: otherPts . nextPt

    '('  shift, and go to state 188

    $default  reduce using rule 140 (pin_option)

    nextPt  go to state 531
    pt      go to state 321


State 1648

  237 layer_stmt: '+' K_VIARULE $@46 T_STRING '+' K_CUTSIZE NUMBER NUMBER '+' K_LAYERS $@47 T_STRING T_STRING T_STRING '+' . K_CUTSPACING NUMBER NUMBER '+' K_ENCLOSURE NUMBER NUMBER NUMBER NUMBER

    K_CUTSPACING  shift, and go to state 1655


State 1649

  446 path_item: T_STRING orient K_DO NUMBER K_BY NUMBER K_STEP NUMBER NUMBER .

    $default  reduce using rule 446 (path_item)


State 1650

  444 path_item: K_MASK NUMBER T_STRING K_DO NUMBER K_BY NUMBER K_STEP NUMBER . NUMBER

    NUMBER  shift, and go to state 1656


State 1651

  447 path_item: K_MASK NUMBER T_STRING orient K_DO NUMBER K_BY NUMBER K_STEP . NUMBER NUMBER

    NUMBER  shift, and go to state 1657


State 1652

  451 path_item: K_MASK NUMBER K_RECT $@83 '(' NUMBER NUMBER NUMBER NUMBER . ')'

    ')'  shift, and go to state 1658


State 1653

  669 floating_pins: '(' T_STRING T_STRING ')' '(' T_STRING T_STRING ')' '(' T_STRING T_STRING ')' .

    $default  reduce using rule 669 (floating_pins)


State 1654

  677 ordered_pins: '(' T_STRING T_STRING ')' '(' T_STRING T_STRING ')' '(' T_STRING T_STRING ')' .

    $default  reduce using rule 677 (ordered_pins)


State 1655

  237 layer_stmt: '+' K_VIARULE $@46 T_STRING '+' K_CUTSIZE NUMBER NUMBER '+' K_LAYERS $@47 T_STRING T_STRING T_STRING '+' K_CUTSPACING . NUMBER NUMBER '+' K_ENCLOSURE NUMBER NUMBER NUMBER NUMBER

    NUMBER  shift, and go to state 1659


State 1656

  444 path_item: K_MASK NUMBER T_STRING K_DO NUMBER K_BY NUMBER K_STEP NUMBER NUMBER .

    $default  reduce using rule 444 (path_item)


State 1657

  447 path_item: K_MASK NUMBER T_STRING orient K_DO NUMBER K_BY NUMBER K_STEP NUMBER . NUMBER

    NUMBER  shift, and go to state 1660


State 1658

  451 path_item: K_MASK NUMBER K_RECT $@83 '(' NUMBER NUMBER NUMBER NUMBER ')' .

    $default  reduce using rule 451 (path_item)


State 1659

  237 layer_stmt: '+' K_VIARULE $@46 T_STRING '+' K_CUTSIZE NUMBER NUMBER '+' K_LAYERS $@47 T_STRING T_STRING T_STRING '+' K_CUTSPACING NUMBER . NUMBER '+' K_ENCLOSURE NUMBER NUMBER NUMBER NUMBER

    NUMBER  shift, and go to state 1661


State 1660

  447 path_item: K_MASK NUMBER T_STRING orient K_DO NUMBER K_BY NUMBER K_STEP NUMBER NUMBER .

    $default  reduce using rule 447 (path_item)


State 1661

  237 layer_stmt: '+' K_VIARULE $@46 T_STRING '+' K_CUTSIZE NUMBER NUMBER '+' K_LAYERS $@47 T_STRING T_STRING T_STRING '+' K_CUTSPACING NUMBER NUMBER . '+' K_ENCLOSURE NUMBER NUMBER NUMBER NUMBER

    '+'  shift, and go to state 1662


State 1662

  237 layer_stmt: '+' K_VIARULE $@46 T_STRING '+' K_CUTSIZE NUMBER NUMBER '+' K_LAYERS $@47 T_STRING T_STRING T_STRING '+' K_CUTSPACING NUMBER NUMBER '+' . K_ENCLOSURE NUMBER NUMBER NUMBER NUMBER

    K_ENCLOSURE  shift, and go to state 1663


State 1663

  237 layer_stmt: '+' K_VIARULE $@46 T_STRING '+' K_CUTSIZE NUMBER NUMBER '+' K_LAYERS $@47 T_STRING T_STRING T_STRING '+' K_CUTSPACING NUMBER NUMBER '+' K_ENCLOSURE . NUMBER NUMBER NUMBER NUMBER

    NUMBER  shift, and go to state 1664


State 1664

  237 layer_stmt: '+' K_VIARULE $@46 T_STRING '+' K_CUTSIZE NUMBER NUMBER '+' K_LAYERS $@47 T_STRING T_STRING T_STRING '+' K_CUTSPACING NUMBER NUMBER '+' K_ENCLOSURE NUMBER . NUMBER NUMBER NUMBER

    NUMBER  shift, and go to state 1665


State 1665

  237 layer_stmt: '+' K_VIARULE $@46 T_STRING '+' K_CUTSIZE NUMBER NUMBER '+' K_LAYERS $@47 T_STRING T_STRING T_STRING '+' K_CUTSPACING NUMBER NUMBER '+' K_ENCLOSURE NUMBER NUMBER . NUMBER NUMBER

    NUMBER  shift, and go to state 1666


State 1666

  237 layer_stmt: '+' K_VIARULE $@46 T_STRING '+' K_CUTSIZE NUMBER NUMBER '+' K_LAYERS $@47 T_STRING T_STRING T_STRING '+' K_CUTSPACING NUMBER NUMBER '+' K_ENCLOSURE NUMBER NUMBER NUMBER . NUMBER

    NUMBER  shift, and go to state 1667


State 1667

  237 layer_stmt: '+' K_VIARULE $@46 T_STRING '+' K_CUTSIZE NUMBER NUMBER '+' K_LAYERS $@47 T_STRING T_STRING T_STRING '+' K_CUTSPACING NUMBER NUMBER '+' K_ENCLOSURE NUMBER NUMBER NUMBER NUMBER .

    $default  reduce using rule 237 (layer_stmt)
