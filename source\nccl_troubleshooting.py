#!/usr/bin/env python3
##
# @file   nccl_troubleshooting.py
# <AUTHOR> Assistant
# @date   2024
# @brief  NCCL troubleshooting and diagnostic tools
#

import os
import sys
import torch
import torch.distributed as dist
import subprocess
import time
import logging

logger = logging.getLogger(__name__)

def check_nccl_environment():
    """Check NCCL environment and configuration"""
    print("🔍 NCCL Environment Check")
    print("=" * 50)
    
    # Check CUDA availability
    print(f"CUDA available: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"CUDA devices: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            props = torch.cuda.get_device_properties(i)
            print(f"  Device {i}: {props.name} ({props.total_memory // 1024**3} GB)")
    
    # Check NCCL environment variables
    nccl_vars = [
        'NCCL_DEBUG', 'NCCL_TIMEOUT', 'NCCL_BLOCKING_WAIT',
        'NCCL_ASYNC_ERROR_HANDLING', 'NCCL_DESYNC_DEBUG',
        'NCCL_IB_TIMEOUT', 'NCCL_IB_RETRY_CNT',
        'NCCL_SOCKET_NTHREADS', 'NCCL_NSOCKS_PERTHREAD',
        'NCCL_BUFFSIZE', 'NCCL_P2P_DISABLE', 'NCCL_SHM_DISABLE'
    ]
    
    print("\nNCCL Environment Variables:")
    for var in nccl_vars:
        value = os.environ.get(var, 'Not set')
        print(f"  {var}: {value}")
    
    # Check network configuration
    print("\nNetwork Configuration:")
    try:
        hostname = subprocess.check_output(['hostname'], text=True).strip()
        print(f"  Hostname: {hostname}")
        
        # Check if InfiniBand is available
        try:
            ib_devices = subprocess.check_output(['ibstat'], text=True, stderr=subprocess.DEVNULL)
            print("  InfiniBand: Available")
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("  InfiniBand: Not available")
        
        # Check network interfaces
        try:
            ip_output = subprocess.check_output(['ip', 'addr'], text=True)
            interfaces = []
            for line in ip_output.split('\n'):
                if 'inet ' in line and '127.0.0.1' not in line:
                    interfaces.append(line.strip())
            print(f"  Network interfaces: {len(interfaces)} found")
        except Exception:
            print("  Network interfaces: Unable to check")
            
    except Exception as e:
        print(f"  Error checking network: {e}")

def test_nccl_basic_communication(rank=0, world_size=1):
    """Test basic NCCL communication"""
    print(f"\n🧪 NCCL Basic Communication Test (Rank {rank}/{world_size})")
    print("=" * 50)
    
    if not torch.cuda.is_available():
        print("❌ CUDA not available, cannot test NCCL")
        return False
    
    try:
        # Set device
        torch.cuda.set_device(rank % torch.cuda.device_count())
        device = torch.cuda.current_device()
        print(f"Using CUDA device: {device}")
        
        # Test tensor creation
        test_tensor = torch.tensor([rank], dtype=torch.float32).cuda()
        print(f"✅ Created test tensor: {test_tensor}")
        
        # Test CUDA operations
        result = test_tensor * 2
        print(f"✅ CUDA operations work: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ Basic CUDA test failed: {e}")
        return False

def test_nccl_memory_usage():
    """Test NCCL memory usage patterns"""
    print("\n💾 NCCL Memory Usage Test")
    print("=" * 50)
    
    if not torch.cuda.is_available():
        print("❌ CUDA not available")
        return False
    
    try:
        device = torch.cuda.current_device()
        
        # Check initial memory
        initial_memory = torch.cuda.memory_allocated(device)
        max_memory = torch.cuda.max_memory_allocated(device)
        total_memory = torch.cuda.get_device_properties(device).total_memory
        
        print(f"Initial memory: {initial_memory / 1024**2:.1f} MB")
        print(f"Max memory: {max_memory / 1024**2:.1f} MB")
        print(f"Total memory: {total_memory / 1024**3:.1f} GB")
        
        # Test large tensor allocation
        test_sizes = [1024, 1024*1024, 10*1024*1024]  # 1KB, 1MB, 10MB
        
        for size in test_sizes:
            try:
                tensor = torch.randn(size, device='cuda')
                current_memory = torch.cuda.memory_allocated(device)
                print(f"✅ Allocated {size} elements: {current_memory / 1024**2:.1f} MB")
                del tensor
                torch.cuda.empty_cache()
            except Exception as e:
                print(f"❌ Failed to allocate {size} elements: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Memory test failed: {e}")
        return False

def diagnose_nccl_error(error_message):
    """Diagnose NCCL error and provide suggestions"""
    print(f"\n🔧 NCCL Error Diagnosis")
    print("=" * 50)
    print(f"Error message: {error_message}")
    
    suggestions = []
    
    if "timeout" in error_message.lower():
        suggestions.extend([
            "Increase NCCL_TIMEOUT (current default: 1800s)",
            "Check network connectivity between nodes",
            "Verify all processes are running and not stuck",
            "Check for GPU memory issues",
            "Consider reducing batch size or model size"
        ])
    
    if "out of memory" in error_message.lower():
        suggestions.extend([
            "Reduce batch size",
            "Use gradient checkpointing",
            "Increase NCCL_BUFFSIZE if too small",
            "Check for memory leaks",
            "Use mixed precision training"
        ])
    
    if "connection" in error_message.lower() or "network" in error_message.lower():
        suggestions.extend([
            "Check network connectivity: ping between nodes",
            "Verify firewall settings",
            "Check if InfiniBand is properly configured",
            "Try different network interface with NCCL_SOCKET_IFNAME",
            "Disable P2P if causing issues: NCCL_P2P_DISABLE=1"
        ])
    
    if "corrupted" in error_message.lower() or "incomplete" in error_message.lower():
        suggestions.extend([
            "This indicates data corruption - restart training",
            "Check GPU hardware health",
            "Verify CUDA driver compatibility",
            "Check for overheating issues",
            "Try reducing communication frequency"
        ])
    
    if not suggestions:
        suggestions = [
            "Enable NCCL debug: NCCL_DEBUG=INFO",
            "Check system logs for hardware errors",
            "Verify PyTorch and NCCL versions compatibility",
            "Try running with fewer GPUs to isolate the issue",
            "Check CUDA and driver versions"
        ]
    
    print("\n💡 Suggested Solutions:")
    for i, suggestion in enumerate(suggestions, 1):
        print(f"  {i}. {suggestion}")

def create_nccl_debug_script():
    """Create a debug script for NCCL issues"""
    script_content = '''#!/bin/bash
# NCCL Debug Script
echo "=== NCCL Debug Information ==="

echo "1. System Information:"
uname -a
nvidia-smi

echo "2. NCCL Environment Variables:"
env | grep NCCL

echo "3. Network Information:"
hostname
ip addr show
if command -v ibstat &> /dev/null; then
    echo "InfiniBand Status:"
    ibstat
fi

echo "4. CUDA Information:"
nvcc --version
python -c "import torch; print(f'PyTorch: {torch.__version__}'); print(f'CUDA: {torch.version.cuda}'); print(f'NCCL: {torch.cuda.nccl.version()}')"

echo "5. Process Information:"
ps aux | grep python

echo "6. Memory Information:"
free -h
nvidia-smi --query-gpu=memory.used,memory.total --format=csv

echo "=== End Debug Information ==="
'''
    
    with open('nccl_debug.sh', 'w') as f:
        f.write(script_content)
    
    os.chmod('nccl_debug.sh', 0o755)
    print("✅ Created nccl_debug.sh script")
    print("   Run with: ./nccl_debug.sh > nccl_debug.log 2>&1")

def main():
    """Main troubleshooting function"""
    print("🚨 NCCL Troubleshooting Tool")
    print("=" * 60)
    
    # Check environment
    check_nccl_environment()
    
    # Test basic functionality
    test_nccl_basic_communication()
    
    # Test memory usage
    test_nccl_memory_usage()
    
    # Create debug script
    create_nccl_debug_script()
    
    print("\n📋 Quick Fixes for Common NCCL Issues:")
    print("=" * 50)
    
    quick_fixes = [
        "Set NCCL_DEBUG=INFO for detailed logging",
        "Increase timeout: NCCL_TIMEOUT=3600",
        "Enable blocking wait: NCCL_BLOCKING_WAIT=1",
        "Try disabling P2P: NCCL_P2P_DISABLE=1",
        "Reduce buffer size: NCCL_BUFFSIZE=4194304",
        "Check GPU memory with nvidia-smi",
        "Restart training with fewer GPUs",
        "Verify network connectivity between nodes"
    ]
    
    for i, fix in enumerate(quick_fixes, 1):
        print(f"  {i}. {fix}")
    
    print(f"\n🔗 Useful Commands:")
    print("  nvidia-smi                    # Check GPU status")
    print("  nvidia-smi -l 1              # Monitor GPU continuously")
    print("  watch -n 1 nvidia-smi       # Monitor with watch")
    print("  dmesg | grep -i nccl         # Check system logs")
    print("  netstat -tuln                # Check network ports")
    
    # Example error diagnosis
    example_error = "Some NCCL operations have failed or timed out"
    diagnose_nccl_error(example_error)

if __name__ == '__main__':
    main()
