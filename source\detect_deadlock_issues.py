#!/usr/bin/env python3
##
# @file   detect_deadlock_issues.py
# <AUTHOR> Assistant
# @date   2024
# @brief  Detect potential deadlock and infinite loop issues in DDP code
#

import os
import re
import ast
import sys
from typing import List, Dict, <PERSON><PERSON>

def analyze_file_for_deadlock_patterns(filepath: str) -> Dict[str, List[Tuple[int, str]]]:
    """
    @brief Analyze a Python file for potential deadlock patterns
    @param filepath path to the Python file
    @return dictionary of issues found
    """
    issues = {
        'unmatched_barriers': [],
        'unmatched_all_reduce': [],
        'infinite_loops': [],
        'blocking_operations': [],
        'rank_specific_branches': [],
        'exception_sync_issues': []
    }
    
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            lines = f.readlines()
    except Exception as e:
        print(f"Error reading {filepath}: {e}")
        return issues
    
    # Pattern matching for potential issues
    for line_num, line in enumerate(lines, 1):
        line_stripped = line.strip()
        
        # Check for unmatched barriers
        if 'dist.barrier()' in line_stripped or 'synchronize()' in line_stripped:
            # Check if it's in a conditional that might not execute on all ranks
            if 'if ' in line_stripped and ('rank' in line_stripped or 'gpu' in line_stripped):
                issues['unmatched_barriers'].append((line_num, line_stripped))
        
        # Check for unmatched all_reduce
        if 'all_reduce' in line_stripped:
            if 'if ' in line_stripped and ('rank' in line_stripped or 'gpu' in line_stripped):
                issues['unmatched_all_reduce'].append((line_num, line_stripped))
        
        # Check for potential infinite loops
        if re.match(r'\s*while\s+.*:', line_stripped):
            # Look for while loops without clear termination
            if 'True' in line_stripped or '1' in line_stripped:
                issues['infinite_loops'].append((line_num, line_stripped))
        
        # Check for blocking operations without timeout
        blocking_patterns = ['dist.barrier()', 'dist.all_reduce(', 'dist.broadcast(', 'torch.cuda.synchronize()']
        for pattern in blocking_patterns:
            if pattern in line_stripped and 'timeout' not in line_stripped:
                issues['blocking_operations'].append((line_num, line_stripped))
        
        # Check for rank-specific branches that might cause divergence
        if re.match(r'\s*if.*rank.*==.*0', line_stripped):
            issues['rank_specific_branches'].append((line_num, line_stripped))
        
        # Check for exception handling that might break synchronization
        if 'except' in line_stripped:
            # Look ahead to see if there's proper sync handling
            next_lines = lines[line_num:line_num+5] if line_num < len(lines) else []
            has_sync = any('barrier' in l or 'all_reduce' in l or 'synchronize' in l for l in next_lines)
            if not has_sync:
                issues['exception_sync_issues'].append((line_num, line_stripped))
    
    return issues

def analyze_ddp_files() -> Dict[str, Dict]:
    """
    @brief Analyze all DDP-related files for potential deadlock issues
    @return dictionary of analysis results
    """
    ddp_files = [
        'dreamplace/ddp_shared_param_utils.py',
        'dreamplace/BasicPlace_shared_ddp.py',
        'dreamplace/PlaceObj_shared_ddp.py',
        'dreamplace/NonLinearPlace_shared_ddp.py',
        'dreamplace/Placer_shared_ddp.py',
        'dreamplace/ops/weighted_average_wirelength/weighted_average_wirelength_shared_ddp.py',
        'dreamplace/ops/electric_potential/electric_potential_shared_ddp.py',
        'dreamplace/ops/electric_potential/electric_overflow_shared_ddp.py',
        'dreamplace/ops/hpwl/hpwl_shared_ddp.py'
    ]
    
    results = {}
    
    for filepath in ddp_files:
        if os.path.exists(filepath):
            print(f"\n🔍 Analyzing {filepath}...")
            issues = analyze_file_for_deadlock_patterns(filepath)
            
            # Filter out files with no issues
            has_issues = any(len(issue_list) > 0 for issue_list in issues.values())
            if has_issues:
                results[filepath] = issues
                
                # Print summary for this file
                total_issues = sum(len(issue_list) for issue_list in issues.values())
                print(f"   Found {total_issues} potential issues")
            else:
                print(f"   ✅ No obvious deadlock patterns found")
        else:
            print(f"   ❌ File not found: {filepath}")
    
    return results

def print_detailed_analysis(results: Dict[str, Dict]):
    """
    @brief Print detailed analysis results
    @param results analysis results from analyze_ddp_files
    """
    print("\n" + "="*80)
    print("DETAILED DEADLOCK ANALYSIS RESULTS")
    print("="*80)
    
    if not results:
        print("✅ No potential deadlock issues found in analyzed files!")
        return
    
    issue_descriptions = {
        'unmatched_barriers': '🚨 Unmatched Barriers (may cause deadlock)',
        'unmatched_all_reduce': '🚨 Unmatched All-Reduce (may cause deadlock)',
        'infinite_loops': '⚠️  Potential Infinite Loops',
        'blocking_operations': '⚠️  Blocking Operations Without Timeout',
        'rank_specific_branches': '⚠️  Rank-Specific Branches (may cause divergence)',
        'exception_sync_issues': '⚠️  Exception Handling Without Sync'
    }
    
    for filepath, file_issues in results.items():
        print(f"\n📁 {filepath}")
        print("-" * len(filepath))
        
        for issue_type, issue_list in file_issues.items():
            if issue_list:
                print(f"\n{issue_descriptions.get(issue_type, issue_type)}:")
                for line_num, line_content in issue_list:
                    print(f"   Line {line_num:4d}: {line_content}")

def generate_recommendations() -> List[str]:
    """
    @brief Generate recommendations for avoiding deadlocks
    @return list of recommendations
    """
    return [
        "🔧 Use timeout mechanisms for all blocking DDP operations",
        "🔧 Ensure all ranks execute the same sequence of collective operations",
        "🔧 Use try-catch blocks with proper synchronization in exception handlers",
        "🔧 Avoid rank-specific conditional branches around collective operations",
        "🔧 Use the safe_* functions from ddp_deadlock_prevention.py",
        "🔧 Add monitoring and health checks for long-running operations",
        "🔧 Implement emergency reset mechanisms for deadlock recovery",
        "🔧 Use context managers for DDP operations to ensure cleanup",
        "🔧 Test with different numbers of GPUs to catch synchronization issues",
        "🔧 Add logging to track the execution flow across different ranks"
    ]

def check_gpu_utilization_patterns():
    """
    @brief Check for patterns that might cause 100% GPU utilization without progress
    """
    print("\n🔍 Checking for GPU utilization patterns...")
    
    patterns_to_check = [
        "Infinite loops in CUDA kernels",
        "Blocking operations without progress indicators",
        "Memory allocation failures causing retries",
        "Synchronization deadlocks keeping kernels active",
        "All-reduce operations stuck in communication"
    ]
    
    print("Potential causes of 100% GPU utilization without progress:")
    for i, pattern in enumerate(patterns_to_check, 1):
        print(f"   {i}. {pattern}")
    
    print("\n💡 Debugging suggestions:")
    print("   - Use nvidia-smi to monitor GPU memory usage")
    print("   - Use nsys or nvprof to profile CUDA kernel execution")
    print("   - Add progress indicators in long-running operations")
    print("   - Monitor DDP communication with NCCL_DEBUG=INFO")
    print("   - Check for memory leaks that might cause allocation failures")

def main():
    """Main function to run deadlock detection"""
    print("🔍 DreamPlace DDP Deadlock Detection")
    print("="*50)
    
    # Analyze files for deadlock patterns
    results = analyze_ddp_files()
    
    # Print detailed analysis
    print_detailed_analysis(results)
    
    # Check GPU utilization patterns
    check_gpu_utilization_patterns()
    
    # Generate recommendations
    print("\n💡 RECOMMENDATIONS FOR DEADLOCK PREVENTION")
    print("="*50)
    recommendations = generate_recommendations()
    for rec in recommendations:
        print(rec)
    
    # Summary
    total_files_with_issues = len(results)
    total_issues = sum(sum(len(issue_list) for issue_list in file_issues.values()) 
                      for file_issues in results.values())
    
    print(f"\n📊 SUMMARY")
    print("="*20)
    print(f"Files analyzed: {len(['dreamplace/ddp_shared_param_utils.py', 'dreamplace/BasicPlace_shared_ddp.py', 'dreamplace/PlaceObj_shared_ddp.py', 'dreamplace/NonLinearPlace_shared_ddp.py'])}")
    print(f"Files with potential issues: {total_files_with_issues}")
    print(f"Total potential issues found: {total_issues}")
    
    if total_issues > 0:
        print("\n⚠️  Please review the identified issues and apply the recommended fixes.")
        print("   Use the ddp_deadlock_prevention.py utilities for safer DDP operations.")
    else:
        print("\n✅ No obvious deadlock patterns detected!")
    
    return total_issues == 0

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
