LEF_TABNAME     = lef
LEF_BISON_SRCS  = lef.y

FAKE_ALL: all

DIRNAME = lef
LEF_BISON_SRCS  = lef.y

LIBTARGET =	libclef.a

PUBLIC_HDRS =       lefiArray.h \
		    lefiCrossTalk.h \
		    lefiDebug.h \
		    lefiEncryptInt.h \
		    lefiLayer.h \
		    lefiMacro.h \
		    lefiMisc.h \
		    lefiNonDefault.h \
		    lefiProp.h \
		    lefiPropType.h \
		    lefiTypedefs.h \
		    lefiUnits.h \
		    lefiUser.h \
		    lefiUtil.h \
		    lefiVia.h \
		    lefiViaRule.h \
		    lefrReader.h \
		    lefwWriter.h \
		    lefwWriterCalls.h

LIBSRCS =           xlefiArray.cpp \
                    xlefiCrossTalk.cpp \
                    xlefiDebug.cpp \
                    xlefiEncryptInt.cpp \
                    xlefiLayer.cpp \
                    xlefiMacro.cpp \
                    xlefiMisc.cpp \
                    xlefiNonDefault.cpp \
                    xlefiProp.cpp \
                    xlefiPropType.cpp \
                    xlefiUnits.cpp \
                    xlefiUtil.cpp \
                    xlefiVia.cpp \
                    xlefiViaRule.cpp \
                    xlefrReader.cpp \
                    xlefwWriter.cpp \
                    xlefwWriterCalls.cpp

include ../template.mk
